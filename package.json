{"name": "tantan-frontend-vip", "version": "0.1.0", "private": true, "dependencies": {"@brainhubeu/react-carousel": "^1.18.6", "@types/brainhubeu__react-carousel": "^1.15.0", "@types/classnames": "^2.2.11", "@types/js-md5": "^0.4.2", "@types/lodash": "^4.14.155", "@types/qrcode.react": "^1.0.1", "@types/react-copy-to-clipboard": "^4.3.0", "@types/react-slick": "^0.23.4", "@types/react-transition-group": "^4.4.0", "@types/express": "4.17.9", "@types/node": "14.14.10", "@types/express-serve-static-core": "4.17.14", "amfe-flexible": "^2.2.1", "antd-mobile": "2.3.1", "axios": "^0.19.2", "axios-mock-adapter": "^1.17.0", "bestzip": "^2.1.5", "classnames": "^2.2.6", "clipboard": "^2.0.8", "cross-env": "^7.0.2", "html2canvas": "^1.0.0-rc.7", "i18next": "^19.8.2", "js-base64": "^3.6.0", "js-md5": "^0.7.3", "lodash": "^4.17.15", "qrcode.react": "^1.0.1", "react": "^16.13.0", "react-app-polyfill": "^3.0.0", "react-copy-to-clipboard": "^5.0.2", "react-dom": "^16.13.0", "react-ga": "^3.1.2", "react-i18next": "^11.7.3", "react-public-ip": "^1.0.0", "react-router-dom": "^5.1.2", "react-scripts": "3.4.0", "react-slick": "^0.27.13", "react-transition-group": "^4.4.1", "slick-carousel": "^1.8.1", "weixin-js-sdk": "^1.6.0"}, "scripts": {"start": "react-scripts start --watch=false", "build": "GENERATE_SOURCEMAP=true PUBLIC_URL=https://static.tancdn.com/commerce/ REACT_APP_NODE_ENV=production react-scripts build", "build-dev": "react-scripts build", "build-test": "cross-env REACT_APP_TEST=true PUBLIC_URL=/commerce/ GENERATE_SOURCEMAP=true react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject", "analyze": "source-map-explorer 'build/static/js/*.js'", "zip": "bestzip bundle.zip build/*"}, "eslintConfig": {"extends": "react-app", "rules": {"complexity": [2, 10], "max-statements": [2, 60, {"ignoreTopLevelFunctions": true}], "max-statements-per-line": [2, {"max": 1}], "max-depth": [2, 3], "max-lines": [2, {"max": 200, "skipBlankLines": true, "skipComments": true}], "max-nested-callbacks": [2, 3], "max-params": [2, 3]}}, "stylelint": {"extends": "stylelint-config-standard", "ignoreFiles": ["**/*.js"], "rules": {"color-no-invalid-hex": true, "block-no-empty": null, "comment-empty-line-before": ["always", {"ignore": ["stylelint-commands", "after-comment"]}], "max-empty-lines": 2, "unit-whitelist": ["em", "rem", "%", "s", "px", "vw"], "indentation": [2, {"except": ["block"], "message": "Please use 2 spaces for indentation.", "severity": "warning"}]}}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "homepage": ".", "proxy": "http://m.staging2.p1staff.com", "overrides": {"webpack": "4.41.5"}, "devDependencies": {"@testing-library/jest-dom": "^4.2.4", "@testing-library/react": "^9.3.2", "@testing-library/user-event": "^7.1.2", "@types/jest": "^24.0.0", "@types/node": "^12.0.0", "@types/react": "^16.9.0", "@types/react-dom": "^16.9.5", "@types/react-router-dom": "^5.1.3", "babel-plugin-import": "^1.13.0", "cross-env": "^5.2.0", "html-webpack-plugin": "^5.0.0-alpha.14", "http-proxy-middleware": "^2.0.1", "node-sass": "^4.14.1", "sass-loader": "^12.0.0", "sass-rem": "^2.0.1", "stylelint": "^13.5.0", "stylelint-config-standard": "^20.0.0", "typescript": "^3.9.3", "webpack": "^4.44.2", "webpack-cli": "^4.10.0", "webpack-dev-middleware": "^5.3.3", "webpack-dev-server": "^4.11.1"}}