variables:
  # Please edit to your GitLab project
  REPO_NAME: gitlab.p1staff.com/frontend/tantan-frontend-commerce  #需要修改   改为对应仓库地址
  HUB_PREFIX: hub.p1staff.com/devbox/
  PROD_HUB_PREFIX: hub.p1staff.com/prod/
  BUILT_PKG_ADDR: *************


stages:
  - static-check #静态代码检查
  - test  # 为自动化测试阶段保留
  - pre-build
  - build  # 打包
  - staging # 为发布staging阶段保留


########################以下deployStaging 是发布staging########################
deployStaging:
  stage: staging
  script:
   - sh docker-files/$SRV_NAME/build.sh latest ${CI_COMMIT_REF_NAME} ${CI_COMMIT_REF_NAME}
   - if (docker ps | grep docker-files_${SRV_NAME}_1); then docker stop docker-files_${SRV_NAME}_1; sleep 2; fi
   - docker run  -d --rm --net=host -v /usr/share/zoneinfo:/usr/share/zoneinfo -v /etc/localtime:/etc/localtime --name=docker-files_${SRV_NAME}_1 hub.p1staff.com/devbox/${SRV_NAME}:latest
  only:
    variables:
      - $DEPLOY_COMMITID
  tags:
    - frontend
