<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width,minimum-scale=1.0,maximum-scale=1.0,user-scalable=no">
    <title>我的群聊</title>
    <style>
        body {
            margin: 0;
            padding: 0;
        }

        .img {
            object-fit: contain;
        }

        .li_item {
            display: flex;
            padding: 14px 0;
            position: relative;
            box-sizing: border-box;
        }

        .li_item::after {
            content: '';
            display: block;
            position: absolute;
            left: -50%;
            bottom: 0;
            width: 200%;
            height: 0.5px;
            background: rgba(0, 0, 0, 0.08);
            -webkit-transform: scale(0.5);
        }

        .li_right {
            display: flex;
            flex-direction: column;
            justify-content: center;
            overflow: hidden;
            max-width: calc(100% - 72px);
        }

        ul {
            margin: 0;
            padding: 0 24px 0 16px;
        }

        .prompt {
            font-size: 14px;
            color: rgb(196, 196, 196);
            margin: 16px 16px 8px;
        }


        .li_image_wrap {
            width: 60px;
            height: 60px;
            position: relative;
            margin-right: 12px;
        }

        .li_left_img,
        .default_picture {
            /* position: absolute; */
            width: 60px;
            height: 60px;
            border-radius: 50%;
            pointer-events: none;
            background-image: url('./image/profile_avator.png');
            background-repeat: no-repeat;
            background-size: contain;
            /* flex: 1; */
            /* white-space: nowrap; */
        }

        .li_left_img {
            /* z-index: 99999; */
        }

        .li_right_title {
            line-height: 20px;
            font-size: 16px;
            color: rgb;
            color: rgb(33, 33, 33);
            display: inline-block;
            font-weight: bold;
            text-overflow: ellipsis;
            white-space: nowrap;
            overflow: hidden;
            margin-right: 6px;
        }

        .li_right_top {
            white-space: nowrap;
            display: flex;
            align-items: center;
        }

        .li_right_category {
            font-size: 11px;
            padding: 0 4px;
            height: 16px;
        }

        .li_right_desc {
            line-height: 16px;
            font-size: 13px;
            color: rgb(144, 144, 144);
            margin: 5px 0 0;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }

        .empty_page {
            display: flex;
            align-items: center;
            flex-direction: column;

        }

        .empty_page>p {
            font-size: 14px;
            line-height: 16px;
            color: rgb(208, 208, 208);
            margin: 30px 0 24px;
        }

        .empty_page>button {
            width: 140px;
            height: 40px;
            border: none;
            border-radius: 24px;
            font-size: 16px;
            line-height: 15px;
            color: rgb(255, 255, 255);
            background-color: rgb(255, 92, 49);
            outline: none;
        }

        .error_text {
            display: flex;
            justify-content: center;
            color: rgb(196, 196, 196);
            font-size: 10px;
        }

        .hidden {
            display: none;
        }
    </style>
</head>

<body>
    <div class="container_wrap">
        <div class="main">
            <div class="main_top">
                <p class="prompt">我创建的群聊（<span class="prompt_created"></span>）</p>
                <ul class="ul_created">
                    <div class="error_text">
                        <p>数据获取失败～</p>
                    </div>
                </ul>
            </div>
            <div class="main_bottom">
                <p class="prompt">我加入的群聊（<span class="prompt_joined"></span>）</p>
                <ul class="ul_joined">
                    <div class="error_text">
                        <p>数据获取失败～</p>
                    </div>
                </ul>
            </div>
        </div>
        <div class="empty_page hidden">
            <p>你还没有加入任何群聊，快去广场看看吧</p>
            <button onClick="hanldeBackGroupSquare()">去群广场</button>
        </div>
    </div>
    <script type="text/javascript" src="./jsbridge.js"></script>
    <script type="text/javascript">
        "use strict";
        window.doWhenGetToken = function () { }
        document.title = '我的群聊';
        let currentUserId = '';
        let createdGroup = [];
        let joinedGroup = [];
        let array = [];
        let triggerPvFirst = true;
        // let defaultURl = '';
        document.addEventListener("visibilitychange", function () {
            if (!document.hidden) {
                trackNew({
                    pageId: 'p_my_group_chat',
                    type: 'PV'
                })
            }
        });
        function generateLi(array, ul, mycreadted) {
            if (!array.length) {
                ul.parentElement.remove(ul.parentElement.firstChild);
                return;
            }
            let li = '';
            for (let i = 0; i < array.length; i++) {
                li += `
                    <li class="li_item" onClick="handleLiClick(${array[i].id}, ${mycreadted})" key=${i}>
                        <div class="li_image_wrap">
                            <img class="li_left_img"  onerror="this.src='./image/profile_avator.png';this.onerror=null"  src=${ array[i].avatars && array[i].avatars[0] && array[i].avatars[0].url ? array[i].avatars[0].url : ''}  >
                        </div>
                        <div class="li_right">
                            <div class="li_right_top">
                                <span class="li_right_title">${array[i].name}</span>
                                <span class="li_right_category" style="color: ${array[i].category.textColor}; background-color: ${array[i].category.backgroundColor}">${array[i].category.name}</span>
                            </div>
                            <p class="li_right_desc">${array[i].description}</p>
                        </div>
                    </li>  
                `
            }
            ul.innerHTML = li;
        }
        let ulCreated = document.querySelector('.ul_created');
        let ulJoined = document.querySelector('.ul_joined');
        let promptCreated = document.querySelector('.prompt_created');
        let promptJoined = document.querySelector('.prompt_joined');
        let main = document.querySelector('.main');
        let emptyPage = document.querySelector('.empty_page');

        function handleLiClick(publicid, bool) {
            trackNew({
                pageId: 'p_my_group_chat',
                eid: 'e_group_chat_item',
                type: 'MC',
                extras: {
                    is_my_creat: bool ? 1 : 0
                }
            })
            openWebview(`tantanapp://group/chat?groupId=${publicid}`);
        }

        function setGroupInfo(data) {
            // console.log(JSON.parse(data));
            if (!JSON.parse(data).length) {
                main.classList.add('hidden');
                emptyPage.classList.remove('hidden');
                if (triggerPvFirst) {
                    trackNew({
                        pageId: 'p_my_group_chat',
                        eid: 'e_group_chat_square_entry',
                        type: 'MV'
                    })
                    triggerPvFirst = false;
                }
                return;
            }
            if (triggerPvFirst) {
                trackNew({
                    pageId: 'p_my_group_chat',
                    type: 'PV'
                })
                triggerPvFirst = false;
            }
            array = JSON.parse(data);
            render();
        }

        function hanldeBackGroupSquare() {
            trackNew({
                pageId: 'p_my_group_chat',
                eid: 'e_group_chat_square_entry',
                type: 'MC'
            })
            openWebview(`tantanapp://group/square`);
        }


        const getJoinGroupInfo = function (params) {
            const fnName = 'getJoinGroupInfo';
            // const { setGroupInfo } = params;
            const setGroupInfo = params.setGroupInfo;
            const res = triggerAction({
                actionType: fnName,
                restParams: {
                    setGroupInfo: setGroupInfo
                }
            })
        }

        getUserInfo().then(function (res) {
            currentUserId = res.userId;
        });

        getJoinGroupInfo({
            setGroupInfo: setGroupInfo
        })

        function render() {
            createdGroup = [];
            joinedGroup = [];
            for (let i = 0; i < array.length; i++) {
                if (array[i].ownerUserId === currentUserId) {
                    createdGroup.push(array[i]);
                } else {
                    joinedGroup.push(array[i])
                }
            }
            promptCreated.innerHTML = createdGroup.length;
            promptJoined.innerHTML = joinedGroup.length;
            generateLi(createdGroup, ulCreated, true);
            generateLi(joinedGroup, ulJoined, false);

        }

    </script>
</body>

</html>