<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport"
        content="width=device-width,initial-scale=1.0,maixmum-scale=1.0,minimum-scale=1.0,user-scalable=no,viewport-fit=cover">
  <meta name=" theme-color" content="#000000"/>
  <title>Lottie</title>
  <style>
    html {
      height: 100vh;
      overflow: hidden;
    }

    body {
      width: 100vw;
      height: 100%;
      padding: 6rem 0 0;
      margin: 0;
      display: flex;
      flex-direction: column;
      align-items: center;
      background-image: url(https://auto.tancdn.com/v1/images/eyJpZCI6IjRQSFpWRVVLUVQyNVBNUlJXNEtSU0MyU1VPV0ZHNjA2IiwidyI6MTEyNSwiaCI6MjQzNiwiZCI6MCwibXQiOiJpbWFnZS9qcGVnIiwiZGgiOjU5MDg4MTI4NzEwNjM1ODUyODh9.png);
      background-position: center center;
      background-size: cover;
      position: relative;
      overflow: hidden;
    }

    .number-box {
      /*margin-top: 90rem;*/
      width: 13rem;
      height: 13rem;
      min-height: 13rem;
      position: relative;
      transform: translateY(-6rem);
      opacity: 0.2;
      overflow: hidden;
    }

    #number {
      width: 100%;
      height: 100%;
    }

    .in-number {
      position: absolute;
      width: 10rem;
      height: 4rem;
      font-family: Roboto-MediumItalic, 'Helvetica Neue';
      font-size: 2.8rem;
      line-height: 4rem;
      font-weight: 500;
      color: #fff;
      text-align: center;
      left: 50%;
      margin-left: -5rem;
      top: 50%;
      margin-top: -2rem;
      text-shadow: 0 0.2rem 0.4rem rgba(126, 81, 15, 0.5);
      font-style: italic;
    }

    .fontStyle {
      position: relative;
      font-size: 2rem;
      margin-top: 2.7rem;
      line-height: 2.8rem;
      width: 32.7rem;
      text-align: center;
      color: rgb(224, 160, 48);
      opacity: 0;
      margin-bottom: 4.2rem;
    }

    .fontCard {
      position: relative;
      width: 32.7rem;
    }

    .btn {
      width: 25rem;
      height: 5rem;
      position: absolute;
      bottom: 9rem;
      left: 50%;
      margin-left: -12.5rem;
      background-image: linear-gradient(rgb(229, 180, 95), rgb(199, 151, 60));
      border-radius: 2.5rem;
      outline: none;
      border: none;
      opacity: 0;
      overflow: hidden;
      display: none;
    }

    .btn-text {
      position: absolute;
      z-index: 9;
      top: 0;
      left: 0;
      color: #fff;
      font-size: 1.6rem;
      line-height: 5rem;
      width: 100%;
      text-align: center;
    }

    .fontBtn {
      display: none;
      width: 17.5rem;
      height: 3.8rem;
      position: absolute;
      left: 50%;
      margin-left: -8.75rem;
      bottom: 5.7rem;
      background-image: linear-gradient(rgb(229, 180, 95), rgb(199, 151, 60));
      opacity: 0.3;
      color: #fff;
      text-align: center;
      line-height: 3.8rem;
      border-radius: 1.9rem;
      font-size: 1.3rem;
    }
  </style>

  <!--  一些工具-->
  <style>
    p {
      padding: 0;
      margin: 0;
    }

    .show {
      display: block;
    }

    .hide {
      display: none;
    }
  </style>

  <!--  各种动画-->
  <style>
    .num-ani {
      -webkit-animation: number-box 0.7s ease-in-out forwards;
      -moz-animation: number-box 0.7s ease-in-out forwards;
      -o-animation: number-box 0.7s ease-in-out forwards;
      animation: number-box 0.7s ease-in-out forwards;
    }

    @keyframes number-box {
      0% {
        transform: translateY(-6rem);
        opacity: 0.2;
      }
      100% {
        transform: translateY(0);
        opacity: 1;
      }
    }

    @-webkit-keyframes number-box {
      0% {
        transform: translateY(-6rem);
        opacity: 0.2;
      }
      100% {
        transform: translateY(0);
        opacity: 1;
      }
    }

    /* 文案出现动画 - from up */
    .box-appear {
      -webkit-animation: box-appear-ani 0.5s ease-in-out forwards;
      -moz-animation: box-appear-ani 0.5s ease-in-out forwards;
      -o-animation: box-appear-ani 0.5s ease-in-out forwards;
      animation: box-appear-ani 0.5s ease-in-out forwards;
    }

    @keyframes box-appear-ani {
      0% {
        transform: translateY(-2rem);
        opacity: 0;
      }
      100% {
        transform: translateY(0);
        opacity: 1;
      }
    }

    @-webkit-keyframes box-appear-ani {
      0% {
        transform: translateY(-2rem);
        opacity: 0;
      }
      100% {
        transform: translateY(0);
        opacity: 1;
      }
    }

    /* 文案出现动画 - from down */
    .box-appear-from-down {
      display: block;
      -webkit-animation: box-appear-from-down-ani 0.5s ease-in-out forwards;
      -moz-animation: box-appear-from-down-ani 0.5s ease-in-out forwards;
      -o-animation: box-appear-from-down-ani 0.5s ease-in-out forwards;
      animation: box-appear-from-down-ani 0.5s ease-in-out forwards;
    }

    @keyframes box-appear-from-down-ani {
      0% {
        transform: translateY(2rem);
        opacity: 0;
      }
      100% {
        transform: translateY(0);
        opacity: 1;
      }
    }

    @-webkit-keyframes box-appear-from-down-ani {
      0% {
        transform: translateY(2rem);
        opacity: 0;
      }
      100% {
        transform: translateY(0);
        opacity: 1;
      }
    }

    /* 文案消失动画 */
    .box-disappear {
      -webkit-animation: box-disappear-ani 0.5s ease-in-out forwards;
      -moz-animation: box-disappear-ani 0.5s ease-in-out forwards;
      -o-animation: box-disappear-ani 0.5s ease-in-out forwards;
      animation: box-disappear-ani 0.5s ease-in-out forwards;
    }

    @keyframes box-disappear-ani {
      0% {
        transform: translateY(0);
        opacity: 0.5;
        background-position-x: 400%;
      }
      50% {
        transform: translateY(-2rem);
        opacity: 0;
        background-position-x: 0%;
      }
      100% {
        transform: translateY(-2rem);
        opacity: 0;
        background-position-x: -400%;
      }
    }

    @-webkit-keyframes box-disappear-ani {
      0% {
        transform: translateY(0);
        opacity: 0.5;
        background-position-x: 400%;
      }
      50% {
        transform: translateY(-2rem);
        opacity: 0;
        background-position-x: 0%;
      }
      100% {
        transform: translateY(-2rem);
        opacity: 0;
        background-position-x: -400%;
      }
    }

    .btn-shine {
      display: inline-block;
      position: absolute;
      left: 0;
      top: 0;
      height: 5rem;
      width: 25rem;
      background: rgb(229, 180, 95);
      background: linear-gradient(-70deg, rgb(229, 180, 95) 55%, rgba(255, 248, 159) 60%, rgb(229, 180, 95) 65%);
      background-size: 600% 100%;
      -webkit-animation: shine 2s;
      animation: shine 2s;
      -webkit-animation-delay: 0s;
      animation-delay: 0s;
      -webkit-animation-timing-function: linear;
      animation-timing-function: linear;
    }

    @keyframes shine {
      0% {
        background-position-x: 100%;
      }
      50% {
        background-position-x: 0%;
      }
      100% {
        background-position-x: -100%;
      }
    }

    @-webkit-keyframes shine {
      0% {
        background-position-x: 100%;
      }
      50% {
        background-position-x: 0%;
      }
      100% {
        background-position-x: -100%;
      }
    }
  </style>
</head>
<script src="https://cdn.bootcdn.net/ajax/libs/lottie-web/5.7.5/lottie.js"></script>
<body>
<div class="number-box" id="numberBox">
  <div id="number"></div>
  <span class="in-number" id="inNum"><span id="numberContent">0</span>%</span>
</div>
<p class="fontStyle" id="fontCenter">已了解您的交友订制诉求，正在检测及更新推荐结果…</p>
<!--未完成文案-->
<div style="position: relative;">
  <div class="fontCard" id="fonts"></div>
  <div class="fontBtn" id="fontBtn">已优化年龄和距离设置</div>
</div>
<!--完成文案-->
<div class="fontCard" id="fonts2"></div>
<button class="btn" id="btn" onclick="completeHandler()"><span class="btn-text">开始全新订制体验</span><span id="shine"></span>
</button>
</body>

<!--设置 REM-->
<script>
  "use strict";
  const isDev = location.origin.includes('10.');
  window.onresize = setRem;

  function doWhenGetToken() {
  }

  function setRem() {
    console.log(document.documentElement.offsetWidth);
    const docEle = document.documentElement;
    docEle.style.fontSize = Math.floor(document.documentElement.offsetWidth / 375 * 10) + 'px';
    console.log(docEle.style.fontSize);
    docEle.style.lineHeight = '20px';
  }

  setRem();
  if (isDev || location.origin.includes('staging2')) {
    let src = "https://auto.tancdn.com/v1/raw/d39d1ccb-9e30-4bef-ab0e-7e35058716c40300.js";
    document.write("<scr" + 'ipt src="' + src + '"></scr' + "ipt>");
    document.write("<scr" + "ipt>eruda.init();</scr" + "ipt>");
  }

  // animation.play(); // 播放，从当前帧开始播放
  //
  // animation.stop(); // 停止，并回到第0帧
  //
  // animation.pause(); // 暂停，并保持当前帧
  //
  // animation.goToAndStop(value, isFrame); // 跳到某个时刻/帧并停止isFrame(默认false)指示value表示帧还是时间(毫秒)
  //
  // animation.goToAndPlay(value, isFrame); // 跳到某个时刻/帧并进行播放
  //
  // animation.goToAndStop(30, true); // 跳转到第30帧并停止
  //
  // animation.goToAndPlay(300); // 跳转到第300毫秒并播放
  //
  // animation.playSegments(arr, forceFlag); // arr可以包含两个数字或者两个数字组成的数组，forceFlag表示是否立即强制播放该片段
  //
  // animation.playSegments([10,20], false); // 播放完之前的片段，播放10-20帧
  //
  // animation.playSegments([[0,5],[10,18]], true); // 直接播放0-5帧和10-18帧
  //
  // animation.setSpeed(speed); // 设置播放速度，speed为1表示正常速度
  //
  // animation.setDirection(direction); // 设置播放方向，1表示正向播放，-1表示反向播放
  //
  // animation.destroy(); // 删除该动画，移除相应的元素标签等。在unmount的时候，需要调用该方法
</script>
<!--jsBridge-->
<script>
  "use strict";
  const ua = typeof navigator !== 'undefined' ? navigator.userAgent.toLowerCase() : '';

  const isiOS = ~ua.indexOf('iphone') || ua === 'tantan-ios';
  const isAndroid = ~ua.indexOf('android');
  const android_method_checker = function (method) {
    return (
      isAndroid &&
      typeof window.tantan !== "undefined" &&
      typeof window.tantan[method] === "function"
    );
  }
  const ios_method_checker = function (method) {
    return isiOS
      && window.webkit
      && window.webkit.messageHandlers
      && window.webkit.messageHandlers[method]
      && typeof window.webkit.messageHandlers[method].postMessage === 'function'
  }

  const handleAndroidParamArr = function (array) {
    array = array || []
    return array.map(function (item) {
      const resultMap = {
        type: 'String',
        value: item,
      };
      if (typeof item === 'number') {
        resultMap.type = 'Integer'
        if (/^\d+\.\d+$/.test(String(item))) {
          resultMap.type = 'Double'
        }
      }
      if (typeof item === 'boolean') {
        resultMap.type = 'Boolean';
      }
      if (Object.prototype.toString.call(item) === '[object Array]') {
        resultMap.isArray = true;
      }
      return resultMap;
    })
  }

  const genUuid = function () {
    const d = +new Date();
    const range = Math.random().toFixed(3) * 1000;
    return `${d}_${range}`;
  }

  const androidMethodAccessible = function (methodName) {
    return window && window.tantan && typeof window.tantan.canIUse === 'function' && window.tantan.canIUse(methodName);
  };

  const triggerActionDiamond = function (params) {
    const actionType = params.actionType;
    const restParams = params.restParams || {};

    if (!actionType) {
      throw new Error('param actionType is REQUIRED');
    }
    const fnName = 'triggerAction'
    const uid = genUuid();

    const handleRestParams = function (params) {
      const resParams = {};
      Object.keys(params).forEach(function (key) {
        if (typeof params[key] === 'function') {
          window[`${actionType}_${key}_${uid}`] = params[key];
          return resParams[key] = `${actionType}_${key}_${uid}`;
        }
        resParams[key] = params[key];
      });
      return resParams;
    };

    if (android_method_checker(fnName)) {
      const filterParams = handleRestParams(restParams);
      const restParamsArr = Object.keys(filterParams).map(function (x) {
        return filterParams[x]
      });
      const paramArr = JSON.stringify(handleAndroidParamArr(restParamsArr));
      return window.tantan[fnName](actionType, paramArr);
    }

    if (ios_method_checker(actionType)) {
      return window.webkit.messageHandlers[actionType].postMessage(handleRestParams(restParams))
    }
    console.warn(`%c no method matched for ${actionType}`, 'color: red;font-style: italic;font-size: 2rem')
  };

  function trackNew(trackData) {
    const fnName = 'trackNew'
    if (isAndroid) {
      const type = trackData.type;
      const pageId = trackData.pageId;
      const extras = trackData.extras;
      const eid = trackData.eid || '';
      let kvs = []
      if (extras) {
        for (let key in extras) {
          kvs = kvs.concat(String(key), String(extras[key]))
        }
      }
      if (androidMethodAccessible(fnName)) {
        const paramsArr = handleAndroidParamArr([type, eid, pageId, kvs])
        return window.tantan.dispatch(fnName, JSON.stringify(paramsArr))
      } else if (android_method_checker(fnName)) {
        return window.tantan[fnName](type, eid, pageId, kvs)
      }
    } else if (ios_method_checker(fnName)) {
      return window.webkit.messageHandlers[fnName].postMessage(trackData)
    } else {
      console.info("无法进行新打点", 0.5)
    }
  }


  const COMPLETE_STR = 'complete';
  const status = getQueryVariable('status');
  const diamond_user_type = getQueryVariable('diamond_user_type');
  let hide = false;
  window.onload = function () {
    trackNew({
      pageId: 'p_custommade_update',
      type: 'PV'
    })
    let isComplete = status === COMPLETE_STR;
    let numberBoxTime = (isComplete ? 8 : 7) * 1000;
    let stopNumber = isComplete ? 100 : 96;
    const initNum = 6;
    let number = initNum;
    const fontsEle1 = document.getElementById('fonts');
    const fontsEle2 = document.getElementById('fonts2');
    const numberEle = document.getElementById('number');
    const fontCenterEle = document.getElementById('fontCenter');
    const numberContentEle = document.getElementById('numberContent');
    const numberBoxEle = document.getElementById('numberBox');
    const inNumEle = document.getElementById('inNum');
    const btnEle = document.getElementById('btn');
    const shineEle = document.getElementById('shine')
    const fontBtnEle = document.getElementById('fontBtn')
    numberBoxEle.className += ' num-ani';
    isComplete ? (fontsEle1.className += ' hide') : (fontsEle2.className += ' hide');
    const publicUrl = isDev ? '' : `${location.origin}/commerce/lottie`;
    const fontsData1 = animDataConfig(fontsEle1, `${publicUrl}/fonts/data.json`);
    const fontsData2 = animDataConfig(fontsEle2, `${publicUrl}/fonts2/data.json`);
    const numberData = animDataConfig(numberEle, `${publicUrl}/nums/data.json`);

    let fontsAni;
    let fonts2Ani;
    let numberAni = bodymovin.loadAnimation(numberData);
    // numberAni.setSpeed(0.02)
    // 数字动画
    const timer = setInterval(function () {
      // if (hide) return false;
      numberContentEle.innerText = String(number++);
      if (number > stopNumber) {
        clearInterval(timer);
        !isComplete && (numberAni.pause())
        isComplete && (inNumEle.className += ' hide')
      }
    }, numberBoxTime / (stopNumber - number))

    // 中间文案动画
    setTimeout(function () {
      fontCenterEle.className += ' box-appear'
    }, 0.7 * 1000)

    // 底部文案动画
    setTimeout(function () {
      if (hide) return false;
      fontsAni = bodymovin.loadAnimation(fontsData1);
      fontsAni.setSpeed(1.5);
      fonts2Ani = bodymovin.loadAnimation(fontsData2);
      fonts2Ani.setSpeed(1.5);
      fontsEle1.className += ' box-appear'
      fontsEle2.className += ' box-appear'
    }, 1.2 * 1000)

    // 底部按钮动画
    setTimeout(function () {
      if (isComplete) {
        fontCenterEle.innerHTML = '<p>恭喜完成订制</p><p>开始您的黑钻惊喜之旅吧</p>';
        fontsEle2.className += ' box-disappear';
        btnEle.className += ' show box-appear-from-down';
      }
    }, 8 * 1000)

    document.querySelector('#fonts').addEventListener('click', function (e) {
      if (e && e.target && e.target.nodeName && e.target.nodeName.toLocaleLowerCase() === 'image') {
        trackNew({
          pageId: 'p_custommade_update',
          eid: 'e_custommade_set',
          type: 'MC',
          extras: {diamond_user_type}
        })
        e.target.style.display = 'none';
        fontBtnEle.className += ' show';
        setTimeout(function () {
          fontsEle1.className += ' box-disappear'
          btnEle.className += ' show box-appear-from-down';
          fontBtnEle.className += ' box-disappear';
          shineEle.className += ' btn-shine';
          numberAni.play();
          isComplete = true;
          stopNumber = 100;
          numberBoxTime = 10 * 1000;
          const timer = setInterval(function () {
            if (hide) return false;
            numberContentEle.innerText = String(number++);
            if (number > stopNumber) {
              fontCenterEle.innerHTML = '<p>恭喜完成订制</p><p>开始您的黑钻惊喜之旅吧</p>';
              clearInterval(timer);
              !isComplete && (numberAni.pause())
              isComplete && (inNumEle.className += ' hide')
            }
          }, numberBoxTime / (stopNumber - initNum))
        }, 1000)
      }
    })

    document.addEventListener('visibilitychange', function () {
      let date = Date.now();
      if (document.visibilityState === 'visible') {
        console.log('window.onpageshow');
        date = Date.now();
        console.log(date);
        hide = false;
        console.log(fontsAni, fonts2Ani, numberAni);
        console.log('window.onpageshow');
        fontsAni && fontsAni.play();
        fonts2Ani && fonts2Ani.play();
        numberAni && numberAni.play();
      } else {
        console.log('window.onpagehide');
        hide = true;
        date = Date.now();
        console.log(date);
        console.log(fontsAni, fonts2Ani, numberAni);
        console.log('window.onpagehide');
        fontsAni && fontsAni.pause();
        fonts2Ani && fonts2Ani.pause();
        numberAni && numberAni.pause();
      }
    })
  }

  function completeHandler() {
    console.log('getQueryVariable');
    const status = getQueryVariable('status');
    let isComplete = status === COMPLETE_STR;
    trackNew({
      pageId: 'p_custommade_update',
      eid: 'e_custommade_update',
      type: 'MC',
      extras: {diamond_user_type}
    })
    triggerActionDiamond({
      actionType: 'privateMatchSettingsIntroFinished',
      restParams: {
        shouldAutoOptimize: !isComplete
      }
    })
  }

  function animDataConfig(ele, dataPath) {
    return {
      wrapper: ele,
      renderer: 'svg',
      // renderer: 'canvas',
      loop: false,
      prerender: true,
      autoplay: true,
      path: dataPath,
    }
  }

  function getQueryVariable(variable) {
    const query = window.location.search.substring(1);
    const vars = query.split("&");
    for (let i = 0; i < vars.length; i++) {
      const pair = vars[i].split("=");
      if (pair[0] === variable) {
        return pair[1];
      }
    }
    return false;
  }
</script>
</html>
