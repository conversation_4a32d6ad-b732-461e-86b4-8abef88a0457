"use strict";
// 中间这一段都是jsbridge
const androidMethodAccessible = function (methodName) {
  return window && window.tantan && typeof window.tantan.canIUse === 'function' && window.tantan.canIUse(methodName);
};
const openWebview = function (urlOrSchema, title) {
  title = title || '';
  if (!urlOrSchema) {
    throwErr('param url is REQUIRED!')
  }
  const fnName = 'openWebview';
  if (androidMethodAccessible(fnName)) {
    return window.tantan.dispatch(fnName, JSON.stringify(handleAndroidParamArr([urlOrSchema, title])));
  }
  if (ios_method_checker(fnName) && ios_method_checker('jumpWebview')) { // 新老版本都存在openwebview的情况，故增加用是否存在jumpWebview来区分是否为新版,若后续需要更精细化的区分，则调用获取系统信息来判断版本号
    const paramBody = /http/g.test(urlOrSchema) ? {url: urlOrSchema, title} : {schema: urlOrSchema, title};
    return new Promise(function (resolve, reject) {
      const uid = genUuid();
      window[`${fnName}Success${uid}`] = function (res) {
        resolve(JSON.parse(res))
      };
      window[`${fnName}Fail${uid}`] = function (err) {
        reject(err)
      }
      window.webkit.messageHandlers[fnName].postMessage(paramBody)
    })
  }
}


const oldIOS_method_checker = function (method) {
  return isiOS && typeof window[method] === "function";
}



const triggerAction = function (params) {
  // const { actionType, restParams = {} } = params;
  const actionType = params.actionType;
  const restParams = params.restParams || {};
  if (!actionType) {
    return throwErr('param actionType is REQUIRED');
  }
  const fnName = 'triggerAction';
  const uid = genUuid();
  const handleRestParams = function (params) {
    const resParams = {};
    Object.keys(params).forEach(function (key) {
      if (typeof params[key] === 'function') {
        window[`${actionType}_${key}_${uid}`] = params[key];
        return resParams[key] = `${actionType}_${key}_${uid}`;
      }
      resParams[key] = params[key]
    });
    return resParams;
  };
  if (actionType === 'track' && restParams.params && Object.prototype.toString.call(restParams.params) === '[object Object]') {
    let kvs = [];
    for (let key in restParams.params) {
      kvs = kvs.concat(String(key), String(restParams.params[key]));
    }
    restParams.params = kvs;
  }
  const filterParams = handleRestParams(restParams);

  if (android_method_checker('triggerAction')) {
    // todo track的特殊逻辑是否要下放至业务项目
    // const restParamsArr = Object.keys(filterParams).map((x) => filterParams[x]);
    const restParamsArr = Object.keys(filterParams).map(function (x) {
      return filterParams[x];
    });
    const paramArr = JSON.stringify(handleAndroidParamArr(restParamsArr));
    console.log(paramArr, '参数');
    console.log(actionType, 'actionType');
    return window.tantan.triggerAction(actionType, paramArr)
  }
  if (ios_method_checker(fnName)) {
    if (actionType === 'subscribeCampaign') {
      window.flagUid = uid;
    }
    let obj = {actionType: actionType};
    for (const item of Object.keys(filterParams)) {
      obj[item] = filterParams[item]
    }
    return window.webkit.messageHandlers[fnName].postMessage(obj);
  }

  const migrateMethods = [
    'refreshLiveAuth', 'jumpRecharge', 'refreshGuildAuth', 'jumpToCover', 'onTeenModeEnable',
    'verifyTeenModePassword', 'dialogJumpRecharge', 'closeLiveCampaignDialog', 'refreshUserCounters',
    'jumpToLiveAnchor', 'jumpToProfile', 'jumpToRoom', 'onRedPacketOpen', 'subscribeCampaign', 'showRechargeDialog',
    'liveGiftDialogController', 'campaignController', 'track', 'jumpToSeeOrBuySee', 'unRegisterBarRight', 'registerBarRight', 'follow', 'liveNewUserRedPacketController',
    'imagePicker', 'jumpToTopicAggregationAct', 'jumpToTopicVoteAggregationAct', 'jumpToTopic', 'jumpToProfileAct',
  ];
  if (~migrateMethods.indexOf(actionType) && (android_method_checker(actionType) || ios_method_checker(actionType) || oldIOS_method_checker(actionType))) {
    console.log('method triggerAction not exist, enter oldBridgeHandler')
    return oldBridgeHandler(actionType, restParams)
  }
  // IOS中track方法名为trackEvent
  if (actionType === 'track' && (ios_method_checker('trackEvent') || oldIOS_method_checker('trackEvent'))) {
    return oldBridgeHandler(actionType, restParams)
  }
  // console.error(`no method matched for ${fnName}`);
};

function getUserInfo() {
  const fnName = 'getUserInfo';
  if (androidMethodAccessible(fnName)) {
    const res = JSON.parse(window.tantan.dispatch(fnName, JSON.stringify([])));
    if (typeof res === 'object') {
      return Object.keys(res).length === 0 ? Promise.reject('unknown user') : Promise.resolve(res)
    }
    return console.error('internal error')
  }
  if (ios_method_checker(fnName)) {
    const uid = genUuid();
    return new Promise(function (resolve, reject) {
      window[`${fnName}Success${uid}`] = function (res) {
        resolve(JSON.parse(res));
      };
      window[`${fnName}Fail${uid}`] = function () {
        reject('unknown user');
      };
      window.webkit.messageHandlers.getUserInfo.postMessage({
        success: `${fnName}Success${uid}`,
        fail: `${fnName}Fail${uid}`,
      });
    })
  }
}
