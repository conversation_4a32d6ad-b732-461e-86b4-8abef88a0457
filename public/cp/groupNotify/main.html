<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width,minimum-scale=1.0,maximum-scale=1.0,user-scalable=no">

    <title>群通知</title>
    <style>
        * {
            outline: none;
        }

        body {
            margin: 0;
            padding: 0;
            overflow: hidden;
        }

        html {
            -webkit-text-size-adjust: none;
        }

        img {
            object-fit: cover;
        }

        ul {
            padding: 0;
            margin: 0;
        }

        .container_wrap {
            padding: 0 16px;
            position: absolute;
            top: 0;
            right: 0;
            left: 0;
            bottom: 0;
            -moz-user-select: none;
            -webkit-user-select: none;
            -ms-user-select: none;
            user-select: none;
            overflow: hidden;
        }

        .main {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            overflow: hidden;
        }

        .li_item {
            padding: 20px 16px 14px;
            display: flex;
        }

        .li_left_img {
            width: 48px;
            height: 48px;
            margin-right: 11px;
            border-radius: 50%;
            background-image: url('../image/profile_avator.png');
            background-repeat: no-repeat;
            background-size: contain;
        }

        .li_right {
            flex: 1;
            overflow: hidden;
        }

        .li_right_name {
            font-size: 15px;
            line-height: 18px;
            color: rgb(80, 80, 80);
            display: inline-block;
            font-weight: bold;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }

        .li_right_title {
            display: flex;
            justify-content: space-between;
        }

        .li_right_gender {
            font-size: 10px;
            line-height: 10px;
            color: rgb(255, 255, 255);
            background-color: rgb(243, 200, 245);
            margin-left: 5px;
            border-radius: 4px;
            display: inline-block;
            padding: 1px 4px;
            display: flex;
            justify-content: center;
            align-items: center;
        }

        .male {
            background-color: #84b9f2;
        }

        .female {
            background-color: rgb(243, 200, 245);
        }

        .li_right_content {
            margin: 4px 0 6px;
        }

        .li_right_content :first-child {
            font-size: 15px;
            line-height: 18px;
            color: rgb(117, 117, 117);
        }

        .li_right_content :last-child {
            font-size: 15px;
            line-height: 18px;
            color: rgb(80, 80, 80);
        }

        .li_right_desc {
            font-size: 13px;
            line-height: 16px;
            color: rgb(144, 144, 144);
            background-color: rgb(248, 248, 248);
            padding: 6px 8px;
            word-break: break-all;
            white-space: pre-line;
        }

        .li_right_bottom>button {
            border: none;
            border-radius: 24px;
            font-size: 12px;
            line-height: 15px;
            height: 28px;
            width: 64px;
        }

        .refuse_default {
            background-color: rgb(208, 208, 208);
            color: rgb(255, 255, 255);
        }

        .approved_default,
        .approved_default {
            color: rgb(208, 208, 208);
            background-color: rgb(255, 255, 255);
            border: 1px solid rgb(208, 208, 208) !important;

        }

        .apply_default {
            background-color: rgb(255, 92, 49);
            color: rgb(255, 255, 255);
        }

        .li_right_date {
            font-size: 12px;
            color: rgb(208, 208, 208);
            line-height: 20px;
        }

        .li_right_top {
            display: flex;
            align-items: center;
            flex: 1;
            overflow: hidden;
            padding-right: 8px;
        }

        .empty_page>p {
            font-size: 14px;
            line-height: 16px;
            color: rgb(208, 208, 208);
            margin-top: 24px;
            text-align: center;
        }

        .hidden {
            display: none;
        }

        .ul_notice {
            width: 100%;
            height: 100%;
            overflow-y: auto;
            -webkit-overflow-scrolling: touch;
        }

        .gender {
            width: 10px;
            height: 10px;
        }

        .age {
            padding-top: 2px;
            padding-left: 2px;
        }

        .loading {
            position: absolute;
            left: 50%;
            top: -30px;
            width: 20px;
            height: 20px;
            border-radius: 50%;
            background-image: url('../image/loading.png');
            background-repeat: no-repeat;
            background-size: contain;
            transform: rotateZ(0deg) translate3d(0, 0, 0);
            z-index: 1000;
        }
    </style>
</head>

<body>
    <div class="container_wrap">
        <div class="main">
            <ul class="ul_notice">

            </ul>
        </div>
        <div class="empty_page hidden">
            <p>没有群相关通知</p>
        </div>
    </div>
    <script src="../utils/jsbridge.js?path=cp/utils/jsbridge.js"></script>
    <script src="../utils/util.js?path=cp/utils/util.js"></script>
    <script src="../utils/ajax.js?path=cp/utils/ajax.js"></script>
    <script src="../utils/common.js?path=cp/utils/common.js"></script>
    <script src="../utils/load.js?path=cp/utils/load.js"></script>
    <script type="text/javascript" src="../utils/inobounce.js?path=cp/utils/inobounce.js"></script>
    <script src="../utils/zepto.js?path=cp/utils/zepto.js"></script>
    <script type="text/javascript">
        "use strict";

        const pageID = 'p_group_notice';
        function doWhenGetToken() { }
        let data = {};
        let nextLinks = {};
        let ulNotice = document.querySelector('.ul_notice');
        let emptyPage = document.querySelector('.empty_page');
        let navTitle = '群通知';

        function renderList(ul, data, refresh) {
            if (!data || !data.groupApplies || !data.groupApplies.length) return;
            let groupApplies = data.groupApplies && data.groupApplies.reverse();
            let li = '';
            let len = groupApplies.length;
            for (let i = 0; i < len; i++) {
                let applyInfo = groupApplies[i];
                let status = groupApplies[i].status;
                trackNew({
                    pageId: 'p_group_notice',
                    eid: 'e_apply_add_group_notice',
                    type: 'MV',
                    extras: {
                        groupchat_id: applyInfo.groupId,
                        is_anonymou_group: applyInfo.groupType === 'anonymous' ? '1' : '0',
                        notice_id: applyInfo.id,
                        notice_type: applyInfo.type,
                        user_id: applyInfo.userId,
                        is_agree: ''
                    }
                })
                li += `
                    <li class="li_item">
                        <img id="${applyInfo.id}" src=${applyInfo.url} class="li_left_img" onClick="JumpPersonProfile('${applyInfo.userId}', '${applyInfo.groupType}')"/>
                        <div class="li_right">
                            <div class="li_right_title">
                                <div class="li_right_top">
                                    <span class="li_right_name">${applyInfo.userName}</span>
                                    <span class="li_right_gender ${applyInfo.gender}"><img class="gender" src=${applyInfo.gender === 'female' ? '../image/female.png' : '../image/male.png'} /><span class="age">${applyInfo.userAge}</span></span>
                                </div>
                                <span class="li_right_date">${getTime(applyInfo.createdTime)}</span>
                            </div>
                            <div class="li_right_content">
                                <span>申请加群</span>
                                <span>${applyInfo.groupName}</span>
                            </div>
                            ${applyInfo.reason && `<p class="li_right_desc">${applyInfo.reason}</p>`}
                            <div class="li_right_bottom">
                                <button
                                    onClick="handleReject('${encodeURI(JSON.stringify(applyInfo))}', '${decodeURI(encodeURI(status.toString()))}', event)"
                                    class="${status === 'default' ? 'refuse_default' : status === 'approved' ? 'hidden' : 'approved_default'}">${status === 'rejected' ? '已拒绝' : '拒绝'}</button>
                                <button
                                     onClick="handleAgree('${encodeURI(JSON.stringify(groupApplies[i]))}', '${decodeURI(encodeURI(status.toString()))}', event)"
                                    class="${status === 'default' ? 'apply_default' : status === 'rejected' ? 'hidden' : 'approved_default'}">${status === 'approved' ? '已同意' : '同意'}</button>
                            </div>
                        </div>
                    </li>
                `
            }
            if (refresh) {
                $(ul).empty().append(li);
            } else {
                $(ul).append(li);
            }
        }

        // 统一处理申请群信息
        function getGroupApplies(data) {
            return data.groupApplies && data.groupApplies.map(function (applyInfo, _, arr) {
                const users = data.users;
                const chatGroups = data.chatGroups;
                const groupNotifications = data.groupNotifications;
                const groupId = applyInfo.groupId;
                const userId = applyInfo.userId;
                const groupInfo = chatGroups.find(function (item) {
                    return item.id === groupId;
                }) || {};
                const userInfo = users.find(function (item) {
                    return item.id === userId;
                });
                const groupNotification = groupNotifications.find(function (item) {
                    return item.groupApply && item.groupApply.id === applyInfo.id;
                });
                applyInfo.createdTime = groupNotification.createdTime;
                applyInfo.groupType = groupInfo.type;
                applyInfo.groupName = groupInfo.name;
                applyInfo.gender = userInfo.gender;
                applyInfo.userName = userInfo.name;
                applyInfo.userAge = userInfo.age;
                if (applyInfo.groupType !== 'anonymous' && userInfo.pictures[0].url.match(/\/images\/(.){2,}/g)) {
                    applyInfo.url = userInfo.pictures[0].url
                } else {
                    applyInfo.url = jsbridge_baseImgUrl;
                }
                if (applyInfo.groupType === 'anonymous') {
                    getAnonymityAvatarById(applyInfo, (function (userInfo) {
                        return function (url) {
                            document.getElementById(`${applyInfo.id}`).src = url;
                            applyInfo.url = url;
                        }
                    })(applyInfo))
                }
                return applyInfo;
            });
        }

        //TODO 下拉刷新加载过慢【无网络判断】
        function fetchData(refresh) {
            getInternetStatus(function () {
                ajax({
                    url: '/v3/users/me/group-notifications?limit=20&with=users,group-applies,chat-groups',
                    method: 'GET',
                    data: ''
                }).then(res => {
                    if (res && res.data && res.data.groupApplies && res.data.groupApplies.length) {
                        data.chatGroups = res.data.chatGroups;
                        data.groupApplies = getGroupApplies(res.data);
                        data.users = res.data.users;
                        nextLinks = res.link;
                        renderList(ulNotice, data, refresh);
                    } else {
                        emptyPage.classList.remove('hidden');
                    }
                })
            })
        }

        // 加载更多数据【无网络判断】
        function loadMoreData() {
            if (nextLinks.links.next) {
                ajax({
                    url: nextLinks.links.next.split('.com')[1],
                    method: 'GET',
                    data: ''
                }).then(res => {
                    data.chatGroups = res.data.chatGroups;
                    data.groupApplies = getGroupApplies(res.data);
                    data.users = res.data.users;
                    nextLinks = res.link;
                    renderList(ulNotice, data);
                })
            }
        }

        // 同意加群【无网络判断】
        function handleAgree(groupApply, status, e) {
            getInternetStatus(function () {
                if (status !== 'default') return;
                // let formatGroup = JSON.parse(decodeURI(group));
                let formatGroupApply = JSON.parse(decodeURI(groupApply));
                let formatGroup = data.chatGroups.find(item => item.id === formatGroupApply.groupId);

                requestAjax('approved', formatGroupApply.id).then(function (res) {
                    if (res === 'success') {
                        e.target.innerText = '已同意';
                        track(formatGroupApply, formatGroup, 1);
                    } else {
                        e.target.innerText = '已拒绝';
                        track(formatGroupApply, formatGroup, 0);
                    }
                    e.target.parentElement.firstElementChild.classList = 'hidden';
                    e.target.classList = 'approved_default';
                })
            })
        }

        // 拒绝加群【无网络判断】
        function handleReject(groupApply, status, e) {
            getInternetStatus(function () {
                if (status !== 'default') return;
                let formatGroupApply = JSON.parse(decodeURI(groupApply));
                let formatGroup = data.chatGroups.find(item => item.id === formatGroupApply.groupId);
                requestAjax('rejected', formatGroupApply.id).then(function (res) {
                    if (res === 'success') {
                        e.target.innerText = '已拒绝';
                        track(formatGroupApply, formatGroup, 0);
                    } else {
                        e.target.innerText = '已同意';
                        track(formatGroupApply, formatGroup, 1);
                    }
                    e.target.parentElement.lastElementChild.classList = 'hidden';
                    e.target.classList = 'approved_default';
                })
            })

        }

        function track(groupApply, formatGroup, value) {
            trackNew({
                pageId: 'p_group_notice',
                eid: 'e_apply_add_group_notice',
                type: 'MC',
                extras: {
                    groupchat_id: groupApply.groupId,
                    is_agree: value,  //这里1是同意，0是拒绝
                    is_anonymou_group: groupApply.groupType === 'anonymous' ? '1' : '0', //1代表匿名群，0代表实名
                    notice_id: groupApply.id,
                    notice_type: groupApply.type,
                    user_id: groupApply.userId
                }
            })
        }

        function requestAjax(status, id) {
            return new Promise(function (resolve, rejected) {
                ajax({
                    url: `/v3/group-applies/${id}?method=patch`,
                    method: 'PATCH',
                    data: { 'status': status }
                }).then(res => {
                    let data = res.data;
                    if (data && data.groupApplies[0]) {
                        if (data.groupApplies[0].status === status) {
                            resolve('success');
                        } else {
                            resolve()
                        }
                    }
                }).catch(res => {
                    if (res && res.meta && res.meta.code === 40399) {
                        //该用户已经注销，不能入群
                        showToast({
                            context: res.meta.message,
                            duration: 2000
                        })
                    }

                })
            })
        }

        function JumpPersonProfile(userid, type) {
            trackPD(pageID)
            openGroupMemberProfile({
                userId: userid,
                anonymity: type === "anonymous"
            })
        }

        // 少了MV和MC的埋点，待确认
        window.onload = function () {
            setWebviewPageID({ pageID: pageID });
            bindLoadEvent('.ul_notice', 600, function () {
                fetchData(true)
            });
            fetchData();
            setNavigation({
                title: navTitle
            })
        }

        document.addEventListener("visibilitychange", function () {
            if (document.visibilityState === 'visible') {
                isAndroid && setWebviewPageID({ pageID: pageID });
            }
        })

    </script>
</body>

</html>