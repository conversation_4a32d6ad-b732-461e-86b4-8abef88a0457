"use strict";
let startTime;

const POSY_MAX = 167;
const START_OFFSET = 10;
const LOADING_IMG_INIT_TOP = -50;
let hasMoved = false;

/**
 * 添加 loading 事件
 * @param listQuery         滑动list类名
 * @param distance          滑动偏移量
 * @param refreshCallback   刷新之后的回调函数
 * loadMoreData             需要在调用该JS的地方定义这个方法(主要用于加载更多数据)
 */
function bindLoadEvent(listQuery, distance, refreshCallback) {
  if (!listQuery && (typeof distance === 'number') && !refreshCallback) {
    console.error('缺少必要参数！');
  }
  let drag = false;
  let isFresh = false;
  let pageY;
  let pageX;
  let dragX;
  let dragY;
  let $bodyEle = $('body');
  let $listELe = $(listQuery);
  let $loadingEle = $('.loading');
  let scrollTimer = null;
  if (!$('#loadingStyle')[0]) {
    $('head').append($(`<style type="text/css" id="loadingStyle">
        @keyframes dash {
            0% {
                stroke-dasharray: 1, 200;
                stroke-dashoffset: 0;
            }

            50% {
                stroke-dasharray: 130, 200;
                stroke-dashoffset: -50;
            }

            100% {
                stroke-dasharray: 130, 200;
                stroke-dashoffset: -188;
            }
        }
        @keyframes rotate {
            0% {
                transform: rotate(0deg);
            }

            100% {
                transform: rotate(360deg);
            }
        }
        
        .loading {
            position: absolute;
            left: 50%;
            margin-left: -16px;
            top: -50px;
            width: 32px;
            height: 32px;
            border-radius: 50%;
            z-index: 9999;
            background: #fff;
            box-shadow: 0 0 10px #cccccc;
            box-sizing: border-box;
            padding: 3px;
        }
        
        .loadSvg {
            animation: rotate 2s linear infinite;
        }
        
        .loadCircle {
            stroke: rgb(255,92,49);
            stroke-width: 5;
            fill: none;
            animation: dash 1.5s linear infinite;
        }
    </style>`))
  }

  if (!$loadingEle[0]) {
    let loadDom = document.createElement('div');
    loadDom.className = 'loading';
    document.body.appendChild(loadDom);
    $loadingEle = $('.loading');
    $loadingEle.html(`<svg class="loadSvg" viewBox="25 25 50 50">
        <circle class="loadCircle" cx="50" cy="50" r="20" fill="none" />
    </svg>`)
  }

  function scrollFn(event, fnType) {
    const scrollTop = this.scrollTop;
    if (!hasMoved && scrollTop < 0) event.preventDefault();
    const scrollHeight = this.scrollHeight;
    const clientHeight = this.clientHeight;
    if (scrollTimer) clearTimeout(scrollTimer);
    // 触顶 ---- 刷新数据 - [iOS]
    // if (isiOS && scrollTop <= -START_OFFSET && drag) {
    //   const posY = Math.abs(scrollTop) * 2;
    //   $loadingEle.css('top', `${posY > POSY_MAX ? POSY_MAX : posY}px`);
    //   scrollTimer = setTimeout(function () {
    //     if (posY > 20) return;
    //     touchEndFn('from scroll ------')
    //     clearTimeout(scrollTimer);
    //   }, 50)
    // }
    // 触底 ---- 加载更多数据【无网络判断】
    if ((clientHeight + scrollTop) > (scrollHeight - distance - 50) && !drag) {
      console.log('触底 ---- 加载更多数据')
      loadMoreData();
      startTime = new Date().getTime();
      scrollTimer = setTimeout(function () {
        getInternetStatus();
        clearTimeout(scrollTimer);
      }, 300);
    }
  }

  // load动画初始｜最终样式
  function loadEndStyle() {
    $loadingEle
      .css('top', `${LOADING_IMG_INIT_TOP}px`)
      .css('transition', 'all .3s linear');
    $listELe.css('overflow', 'auto')
  }

  // 滑动事件 - [Android]
  function touchMoveFn(event) {
    if (drag) {
      hasMoved = true;
      dragX = event.touches[0].pageX - pageX;
      dragY = event.touches[0].pageY - pageY;
      if (dragY > 0) {
        $listELe.css('overflow', 'hidden');
        $listELe[0].scrollTop = 0;
      }
      // 横向
      if (Math.abs(dragX) > Math.abs(dragY)) {
        // console.log('横向')
      }
      // 纵向
      if (Math.abs(dragX) < Math.abs(dragY) && Math.abs(dragY) > START_OFFSET) {
        // console.log('纵向')
        // loading位置最多超过nav的底部 30(小球初-50px)+input（33px高） + nav（42px高）
        $loadingEle.css('top', `${LOADING_IMG_INIT_TOP + (dragY > POSY_MAX ? POSY_MAX : dragY)}px`)
      }
    }
  }

  // 当你正在触摸时如果来电话，或者把浏览器切换到后台，或者手指移动到了浏览器窗口外都有可能触发这个事件【无网络判断】
  function touchEndFn(event) {
    // console.log('touchend & touchcancel', event);
    if (drag) {
      if ($loadingEle[0].getBoundingClientRect().top >= 100) {
        getInternetStatus(refreshCallback);
        drag = false;
        dragY = 0;
      }
      loadEndStyle()
    }
    $bodyEle.off('touchmove');
    $bodyEle.off('touchend');
    $bodyEle.off('touchcancel');
    $listELe.off('scroll');
  }

  window.addEventListener('touchstart', function (event) {
    // console.log('body touchstart', '----- $listELe.scrollTop = ', $listELe[0].scrollTop);
    const listEle = $listELe[0];
    hasMoved = false;
    let node = event.target;
    while (node && node.nodeName !== 'BODY' && node !== listEle) {
      node = node.parentElement;
    }
    if (node !== listEle) return;
    if (Math.abs(listEle.scrollTop) < START_OFFSET) {
      $loadingEle
        .css('display', 'block')
        .css('transition', 'none');
      drag = true;
      pageX = event.touches[0].pageX;
      pageY = event.touches[0].pageY;
      $bodyEle.on('touchmove', touchMoveFn);
      $bodyEle.on('touchcancel', touchEndFn);
      $bodyEle.on('touchend', touchEndFn);
      $listELe.on('scroll', scrollFn);
    } else {
      loadEndStyle();
      $listELe.off('scroll');
      $listELe.on('scroll', throttle($listELe[0], scrollFn, 300, ['throttleFn']));
      drag = false;
    }
  }, {passive: false})
}
