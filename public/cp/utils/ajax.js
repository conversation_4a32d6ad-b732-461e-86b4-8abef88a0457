
"use strict";
// function addScript(url) {
//     document.write("<script language=javascript src=" + url + "></script>");
// }
// addScript(`../utils/zepto.js?path=cp/utils/zepto.js`);
// addScript(`../utils/jsbridge.js?path=cp/utils/jsbridge.js`);

// let baseUrl = 'https://core.tantanapp.com';


const ajax = function (config) {
    config = config || {};
    return new Promise(async function (resolve, reject) {
        let macUrl = config.url.split('?')[0];
        let abHeaders = await getAbHeader();
        let data = config.data ? JSON.stringify(config.data) : config.data;
        getNetworkEnv(
            (env) => {
                let baseUrl = (env === 'online') ? 'https://core.tantanapp.com' :'http://core.staging2.p1staff.com';
                getAuthorizationHeader(macUrl, isAndroid ? data : config.data).then(function (res) {
                    $.ajax({
                        type: config.method,
                        contentType: 'application/json;charset=utf-8',
                        data: data,
                        dataType: config.type || 'json',
                        url: `${baseUrl}${config.url}`,
                        beforeSend: function (xhr) {
                            xhr.setRequestHeader("Authorization", res);
                            xhr.setRequestHeader("X-Testing-Group", abHeaders);
                        },
                        xhrFields: { withCredentials: false },
                        success: function (result) {
                            if (result && result.meta && (result.meta.code >= 200 && result.meta.code < 300)) {
                                resolve({ data: result.data, link: result.pagination, meta: result.meta });
                            } else {
                                reject(result.meta.code)
                            }
                        },
                        error: function (XMLHttpRequest, textStatus, errorThrown) {
                            reject(XMLHttpRequest && XMLHttpRequest.response && JSON.parse(XMLHttpRequest.response));
                        }
                    })
                })
            }

        )
    })
}


