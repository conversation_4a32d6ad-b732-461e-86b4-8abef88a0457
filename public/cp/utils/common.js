// 群状态
let statusMap = {
  0: '加入',
  1: '已申请',
  2: '聊天'
}
// list是否触顶
let middleTopping = false;
// scroll滑动是否停止
let scrollStop = true;
let userid;

/**
 * 如果members有自己，状态为default说明在群内, 如果不在群内再看group-applies，如果applies有数据且状态为default说明已申请
 * @param group     群信息
 * @param data      当前用户信息
 * @return {number}
 */

getUserInfo().then(function (res) {
  userid = res.userId;
});
function getGroupStatusValue(group, data) {
  let flag = 0;// 默认加入
  // let user = data.users && data.users.length && data.users[0];
  // 聊天
  if (data.chatGroupMembers && data.chatGroupMembers.length) {
    let filterGroupMembers = data.chatGroupMembers.filter(function (_) {
      return _.userId === userid
    });
    if (filterGroupMembers.length) {
      filterGroupMembers.forEach(function (_) {
        if (_.groupId === group.id
          && _.userId === userid
          && _.status === 'default') {
          flag = 2
        }
      })
    }
  }
  // 已申请
  if (data.groupApplies && data.groupApplies.length) {
    data.groupApplies && data.groupApplies.forEach(function (_) {
      if (_.groupId === group.id
        && _.userId === userid
        && _.status === 'default') {
        flag = 1
      }
    })
  }
  return flag;
}

function clickHandle(event, item, statusValue, groupId, clickCallback, whichPage) {
  event.stopPropagation();
  let targetEle = event.target;
  !targetEle.id && (targetEle = targetEle.parentNode);
  let formatItem = JSON.parse(decodeURI(item));
  let formatStatus = +targetEle.dataset.status;
  console.log(formatStatus);

  if (scrollStop) {
    // TODO: 调jsbridge 传递id 和 callback， callback改变入组状态为已申请
    // 加入
    if (formatStatus === 0) {
      groupApply(formatItem, formatStatus, groupId);
      clickCallback(formatItem, whichPage);
    }
    // 聊天
    if (formatStatus === 2) {
      openWebview(`tantanapp://group/chat?groupId=${groupId}`);
    }
  }
}

/* 加入
  201：申请创建成功
  404：群不存在
  40399: 通用toast提醒
*/
function groupApply(item, statusValue, groupId, description, target) {
  let domItemWrapper = document.querySelector(`[data-id='${groupId}']`);

  getInternetStatus(function () {
      // 判断用户是否被ban
    getUserIsBanedToAddGroup(function (res) {
      if (res === '0') {
        ajax({
          url: `/v3/chat-groups/${item.id}/group-applies`,
          method: 'POST',
          data: {"type": "join_group", reason: description ? description : ''}
        }).then(function (res) {
          if (res.meta && res.meta.code) {
            if (res.meta.code === 201) {
              //申请成功，已通知群主处理～
              showToast({context: '申请成功，已通知群主处理'});
              if (target) {
                target.innerText = '已申请';
                target.style.color = "rgb(208,208,208)";
                target.style.backgroundColor = '#ffffff';
                target.parentElement.firstElementChild.classList.add('hidden');
              }
              if (domItemWrapper) {
                $(domItemWrapper)
                  .data('status', '1')
                  .find('.action-button')
                  .removeClass('action-button__active')
                  .addClass('action-button__application')
                  .html('已申请');
                $(domItemWrapper)
                  .find(`#groupItemAction${groupId}`)
                  .data('status', '1');
                console.log($(domItemWrapper).find(`#groupItemAction${groupId}`)[0]);
              }
            } else if (res.meta.code === 404) {
              // 群已解散，换个群试试吧～
              showToast({context: '群已解散，换个群试试吧～'})
            }
          }
        }, function (res) {
          if (res.meta.code === 40399) {
            showToast({context: res.meta.message});
          }
        })
      }
    })
  })
}

// 点击群聊item 跳转到群详情页面
function showGroupDetail(group, currentUserId, statusValue) {
  // 跳转详情在localeStorage中记录id、statue、time（时间戳）
  localStorage['square_toDetail_info'] = JSON.stringify({
    groupID: group.id,
    status: statusValue,
    time: Date.now()
  })
  return openOfflineWebview(`cp/groupSettings/groupDetail.html?groupId=${group.id}&currentUserId=${currentUserId}&statusValue=${statusValue}&pageId=Group_Detail&hideNavigationBar=1&hideNotch=1`);
}


function handleGroupOwnerFirstPlace(data, currentUserid) {
  let posIndex;
  let element = data.filter((item, index)=> {
    if(item.userId === currentUserid) {
      posIndex = index;
      return item;
    }
  })
  if(posIndex) {
    data.splice(posIndex, 1);
    data.unshift(element[0]);
  }
  return data;
}