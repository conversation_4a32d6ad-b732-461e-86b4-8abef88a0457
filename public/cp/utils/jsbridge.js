
"use strict";
// 中间这一段都是jsbridge
const jsbridge_isLoaded = true;
const jsbridge_isDev = window.location.origin.includes('10.') || window.location.origin.includes('192.');
const jsbridge_isStaging = window.location.origin.includes('staging2');
const jsbridge_isLocal = window.location.origin.includes('file');
const jsbridge_baseImgUrl = '../image/profile_avator.png';
if (jsbridge_isDev) {
    var src =
        "https://auto.tancdn.com/v1/raw/d39d1ccb-9e30-4bef-ab0e-7e35058716c40300.js";
    document.write("<scr" + 'ipt src="' + src + '"></scr' + "ipt>");
    document.write("<scr" + "ipt>eruda.init();</scr" + "ipt>");
}
const ua = typeof navigator !== 'undefined' ? navigator.userAgent.toLowerCase() : '';
const isiOS = ~ua.indexOf('iphone') || ua === 'tantan-ios';
const isAndroid = ~ua.indexOf('android');

const android_method_checker = function (method) {
    return (
        isAndroid &&
        typeof window.tantan !== "undefined" &&
        typeof window.tantan[method] === "function"
    );
}

const androidMethodAccessible = function (methodName) {
    return window && window.tantan && typeof window.tantan.canIUse === 'function' && window.tantan.canIUse(methodName);
};

const ios_method_checker = function (method) {
    return isiOS
        && window.webkit
        && window.webkit.messageHandlers
        && window.webkit.messageHandlers[method]
        && typeof window.webkit.messageHandlers[method].postMessage === 'function'
}

const handleAndroidParamArr = function (array) {
    array = array || []
    //  const handleAndroidParamArr = (array = [] ) => array.map((item) => {
    return array.map(function (item) {
        const resultMap = {
            type: 'String',
            value: item,
        };
        if (typeof item === 'number') {
            resultMap.type = 'Integer'
            if (/^\d+\.\d+$/.test(String(item))) {
                resultMap.type = 'Double'
            }
        }
        if (typeof item === 'boolean') {
            resultMap.type = 'Boolean';
        }
        if (Object.prototype.toString.call(item) === '[object Array]') {
            resultMap.isArray = true;
        }
        return resultMap;
    })
}

const genUuid = function () {
    const d = +new Date();
    const range = Math.random().toFixed(3) * 1000;
    return `${d}_${range}`;
}

const openWebview = function (urlOrSchema, title) {
    title = title || '';
    if (!urlOrSchema) {
        throwErr('param url is REQUIRED!')
    }
    const fnName = 'openWebview';
    if (androidMethodAccessible(fnName)) {
        return window.tantan.dispatch(fnName, JSON.stringify(handleAndroidParamArr([urlOrSchema, title])));
    }
    if (ios_method_checker(fnName) && ios_method_checker('jumpWebview')) { // 新老版本都存在openwebview的情况，故增加用是否存在jumpWebview来区分是否为新版,若后续需要更精细化的区分，则调用获取系统信息来判断版本号
        const paramBody = /http/g.test(urlOrSchema) ? { url: urlOrSchema, title } : { schema: urlOrSchema, title };
        return new Promise(function (resolve, reject) {
            const uid = genUuid();
            window[`${fnName}Success${uid}`] = function (res) {
                resolve(JSON.parse(res))
            };
            window[`${fnName}Fail${uid}`] = function (err) { reject(err) }
            window.webkit.messageHandlers[fnName].postMessage(paramBody)
        })
    }
}

const openOfflineWebview = function (url) {
    const fnName = 'openOfflineWebview';
    jsbridge_isDev ? openWebview(`${location.origin}/${url}`) :
    getNetworkEnv((env) => {
        let baseUrl = (env === 'online') ? 'https://m.tantanapp.com/commerce' : 'http://m.staging2.p1staff.com/commerce/';
        triggerAction({
            actionType: fnName,
            restParams: {
                url: `${baseUrl}${url}`
            }
        })
    })
}

const hideNavigation = function (params) {
    const fnName = 'hideNavigation';
    const res = triggerAction({
        actionType: fnName,
        restParams: {
            sucessHandler: params.sucessHandler,
            errorHandler: params.errorHandler
        }
    })
}

const openGroupMemberProfile = function (params) {
    const fnName = 'openGroupMemberProfile';
    const res = triggerAction({
        actionType: fnName,
        restParams: {
            userId: params.userId,
            anonymity: params.anonymity
        }
    })
}

const oldIOS_method_checker = function (method) {
    return isiOS && typeof window[method] === "function";
}


function trackNew(trackData) {
    const fnName = 'trackNew'
    if (isAndroid) {
        // const { type, pageId, eid = '', extras } = trackData
        const type = trackData.type;
        const pageId = trackData.pageId;
        const extras = trackData.extras;
        const eid = trackData.eid || '';
        let kvs = []
        if (extras) {
            for (let key in extras) {
                kvs = kvs.concat(String(key), String(extras[key]))
            }
        }
        if (androidMethodAccessible(fnName)) {
            const paramsArr = handleAndroidParamArr([type, eid, pageId, kvs])
            return window.tantan.dispatch(fnName, JSON.stringify(paramsArr))
        } else if (android_method_checker(fnName)) {
            return window.tantan.trackNew(type, eid, pageId, kvs)
        }
    } else if (ios_method_checker('trackNew')) {
        return window.webkit.messageHandlers.trackNew.postMessage(trackData)
    } else {
        console.info("无法进行新打点", 0.5)
    }
}

// PD打点
function trackPD(pageId) {
    if (isiOS || isAndroid) return;
    trackNew({ pageId: pageId, type: 'PD' });
}

const triggerAction = function (params) {
    const actionType = params.actionType;
    const restParams = params.restParams || {};
    if (!actionType) {
        return throwErr('param actionType is REQUIRED');
    }
    const fnName = 'triggerAction';
    const uid = genUuid();
    const handleRestParams = function (params) {
        const resParams = {};
        Object.keys(params).forEach(function (key) {
            if (typeof params[key] === 'function') {
                window[`${actionType}_${key}_${uid}`] = params[key];
                return resParams[key] = `${actionType}_${key}_${uid}`;
            }
            resParams[key] = params[key]
        });
        return resParams;
    };
    if (actionType === 'track' && restParams.params && Object.prototype.toString.call(restParams.params) === '[object Object]') {
        let kvs = [];
        for (let key in restParams.params) {
            kvs = kvs.concat(String(key), String(restParams.params[key]));
        }
        restParams.params = kvs;
    }
    const filterParams = handleRestParams(restParams);

    if (android_method_checker('triggerAction')) {
        // todo track的特殊逻辑是否要下放至业务项目
        // const restParamsArr = Object.keys(filterParams).map((x) => filterParams[x]);
        const restParamsArr = Object.keys(filterParams).map(function (x) {
            return filterParams[x];
        });
        const paramArr = JSON.stringify(handleAndroidParamArr(restParamsArr));
        return window.tantan.triggerAction(actionType, paramArr)
    }
    if (ios_method_checker(fnName)) {
        if (actionType === 'subscribeCampaign') {
            window.flagUid = uid;
        }
        let obj = { actionType: actionType };
        for (const item of Object.keys(filterParams)) {
            obj[item] = filterParams[item]
        }
        return window.webkit.messageHandlers[fnName].postMessage(obj);
    }

    const migrateMethods = [
        'refreshLiveAuth', 'jumpRecharge', 'refreshGuildAuth', 'jumpToCover', 'onTeenModeEnable',
        'verifyTeenModePassword', 'dialogJumpRecharge', 'closeLiveCampaignDialog', 'refreshUserCounters',
        'jumpToLiveAnchor', 'jumpToProfile', 'jumpToRoom', 'onRedPacketOpen', 'subscribeCampaign', 'showRechargeDialog',
        'liveGiftDialogController', 'campaignController', 'track', 'jumpToSeeOrBuySee', 'unRegisterBarRight', 'registerBarRight', 'follow', 'liveNewUserRedPacketController',
        'imagePicker', 'jumpToTopicAggregationAct', 'jumpToTopicVoteAggregationAct', 'jumpToTopic', 'jumpToProfileAct',
    ];
    if (~migrateMethods.indexOf(actionType) && (android_method_checker(actionType) || ios_method_checker(actionType) || oldIOS_method_checker(actionType))) {
        console.log('method triggerAction not exist, enter oldBridgeHandler')
        return oldBridgeHandler(actionType, restParams)
    }
    // IOS中track方法名为trackEvent
    if (actionType === 'track' && (ios_method_checker('trackEvent') || oldIOS_method_checker('trackEvent'))) {
        return oldBridgeHandler(actionType, restParams)
    }
    // console.error(`no method matched for ${fnName}`);
};

const getUserInfo = function () {
    const fnName = 'getUserInfo';
    if (androidMethodAccessible(fnName)) {
        const res = JSON.parse(window.tantan.dispatch(fnName, JSON.stringify([])));
        if (typeof res === 'object') {
            return Object.keys(res).length === 0 ? Promise.reject('unknown user') : Promise.resolve(res)
        }
        return console.error('internal error')
    }
    if (ios_method_checker(fnName)) {
        const uid = genUuid();
        return new Promise(function (resolve, reject) {
            window[`${fnName}Success${uid}`] = function (res) {
                resolve(JSON.parse(res));
            };
            window[`${fnName}Fail${uid}`] = function () {
                reject('unknown user');
            };
            window.webkit.messageHandlers.getUserInfo.postMessage({
                success: `${fnName}Success${uid}`,
                fail: `${fnName}Fail${uid}`,
            });
        })
    }
}


function throwErr(errorMsg) {
    throw errorMsg;
}


function checkIsFn(fnArray) {
    if (!Array.isArray(fnArray)) {
        throwErr('param must be array')
    }
    // return fnArray.every(fn => fn && typeof fn === 'function');
    return fnArray.every(function (fn) {
        if (fn) {
            return typeof fn === 'function'
        }
    });
}

const setNavigationTitle = function (params) {
    // const { title = '', handler = Function.prototype } = params
    let title = params.title || '';
    let handler = params.handler || Function.prototype;
    if (!checkIsFn([handler])) {
        return throwErr('handler must be function')
    }
    if (!title) {
        return throwErr('param title is REQUIRED')
    }
    const fnName = 'setNavigationTitle'
    const uid = genUuid()
    window[`handler${uid}`] = handler
    if (androidMethodAccessible(fnName)) {
        return new Promise(function (resolve, reject) {
            window[`success${uid}`] = function () {
                resolve()
            }
            window[`error${uid}`] = function () {
                reject()
            }
            const paramArr = JSON.stringify(handleAndroidParamArr([title, `handler${uid}`, `success${uid}`, `error${uid}`]))
            window.tantan.dispatch(fnName, paramArr)
        })
    }
    if (ios_method_checker(fnName)) {
        return new Promise(function (resolve, reject) {
            window[`success${uid}`] = function () {
                resolve()
            }
            window[`error${uid}`] = function () {
                reject()
            }
            window.webkit.messageHandlers[fnName].postMessage({
                title,
                handler: `handler${uid}`,
                success: `success${uid}`,
                fail: `error${uid}`
            })
        })

    }
    if (android_method_checker('setTitle') || oldIOS_method_checker('setTitle')) {
        oldBridgeHandler('setTitle', title)
    }
    return Promise.reject(`no method matched for ${fnName}`)
}


/** AB分组的header头 */
const getAbHeader = () => {
    const fnName = 'getAbHeader';
    if (isAndroid) {
        if (androidMethodAccessible(fnName)) {
            const res = window.tantan.dispatch(fnName, JSON.stringify([]));
            return Promise.resolve(res);
        } else if (android_method_checker(fnName)) {
            return Promise.resolve(window.tantan[fnName]());
        }
    }
    if (oldIOS_method_checker(fnName)) {
        return window[fnName]();
    }
    if (ios_method_checker(fnName)) {
        console.log('enter into method getAbHeader')
        return new Promise((resolve, reject) => {
            const uid = genUuid();
            window[`${fnName}Success${uid}`] = (res) => {
                resolve(res);
            };
            window[`${fnName}Fail${uid}`] = (err) => {
                reject(err);
            };
            window.webkit.messageHandlers[fnName].postMessage({
                success: `${fnName}Success${uid}`,
                fail: `${fnName}Fail${uid}`,
            });
        })
    }
    console.error('no method matched for getAbHeader')
}

/**
* 设置导航栏4
*/

const setNavigation = function (params) {
    const {
        title = '',
        handler,
        leftImgUrl = '',
        leftText = '',
        skipBack = false,
        leftHandler,
        rightImgUrl = '',
        rightText = '',
        rightHandler
    } = params
    if (!checkIsFn([handler || Function.prototype, leftHandler || Function.prototype, rightHandler || Function.prototype])) {
        return throwErr('handler must be function')
    }
    if (!title) {
        return throwErr('param title is REQUIRED')
    }
    const fnName = 'setNavigation'
    const uid = genUuid()
    window[`leftHandler${uid}`] = leftHandler
    window[`rightHandler${uid}`] = rightHandler
    window[`handler${uid}`] = handler
    if (androidMethodAccessible(fnName)) {
        return new Promise((resolve, reject) => {
            window[`success${uid}`] = () => {
                resolve()
            }
            window[`error${uid}`] = () => {
                reject()
            }
            const paramArr = JSON.stringify(handleAndroidParamArr([title, `handler${uid}`, leftImgUrl, leftText && leftImgUrl ? '' : leftText, `leftHandler${uid}`, rightImgUrl, rightText && rightImgUrl ? '' : rightText, `rightHandler${uid}`, `success${uid}`, `error${uid}`, skipBack]))
            window.tantan.dispatch(fnName, paramArr)
        })
    }
    if (ios_method_checker(fnName)) {
        return new Promise((resolve, reject) => {
            window[`success${uid}`] = () => {
                resolve()
            }
            window[`error${uid}`] = () => {
                reject()
            }
            window.webkit.messageHandlers[fnName].postMessage({
                title,
                handler: `handler${uid}`,
                leftImgUrl,
                leftText: leftText && leftImgUrl ? '' : leftText,
                leftHandler: `leftHandler${uid}`,
                rightImgUrl,
                rightText: rightText && rightImgUrl ? '' : rightText,
                rightHandler: `rightHandler${uid}`,
                skipBack,
                success: `success${uid}`,
                fail: `error${uid}`
            })
        })

    }
    return Promise.reject(`no method matched for ${fnName}`)
}

const getAuthorizationHeader = function (url, body) {
    const fnName = 'getAuthorizationHeader';
    if (isAndroid) {
        if (androidMethodAccessible(fnName)) {
            const paramArr = JSON.stringify(handleAndroidParamArr([url, body || '']))
            const res = window.tantan.dispatch(fnName, paramArr)
            return Promise.resolve(res)
        } else if (android_method_checker(fnName)) {
            if (body) {
                return Promise.resolve(window.tantan[fnName](url, JSON.stringify(body)))
            } else {
                return Promise.resolve(window.tantan[fnName](url))
            }
        }
    } else if (oldIOS_method_checker(fnName)) {
        if (body) {
            return window[fnName](url, JSON.stringify(body))
        } else {
            return window[fnName](url)
        }
    } else if (ios_method_checker(fnName)) {
        return new Promise(function (resolve, reject) {
            const uid = genUuid()
            window[`${fnName}Success${uid}`] = function (res) { resolve(res) }
            // window[`${fnName}Success${uid}`] = (res) => { resolve(res) }
            window[`${fnName}Fail${uid}`] = function (err) { reject(err) }
            window.webkit.messageHandlers.getAuthorizationHeader.postMessage({
                url,
                body: body ? JSON.stringify(body) : undefined,
                success: `${fnName}Success${uid}`,
                fail: `${fnName}Fail${uid}`,
            })
        })
    }
    return Promise.reject(`no method matched for ${fnName}`)
}

const getSystemInfo = function () {
    const fnName = 'getSystemInfo'
    if (androidMethodAccessible(fnName)) {
        const res = JSON.parse(window.tantan.dispatch(fnName, JSON.stringify([])))
        if (res.appVersion) {
            res.versionCode = res.appVersion
        }
        if (res.sdkVersion) {
            res.schemeVersion = res.sdkVersion
        }
        if (res.os) {
            res.platform = res.os
        }
        return Promise.resolve(typeof res === 'object' ? res : {})
    }
    if (ios_method_checker(fnName)) {
        return new Promise(function (resolve, reject) {
            const uid = genUuid()
            window[`${fnName}Success${uid}`] = function (res) {
                const result = JSON.parse(res)
                if (result.appVersion) {
                    result.versionCode = result.appVersion
                }
                if (result.sdkVersion) {
                    result.schemeVersion = result.sdkVersion
                }
                if (result.os) {
                    result.platform = result.os
                }
                resolve(result)
            }
            window[`${fnName}Fail${uid}`] = function (err) { reject(err) }
            window.webkit.messageHandlers[fnName].postMessage({
                success: `${fnName}Success${uid}`,
                fail: `${fnName}Fail${uid}`,
            })
        })
    }
    // 若为旧版sdk，则调用saveBean方法
    if (android_method_checker('saveBean') || ios_method_checker('saveBean') || oldIOS_method_checker('saveBean')) {
        return oldBridgeHandler('saveBean')
    }
    return Promise.reject(`no method matched for ${fnName}`)
}

const share = function (params) {
    // const { url, title = '', description = '', imgUrl, channel, successHandler = Function.prototype, errorHandler = Function.prototype } = params;
    let url = params.url;
    let title = params.title || '';
    let description = params.description || '';
    let imgUrl = params.imgUrl;
    let channel = params.channel;
    let successHandler = params.successHandler || Function.prototype;
    let errorHandler = params.errorHandler || Function.prototype;
    if (successHandler && typeof successHandler !== "function" || errorHandler && typeof errorHandler !== "function") {
        throw 'handler must be a function!'
    }
    if (!url || !channel) {
        throw 'param is invalid'
    }
    const fnName = 'share';
    if (isAndroid) {
        const uid = genUuid();
        window[`success${uid}`] = successHandler;
        window[`error${uid}`] = errorHandler;
        if (androidMethodAccessible(fnName)) {
            const paramArr = JSON.stringify(handleAndroidParamArr([url, title, description, imgUrl, channel, `success${uid}`, `error${uid}`]))
            window.tantan.dispatch(fnName, paramArr)
        } else if (android_method_checker(fnName)) {
            return window.tantan.share(url, title, description, imgUrl, channel, `success${uid}`)
        }
    }
    if (ios_method_checker(fnName)) {
        const uid = genUuid();
        window[`success${uid}`] = successHandler;
        window[`error${uid}`] = errorHandler;
        window.webkit.messageHandlers[fnName].postMessage({
            url,
            title,
            description,
            imgUrl,
            channel,
            success: `success${uid}`,
            fail: `error${uid}`
        });
    }
    return console.error('method share not exist')
};

const showToast = function (params) {
    // const { context, duration = 3000 } = params
    let context = params.context;
    let duration = params.duration || 2000;
    if (!context) {
        return throwErr('param context is REQUIRED')
    }
    const fnName = 'showToast'
    if (androidMethodAccessible(fnName)) {
        const paramArr = JSON.stringify(handleAndroidParamArr([context, 0]))
        window.tantan.dispatch('showToast', paramArr)
        return
    }
    if (ios_method_checker(fnName)) {
        window.webkit.messageHandlers[fnName].postMessage({
            context,
            duration
        })
        return
    }
}
const closeWebview = function () {
    const fnName = 'closeWebview'
    if (androidMethodAccessible(fnName)) {
        return window.tantan.dispatch(fnName, JSON.stringify([]))
    }
    if (ios_method_checker(fnName)) {
        window.webkit.messageHandlers[fnName].postMessage({})
        return
    }
    if (android_method_checker(fnName) || oldIOS_method_checker(fnName)) {
        return oldBridgeHandler(fnName)
    }
    customConsole(`no method matched for ${fnName}`)
}

// 添加新的bridge监听关闭webview事件，当前webview关闭之后的回调
function setWebviewPageID({ pageID, extras = {} }) {
    const fnName = 'setWebviewPageID'
    let kvs = []
    if (extras) {
        for (let key in extras) {
            kvs = kvs.concat(String(key), String(extras[key]))
        }
    }
    if (androidMethodAccessible(fnName)) {
        const paramsArr = handleAndroidParamArr([pageID, kvs])
        return window.tantan.dispatch(fnName, JSON.stringify(paramsArr))
    }
    if (ios_method_checker(fnName)) {
        window.webkit.messageHandlers[fnName].postMessage({ pageID, extra: extras })
        return
    }
}


/*************************************************************************************/
/**
 * 根据userID获取匿名头像
 * @param userInfo String
 * @param setAnonymityAvatarById Function
 */
const getAnonymityAvatarById = function (userInfo, setAnonymityAvatarById) {
    const fnName = 'getAnonymityAvatarById';
    const res = triggerAction({
        actionType: fnName,
        restParams: {
            userid: userInfo.userId,
            isfemale: userInfo.gender === 'female',
            setAnonymityAvatarById: setAnonymityAvatarById
        }
    })
}

/**
 * 获取电量状态栏的高度
 * @param params: {
 *   setStatusBar: Function
 * }
 */
const getStatusBar = function (params) {
    if (isAndroid) return;
    const fnName = 'getStatusBar';
    const res = triggerAction({
        actionType: fnName,
        restParams: {
            setStatusBar: params.setStatusBar
        }
    })
}

/**
 * 在申请加群之前判断用户被ban
 * @param setUserIsBanedToAddGroupFn Function
 */
const getUserIsBanedToAddGroup = function (setUserIsBanedToAddGroupFn) {
    triggerAction({
        actionType: 'getUserIsBanedToAddGroup',
        restParams: {
            /**
             * @param res
             *  1 -> baned：被ban之后不作处理，客户端处理
             *  0 => no baned：没被ban，请求加群接口
             */
            setUserIsBanedToAddGroup: setUserIsBanedToAddGroupFn
        }
    })
}

/**
 * 获取网络状态
 * @param callback: Function 有网络成功回调
 * @param fallback: Function 无网络失败回调
 */
const getInternetStatus = function (callback, fallback) {
    const fnName = 'getInternetStatus';
    const res = triggerAction({
        actionType: fnName,
        restParams: {
            setInternetStatus: function (res) {
                console.log(fnName, '获取网络状态', res);
                if (res === '1') {
                    showToast({
                        context: '没有网络信号，请再试一次',
                        duration: 2000
                    });
                    fallback && fallback();
                } else {
                    callback && callback()
                }
            }
        }
    })
}

const getNetworkEnv = function (callback) {
    const fnName = 'getNetworkEnv';
    const res = triggerAction({
        actionType: fnName,
        restParams: {
            setNetworkEnv: function (res) {
                console.log(fnName, '获取当前环境', res);
                if (res === '0') {
                    // 当前是测试环境
                    callback && callback('staging2')
                } else {
                    // 当前是线上环境
                    callback && callback('online')
                }
            }
        }
    })
}


/**
 * 获取Tab栏的高度
 * @param setTabBar Function<numberString>
 */
const getTabBarHeight = function (setTabBar) {
    const fnName = 'getTabBarHeight';
    const res = triggerAction({
        actionType: fnName,
        restParams: {
            setTabBar: setTabBar
        }
    })
}

