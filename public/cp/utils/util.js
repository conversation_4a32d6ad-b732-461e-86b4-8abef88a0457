"use strict";

const envUrl = (
  window.location.origin.includes('tantanapp.com')
    ? 'https://m.tantanapp.com/'
    : window.location.origin.includes('staging2.p1staff.com')
    ? 'http://m.staging2.p1staff.com/commerce/'
    : `${location.origin}/`
);
//跳转到已存在的h5
const envUrlh5 = (
  window.location.origin.includes('tantanapp.com')
    ? 'https://m.tantanapp.com/'
    : 'http://m.staging2.p1staff.com/'
)

function getShareUrl(url) {
  return window.location.origin.includes('10') ? `${envUrl}${url.split('commerce')[1].slice(1)}` : url;
}

window.doWhenGetToken = new Function();

// function addScript(url) {
//   document.write("<script language=javascript src=" + url + "></script>");
// }

// addScript(`../utils/jsbridge.js?path=cp/utils/jsbridge.js`);


function getUrlParams(url) {
  url = isAndroid ? url : decodeURIComponent(url);
  let objParams = {};
  let queryParams = url.split('&');
  queryParams.forEach(function (item) {
    let arr = item.split('=');
    objParams[arr[0]] = decodeURIComponent(arr[1]);
  })
  return objParams;
}


function debounce(that, fn, time, arg) {
  let timer;
  return function () {
    !!timer && clearTimeout(timer);
    timer = setTimeout(function () {
      let args = [].slice.call(arguments, 0).concat(arg);
      fn.apply(that, args);
      clearTimeout(timer);
    }, time)
  }
}

function throttle(that, fn, time, arg) {
  let timer;
  return function () {
    let args = [].slice.call(arguments, 0).concat(arg);
    let last = timer;
    let now = Date.now();
    if (!last) {
      timer = now;
      fn.apply(that, args)
      return;
    }
    if (last + time > now) return;
    timer = now;
    fn.apply(that, args)
  }
}

function getTime(time, separator) {
  if (!time) {
    console.error('未传必填参数')
    return;
  }
  //ios 处理带‘T’格式的日期有bug,
  let date = isAndroid ? new Date(time) : new Date(time.split('T')[0]);
  // let date = isAndroid ? new Date(time) : new Date(new Date().getTime(time));
  let myseparator = separator || '/'
  return [date.getFullYear(), +date.getMonth() + 1, date.getDate()].join(myseparator);
}

function getMathRandom(N) {
  if (!N) {
    console.error('未传必填参数')
    return;
  }

  let t = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz123456789';
  let str = '';
  while (N) {
    str += t[Math.floor(Math.random() * t.length)];
    N--;
  }
  return str;
}

function setCommonTitleStyle(closeArrow, titleValue) {
  if (isAndroid) {
    closeArrow.src = '../image/backarrow_android.png';
    closeArrow.style.width = '24px';
    titleValue.style.marginLeft = '22px';
    titleValue.style.flex = '1';
  }
}

function stopPropagation(event) {
  if (event.stopPropagation) {
    return event.stopPropagation()
  }
  return event.cancelBubble = true;
}

function preventDefault(event) {
  if (event.preventDefault) {
    return evnet.preventDefault();
  }
  return event.returnValue = false;

}

function hideNavigationBar() {
  hideNavigation({
    sucessHandler: function () {
      // console.log('success');
    },
    errorHandler: function () {
      // console.log('erroer');
    }
  });
}

// 判断是否输入了emoji
function isEmojiCharacter(substring) {
  if (!substring) return;
  for (var i = 0; i < substring.length; i++) {
    var hs = substring.charCodeAt(i);
    if (0xd800 <= hs && hs <= 0xdbff) {
      if (substring.length > 1) {
        var ls = substring.charCodeAt(i + 1);
        var uc = ((hs - 0xd800) * 0x400) + (ls - 0xdc00) + 0x10000;
        if (0x1d000 <= uc && uc <= 0x1f77f) {
          return true;
        }
      }
    } else if (substring.length > 1) {
      var ls = substring.charCodeAt(i + 1);
      if (ls == 0x20e3) {
        return true;
      }
    } else {
      if (0x2100 <= hs && hs <= 0x27ff) {
        return true;
      } else if (0x2B05 <= hs && hs <= 0x2b07) {
        return true;
      } else if (0x2934 <= hs && hs <= 0x2935) {
        return true;
      } else if (0x3297 <= hs && hs <= 0x3299) {
        return true;
      } else if (hs == 0xa9 || hs == 0xae || hs == 0x303d || hs == 0x3030
        || hs == 0x2b55 || hs == 0x2b1c || hs == 0x2b1b
        || hs == 0x2b50) {
        return true;
      }
    }
  }
}

let rsAstralRange = '\\ud800-\\udfff',
  rsZWJ = '\\u200d',
  rsVarRange = '\\ufe0e\\ufe0f',
  rsComboMarksRange = '\\u0300-\\u036f',
  reComboHalfMarksRange = '\\ufe20-\\ufe2f',
  rsComboSymbolsRange = '\\u20d0-\\u20ff',
  rsComboRange = rsComboMarksRange + reComboHalfMarksRange + rsComboSymbolsRange;
let reHasUnicode = RegExp('[' + rsZWJ + rsAstralRange + rsComboRange + rsVarRange + ']');

let rsFitz = '\\ud83c[\\udffb-\\udfff]',
  rsOptVar = '[' + rsVarRange + ']?',
  rsCombo = '[' + rsComboRange + ']',
  rsModifier = '(?:' + rsCombo + '|' + rsFitz + ')',
  reOptMod = rsModifier + '?',
  rsAstral = '[' + rsAstralRange + ']',
  rsNonAstral = '[^' + rsAstralRange + ']',
  rsRegional = '(?:\\ud83c[\\udde6-\\uddff]){2}',
  rsSurrPair = '[\\ud800-\\udbff][\\udc00-\\udfff]',
  rsOptJoin = '(?:' + rsZWJ + '(?:' + [rsNonAstral, rsRegional, rsSurrPair].join('|') + ')' + rsOptVar + reOptMod + ')*',
  rsSeq = rsOptVar + reOptMod + rsOptJoin,
  rsSymbol = '(?:' + [rsNonAstral + rsCombo + '?', rsCombo, rsRegional, rsSurrPair, rsAstral].join('|') + ')';
let reUnicode = RegExp(rsFitz + '(?=' + rsFitz + ')|' + rsSymbol + rsSeq, 'g');

// 把inputValue转为数组（区分emoji表情）
function inputValueToArray(val) { // 字符串转成数组
  return hasUnicode(val)
    ? unicodeToArray(val)
    : asciiToArray(val);
}

function hasUnicode(val) {
  return reHasUnicode.test(val);
}

function unicodeToArray(val) {
  return val.match(reUnicode) || [];
}

function asciiToArray(val) {
  return val.split('');
}


// 判断是否是汉字
function isHanzi(str) {
  const regx = /.*[\u4e00-\u9fa5]+.*$/;
  return regx.test(str);
}

//ios maxlength失效，js手动控制最大长度
function handleIosTextLength(e, previousValue, maxlength) {
  const dataContent = e.data || e.pasteData || '';
  const inputEle = e.target;
  const selectionStart = inputEle.selectionStart;
  let value = inputEle.value;
  let otherVal = '';
  let otherAdd = 0;
  console.log(value, e.data, value.length, maxlength);
  if (value.length > maxlength) {
    e.preventDefault();
    console.log(previousValue.length);
    if (previousValue.length < maxlength && !isEmojiCharacter(dataContent)) {
      otherAdd = maxlength - previousValue.length;
      otherVal = dataContent.slice(0, otherAdd);
      console.log('otherVal', otherVal);
    }
    const dataLen = dataContent && dataContent.length;
    const androidLen = (isHanzi(dataContent) || isEmojiCharacter(dataContent)) ? dataLen : 1;
    const start = selectionStart - (isiOS ? dataLen : androidLen) + otherAdd;
    const arr = previousValue.split('');
    arr.splice(start - otherAdd, 0, otherVal);
    const resValue = arr.join('');
    inputEle.value = resValue;
    setTimeout(() => {
      inputEle.setSelectionRange(start, start);
    }, 0)
    return resValue;
  }
  return false;
}
