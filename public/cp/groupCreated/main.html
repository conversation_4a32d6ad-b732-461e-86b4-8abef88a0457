<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width,minimum-scale=1.0,maximum-scale=1.0,user-scalable=no">

    <title></title>
    <link href="../utils/common.css?path=cp/utils/common.css" rel="stylesheet" type="text/css" />
    <style>
        body {
            margin: 0;
            padding: 0;
        }

        img {
            object-fit: cover;
        }

        .container_wrap {
            padding: 0 20px;
            -webkit-user-select: none;
            -moz-user-select: none;
            -ms-user-select: none;
            user-select: none;
        }

        .header {
            display: flex;
            flex-direction: column;
            align-items: center;
        }

        .header_title {
            font-size: 22px;
            color: #222222;
            line-height: 33px;
            margin: 0;
            padding: 0;
        }

        .header_tips {
            font-size: 13px;
            color: #a8a8a8;
            margin: 6px 0 40px;
        }

        ul,
        p {
            margin: 0;
            padding: 0;
        }

        .list_li {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 13px 13px 14px 16px;
            background-color: #f7f7f7;
            margin-bottom: 16px;
            border-radius: 10px;
        }

        .content {
            display: flex;
            flex-direction: column;
        }

        .icon {
            width: 6px;
            height: 10px;
        }

        .content_title {
            font-size: 16px;
            color: #222222;
            font-weight: bold;
        }

        .content_desc {
            font-size: 13px;
            color: #757575;
            margin-top: 5px;
        }
    </style>
</head>

<body>
    <div class="container_wrap">
        <div class="header">
            <p class="header_title">创建实名群还是匿名群？</p>
            <p class="header_tips">群创建完成后不支持修改</p>
        </div>
        <ul class="list">
            <li class="list_li" onclick="jumpGroupCategory('realname')">
                <div class="content">
                    <p class="content_title">实名群</p>
                    <p class="content_desc">加群及聊天时，用户展示真实头像</p>
                </div>
                <img class="icon"
                    src="../image/arrow.png"></img>
            </li>
            <li class="list_li" onclick="jumpGroupCategory('anonymous')">
                <div class="content">
                    <p class="content_title">匿名群</p>
                    <p class="content_desc">加群及聊天时，用户展示匿名头像</p>
                </div>
                <img class="icon"
                    src="../image/arrow.png"></img>
            </li>
        </ul>
    </div>
    <script src="../utils/jsbridge.js?path=cp/utils/jsbridge.js"></script>
    <script src="../utils/util.js?path=cp/utils/util.js"></script>
    <script type="text/javascript">
        "use strict";
        const pageID = 'p_select_group_type_step_one';

        function doWhenGetToken() { }
        // 选择创建群类型【无网络判断】
        function jumpGroupCategory(type) {
            getInternetStatus(function () {
                trackNew({
                    pageId: pageID,
                    eid: 'e_select_group_type_step_one',
                    type: 'MC',
                    extras: {
                        is_anonymou_group: type === 'anonymous' ? '1' : '0'
                    }
                })
                const groupType = type === 'anonymous' ? '' : type;
                trackPD(pageID);
                openOfflineWebview(`cp/groupCreated/groupcategory.html?type=${groupType}&pageId=Group_groupcategory`)
            })
        }

        window.onload = function () {
            // pv埋点
            setWebviewPageID({pageID: pageID})
            //标题设置为空
            document.title='\u200E';
        }
        document.addEventListener("visibilitychange", function () {
            if (document.visibilityState === 'visible') {
                isAndroid && setWebviewPageID({pageID: pageID})
            }
        });

    </script>
</body>

</html>
