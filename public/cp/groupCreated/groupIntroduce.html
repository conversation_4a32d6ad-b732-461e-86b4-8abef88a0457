<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width,minimum-scale=1.0,maximum-scale=1.0,user-scalable=no">

    <title></title>
    <link href="../utils/common.css?path=cp/utils/common.css" rel="stylesheet" type="text/css" />
    <style>
        body {
            margin: 0;
            padding: 0;
        }

        ul,
        p {
            margin: 0;
            padding: 0;
        }

        img {
            object-fit: cover;
        }

        .container_wrap {
            padding: 0 16px;
            display: flex;
            flex-direction: column;
            align-items: center;
            padding-bottom: 54px;
            -webkit-user-select: none;
            -moz-user-select: none;
            -ms-user-select: none;
            user-select: none;
        }

        .header {
            display: flex;
            flex-direction: column;
            align-items: center;
            margin-top: 11px;
        }

        .header_title {
            font-size: 22px;
            color: rgb(34, 34, 34);
        }

        .header_tips {
            font-size: 13px;
            color: rgb(168, 168, 168);
            margin: 7px 0 30px;
        }

        .textarea {
            width: calc(100% - 32px);
            height: 210px;
            border: none;
            outline: none;
            font-size: 16px;
            background-color: rgb(251, 251, 251);
            padding: 12px;
            resize: none;
            user-select: text;
            -webkit-user-select: text;
        }

        .textarea::placeholder {
            color: rgb(208, 208, 208);
        }

        .count_limit {
            width: 100%;
            margin: 6px 0 10px;
            font-size: 13px;
            color: rgb(168, 168, 168);
            display: flex;
            justify-content: flex-end;
        }

        .button {
            width: 100%;
            border-radius: 24px;
            outline: none;
            border: none;
            height: 44px;
            font-size: 14px;
            color: rgb(255, 255, 255);
            background-color: rgb(208, 208, 208);
        }

        .bottom {
            position: fixed;
            bottom: 0;
            width: 100%;
            width: 100%;
            box-sizing: border-box;
            padding: 8px 30px 20px;
            background-color: #ffffff;
        }
    </style>
</head>

<body>
    <div class="container_wrap">
        <div class="header">
            <p class="header_title">填写群简介</p>
            <p class="header_tips">描述丰富且精确的群更易通过审核</p>
        </div>
        <textarea class="textarea" name="textarea" placeholder="可描述你的建群目的、希望什么样的朋友加入和群内规则，不超过300字"></textarea>
        <div class="count_limit"><span class="count">0</span>/300</div>
        <div class="bottom">
            <button class="button" data-edit="false" onclick="handleCreateSuccess()">完成</button>
        </div>
    </div>

    <script src="../utils/jsbridge.js?path=cp/utils/jsbridge.js"></script>
    <script src="../utils/util.js?path=cp/utils/util.js"></script>
    <script src="../utils/ajax.js?path=cp/utils/ajax.js"></script>
    <script src="../utils/zepto.js?path=cp/utils/zepto.js"></script>

    <script type="text/javascript">
        "use strict";
        window.doWhenGetToken = new Function();

        const pageID = 'p_add_group_state';
        let count = document.querySelector('.count');
        let button = document.querySelector('.button');
        let textarea = document.querySelector('.textarea');
        let previousValue = '';
        let pasteVal = '';
        let cpLock = true;
        let DEFAULT_TEXTAREA = '可描述你的建群目的、希望什么样的朋友加入和群内规则，不超过300字';

        let queryParams = JSON.parse(window.localStorage.getItem('groupCreatedInfo') || '{}');

        // let queryParams = JSON.parse(getUrlParams(window.location.search.slice(1)).data);
        function handleTextarea(e) {
            let value = e.target.value;
            // 处理删除
            if (e.inputType === 'deleteContentBackward') previousValue = value;
            // 处理粘贴
            if (e.inputType === 'insertFromPaste') e.pasteData = pasteVal;
            if (value.length && (e.data || e.pasteData)) {
                const checkLenRes = handleIosTextLength(e, previousValue, 300);
                //ios maxlength失效，js手动控制最大长度
                if (!checkLenRes) {
                    previousValue = value;
                } else {
                    previousValue = checkLenRes;
                }
            }

            count.innerHTML = e.target.value.trim().length;
            if (value.length) {
                button.style.backgroundColor = 'rgb(255, 92, 49)';
                button.dataset.edit = 'true';
            } else {
                button.style.backgroundColor = 'rgb(208, 208, 208)';
                button.dataset.edit = 'false';
            }
            if (isiOS) {
                textarea.placeholder = DEFAULT_TEXTAREA;
            }

        }

        function handleCreateSuccess() {
            getInternetStatus(function () {
                getUserIsBanedToAddGroup(function (res) {
                    if (res === '1') return;
                    if (!textarea.value.length) {
                        return false;
                    }
                    trackNew({
                        pageId: pageID,
                        eid: 'e_group_complete_button',
                        type: 'MC'
                    })
                    ajax({
                        url: '/v3/chat-groups',
                        method: 'POST',
                        type: 'json',
                        data: {
                            name: queryParams.name,
                            description: textarea.value,
                            type: queryParams.type === 'anonymous' ? 'anonymous' : 'realname',
                            avatars: queryParams.avatars,
                            category: JSON.parse(queryParams.category)
                        }
                    }).then((res) => {
                        //这里post调用成功
                        if (res && res.data && res.data.chatGroups && res.data.chatGroups[0] && res.data.chatGroups[0].status === 'pending') {
                            showToast({
                                context: '已经提交申请，请留意【探探小助手消息】',
                                duration: 2000
                            })
                            window.localStorage.removeItem('groupCreatedInfo');
                            openWebview(`tantanapp://group/square`);
                            //这里要跳转到群广场页面
                        }
                    }).catch(res => {
                        if (res && res.meta) {
                            if (res.meta.code === 40399) {
                                //建群机会已经用完了
                                console.log(res.meta.message);
                                showToast({
                                    context: res.meta.message,
                                    duration: 2000
                                })
                            }
                        }
                    })
                })
            })
        }
        window.onload = function () {
            document.title = '\u200E';
            setWebviewPageID({pageID: pageID})
            setTimeout(function () {
                textarea.addEventListener('compositionstart', function () {
                    cpLock = false;
                });
                textarea.addEventListener('compositionend', function () {
                    cpLock = true;
                });
                textarea.addEventListener('click', function (event) {
                    trackNew({
                        pageId: pageID,
                        eid: 'e_add_group_state',
                        type: 'MC'
                    })
                })
                textarea.addEventListener('input', function (event) {
                    setTimeout(function (){
                        if (isiOS && !cpLock) return;
                        handleTextarea(event)
                    }, 20)
                })
                textarea.addEventListener('paste', function (event) {
                    if (isAndroid && event.clipboardData || event.originalEvent) {
                        let clipboardData = (event.clipboardData || window.clipboardData);
                        pasteVal = clipboardData.getData('text');
                    }
                })
            }, 10)
        }

        document.addEventListener("visibilitychange", function () {
            if (document.visibilityState !== 'visible') {
                window.localStorage.removeItem('groupCreatedInfo');
            }
        });

    </script>
</body>

</html>
