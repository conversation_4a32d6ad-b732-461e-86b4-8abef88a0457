<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width,minimum-scale=1.0,maximum-scale=1.0,user-scalable=no">

    <title></title>
    <link href="../utils/common.css?path=cp/utils/common.css" rel="stylesheet" type="text/css" />
    <style>
        body {
            margin: 0;
            padding: 0;
        }

        img {
            object-fit: cover;
        }

        ul,
        p {
            margin: 0;
            padding: 0;
        }

        .container_wrap {
            padding: 0 20px;
            -webkit-user-select: none;
            -moz-user-select: none;
            -ms-user-select: none;
            user-select: none;
        }

        .li_item {
            display: flex;
            justify-content: space-between;
            margin-bottom: 12px;
            align-items: center;
            padding: 11px 15px 13px 16px;
            border-radius: 10px;
        }

        .header {
            display: flex;
            flex-direction: column;
            align-items: center;
            padding: 0 58px;
        }

        .header_title {
            font-size: 22px;
            color: #222222;
        }

        .header_tips {
            font-size: 13px;
            color: #a8a8a8;
            margin: 6px 0 40px;
            text-align: center;
        }

        .icon {
            width: 6px;
            height: 10px;
        }
    </style>
</head>

<body>
    <div class="container_wrap">
        <div class="header">
            <p class="header_title">选择群分类</p>
            <p class="header_tips"></p>
        </div>
        <ul class="list">

        </ul>
    </div>
    <script src="../utils/jsbridge.js?path=cp/utils/jsbridge.js"></script>
    <script src="../utils/util.js?path=cp/utils/util.js"></script>
    <script src="../utils/ajax.js?path=cp/utils/ajax.js"></script>
    <script src="../utils/zepto.js?path=cp/utils/zepto.js"></script>
    <script type="text/javascript">
        "use strict";
        const pageID = 'p_select_group_type';
        let queryParams = getUrlParams(window.location.search.slice(1));
        const type = queryParams.type || 'anonymous';

        window.onload = function () {
            document.title = '\u200E'
            let ullist = document.querySelector('.list');
            let headerTips= document.querySelector('.header_tips');
            // 获取tag数据【无网络判断】
            getInternetStatus(function () {
                ajax({
                    url: '/v3/users/me/group-attributes',
                    method: 'GET',
                    data: ''
                }).then(function(res) {
                    if(res && res.data && res.data.groupAttributes && res.data.groupAttributes[0]) {
                        // console.log('hhh');
                        let data = res.data.groupAttributes[0];
                        const limit = data.creationLimit && data.creationLimit.value;
                        const categories = data.categories;

                        (function renderList(ul) {
                            let li = '';
                            let data = categories;
                            for (let i = 0; i < data.length; i++) {
                                li += `
                    <li class="li_item" onClick="handleRegisterGroup(event)"
                        data-value=${JSON.stringify(data[i])}
                        style="background-color: ${data[i].backgroundColor};color: ${data[i].textColor}">
                        <p>${data[i].name}</p>
                        <img class="icon" src="../image/arrow.png" />
                    </li>`
                            }
                            ul.innerHTML = li;
                        })(ullist)
                        headerTips.innerHTML = limit;
                        document.title='\u200E';
                        setNavigation({title: ' '})
                        setWebviewPageID({pageID: pageID})
                    }
                })
            })
        }
        document.addEventListener("visibilitychange", function () {
            if (document.visibilityState === 'visible') {
                isAndroid && setWebviewPageID({pageID: pageID})
            }
        });
        function handleRegisterGroup(e) {
            trackNew({
                pageId: pageID,
                eid: 'e_select_group_type',
                type: 'MC',
                extras: {
                  //这里需要传具体的群分类的id
                  group_type_id: JSON.parse(e.target.dataset.value).id
                }
            })
            trackPD(pageID);
            openOfflineWebview(`cp/groupCreated/registergroup.html?type=${type}&category=${encodeURIComponent(e.target.dataset.value)}&pageId=Group_registergroup`);
        }


    </script>
</body>

</html>
