<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width,minimum-scale=1.0,maximum-scale=1.0,user-scalable=no">

    <title></title>
    <link href="../utils/common.css?path=cp/utils/common.css" rel="stylesheet" type="text/css" />
    <style>
        body, html {
            margin: 0;
            padding: 0;
            height: 100%;
        }

        ul,
        p {
            margin: 0;
            padding: 0;
        }

        img {
            object-fit: cover;
        }

        .container_wrap {
            height: 100%;
            display: flex;
            flex-direction: column;
            align-items: center;
            padding-bottom: 64px;
            -webkit-user-select: none;
            -moz-user-select: none;
            -ms-user-select: none;
            user-select: none;
            overflow-y: scroll;
        }


        .header {
            display: flex;
            flex-direction: column;
            align-items: center;
        }

        .header_title {
            font-size: 22px;
            color: rgb(34, 34, 34);
            /* font-weight: bold; */
        }

        .header_tips {
            font-size: 13px;
            color: #a8a8a8;
            margin: 6px 0 30px;
        }

        .circle {
            width: 160px;
            height: 160px;
            /* border: 1px dashed black; */
            background-image: url('data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZlcnNpb249IjEuMSI+CiAgICAgICAgPGcgc3Ryb2tlPSJyZ2IoMjA4LCAyMDgsIDIwOCkiIGZpbGw9InRyYW5zcGFyZW50IiBzdHJva2Utd2lkdGg9IjIiPgogICAgICAgICAgICA8Y2lyY2xlIHI9IjgwIiBjeD0iODAiIGN5PSI4MCIgc3Ryb2tlLWRhc2hhcnJheT0iNywgNyI+PC9jaXJjbGU+CiAgICAgICAgPC9nPgogICAgPC9zdmc+');
            border-radius: 50%;
            position: relative;
        }

        .default_picture {
            position: absolute;
            width: 48px;
            height: 48px;
            left: 50%;
            top: 50%;
            transform: translate(-50%, -50%);
            object-fit: cover;
        }

        .group_picture {
            width: 100%;
            height: 100%;
            display: none;
            border-radius: 50%;
            object-fit: cover;
        }

        .input_container {
            width: calc(100% - 40px);
            display: flex;
            height: 48px;
            margin-top: 40px;
            background-color: rgb(251, 251, 251);
            border-radius: 6px;
            padding: 0 17px 0 12px;
            align-items: center;
            box-sizing: border-box;
        }

        .groupname_input {
            flex: 1;
            font-size: 16px;
            /* font-weight: bold; */
            background-color: rgb(251, 251, 251);
            color: rgb(34, 34, 34);
            border: none;
            outline: none;
            user-select: text;
            -webkit-user-select: text;
        }

        .groupname_input::placeholder {
            font-weight: normal;
            color: #D0D0D0;
        }

        .clear {
            width: 17px;
            height: 17px;
            display: block;
        }

        .next_step {
            width: 100%;
            border-radius: 24px;
            outline: none;
            border: none;
            height: 44px;
            font-size: 14px;
            color: rgb(255, 255, 255);
            background-color: rgb(208, 208, 208);
        }

        .bottom {
            position: fixed;
            bottom: 20px;
            bottom: 0;
            width: 100%;
            width: 100%;
            box-sizing: border-box;
            padding: 0 30px 20px;
            background-color: #ffffff;
            transition: bottom 0.1s ease-in-out;
        }

        .hidden {
            display: none;
        }
    </style>
</head>

<body>
    <div class="container_wrap">
        <div class="header">
            <p class="header_title">设置群名和群头像</p>
            <p class="header_tips">请上传清晰、表意明确的群头像</p>
        </div>
        <div class="circle" onclick="hanldeUploadImage()">
            <img class="default_picture" src="../image/take_pictures.png" />
            <img class="group_picture" onerror="imgErrorHandler()" />
        </div>
        <div class="input_container">
            <input type="text" class="groupname_input" placeholder="群名可包含地点、目的、15字以内..."/>
            <span onclick="hanldeClear()" id="clearBtn">
                <img class="clear hidden"  src="../image/clear.png" alt="">
            </span>
        </div>
        <div class="bottom">
            <button class="next_step">下一步</button>
        </div>
    </div>
    <script src="../utils/jsbridge.js?path=cp/utils/jsbridge.js"></script>
    <script src="../utils/util.js?path=cp/utils/util.js"></script>
    <script type="text/javascript">
        "use strict";
        window.doWhenGetToken = new Function()
        const pageID = 'p_add_group_photo_and_name';
        let image = document.querySelector('.group_picture');
        let defaultImage = document.querySelector('.default_picture');
        let nextButton = document.querySelector('.next_step');
        let input = document.querySelector('.groupname_input');
        let clearIcon = document.querySelector('.clear');
        let bottomBtn = document.querySelector('.bottom');
        let containerWrap = document.querySelector('.container_wrap');
        let clearBtn = document.getElementById('clearBtn');
        let avatars;
        let previousValue = '';
        let pasteVal = '';
        let cpLock = true;
        let originHeight;

        let queryParams = getUrlParams(window.location.search.slice(1));
        function hanldeUploadImage() {
            trackPD(pageID);
            triggerAction({
                actionType: 'imagePickerPhoto',
                restParams: {
                    success: function (localUrl, imgArr, error) {
                        try {
                            defaultImage.style.display = 'none';
                            image.style.display = 'block';
                            if (isAndroid) {
                                avatars = JSON.parse(imgArr);
                                image.src = localUrl ? `${localUrl}?path=${localUrl}` : avatars[0].url;
                            } else if (isiOS) {
                                const args = JSON.parse(localUrl);
                                avatars = [args.pictureInfo];
                                image.src = `data:image/png;base64,${args.base64Image}`;
                            }

                            trackNew({
                                pageId: pageID,
                                eid: 'e_add_group_photo',
                                type: 'MC'
                            })

                            if (input.value.trim()) {
                                nextButton.disabled = false;
                                nextButton.style.backgroundColor = '#FF5C31';
                                return;
                            }
                            nextButton.disabled = true;
                            nextButton.style.backgroundColor = '#D0D0D0';
                        } catch (e) {
                            showToast({
                                context: '图片上传失败',
                                duration: 2000
                            })
                        }
                    },
                },
            })
        }

        function hanldeClear() {
            input.value = '';
            previousValue = '';
            disableSubmitStyle();
        }

        function disableSubmitStyle() {
            clearIcon.classList.add('hidden');
            nextButton.disabled = true;
            nextButton.style.backgroundColor = '#D0D0D0';
        }

        function handleNextButton(event) {
            if (!event.disabled && input.value.length && avatars) {
                trackPD(pageID)
                trackNew({
                    pageId: pageID,
                    eid: 'e_group_next_step_button',
                    type: 'MC'
                })
                window.localStorage.setItem('groupCreatedInfo', JSON.stringify({
                    type: queryParams.type,
                    category: queryParams.category,
                    name: input.value,
                    avatars: avatars
                }));
                openOfflineWebview(`cp/groupCreated/groupIntroduce.html?pageId=Group_groupIntroduce`)
            }
        }

        function hanldeInput(e) {
            let value = e.target.value;
            // 处理删除
            if (e.inputType === 'deleteContentBackward') previousValue = value;
            // 处理粘贴
            if (e.inputType === 'insertFromPaste') e.pasteData = pasteVal;
            if (value.length && (e.data || e.pasteData)) {
                const checkLenRes = handleIosTextLength(e, previousValue, 15);
                if (!checkLenRes) {
                    previousValue = value;
                } else {
                    previousValue = checkLenRes;
                }

                //设置‘下一步’按钮样式
                if (image.src) {
                    nextButton.disabled = false;
                    nextButton.style.backgroundColor = '#FF5C31';
                }
            }

            if (!!e.target.value) {
                clearIcon && clearIcon.classList.remove('hidden');
            } else {
                clearIcon && clearIcon.classList.add('hidden');
            }
            !value.length && disableSubmitStyle();
        }

        function handleIOSBlur(e) {
            if (e.target.nodeName != 'INPUT') {
                input.blur();
            }
        }

        function handleAndroidKeyword() {
            let resizeHeight = document.documentElement.clientHeight || document.body.clientHeight;
            if(resizeHeight < originHeight) {
                //软键盘弹起
                setTimeout(() => {
                    input.scrollIntoView({behavior: "smooth", block: "start", inline: "nearest"});
                }, 100)
            }else {
                //软键盘收起
                setTimeout(() => {
                    document.body.scrollIntoView({behavior: "smooth", block: "start", inline: "nearest"});
                }, 100)
            }
        }

        // 图片上传错误处理
        function imgErrorHandler() {
            if (isAndroid && !jsbridge_isLocal && avatars && avatars[0] && avatars[0].url && image.src !== avatars[0].url) {
                image.src = avatars[0].url;
            } else {
                showToast({context: '上传图片错误'})
                defaultImage.style.display = 'block';
                image.style.display = 'none';
            }
        }

        window.onload = function () {
            document.title = '\u200E';
            originHeight = document.documentElement.clientHeight;
            setWebviewPageID({pageID: pageID})
            document.addEventListener("visibilitychange", function () {
                if (document.visibilityState === 'visible') {
                    isAndroid && setWebviewPageID({pageID: pageID});
                    window.localStorage.removeItem('groupCreatedInfo');
                }
            });

            isAndroid && window.addEventListener('resize', handleAndroidKeyword);
            setTimeout(function () {
                input.addEventListener('compositionstart', function () {
                    cpLock = false;
                });
                input.addEventListener('compositionend', function () {
                    cpLock = true;
                });
                input.addEventListener('input', function (event) {
                    setTimeout(function (){
                        if (isiOS && !cpLock) return;
                        hanldeInput(event)
                    }, 20)
                })
                input.addEventListener('paste', function (event) {
                    if (isAndroid && event.clipboardData || event.originalEvent) {
                        let clipboardData = (event.clipboardData || window.clipboardData);
                        pasteVal = clipboardData.getData('text');
                        // event.preventDefault();
                    }
                })
                nextButton.addEventListener('click', handleNextButton);
                input.addEventListener('click', function (event) {
                    trackNew({
                        pageId: pageID,
                        eid: 'e_add_group_name',
                        type: 'MC'
                    })
                })
                isiOS && getTabBarHeight(function (height) {
                    input.addEventListener('touchstart', function (event) {
                        event.stopPropagation();
                    })
                    clearBtn.addEventListener('touchstart', function (event) {
                        event.stopPropagation();
                    })
                    nextButton.addEventListener('touchstart', function (event) {
                        event.stopPropagation();
                    })
                    containerWrap.addEventListener('touchstart', function (event) {
                        input.blur()
                    })
                    input.addEventListener('focus', function () {
                        setTimeout(function () {
                            bottomBtn.style.bottom = `${window.pageYOffset + (+height)}px`;
                        }, 200)
                    })
                    input.addEventListener('blur', function () {
                        bottomBtn.style.bottom = '0'
                    })
                })
            }, 100)
        }
    </script>
</body>

</html>
