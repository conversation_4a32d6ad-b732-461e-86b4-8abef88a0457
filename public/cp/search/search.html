<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width,minimum-scale=1.0,maximum-scale=1.0,user-scalable=no">

    <title>群搜索</title>
    <link href="../utils/common.css?path=cp/utils/common.css" rel="stylesheet" type="text/css" />

    <style>
        body {
            margin: 0;
            padding: 0;
        }

        ul,
        p {
            margin: 0;
            padding: 0;
        }

        img {
            object-fit: cover;
        }

        .container_wrap {
            user-select: none;
            -webkit-user-select: none;
            /*webkit浏览器*/
            -moz-user-select: none;
            /*火狐*/
            -ms-user-select: none;
            /*IE10*/
        }

        .searchBox {
            position: fixed;
            width: 100%;
            left: 0;
            top: 0;
            background: white;
            z-index: 9999;
            padding: 0 16px;
        }

        .search {
            width: calc(100% - 32px);
            height: 44px;
            padding: 7px 0;
            border-radius: 6px;
            display: flex;
            align-items: center;
        }

        .search_input input {
            height: 26px;
            color: rgba(34, 34, 34);
            flex: 1;
            border: none;
            outline: none;
            font-size: 14px;
            background-color: transparent;
            padding: 0 10px;
            -webkit-tap-highlight-color: transparent;
        }

        .search_input input::-webkit-search-cancel-button {
            display: none;
        }

        .search_input {
            flex: 1;
            background-color: rgb(247, 247, 247);
            border-radius: 18px;
            display: flex;
            align-items: center;
            padding: 5px 10px 5px 12px;
        }

        .search_icon {
            display: inline-block;
            width: 22px;
            height: 22px;
            vertical-align: middle;
            margin-right: 5px;
        }

        .search_cancel {
            font-size: 16px;
            color: rgb(33, 33, 33);
            border: none;
            background-color: transparent;
            margin-left: 8px;
            padding-right: 0;
            outline: none;
            width: 40px;
            flex-shrink: 0;
            white-space: nowrap;
            padding: 0;
        }

        .clear_icon {
            display: inline-block;
            line-height: 16px;
            width: 16px;
            height: 16px;
        }

        .line {
            height: 0.5px;
            background-color: rgb(236, 236, 236);
            padding: 0;
            margin: 0;
            border: none;
            width: calc(100vw - 32px);
        }

        .recommend_search {
            margin-top: 12px;
        }

        .recommend_search>p {
            font-size: 14px;
            color: rgb(196, 196, 196);
        }

        .recommend_ul {
            list-style: none;
            display: flex;
            flex-wrap: wrap;
        }

        .recomment_li {
            background-color: rgb(250, 250, 250);
            border-radius: 14px;
            font-size: 14px;
            color: rgb(117, 117, 117);
            padding: 4px 12px;
            margin-right: 8px;
            margin-top: 10px;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }

        .search_none {
            font-size: 14px;
            line-height: 16px;
            letter-spacing: 0;
            color: rgb(208, 208, 208);
            padding: 28px 0 60px;
            text-align: center;
            white-space: nowrap;
        }

        .keyword {
            display: inline-block;
            max-width: calc(100% - 180px);
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            vertical-align: bottom;
        }

        .search_result {
            width: 100%;
            height: 100%;
        }

        .content_main {
            position: relative;
            width: 100vw;
            top: 58px;
            padding: 0 20px;
            box-sizing: border-box;
        }

        .content-wrapper {
            margin-top: 24px;
            display: flex;
        }

        .content-wrapper:first-child {
            margin-top: 10px;
        }

        .content-wrapper .title {
            display: flex;
            align-items: center;
            overflow: hidden;
            -webkit-box-orient: vertical;
            -webkit-line-clamp: 1;
            width: 100%;
        }

        .content-wrapper .title .title_name {
            overflow: hidden;
            display: inline-block;
            max-width: calc(100% - 38px);
            white-space: nowrap;
            text-overflow: ellipsis;
        }

        .content-wrapper .avator {
            flex-basis: 68px;
            width: 68px;
            height: 68px;
            border-radius: 50%;
            background-image: url('../image/profile_avator.png');
            background-repeat: no-repeat;
            background-size: contain;
        }

        .content-wrapper .content {
            flex: 1;
            padding: 0 10px 0 12px;
            width: 0;
        }

        .tag-wrapper {
            margin-top: 5px;
            height: 22px;
            overflow: hidden;
        }

        .content-wrapper .tag {
            display: inline-block;
            padding: 0 5px;
            border-radius: 11px;
            font-size: 11px;
            color: rgb(150, 150, 150);
            border: 1px solid rgb(230, 230, 230);
        }

        .content-wrapper .desc {
            margin-top: 8px;
            text-overflow: ellipsis;
            overflow: hidden;
            display: -webkit-box;
            -webkit-box-orient: vertical;
            -webkit-line-clamp: 2;
            font-size: 13px;
            color: rgb(144, 144, 144);
            word-break: break-all;
            white-space: pre-line;
        }

        .content-wrapper .action {
            flex-basis: 68px;
            display: flex;
            justify-content: center;
            align-items: center;
            -webkit-tap-highlight-color: transparent;
        }

        .content-wrapper .action-button {
            padding: 6px 12px;
            font-size: 12px;
            border-radius: 24px;
        }

        .action-button.action-button__active {
            background-color: rgb(255, 92, 49);
            color: rgb(255, 255, 255);
        }

        .action-button.action-button__application {
            color: rgb(208, 208, 208);
            border: 1px solid rgb(208, 208, 208);
        }

        .interest_ul {
            flex: 1;
            overflow: auto;
            /* padding: 0 16px; */
        }


        .hidden {
            display: none;
        }

        .result {
            width: 100%;
            height: 100%;
            overflow: auto;
        }

        .clear_icon {
            width: 14px;
            height: 14px;
            border-radius: 50%;
            background-color: #ccc;
            background-image: url('../image/clear.png');
            background-repeat: no-repeat;
            background-position: center;
            background-size: contain;
        }

        .noMore {
            padding-top: 10px;
            padding-bottom: 30px;
            text-align: center;
            color: rgb(150, 150, 150);
            font-size: 12px;
        }
    </style>
</head>

<body>
    <div class="container_wrap">
        <div class="searchBox">
            <div class="search">
                <form target="#" action="javascript:return true" id="search_from" class="search_input">
                    <img src="../image/search.png" class="search_icon" alt="">
                    <input class="input" type="search" placeholder="搜索关键词或完整群号" oninput="handleInput(event.target)" />
                    <span class="clear_icon hidden" onclick="handleCancel()"></span>
                </form>
                <button class="search_cancel" onclick="handleGoBack()">取消</button>
            </div>
            <hr class="line" />
        </div>
        <div class="content_main">
            <div class="recommend_search hidden">
                <p>推荐搜索</p>
                <ul class="recommend_ul" onclick="handleCommend(event)">

                </ul>
            </div>
            <div class="search_none hidden">
                <p>未找到“<span class="keyword"></span>”相关的群 <span style="color: rgb(255, 92, 49); text-decoration: none;"
                        onclick="handleCreate()">去创建</span>
                </p>
            </div>
            <div class="search_result hidden">
                <ul class="result"></ul>
            </div>
            <div class="may_interest hidden">
                <p>可能感兴趣的群</p>
                <ul class="interest_ul"></ul>
            </div>
        </div>

    </div>
    <script type="text/javascript" src="../utils/jsbridge.js?path=cp/utils/jsbridge.js"></script>
    <script src="../utils/zepto.js?path=cp/utils/zepto.js"></script>
    <script type="text/javascript" src="../utils/ajax.js?path=cp/utils/ajax.js"></script>
    <script type="text/javascript" src="../utils/load.js?path=cp/utils/load.js"></script>
    <script type="text/javascript" src="../utils/util.js?path=cp/utils/util.js"></script>
    <script type="text/javascript" src="../utils/common.js?path=cp/utils/common.js"></script>

    <script type="text/javascript">
        "use strict";
        let pageID = 'p_search_result';
        let nextlink = {};
        let recommendList;
        // 存放当前列表所有ID
        let groupIds = [];
        // let navTitle = '群搜索';

        let recommendUl = document.querySelector('.recommend_ul');
        let clearIcon = document.querySelector('.clear_icon');
        let searchNone = document.querySelector('.search_none');
        let interestUl = document.querySelector('.interest_ul');
        let resultUl = document.querySelector('.result');
        let input = document.querySelector('.input');
        let searchTimer = null;

        if (isiOS) {
            //ios键盘弹出之后页面页面会被强制滚动消失
            window.ontouchmove = function () {
                input.blur();
            };
        }


        function handleCommend(event) {
            if (event.target.nodeName === 'LI') {
                input.value = event.target.innerText;
            }
        }

        const showNativeVerified = function (params) {
            const fnName = 'showNativeVerified';
            const res = triggerAction({
                actionType: fnName
            })
        }

        function setUserVerified(result) {
            //‘0'已经实名认证
            if (result === '0') {
                trackPD(pageID);
                openOfflineWebview(`cp/groupCreated/main.html?pageId=Group_created`)
            } else if (result === '1') {
                //‘1'未实名认证
                showNativeVerified();
            }

        }

        function handleCreate() {
            createGroupToH5();
        }

        const createGroupToH5 = function (params) {
            const fnName = 'createGroupToH5';
            const res = triggerAction({
                actionType: fnName
            })
        }

        function renderRecommemd(recommendList) {
            let li = '';
            recommendList.forEach(function (element) {
                li += `
                    <li class="recomment_li" onclick=searchHandle('${element}')>${element}</li>
                `
            })
            li && recommendUl.parentElement.classList.remove('hidden');
            !li && recommendUl.parentElement.classList.add('hidden');
            recommendUl.innerHTML = li;
        }

        let count = 0;
        let users = [];
        let chatGroups = [];
        let groupApplies = [];
        let chatGroupMembers = [];



        function searchHandle(value) {
            getInternetStatus(function () {
                handleInput('', value)
            })
        }

        // 边输入边搜索【无网络判断】
        function handleInput(inputEle, initalValue) {
            getInternetStatus(function () {
                if (searchTimer) clearTimeout(searchTimer);
                searchTimer = setTimeout(function () {
                    clearTimeout(searchTimer);
                    let value = initalValue || inputEle.value;
                    if (value.trim().length) {
                        clearIcon.classList.remove('hidden');
                        recommendUl.parentElement.classList.add('hidden');
                        fetchList(value)
                    } else {
                        clearIcon.classList.add('hidden');
                        recommendUl.innerHTML && recommendUl.parentElement.classList.remove('hidden');
                        searchNone.classList.add('hidden');
                        resultUl.parentElement.classList.add('hidden');
                        interestUl.parentElement.classList.add('hidden');

                    }
                }, 300)
            }, function () {
                interestUl.innerHTML = '';
                resultUl.innerHTML = '';
            })
        }

        //这里去搜索数据 首次渲染2屏数据【无网络判断】
        function fetchList(value) {
            getInternetStatus(function () {
                ajax({
                    url: `/v3/chat-groups?keyword=${encodeURIComponent(value)}&with=members,group-applies`,
                    method: 'GET',
                    data: ``
                }).then(function (res) {
                    // 搜索清空数组
                    groupIds = [];
                    if (res && res.data) {
                        users = res.data.users;
                        const arr = inputValueToArray(value);
                        if (res.data.chatGroups.length) {
                            renderSearchResult(res.data.chatGroups, res.data);
                        } else if (res.data.interestedGroups.length) {
                            //如果没有搜到数据
                            renderInterestList(res.data.interestedGroups, res.data, value);
                            $('.keyword').html(value);
                        } else {
                            searchNone.classList.remove('hidden');
                            resultUl.parentElement.classList.add('hidden');
                            interestUl.parentElement.classList.add('hidden');
                            $('.keyword').html(arr.length > 9 ? `${arr.splice(0, 9).join('')}...` : value);
                        }

                    }
                })
            })
        }


        function renderLiItem(list, which_page, data) {
            let li = '';
            list.forEach(function (item, index) {
                if (!!~groupIds.indexOf(item.id)) {
                    return ''
                }
                groupIds.push(item.id);
                let avatar = item.avatars && item.avatars[0] && item.avatars[0].url;
                let statusValue = getGroupStatusValue(item, data);
                trackNew({
                    pageId: pageID,
                    eid: 'e_group_chat_list',
                    type: 'MV',
                    extras: {
                        group_name: item.name,
                        group_order_id: index + 1,
                        group_tag: item.tags && JSON.stringify(item.tags) || '[]',
                        groupchat_id: item.id,
                        groupchat_type: item.type,
                        is_anonymou_group: item.category.hidden,
                        which_page
                    }
                })
                li += `
                <div class="content-wrapper" data-id="${item.id}" data-status="${statusValue}" onclick="clickItemHandle('${encodeURI(JSON.stringify(item))}', ${index}, '${encodeURI(item.id)}', event)">
                    <img class="avator" src=${avatar ? avatar : jsbridge_baseImgUrl} />
                    <div class="content">
                        <div class="title">
                            <span class="title_name">${item.name}</span>
                            ${(item.category && item.category.name !== '其他') ?
                        `<span
                            style="background-color: ${item.category.backgroundColor};
                                color: ${item.category.textColor};
                                border-radius: 4px;
                                font-size: 11px;padding: 0px 5px;display:inline-block; margin-left: 6px; white-space: nowrap;">
                            ${item.category.name}
                        </span>`: ''}
                        </div>
                        ${item.tags && item.tags.length ?
                        `
                            <div class="tag-wrapper">
                        ${item.tags.map(function (tag) {
                            return `<div class="tag"
                                        style="background-color: ${tag.backgroundColor};
                                        color: ${tag.textColor}"
                                    >${tag.name}</div>`
                        }).join(' ')}
                        </div>
                            `: ''}
                        <div class="desc">${item.description}</div>
                    </div>
                    <div class="action" id="groupItemAction${item.id}" onClick="clickHandle(event, '${encodeURI(JSON.stringify(item))}', ${encodeURI(statusValue)}, '${encodeURI(item.id)}', clickCallback, '${which_page}')" data-status="${statusValue}">
                        <div class="action-button ${[0, 2].indexOf(statusValue) !== -1 ? 'action-button__active' : 'action-button__application'}">${statusMap[statusValue]}</div>
                    </div>
                </div>
                `
            })
            return li;
        }


        function renderInterestList(list, data, keyword) {
            $('.search_result').addClass('hidden');
            searchNone.classList.remove('hidden');
            interestUl.parentElement.classList.remove('hidden');
            let li = renderLiItem(list, 'search_empty', data)
            interestUl.innerHTML = li;
            resultUl.innerHTML = '';
        }

        function renderSearchResult(list, data) {
            interestUl.parentElement.classList.add('hidden');
            $('.search_none').addClass('hidden');
            $('.search_result').removeClass('hidden');
            let li = renderLiItem(list, 'search_no_empty', data)
            resultUl.innerHTML = li + `<div class="noMore">没有更多了</div>`;
            // $(resultUl).append(li);
        }

        // 清空搜索内容
        function handleCancel() {
            // 取消搜索清空数组
            groupIds = [];
            input.value = '';
            $('.clear_icon').addClass('hidden')
            searchNone.classList.add('hidden');
            interestUl.parentElement.classList.add('hidden');
            resultUl.parentElement.classList.add('hidden');
            recommendUl.innerHTML && recommendUl.parentElement.classList.remove('hidden');
            resultUl.innerHTML = '';
        }

        // 取消
        function handleGoBack() {
            //这里返回上一个页面
            // window.history.go(-1);
            closeWebview()
        }

        // 加载更多数据
        function loadMoreData() {
            if (nextlink.next) {
                ajax({
                    url: nextlink.next.split('.com')[1],
                    method: 'GET',
                    data: ''
                }).then(function (res) {
                    if (res && res.data && res.data.chatGroups) {
                        renderSearchResult(res.data.chatGroups, res.data);
                        nextlink = res.link && res.link.links;
                    }
                })
            }
        }

        // 获取关键字【无网络判断】
        function getRecommend() {
            getInternetStatus(function () {
                ajax({
                    url: '/v3/search-keywords',
                    method: 'GET'
                }).then(function (res) {
                    if (res && res.meta && res.meta.code === 200) {
                        recommendList = res.data.keywords;
                        renderRecommemd(recommendList);
                    }
                })
            })
        }

        function clickCallback(item, which_page) {
            trackNew({
                pageId: pageID,
                eid: 'e_enter_group_chat',
                type: 'MC',
                extras: {
                    groupchat_id: item.id,
                    is_anonymou_group: item.type === 'anonymous' ? '1' : '0',
                    which_page: which_page
                }
            })
        }

        // 点击元素【无网络判断】
        function clickItemHandle(item, index, groupid, event) {
            getInternetStatus(function () {
                let domItemWrapper = document.querySelector(`[data-id='${groupid}']`);
                let statusValue = $(domItemWrapper).data('status');;
                let formatGroup = JSON.parse(decodeURI(item));
                let currentUserId = users[0].id;
                trackNew({
                    pageId: pageID,
                    eid: 'e_group_chat_list',
                    type: 'MC',
                    extras: {
                        group_name: formatGroup.name,
                        group_order_id: index + 1,
                        group_tag: (formatGroup.tags || []).map(item => item.name).join(':'),
                        groupchat_id: formatGroup.id,
                        groupchat_type: formatGroup.category.name,
                        is_anonymou_group: formatGroup.type === 'anonymous' ? '1' : '0',
                        which_page: 'search_no_empty'
                    }
                })
                trackPD(pageID);
                showGroupDetail(formatGroup, currentUserId, statusValue);
            })
        }

        getStatusBar({
            setStatusBar: function (result) {
                const searchEle = document.querySelector('.searchBox');
                if (!isAndroid) {
                    searchEle.style.paddingTop = `${result}px`;
                    $('.content_main').css('top', `${parseInt(result) + 60}px`)
                }
            }
        })


        setWebviewPageID({ pageID: pageID });
        getRecommend()
        hideNavigationBar();

        window.onload = function () {
            setTimeout(function () {
                input.focus();
            }, 500)
            document.getElementById('search_from').onsubmit = function (ev) {
                ev.stopPropagation();
                ev.preventDefault();
                const inputEle = Array.from(ev.target.childNodes).find(item => item.nodeName === 'INPUT')
                handleInput(inputEle)
            }
        }

        document.addEventListener("visibilitychange", function () {
            if (document.visibilityState === 'visible') {
                const obj = JSON.parse(localStorage['square_toDetail_info'] || '{}');
                const groupID = obj.groupID || 0;
                if (!groupID) return false;
                ajax({
                    url: `/v3/chat-groups/${groupID}?with=members,users,group-applies`,
                    method: 'GET'
                }).then(res => {
                    if (res && res.meta && res.meta.code === 200) {
                        localStorage['square_toDetail_info'] = '';
                        const status = String(getGroupStatusValue(res.data.chatGroups[0], res.data));
                        const actionBtnEle = $(`#groupItemAction${groupID}`)[0];
                        actionBtnEle.dataset.status = status;
                        actionBtnEle.innerHTML = `
                          <div class="action-button ${[0, 2].indexOf(+status) !== -1 ? 'action-button__active' : 'action-button__application'}">${statusMap[status]}</div>
                        `;
                    }
                });
            }
        });
    </script>

</body>

</html>
