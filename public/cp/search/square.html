<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
  <title>广场</title>
  <script type="text/javascript">
    window.doWhenGetToken = new Function()
  </script>
  <link href="../utils/common.css?path=cp/utils/common.css" rel="stylesheet" type="text/css" />
  <style>
    * {
      margin: 0;
      padding: 0;
    }

    img {
      object-fit: cover;
    }

    html,
    body {
      scroll-behavior: smooth;
      overflow: hidden;
    }

    input {
      background-color: transparent;
      border: none;
      outline: none;
    }

    .wrapper {
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      overflow: auto;
      display: flex;
      flex-direction: column;
      -moz-user-select: none;
      -webkit-user-select: none;
      -ms-user-select: none;
      user-select: none;
    }

    .top {
      flex-basis: 32px;
      margin: 0 12px;
    }

    .search-input {
      height: 32px;
      padding-left: 7px;
      border-radius: 18px;
      background-color: rgba(203, 203, 203, 0.1);
    }

    .search-input input {
      height: 100%;
      width: calc(100% - 32px);
      color: rgba(203, 203, 203);
      font-size: 14px;
    }

    .search-icon {
      display: inline-block;
      width: 22px;
      height: 22px;
      vertical-align: middle;
    }

    .nav-box {
      height: 44px;
      overflow: hidden;
    }

    .nav {
      flex-basis: 44px;
      width: 100%;
      height: 48px;
      padding-bottom: 3px;
      white-space: nowrap;
      overflow-x: scroll;
      overflow-y: hidden;
      color: rgb(203, 203, 203);
      -webkit-overflow-scrolling: touch;
    }

    .nav div {
      display: inline-block;
      font-size: 15px;
      padding: 12px;
    }

    .nav div.tab-active {
      color: rgb(255, 92, 49);
    }

    .middle {
      flex: 1;
      overflow: auto;
      padding: 0 16px;
      touch-action: pan-y;
      -webkit-overflow-scrolling: touch;
    }

    .content-wrapper {
      margin-top: 24px;
      display: flex;
    }

    .content-wrapper:first-child {
      margin-top: 10px;
    }

    .content-wrapper .title {
      display: flex;
      align-items: center;
      overflow: hidden;
      -webkit-box-orient: vertical;
      -webkit-line-clamp: 1;
      width: 100%;
    }

    .content-wrapper .title .title_name {
      overflow: hidden;
      display: inline-block;
      max-width: calc(100% - 38px);
      white-space: nowrap;
      text-overflow: ellipsis;
    }

    .li_image_wrap {
      width: 68px;
      height: 68px;
      position: relative;
      /* margin-right: 12px; */
    }

    .li_left_img,
    .default_picture {
      position: absolute;
      width: 100%;
      height: 100%;
      border-radius: 50%;
      pointer-events: none;
      background-image: url('../image/profile_avator.png');
      background-repeat: no-repeat;
      background-size: contain;
      /* flex: 1; */
      /* white-space: nowrap; */
    }

    .li_left_img {
      z-index: 99999;
    }

    .content-wrapper .content {
      flex: 1;
      width: 0;
      padding: 0 10px 0 12px;
    }

    .tag-wrapper {
      margin-top: 5px;
      overflow: hidden;
      height: 20px;
    }

    .content-wrapper .tag {
      display: inline-block;
      padding: 0 5px;
      border-radius: 9px;
      font-size: 11px;
      font-weight: bold;
      position: relative;
      bottom: 1px;
      color: rgb(150, 150, 150);
      border: 1px solid rgb(230, 230, 230);
    }

    .content-wrapper .desc {
      margin-top: 8px;
      display: -webkit-box;
      overflow: hidden;
      -webkit-box-orient: vertical;
      -webkit-line-clamp: 2;
      font-size: 13px;
      color: rgb(144, 144, 144);
      word-break: break-all;
      white-space: pre-line;
    }

    .content-wrapper .action {
      /* flex-basis: 68px; */
      display: flex;
      justify-content: center;
      align-items: center;
      outline: none;
      background: #fff;
      -webkit-tap-highlight-color: transparent;
    }

    .content-wrapper .action:active {
      background: #fff;
    }

    .content-wrapper .action-button {
      padding: 6px 13px;
      font-size: 12px;
      border-radius: 24px;
    }

    .action-button.action-button__active {
      background-color: rgb(255, 92, 49);
      color: rgb(255, 255, 255);
    }

    .action-button.action-button__application {
      color: rgb(208, 208, 208);
      border: 1px solid rgb(208, 208, 208);
    }

    .notice {
      position: fixed;
      right: 10px;
      bottom: 0;
    }

    .bell {
      width: 60px;
      height: 60px;
    }

    .notice-num {
      position: absolute;
      right: 10px;
      top: 10px;
      width: 16px;
      height: 16px;
      font-size: 10px;
      text-align: center;
      color: rgb(255, 255, 255);
      border-radius: 50px;
      line-height: 14px;
      background-color: red;
      border: 1.5px solid #ffffff;
      box-sizing: border-box;
    }

    .noMove {
      padding-top: 10px;
      padding-bottom: 30px;
      text-align: center;
      color: rgb(150, 150, 150);
      font-size: 12px;
    }

    .more {
      padding-top: 10px;
      padding-bottom: 20px;
      text-align: center;
      color: rgb(150, 150, 150);
      font-size: 12px;
      transition: all .3s linear;
    }
  </style>
</head>

<body>
  <div class="wrapper">
    <div class="top">
      <div class="search-input">
        <img src="../image/search.png" class="search-icon" alt="">
        <input type="search" class="search" readonly="readonly" placeholder="搜索关键字或完整群号"
          onclick="clickInputHandle()"></input>
      </div>
    </div>
    <div class="nav-box">
      <div class="nav"></div>
    </div>
    <div class="middle"></div>
    <div class="notice" onclick="goNoticePage()">
      <img class="bell" src="../image/bell.png"></img>
      <div class="notice-num"></div>
    </div>
  </div>
  <!-- <div class="loading"></div> -->
  <script src="../utils/jsbridge.js?path=cp/utils/jsbridge.js"></script>
  <script type="text/javascript" src="../utils/ajax.js?path=cp/utils/ajax.js"></script>
  <script type="text/javascript" src="../utils/common.js?path=cp/utils/common.js"></script>
  <script type="text/javascript" src="../utils/util.js?path=cp/utils/util.js"></script>
  <script type="text/javascript" src="../utils/load.js?path=cp/utils/load.js"></script>
  <script type="text/javascript" src="../utils/inobounce.js?path=cp/utils/inobounce.js"></script>
  <script src="../utils/zepto.js?path=cp/utils/zepto.js"></script>

  <script type="text/javascript">
    "use strict";
    let pageID = 'p_group_chat_square';
    // 当前Tab页
    let activeTab;
    // 触发上滑加载跟多数据的距离
    let distance = $('.wrapper')[0].clientHeight;
    // 未读通知的个数
    let noticeNumber;
    let nextlink = {};
    // 当前userId
    let currentUserId;
    // 存放当前列表所有ID
    let groupIds = [];

    let queryParams = getUrlParams(window.location.search.slice(1));

    function renderList(data, refresh) {
      let list = data.chatGroups;
      if (!list || !list.length) {
        $('.middle').empty();
        return
      }

      let dom = list.map(function (item, index) {
        if (!!~groupIds.indexOf(item.id)) {
          return ''
        }
        groupIds.push(item.id);
        let statusValue = getGroupStatusValue(item, data);
        trackNew({
          pageId: pageID,
          eid: 'e_group_chat_list',
          type: 'MV',
          extras: {
            group_name: item.name,
            group_order_id: index + 1,
            group_tag: item.tags ? item.tags.map(item => item.name).join(':') : '',
            groupchat_id: item.id,
            groupchat_type: item.category.name,
            is_anonymou_group: item.type === 'anonymous' ? '1' : '0',
          }
        })
        return `
          <div class="content-wrapper" data-id="${item.id}" data-status="${statusValue}" onclick="clickItemHandle('${encodeURI(JSON.stringify(item))}', ${index}, '${encodeURI(item.id)}', event)">
          <div class="li_image_wrap">
              <img class="default_picture" src=${item.avatars && item.avatars[0] && item.avatars[0].url ? item.avatars[0].url : '../image/profile_avator.png'} class="li_left_img" />
          </div>
          <div class="content">
            <div class="title">
                <span class="title_name">${item.name}</span>
                ${(item.category && item.category.name !== '其他') ?
            `<span
                          style="background-color: ${item.category.backgroundColor};
                              color: ${item.category.textColor};
                              border-radius: 4px;
                              font-size: 11px;display:inline-block; margin-left: 6px;line-height: 13px;
                              padding: 2px 5px;white-space: nowrap;">
                          ${item.category.name}
                    </span>`: ''}
            </div>
            ${item.tags && item.tags.length ?
            `
            <div class="tag-wrapper">
              ${item.tags.map(function (tag) {
              return `<div class="tag" style="background-color: ${tag.backgroundColor};color: ${tag.textColor}">${tag.name}</div>`
            }).join(' ')}
            </div>
              `: ''}
            <div class="desc">${item.description}</div>
          </div>
          <div class="action" id="groupItemAction${item.id}" onclick="clickHandle(event,'${encodeURI(JSON.stringify(item))}', ${encodeURI(statusValue)}, '${encodeURI(item.id)}', clickCallback)" data-status="${statusValue}">
            <div class="action-button ${[0, 2].indexOf(statusValue) !== -1 ? 'action-button__active' : 'action-button__application'}">${statusMap[statusValue]}</div>
          </div>
        </div>
        `;
      })
      if (refresh) {
        $('.middle').empty();
      }
      $('.middle').append(dom);
    }

    // 点击每个item【无网络判断】
    function clickItemHandle(item, index, groupid, event) {
      getInternetStatus(function () {
        let domItemWrapper = document.querySelector(`[data-id='${groupid}']`);
        // let statusValue = event.target.dataset.status;
        let statusValue = $(domItemWrapper).data('status');
        let formatGroup = JSON.parse(decodeURI(item));
        trackNew({
          pageId: pageID,
          eid: 'e_group_chat_list',
          type: 'MC',
          extras: {
            group_name: formatGroup.name,
            group_order_id: index + 1,
            group_tag: formatGroup.tags ? formatGroup.tags.map(item => item.name).join(':') : '',
            groupchat_id: formatGroup.id,
            groupchat_type: formatGroup.category.name,
            is_anonymou_group: formatGroup.type === 'anonymous' ? '1' : '0',
            which_page: 'search_no_empty'
          }
        })
        trackPD(pageID);
        showGroupDetail(formatGroup, currentUserId, statusValue);
      })
    }

    function renderNav(navData) {
      if (!navData.length) {
        return
      }

      activeTab = navData[0].id;
      let dom = navData.map(function (item) {
        return `
          <div class="tab ${item.id == 1 ? 'tab-active' : ''}" data-value="${item.id}">
            ${item.name}
          </div>
        `
      });
      $('.nav').empty().append(dom);
    }

    // 点击nav【无网络判断】
    function bindNav() {
      let navDiv = $('.nav');
      navDiv.on('click', function (event) {
        // 点击tab清空数组
        groupIds = [];
        getInternetStatus(function () {
          let target = event.target || event.srcElement;
          let tabValue = target.dataset.value
          if (!tabValue || tabValue === activeTab) return;
          let tabDivs = $('.nav .tab');

          tabDivs.removeClass('tab-active');
          $(target).addClass('tab-active');
          activeTab = tabValue;
          getGroupDataByTabID(activeTab);
          $('.middle').scrollTop(0);
        });
      });
    }

    function render(data) {
      renderNav(data);
      bindNav();
      bindLoadEvent('.middle', distance, refreshCallback);
    }

    function refreshCallback() {
      groupIds = [];
      getGroupDataByTabID(activeTab);
    }

    // 加载更多数据
    function loadMoreData() {
      if (nextlink.next) {
        if (!$('.middle .more').length) {
          $('.middle').append('<div class="more">加载中...</div>');
          $('.middle')[0].scrollTop = $('.middle')[0].scrollTop + 40;
        }
        ajax({
          url: nextlink.next.split('.com')[1],
          method: 'GET',
          data: ''
        }).then(function (res) {
          if (res && res.data && res.data.chatGroups) {
            $('.middle .more').css('color', 'transparent');
            $('.middle .more').remove();
            renderList(res.data);
            nextlink = res.link && res.link.links;;
            renderNoMoreData(nextlink)
          }
        })
      }
    }

    function getGroupDataByTabID(id) {
      ajax({
        url: `/v3/chat-groups?search=suggested&tab_id=${id}`,
        method: 'GET',
        data: ''
      }).then(function (res) {
        if (res && res.data && res.data.chatGroups) {
          renderList(res.data, true);
          nextlink = res.link && res.link.links;;
          renderNoMoreData(nextlink);
        }
      })
    }

    function renderNoMoreData(nextlink) {
      if (!nextlink || !nextlink.next) {
        if (!$('.middle').has('.noMove').length) {
          let ele = `<div class="noMove">${groupIds.length ? '没有更多数据了～' : '暂时没有新的内容'}</div>`
          $('.middle').append(ele)
          !groupIds.length && $('.noMove').css('padding-top', '30px');
        }
      }
    }

    // 根据jsbridge获取tabs和推荐第页的数据
    function getTabData(params) {
      let fnName = 'getTabData';
      let res = triggerAction({
        actionType: fnName,
        restParams: {
          setTabData: params.setTabData
        }
      })
    }

    function getNoticeNumber(params) {
      let fnName = 'getNoticeNumber';
      let res = triggerAction({
        actionType: fnName,
        restParams: {
          setNoticeNumber: params.setNoticeNumber
        }
      })
    }

    function setTabData(result) {
      try {
        let formatResult = JSON.parse(result);
        let data = formatResult.data;
        let tabsData = data.groupTabs;

        if (tabsData && tabsData.length > 0) {
          render(tabsData);
          getGroupDataByTabID(tabsData.find(function (item) {
            return item.name === '推荐'
          }).id)
        } else {
          getTabs();
        }
        if (data.links && data.links.next) {
          nextlink = {
            next: data.links.next
          }
        } else {
          renderNoMoreData(data.links);
        }
      } catch (e) {
        getTabs();
      }
    }

    function getTabs() {
      ajax({
        url: '/v3/group-settings',
        method: 'GET',
        data: ''
      }).then(function (res) {
        if (res && res.data && res.data.groupTabs && res.data.groupTabs.length > 0) {
          render(res.data.groupTabs);
          getGroupDataByTabID(res.data.groupTabs.find(function (item) {
            return item.name === '推荐'
          }).id)
        } else {
          $('.nav').css('display', 'none');
          // 默认如果没有拉到groupTabs， 直接去拉推荐的数据
          getGroupDataByTabID('1');
          activeTab = '1';
          bindLoadEvent('.middle', distance, refreshCallback);
        }
      })
    }

    function setNoticeNumber(result) {
      let data = JSON.parse(result).data;
      const $noticeNum = $('.notice-num');
      if (data.notificationCounters && data.notificationCounters.length && data.notificationCounters[0].unread) {
        const unreadCount = +data.notificationCounters[0].unread;
        if (!noticeNumber && unreadCount > 0) {
          trackNew({
            pageId: pageID,
            eid: 'e_group_notice_red_number_bubble',
            type: 'MV'
          })
        }
        noticeNumber = unreadCount;
        $noticeNum.css('display', 'block');
        $noticeNum.text(noticeNumber);
      } else {
        $noticeNum.css('display', 'none');
      }
    }

    //TODO: 跳转通知页面
    function goNoticePage(is_red_bubble) {
      trackNew({
        pageId: pageID,
        eid: 'e_group_notice_entrance',
        type: 'MC',
        extras: {
          is_red_bubble: noticeNumber ? 1 : 0  //1是有气泡数字，0是无气泡数字
        }
      })
      trackPD(pageID);
      openOfflineWebview(`cp/groupNotify/main.html?pageId=Group_groupNotify`);
    }

    function clickInputHandle() {
      getInternetStatus(function () {
        trackNew({
          pageId: pageID,
          eid: 'e_group_search',
          type: 'MC'
        })
        trackPD(pageID);
        openOfflineWebview(`cp/search/search.html?pageId=Group_search&hideNavigationBar=1&hideNotch=1`)
      })
    }

    function clickCallback(item) {
      trackNew({
        pageId: pageID,
        eid: 'e_enter_group_chat',
        type: 'MC',
        extras: {
          groupchat_id: item.id,
          is_anonymou_group: item.type === 'anonymous' ? '1' : '0',
          which_page: 'search_no_empty'
        }
      })
    }

    window.onload = function () {
      setWebviewPageID({ pageID: pageID, })
      getNoticeNumber({
        setNoticeNumber: setNoticeNumber
      })
      getTabData({
        setTabData: setTabData
      })
      getUserInfo().then(function (res) {
        currentUserId = res.userId;
      });
      setLittleBellStyle();
    }

    function setLittleBellStyle() {
    
      if (isiOS && !queryParams.from) {
        getTabBarHeight(function (res) {
          console.log(res);
          $('.notice').css('bottom', `${res}px`);
          $('.noMove').css('padding-bottom', `${+res + 30}px`)
          $('.wrapper').css('padding-bottom', `${res}px`)
        })
        return false;
      }
      $('.notice').css('bottom', '0px')
    }


    document.addEventListener("visibilitychange", function () {
      if (document.visibilityState === 'visible') {
        isAndroid && setWebviewPageID({ pageID: pageID })
        noticeNumber = 0;
        getNoticeNumber({
          setNoticeNumber: setNoticeNumber
        })
        const obj = JSON.parse(localStorage['square_toDetail_info'] || '{}');
        const groupID = obj.groupID || 0;
        if (!groupID) return false;
        ajax({
          url: `/v3/chat-groups/${groupID}?with=members,users,group-applies`,
          method: 'GET'
        }).then(res => {
          if (res && res.meta && res.meta.code === 200) {
            localStorage['square_toDetail_info'] = '';
            const status = String(getGroupStatusValue(res.data.chatGroups[0], res.data));
            const actionBtnEle = $(`#groupItemAction${groupID}`)[0];
            actionBtnEle.dataset.status = status;
            actionBtnEle.innerHTML = `
            <div class="action-button ${[0, 2].indexOf(+status) !== -1 ? 'action-button__active' : 'action-button__application'}">${statusMap[status]}</div>
            `;
          }
        });
      }
    });
  </script>
</body>

</html>
