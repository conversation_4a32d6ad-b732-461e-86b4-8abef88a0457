<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width,minimum-scale=1.0,maximum-scale=1.0,user-scalable=no">
    <title>群成员</title>
    <link href="../utils/common.css?path=cp/utils/common.css" rel="stylesheet" type="text/css" />
    <style>
        body {
            margin: 0;
            padding: 0;
        }

        .navigation_bar {
            background-color: #ffffff;
            display: flex;
            height: 56px;
            align-items: center;
            color: rgb(33, 33, 33);
            padding: 0 11px 0 15px;
            position: fixed;
            width: calc(100% - 36px);
        }

        .navigation_bar>img:first-child {
            width: 12px;
            height: 24px;
        }

        .delete_button {
            font-size: 16px;
        }

        .title_value {
            font-size: 17px;
            margin-left: 22px;
        }

        .member_container {
            padding: 0 5px;
            overflow: hidden;
        }

        .photo {
            width: 52px;
            height: 52px;
            border-radius: 50%;
            background-image: url('../image/profile_avator.png');
            background-repeat: no-repeat;
            background-size: contain;
        }

        .member-ul {
            list-style: none;
            padding: 0;
            margin: 0;
            font-size: 0;
            margin-top: 56px;
        }

        .member-li {
            width: 20%;
            height: 92px;
            display: inline-block;
            font-size: 0;
            padding: 10px 9px;
            box-sizing: border-box;
        }

        .li-spin {
            animation-name: spin;
            animation-duration: .2s;
        }

        .member-name {
            /* width: 44px; */
            max-width: 100%;
            line-height: 14px;
            text-align: center;
            font-size: 11px;
            padding: 0;
            margin: 0 auto;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }

        .photo {
            margin-bottom: 6px;
            display: block;
        }

        @keyframes spin {
            0% {
                transform: rotate(0deg);
            }

            50% {
                transform: rotate(-10deg);
            }

            100% {
                transform: rotate(10deg);
            }
        }

        .hidden {
            display: none;
        }
    </style>
</head>

<body>
    <div class="member_container">
        <div class="navigation_bar">
            <img class="close_arrow" src="../image/backarrow_ios.png" alt="" onclick="closeWebview()">
            <span class="title_value">群成员(<span></span>)</span>
            <span class="delete_button hidden" onclick="handleDelMember()">移除成员</span>
        </div>
        <ul class="member-ul" id="ul-box">
        </ul>
    </div>
</body>
<script src="../utils/jsbridge.js?path=cp/utils/jsbridge.js"></script>
<script src="../utils/util.js?path=cp/utils/util.js"></script>
<script src="../utils/common.js?path=cp/utils/common.js"></script>

<script type="text/javascript">
    "use strict";
    const pageID = 'p_group_members';
    window.doWhenGetToken = function () { }
    document.title = '群成员';
    var data = {};
    let timer;
    let titleValue = document.querySelector('.title_value');
    let closeArrow = document.querySelector('.close_arrow');
    let rightButton = document.querySelector('.delete_button');
    var divWrap = document.getElementById("ul-box");
    setCommonTitleStyle(closeArrow, titleValue);
    function initSize() {
        var docEle = document.documentElement;
        docEle.style.fontSize = (document.documentElement.offsetWidth / (375 / 100)) + 'px';
    }

    getStatusBar({
        setStatusBar: setStatusBar
    })

    function setStatusBar(result) {
        let navigationBar = closeArrow.parentElement;
        if (!isAndroid) {
            navigationBar.style.paddingTop = `${result}px`;
            navigationBar.style.height = '44px';
            divWrap.style.marginTop = `${parseInt(window.getComputedStyle(navigationBar, null).paddingTop) + parseInt(window.getComputedStyle(navigationBar, null).height)}px`;
        }
    }

    let query = window.localStorage.getItem('membersInfo');
    data = isAndroid ? JSON.parse(query) : JSON.parse(decodeURIComponent(query));

    const getGroupMember = function (params) {
        const fnName = 'getGroupMember';
        const setGroupMember = params.setGroupMember;
        const res = triggerAction({
            actionType: fnName,
            restParams: {
                groupid: data.groupid,
                setGroupMember: setGroupMember
            }
        })
    }

    function setGroupMember(result) {
        //重新获取一次群成员的数据
        let members = handleGroupOwnerFirstPlace(JSON.parse(result).chatGroupMembers, data.currentUserid);
        if (data.members && JSON.stringify(data.members) === JSON.stringify(members)) {
            return false;
        }

        data.members = members;
        divWrap.innerHTML = '';
        // initSize();
        initData(members);
        titleValue.firstElementChild.innerHTML = members.length;
        if (data.groupOwner) {
            rightButton.classList.remove('hidden');
            rightButton.parentElement.style.justifyContent = 'space-between';
        }
    }

    window.onload = function () {
        // initSize();
        if (data) {
            console.log(' 获取到--data');
            initData(data.members);
            titleValue.firstElementChild.innerHTML = data.members.length;
            if (data.groupOwner) {
                rightButton.classList.remove('hidden');
                rightButton.parentElement.style.justifyContent = 'space-between';
            } 
        } else {
            // iphone6获取不到localstorage值，手动再去拉取数据
            console.log('没有获取到--data');
            let queryParams = getUrlParams(window.location.search.slice(1));
            data = {
                groupid: queryParams.groupid,
                groupOwner: queryParams.groupOwner === 'false' ? false : true,
                type: queryParams.type,
                currentUserid: queryParams.currentUserid,
                conversationId: queryParams.conversationId,
            }
            getGroupMember({
                setGroupMember: setGroupMember
            })
        }

        setWebviewPageID({
            pageID: pageID,
            extras: {
                groupchat_id: data.groupid, //群聊的id
                is_anonymou_group: data.type === 'anonymous' ? '1' : '0' //是否是匿名群，0是实名，1是匿名
            }
        });
    }

    // 点击删除成员
    function handleDelMember() {
        getInternetStatus(function () {
            window.localStorage.setItem('membersInfo', JSON.stringify(data));
            trackPD(pageID);
            openOfflineWebview(`cp/groupMember/removeMember.html?pageId=Group_removeMember&hideNavigationBar=1&hideNotch=1`)
        })
    }

    // 生成一个成员
    function createMember(item, index) {
        switch (data.type) {
            case 'anonymous':
                getAnonymityAvatarById(item, function (url) {
                    var ele = "<li class='member-li' data-userid='" + item.userId + "' ><span class='photo_box'><img class='photo' src='" + url + "' alt=''></span><p class='member-name'>" + (item.nickName ? item.nickName : item.userName) + "</p></li>";
                    divWrap.innerHTML += ele;
                    handleBindEvent(index);
                    item.avatar = url;
                }, index, item);

                break;
            default:
                var ele = "<li class='member-li' data-userid='" + item.userId + "' ><span class='photo_box'><img class='photo' src='" + item.avatar + "' alt=''></span><p class='member-name'>" + (item.nickName ? item.nickName : item.userName) + "</p></li>";
                divWrap.innerHTML += ele;
                handleBindEvent(index)
                break;
        }

    }

    function handleBindEvent(index) {
        if (data.members.length - 1 === index) {
            bindEvent();
        }
    }

    // 初始化页面，绑定事件，点击头像进入个人信息页面
    function initData(data) {
        for (var i = 0; i < data.length; i++) {
            createMember(data[i], i);
        }
    }

    const sendTickleMessage = function (params) {
        const fnName = 'sendTickleMessage';
        console.log(params.conversationId);
        const res = triggerAction({
            actionType: fnName,
            restParams: {
                conversationId: params.conversationId,
                receiverUserId: params.receiverUserId
            }
        })
    }

    function getTargetElement(e) {
        let ele = e.target;
        while (ele.tagName !== 'LI') {
            ele = ele.parentElement;
        }
        return ele;
    }

    function bindEvent() {
        var checkItem = document.getElementsByClassName('photo_box');
        for (var i = 0; i < checkItem.length; i++) {
            // 单击头像跳转主页
            checkItem[i].addEventListener('click', function (e) {
                clearTimeout(timer);
                //这里要跳转到个人详情页
                timer = setTimeout(function (e) {
                    let ele = getTargetElement(e);
                    // let ele = e.target;
                    // while (ele.tagName !== 'LI') {
                    //     ele = ele.parentElement;
                    // }
                    openGroupMemberProfile({
                        userId: ele.dataset.userid,
                        anonymity: data.type === "anonymous" ? true : false
                    })
                    trackNew({
                        pageId: pageID,
                        eid: 'e_chat_avatar',
                        type: 'MC',
                        extras: {
                            is_self_avatar: ele.dataset.userid == data.currentUserid ? 'true' : 'false' //‘true代表自己的头像’，‘false’代表别人的头像
                        }
                    })
                }, 200, e)
            });

            // 双击头像摇晃
            checkItem[i].addEventListener('dblclick', function (e) {
                clearTimeout(timer);
                e.target.firstElementChild.classList.add('li-spin');
                let ele = getTargetElement(e);
                sendTickleMessage({ conversationId: data.conversationId, receiverUserId: ele.dataset.userid });
                let tempTimer = setTimeout(function () {
                    e.target.firstElementChild.classList.remove('li-spin');
                    clearTimeout(tempTimer);
                }, 500)
            });
        }
    }

    document.addEventListener("visibilitychange", function () {
        //从删除群成员列表返回的话，如果成员数据发生了变化，本页面数据需要刷新
        if (document.visibilityState === 'visible') {
            isAndroid && setWebviewPageID({
                pageID: pageID,
                extras: {
                    groupchat_id: data.groupid, //群聊的id
                    is_anonymou_group: data.type === 'anonymous' ? '1' : '0' //是否是匿名群，0是实名，1是匿名
                }
            });
            getGroupMember({
                setGroupMember: setGroupMember
            })
        }
    });
    hideNavigationBar();
</script>

</html>