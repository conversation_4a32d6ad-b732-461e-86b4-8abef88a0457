<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width,minimum-scale=1.0,maximum-scale=1.0,user-scalable=no">
    <title>移除成员</title>
    <link href="../utils/common.css?path=cp/utils/common.css" rel="stylesheet" type="text/css" />
    <style>
        body {
            margin: 0;
            padding: 0;
        }

        p {
            margin: 0;
            padding: 0;
        }

        ul {
            list-style: none;
            margin: 0;
            padding: 0;
        }

        li {
            font-size: 0;
        }

        a {
            text-decoration: none;
            color: royalblue;
        }

        .member_container {
            position: relative;
            z-index: 1;
            margin-top: 56px;
        }

        .navigation_bar {
            width: 100%;
            background-color: #ffffff;
            display: flex;
            justify-content: space-between;
            height: 56px;
            align-items: center;
            position: fixed;
            top: 0;
            z-index: 9999;
        }

        .navigation_bar>img:first-child {
            width: 12px;
            height: 24px;
            margin-left: 20px;
        }


        .delete_count {
            font-size: 16px;
            opacity: 0.5;
            line-height: 20px;
            margin-right: 16px;
            display: inline-block;
            width: 70px;
            text-align: right;
        }

        .title_value {
            font-size: 18px;
            margin-left: 25px;
        }

        .title_contain {
            height: 28px;
            background-color: rgb(247, 247, 247);
            padding: 0 16px;
        }

        .title {
            font-size: 14px;
            line-height: 28px;
            color: rgb(208, 208, 208);
            margin: 0;
        }

        .member-item {
            height: 80px;
            display: flex;
            flex-direction: row;
            align-items: center;
        }

        .photo {
            width: 52px;
            height: 52px;
            border-radius: 50%;
            margin-right: 12px;
        }

        .uncheck-style {
            width: 22px;
            height: 22px;
            margin: 0 16px;
        }

        .name {
            font-size: 15px;
            color: rgb(33, 33, 33);
            max-width: 230px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }

        .index-container {
            width: 14px;
            position: fixed;
            z-index: 35;
            right: 0;
            height: 100%;
            display: flex;
            align-items: center;
        }

        .index-li {
            font-size: 12px;
            color: royalblue;
            text-align: center;
        }

        .index-li>a {
            -webkit-touch-callout: none;
        }

        .mask {
            position: fixed;
            top: 0;
            left: 0;
            bottom: 0;
            right: 0;
            z-index: 15;
            display: none;
        }

        .shadow {
            position: fixed;
            top: 0;
            left: 0;
            bottom: 0;
            right: 0;
            background-color: rgba(0, 0, 0, 0.7);
            z-index: 9999;
            display: none;
        }

        .model {
            width: 270px;
            height: 106px;
            margin: auto;
            border-radius: 10px;
            background-color: #ffffff;
        }

        .model-content {
            height: 62px;
            border-bottom: 0.1px solid rgba(60, 60, 67, 0.29);
            font-size: 17px;
            text-align: center;
            line-height: 62px;
            font-weight: 600;
        }

        .model-btn {
            height: 43px;
            font-size: 0;
            display: flex;
        }

        .btn-left,
        .btn-right {
            font-size: 17px;
            text-align: center;
            line-height: 40px;
            color: rgb(0, 122, 255);
        }

        .btn-left {
            border-right: 0.1px solid rgba(60, 60, 67, 0.29);
            width: 50%;
            display: inline-block;
        }

        .btn-right {
            width: 50%;
            display: inline-block;
        }
    </style>
</head>

<body>
    <div class="navigation_bar">
        <img class="close_arrow" src="../image/backarrow_ios.png" alt="" onclick="closeWebview()">
        <span class="title_value">移除成员</span>
        <span class="delete_count" onclick="submit()">移除<span></span></span>
    </div>
    <div class="index-container">
        <ul class="index-ul" id="index-ul" onClick="JumpIndexAnchor(event)">
        </ul>
    </div>
    <div class="member_container">
        <div class="card" id="container">
        </div>
    </div>
    <div class="mask" id="mask"></div>
    <div class="shadow" id="shadow">
        <div class="model">
            <div class="model-content" id="model-content">确定要移除1位群成员吗?</div>
            <div class="model-btn">
                <div class="btn-left" onclick="handleCancel()">取消</div>
                <div class="btn-right" onclick="handleConfirm()">确认</div>
            </div>
        </div>
    </div>
</body>
<script src="../utils/jsbridge.js?path=cp/utils/jsbridge.js"></script>
<script src="../utils/util.js?path=cp/utils/util.js"></script>
<script type="text/javascript">
    "use strict";
    window.doWhenGetToken = function () { }
    // document.title = '移除成员';
    const pageID = 'p_delete_group_person';
    let titleValue = document.querySelector('.title_value');
    let closeArrow = document.querySelector('.close_arrow');
    let deleteCount = document.querySelector('.delete_count');
    let containerIndex = document.getElementById("index-ul");
    let containerMember = document.getElementById("container");
    let shadowPopup = document.querySelector('.shadow');
    let indexLetters;

    setCommonTitleStyle(closeArrow, titleValue);
    getStatusBar({
        setStatusBar: setStatusBar
    })

    function setStatusBar(result) {
        let navigationBar = closeArrow.parentElement;
        if (!isAndroid) {
            navigationBar.style.paddingTop = `${result}px`;
            navigationBar.style.height = '44px';
            containerMember.parentElement.style.marginTop = `${parseInt(window.getComputedStyle(navigationBar, null).paddingTop) + parseInt(window.getComputedStyle(navigationBar, null).height)}px`;
        }
    }

    var activeRmvArr = [];
    var data = [];
    var currUser = '';
    let groupid;
    let originData;

    window.onload = function () {
        hideNavigationBar();
        init();
        // passive属性为true，永远不会调用preventDefault
        document.body.addEventListener('touchmove', hanldeTouchMove, { passive: false });
    }

    document.addEventListener("visibilitychange", function () {
        if (document.visibilityState !== 'visible') {
            document.body.removeEventListener('touchmove', hanldeTouchMove, { passive: false })
        }
    });


    function hanldeTouchMove(event) {
        if (window.getComputedStyle(shadowPopup, null).display !== 'none') {
            if (event.cancelable) {
                // 判断默认行为是否已经被禁用
                if (!event.defaultPrevented) {
                    event.preventDefault();
                }
            }
        }
    }

    function init() {
        let query = window.localStorage.getItem('membersInfo');
        let result = isAndroid ? JSON.parse(query) : JSON.parse(decodeURIComponent(query));
        originData = result.members;
        groupid = result.groupid;
        //  判断当前用户是否是群主
        getUserInfo().then(function (res) {
            currUser = res.userId;
            originData = originData.filter(element => {
                if (element.userId !== res.userId) {
                    element.checked = false;
                    return element;
                }
            });
            data = formatData(originData);
            initPage();
        });

        setWebviewPageID({pageID: pageID});
    }

    function getMembersTotal() {
        let count = 0;
        if (!data || !data.segs || !data.segs.length) return 0;
        data.segs.map(item => count += item.data.length);
        return count;
    }

    function handleCancel() {
        shadowPopup.style.display = 'none';
        document.getElementById('mask').style.display = 'none';
    }

    function handleConfirm() {
        getInternetStatus(function () {
            shadowPopup.style.display = 'none';
            document.getElementById('mask').style.display = 'none';

            //移除群成员,获取要移除群成员的userid数组
            let userArray = activeRmvArr.map(function (item) {
                return item.userId;
            })

            handleDivideDeleteMembers(userArray);
            trackNew({
                pageId: pageID,
                eid: 'e_delete_group_person_confirm',
                type: 'MC',
                extras: {
                    user_id: userArray.toString()
                }
            })
        })
    }


    function handleDivideDeleteMembers(users) {
        let len = users.length;
        //每次批量删除最多20个
        let n = 20;
        for (let i = 0; i < len; i += n) {
            deleteGroupMember({
                setDeleteGroupMember: setDeleteGroupMember,
                userIds: users.slice(i, i + n)
            })
        }
    }

    function setDeleteGroupMember(result) {
        //删除群成员成功了
        if (!JSON.parse(result)) return;
        let successExitMembers = []; //删除成功的成员的id
        JSON.parse(result).data.chatGroupMembers.forEach(function (item) {
            if (item.status !== 'exited') {
                //未删除成功的成员依然出现在列表里，保持选中的状态
            } else {
                successExitMembers.push(item.userId);
                activeRmvArr.splice(activeRmvArr.map(item => item.userId).indexOf(item.userId), 1);
            }
        })
        if (successExitMembers.length) {
            originData = originData.filter(function (item) {
                if (successExitMembers.indexOf(item.userId) === -1) {
                    return item;
                }
            })
        }
        data = formatData(originData);
        handleRightDeleteText(activeRmvArr);
        initPage()
    }

    const deleteGroupMember = function (params) {
        const fnName = 'deleteGroupMember';
        const res = triggerAction({
            actionType: fnName,
            restParams: {
                groupid: groupid,
                userIds: params.userIds,
                setDeleteGroupMember: params.setDeleteGroupMember
            }
        })
    }


    // 提交移除
    function submit() {
        if (activeRmvArr.length <= 0) {
            return false;
        }
        document.getElementById('model-content').innerHTML = '确定要移除' + activeRmvArr.length + '位群成员吗?'
        shadowPopup.style.display = 'flex';
        document.getElementById('mask').style.display = 'block';

    }

    // 初始化导航栏
    function initSize() {

        var docEle = document.documentElement;
        console.log(document.documentElement.offsetWidth, 'document.documentElement.offsetWidth');
        docEle.style.fontSize = (document.documentElement.offsetWidth / (375 / 100)) + 'px';
    }

    // 初始化右侧索引
    function initIndex(arr) {
        containerIndex.innerHTML = '';
        for (var i = 0; i < arr.length; i++) {
            // var ele = "<li class='index-li'><a href='#" + arr[i] + "'>" + arr[i] + "</a></li>";
            var ele = "<li class='index-li'><a data-index='" + arr[i] + "'>" + arr[i] + "</a></li>";
            containerIndex.innerHTML += ele;
        }
    }


    function JumpIndexAnchor(event) {
        if (indexLetters.includes(event.target.dataset.index)) {
            window.scrollTo(0, document.querySelector(`#${event.target.dataset.index}`).offsetTop)
        }
    }

    // 初始化页面，生成成员列表，绑定点击事件
    function initPage() {
        var indexType = [];
        containerMember.innerHTML = '';
        if (!data || !data.segs) return;
        for (var i = 0; i < data.segs.length; i++) {
            createRow(data.segs[i]);
            indexType.push(data.segs[i].initial);
        }
        bindEvent();

        initIndex(indexType);
    }

    // 生成成员列表（一块）
    function createRow(item) {
        var ele = "<div class='title_contain' id=" + item.initial + "><p class='title'>" + item.initial + "</p></div>";
        containerMember.innerHTML += ele;

        var ulEle = document.createElement("ul");
        ulEle.classList.add("members");
        containerMember.appendChild(ulEle);
        for (var i = 0; i < item.data.length; i++) {
            let name = item.data[i].nickName ? item.data[i].nickName : item.data[i].userName;
            var checkIcon = item.data[i].checked ? '../image/member_checked.png' : '../image/member_uncheck.png'
            var elem = "<li data-userid=" + item.data[i].userId + " class='member-item'><span class='uncheck-style-box'><img class='uncheck-style' dataType=" + item.initial + " dataIndex=" + i + " src='" + checkIcon + "' alt=''></span><img class='photo' src='" + item.data[i].avatar + "' alt=''><p class='name'>" + name + "</p></li>";
            ulEle.innerHTML += elem;
        }
    }

    // 绑定选中成员事件
    function bindEvent() {
        var checkItem = document.getElementsByClassName('uncheck-style-box');
        for (var i = 0; i < checkItem.length; i++) {
            checkItem[i].addEventListener('click', function (e) {
                getInternetStatus(function () {
                    let img = e.target.firstElementChild;
                    var segs = data.segs.filter(function (it) { return it.initial === img.getAttribute('dataType'); })
                    if (segs.length && segs[0].data[img.getAttribute('dataIndex')]) {
                        segs[0].data[img.getAttribute('dataIndex')].checked = !segs[0].data[img.getAttribute('dataIndex')].checked;
                        if (segs[0].data[img.getAttribute('dataIndex')].checked) {
                            activeRmvArr.push({
                                userId: e.target.parentElement.dataset.userid,
                                checked: true
                            })
                            img.src = '../image/member_checked.png';
                        } else {

                            let id = e.target.parentElement.dataset.userid;
                            activeRmvArr = activeRmvArr.filter(function (item) { return item.userId !== id });
                            img.src = '../image/member_uncheck.png';
                        }
                        handleRightDeleteText(activeRmvArr)
                    }
                })
            })
        }
    }

    function handleRightDeleteText(activeRmvArr) {
        if (activeRmvArr.length > 0) {
            deleteCount.style.opacity = '1';
            deleteCount.firstElementChild.innerText = `(${activeRmvArr.length})`;
        } else {
            deleteCount.style.opacity = '0.5';
            deleteCount.firstElementChild.innerText = ''
        }
    }

    // 格式化数据，分类A-Z
    function formatData(data) {
        if (data.length == 0) return;

        var res = {};
        var letterRegExp = /[ABCDEFGHJKLMNOPQRSTWXYZ]/;

        let indexArray = [];

        data.forEach(function (item) {
            // 当前匹配字幕
            let AlphaObj = this.letterRegExp.exec(item.nickNamePinyin[0].toUpperCase());
            // 如果AlphaIndex 不存在说明存在特殊字符，
            let AlphaIndex = AlphaObj ? AlphaObj[0] : '#';
            let currIndex = this.indexArray.filter(item => item.initial === AlphaIndex)
            if (currIndex.length) {
                currIndex[0].data.push(item);
            } else {
                this.indexArray.push({
                    initial: AlphaIndex,
                    data: [item]
                })
            }

        }, { letterRegExp: letterRegExp, indexArray: indexArray })

        // 按照顺序进行排序
        indexArray.sort(function (a, b) {
            var textA = a.initial;
            var textB = b.initial;
            return (textA < textB) ? -1 : (textA > textB) ? 1 : 0
        });

        // 将‘#’元素移动到最后
        let speSymbolIndex;
        let speSymbolNum;

        indexLetters = indexArray.map((item, index) => {
            if (item.initial === '#') {
                speSymbolIndex = index;
                speSymbolNum = item;
            }
            return item.initial;
        })

        if (speSymbolNum) {
            // 如果存在#
            indexArray.splice(speSymbolIndex, 1);
            indexArray.push(speSymbolNum);
        }


        res.segs = indexArray;
        return res;
    }

</script>

</html>
