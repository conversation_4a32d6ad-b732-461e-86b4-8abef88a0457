<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width,minimum-scale=1.0,maximum-scale=1.0,user-scalable=no">

    <title>群简介</title>
    <link href="../utils/common.css?path=cp/utils/common.css" rel="stylesheet" type="text/css" />
    <style>
        body {
            margin: 0;
            padding: 0;
        }

        img {
            object-fit: cover;
        }

        .container_wrap {
            padding: 0 16px;
        }

        .navigation_bar {
            width: 100%;
            display: flex;
            background-color: #ffffff;
            justify-content: space-between;
            height: 56px;
            align-items: center;
            top: 0;
            z-index: 9999;
            padding-left: 4px;
        }

        .navigation_bar>img:first-child {
            width: 12px;
            height: 24px;
        }

        .navigation_bar>img:last-child {
            width: 44px;
            height: 26px;
        }

        .input_wrap {
            margin-top: 20px;
            background-color: rgb(251, 251, 251);
            border-radius: 6px;
            display: flex;
            align-items: center;
        }

        .image_wrap {
            width: 32px;
            height: 32px;
            position: relative;
        }

        .left_img {
            position: absolute;
            width: 32px;
            height: 32px;
            border-radius: 50%;
            pointer-events: none;
        }

        .input_wrap input {
            flex: 1;
            font-size: 16px;
            color: rgb(34, 34, 34);
            border: none;
            background-color: rgb(251, 251, 251);
            padding: 13px 16px;
            outline: none;
            box-sizing: border-box;
            caret-color: red;
        }

        .input_wrap input::placeholder {
            color: rgb(208, 208, 208);
        }

        .clear {
            width: 17px;
            height: 17px;
            display: block;
        }


        .count_wrap {
            display: flex;
            font-size: 13px;
            color: rgb(168, 168, 168);
            justify-content: space-between;
        }

        .count_wrap>p {
            margin: 8px 0 0;
        }

        .description_info {
            height: 18px;
        }

        .hidden {
            display: none;
        }

        textarea {
            width: 100%;
            height: 210px;
            resize: none;
            border: none;
            outline: none;
            background-color: transparent;
            font-size: 16px;
            padding: 13px 12px;
            color: rgb(34, 34, 34);
        }

        textarea::placeholder {
            color: rgb(208, 208, 208);
        }
    </style>
</head>

<body>
    <div class="container_wrap">
        <div class="navigation_bar">
            <img class="close_arrow" src="../image/backarrow_ios.png" alt="" onclick="closeWebview()">
            <span class="title_value"></span>
            <img class="finish_button" src="../image/finish_unchecked.png" alt="" onclick="handleRightButton(event)">
        </div>
        <div class="input_wrap">

        </div>
        <div class="count_wrap">
            <p class="description_info"></p>
            <p><span class="count">0</span>/<span class="total"></span></p>
        </div>
    </div>
    <script src="../utils/jsbridge.js?path=cp/utils/jsbridge.js"></script>
    <script src="../utils/util.js?path=cp/utils/util.js"></script>
    <script type="text/javascript">
        "use strict";

        let pageID = '';
        let clearIcon, input, count, textarea;
        let inputWrap = document.querySelector('.input_wrap');
        let total = document.querySelector('.total');
        let closeArrow = document.querySelector('.close_arrow');
        let finishButton = document.querySelector('.finish_button');
        let titleValue = document.querySelector('.title_value');
        let descriptionInfo = document.querySelector('.description_info');

        let initialValue;
        let data;
        let previousValue = '';
        let pasteVal = '';
        let DEFAULT_TEXTAREA = '可描述你的建群目的、希望什么样的朋友加入和群内规则，不超过300字';

        let cpLock = true;

        setCommonTitleStyle(closeArrow, titleValue);

        getStatusBar({
            setStatusBar: setStatusBar
        })
        //设置电量栏状态(只处理ios,ios的电量栏是透明的，单独处理，安卓正常)
        function setStatusBar(result) {
            let navigationBar = closeArrow.parentElement;
            if (!isAndroid) {
                navigationBar.style.width = 'calc(100% - 32px)';
                navigationBar.style.position = 'fixed';
                navigationBar.style.height = '44px';
                navigationBar.style.paddingTop = `${result}px`;
                inputWrap.style.marginTop = `${parseInt(window.getComputedStyle(navigationBar, null).paddingTop) + parseInt(window.getComputedStyle(navigationBar, null).height) + 20}px`;
            }
        }

        window.onload = function () {
            hideNavigationBar();
            try {
                data = JSON.parse(decodeURIComponent(getUrlParams(window.location.search.slice(1)).params));
            } catch (e) {
                data = JSON.parse(getUrlParams(window.location.search.slice(1)).params);
            }
            initialValue = data.value;
            previousValue = data.value;
            renderInputMode(data);
            getElement();
            setDefaultStyle(data);
            let inputbox = inputWrap.querySelector('input') || inputWrap.querySelector('textarea');
            inputbox.addEventListener('compositionstart', function () {
                console.log('compositionstart');
                cpLock = false;
            });
            inputbox.addEventListener('compositionend', function () {
                console.log('compositionend');
                cpLock = true;
            });
            inputbox.addEventListener('input', function (event) {
                setTimeout(function (){
                    if (isiOS && !cpLock) return;
                    handleInput(event)
                }, 20)
            })
            inputbox.addEventListener('paste', function (event) {
                console.log('paste');
                if (isAndroid && (event.clipboardData || event.originalEvent)) {
                    let clipboardData = (event.clipboardData || window.clipboardData);
                    pasteVal = clipboardData.getData('text');
                    // event.preventDefault();
                }
            })
        }

        if (isiOS) {
            //ios键盘弹出之后页面页面会被强制滚动消失
            window.ontouchmove = function () {
                // 群名称和本群昵称是input输入框，群简介是textare
                let inputbox = inputWrap.querySelector('input') || inputWrap.querySelector('textarea');
                inputbox.blur();
            };
        }

        function setModifyGroupInfo(infoData) {
            let result = JSON.parse(infoData);
            if (result.status === '0') {
                console.log(result, data.type);
                //群名称或者群简介修改成功
                switch (data.type) {
                    case 'name':
                        trackNew({
                            pageId: 'p_alter_group_name',
                            eid: 'e_group_name_finish_button',
                            type: 'MC',
                            extras: {
                                groupchat_id: data.groupid,
                                is_anonymou_group: data.isanonymous === 'anonymous' ? '1' : '0'
                            }
                        })
                        break;
                    case 'description':
                        trackNew({
                            pageId: 'p_alter_group_state',
                            eid: 'e_group_state_finish_button',
                            type: 'MC',
                            extras: {
                                groupchat_id: data.groupid,
                            }
                        })
                        break;
                }
                setTimeout(closeWebview, 200)
            } else if (result.status === '1') {
                //否则修改失败
                !isAndroid && showToast({
                    context: result.message,
                    duration: 2000
                })
            }
        }

        const modifyGroupInfo = function (params) {
            const fnName = 'modifyGroupInfo';
            const res = triggerAction({
                actionType: fnName,
                restParams: {
                    groupid: params.groupid,
                    [params.type]: isAndroid ? `{'${params.type}': '${params[params.type]}'}` : params[params.type],
                    conversationid: params.conversationid,
                    setModifyGroupInfo: params.setModifyGroupInfo
                }
            })
        }

        // 点击完成按钮保存数据【无网络判断】
        function handleRightButton(e) {
            // 右上角完成按钮， 灰色的时候如果键盘拉起，要保持input获得焦点，如果键盘没拉起，保持现状
            if (e.target.src.includes('finish_unchecked')) {
                return;
            }
            getInternetStatus(function () {
                switch (data.type) {
                    case 'name':
                        modifyGroupInfo({ type: 'name', conversationid: data.conversationid, groupid: data.groupid, name: input && input.value.trim(), setModifyGroupInfo: setModifyGroupInfo });
                        break;
                    case 'description':
                        if (textarea.value !== initialValue) {
                            let value = textarea.value.trim();
                            // let value = isiOS ? textarea.value.trim() : JSON.stringify(textarea.value.trim());
                            modifyGroupInfo({ type: 'description', conversationid: data.conversationid, groupid: data.groupid, description: textarea && value, setModifyGroupInfo: setModifyGroupInfo });
                        }
                        break;
                    case 'nickname':
                        modifyGroupNickname({ conversationid: data.conversationid, groupid: data.groupid, value: input && input.value, setModifyGroupNickname: setModifyGroupNickname });
                        break;
                }
            })
        }

        const modifyGroupNickname = function (params) {
            const fnName = 'modifyGroupNickname';
            const res = triggerAction({
                actionType: fnName,
                restParams: {
                    groupid: params.groupid,
                    nickname: params.value,
                    conversationid: params.conversationid,
                    setModifyGroupNickname: params.setModifyGroupNickname
                }
            })
        }

        function setModifyGroupNickname(data) {
            let result = JSON.parse(data);
            if (result.status == '0') {
                //群昵称修改成功
                closeWebview()
            } else if (result.status === '1') {
                //群昵称修改失败
                !isAndroid && showToast({
                    context: result.message,
                    duration: 2000
                })
            }
        }

        function setDefaultStyle(objParams) {
            if (objParams.type === 'name' || objParams.type === 'nickname') {
                inputWrap.style.paddingRight = '17px';
                if (objParams.type === 'nickname') {
                    titleValue.innerText = '修改群聊昵称'
                    inputWrap.style.paddingLeft = '10px';
                    input.style.paddingLeft = '10px';
                    input.style.paddingRight = '10px';
                } else {
                    descriptionInfo.innerText = objParams.descriptionInfo.groupNameEditTip ? objParams.descriptionInfo.groupNameEditTip : '';
                    titleValue.innerText = '群名称';
                }

                const inputVal = objParams.value.length > 15 ? objParams.value.substring(0, 15) : objParams.value;
                checkInputValWithInit(inputVal)
                input.value = inputVal;
                count.innerHTML = input.value ? input.value.length : 0;
                input.focus();
                clearIcon && clearIcon.classList.remove('hidden');
                //群名称埋点, ----群名称修改完成后还需要一个埋点
                trackPV('p_alter_group_name', { is_anonymou_group: objParams.isanonymous === 'anonymous' ? '1' : '0' })
            } else if (objParams.type === 'description') {
                descriptionInfo.innerText = objParams.descriptionInfo.groupDescriptionEditTip ? objParams.descriptionInfo.groupDescriptionEditTip : '';
                titleValue.innerText = '群简介'
                textarea.value = objParams.value;
                textarea.focus();
                //群简介埋点
                trackPV('p_alter_group_state', { is_anonymou_group: objParams.isanonymous === 'anonymous' ? '1' : '0' });
                count.innerHTML = objParams.value ? objParams.value.length : 0;
            }
        }

        function trackPV(pid, extrObj) {
            pageID = pid;
            setWebviewPageID({pageID: pageID, extras: extrObj})
        }


        function getElement() {
            clearIcon = document.querySelector('.clear');
            input = document.getElementsByTagName('input')[0];
            count = document.querySelector('.count');
            textarea = inputWrap.getElementsByTagName('textarea')[0];
        }

        function renderInputMode(objParams) {
            let element;
            switch (objParams.type) {
                // 昵称
                case 'nickname':
                    const isanonymous = objParams.isanonymous === 'anonymous';
                    element = `
                        <div class="image_wrap">
                            <img onerror="this.src=${jsbridge_baseImgUrl};this.onerror=null" src=${!isanonymous ? objParams.avatar : jsbridge_baseImgUrl} class="left_img" />
                        </div>
                        <input type="text" placeholder="输入本群昵称" />
                        <span onclick="hanldeClear()"><img class="clear hidden" onclick="hanldeClear()" src="../image/clear.png" alt=""></span>

                    `
                    isanonymous && getUserInfo().then(res => {
                        getAnonymityAvatarById({
                            userId: res.userId,
                            gender: res.gender
                        }, function (imgSrc) {
                            document.querySelector('.left_img').src = imgSrc;
                        })
                    })
                    total.innerHTML = '15';
                    break;
                // 群名称
                case 'name':
                    element = `
                        <input type="text" placeholder="群名可包含地点、目的、15字以内..." />
                        <span onclick="hanldeClear()"><img class="clear hidden" onclick="hanldeClear()" src="../image/clear.png" alt=""></span>
                    `
                    total.innerHTML = '15';
                    break;
                // 群简介
                case 'description':
                    element = `
                        <textarea type="text" placeholder=${DEFAULT_TEXTAREA} ></textarea>
                    `
                    total.innerHTML = '300';
                    break;
            }
            inputWrap.innerHTML = element;
        }

        function handleInput(e) {
            let value = e.target.value;
            console.log('paste', e.inputType, e, value);
            let MAXLENGTH = e.target.nodeName === 'INPUT' ? 15 : 300;

            // 处理删除
            if (e.inputType === 'deleteContentBackward') previousValue = value;
            // 处理粘贴
            if (e.inputType === 'insertFromPaste') e.pasteData = pasteVal;

            if (value.length > 0 && (e.data || e.pasteData)) {
                const checkLenRes = handleIosTextLength(e, previousValue, MAXLENGTH);
                //ios maxlength失效，js手动控制最大长度
                if (value.length < MAXLENGTH) {
                    previousValue = value;
                } else if (e.data && !checkLenRes) {
                    previousValue = value;
                } else {
                    previousValue = checkLenRes;
                }

                if (isiOS) {
                    // 如果用户输入的是纯空格，不能继续输入空格,
                    // 全是空格有两种情况，用户直接输入空格；用户输入文字，前面插入空格
                    if ((/^\s+$/).test(value)) {
                        if (value.length <= 1) {// 每次手动输入空格自动禁止，这种（用户输入文字，前面插入空格）需要可以正常删除
                            e.target.value = value.replace(/\s+/g, '')
                        }
                    }
                }
            } else {
                // ios 多行placeholder 只显示一行的bug
                if (isiOS && data.type === 'description') {
                    textarea.placeholder = DEFAULT_TEXTAREA;
                }
            }

            if (!!e.target.value) {
                clearIcon && clearIcon.classList.remove('hidden');
            } else {
                clearIcon && clearIcon.classList.add('hidden');
            }

            checkInputValWithInit(e.target.value.trim());
            count.innerHTML = e.target.value.length;
        }

        //右上角按钮的样式
        function checkInputValWithInit(value) {
            if(value === initialValue || !value.length) {
                // 数据没有发生变化，或者数据是空
                finishButton.src = '../image/finish_unchecked.png';
            } else {
                previousValue = value;
                //数据发生了变化
                finishButton.src = '../image/finish_checked.png';
            }

        }

        function hanldeClear() {
            input.value = '';
            previousValue = '';
            count.innerHTML = 0;
            input.focus();
            clearIcon && clearIcon.classList.add('hidden');
            finishButton.src = '../image/finish_unchecked.png'
        }

        document.addEventListener("visibilitychange", function () {
            if (document.visibilityState === 'visible') {
                isAndroid && setWebviewPageID({pageID: pageID});
            }
        })
    </script>
</body>

</html>
