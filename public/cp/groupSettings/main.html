<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width,minimum-scale=1.0,maximum-scale=1.0,user-scalable=no">

    <title>群聊设置</title>
    <link href="../utils/common.css?path=cp/utils/common.css" rel="stylesheet" type="text/css" />
    <style>
        body {
            margin: 0;
            padding: 0;
        }

        ul,
        p {
            margin: 0;
            padding: 0;
        }

        .container_wrap {
            width: 100%;
            display: flex;
            flex-direction: column;
            align-items: center;
            background-color: rgb(247, 247, 247);
        }

        .header {
            width: 100%;
            display: flex;
            align-items: center;
            flex-direction: column;
            background-color: rgb(255, 255, 255);
        }

        .header_top {
            width: calc(100% - 32px);
            display: flex;
            align-items: center;
            padding: 16px;
            justify-content: space-between;
        }

        .header_top>div {
            width: calc(100% - 16px);
            display: flex;
            align-items: center;
        }

        .header_top>div>img:first-child {
            width: 68px;
            height: 68px;
            margin-right: 16px;
            border-radius: 50%;
            background-image: url('../image/profile_avator.png');
            background-repeat: no-repeat;
            background-size: cover;
            flex-basis: 68px;
            flex-shrink: 0;
        }

        .header_content {
            display: flex;
            flex-direction: column;
            margin-right: 10px;
            width: calc(100% - 84px);
            overflow: hidden;
            height: 68px;
            /* padding-top: 6px; */
            box-sizing: border-box;
            justify-content: center;
        }

        .header_content_title {
            white-space: nowrap;
            display: flex;
            align-items: center;
        }

        .header_content_title :first-child {
            display: inline-block;
            font-size: 16px;
            /* height: 20px; */
            /* line-height: 20px; */
            color: rgb(34, 34, 34);
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }

        .header_content_desc {
            font-size: 13px;
            line-height: 16px;
            color: rgb(144, 144, 144);
            margin-top: 4px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: pre-line;
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
            word-break: break-all;
        }

        .arrow {
            width: 6px;
            height: 10px;
            margin-left: 10px;
        }

        .group_members {
            padding: 0 5px;
            display: flex;
            flex-direction: column;
            width: calc(100% - 10px);
        }

        .group_members_top {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 11px;
        }


        .form {
            width: 100%;
            background-color: #ffffff;
            margin-top: 12px;
        }

        .form_item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            height: 56px;
            padding-right: 16px;
            margin-left: 16px;
        }

        .form_item>p:first-child {
            font-size: 16px;
            line-height: 19px;
            color: rgb(33, 33, 33);
            white-space: nowrap;
            flex-shrink: 0;
        }

        .form_item:not(:last-child) {
            border-bottom: 0.5px solid rgb(233, 233, 233);
        }

        .layout {
            width: calc(100% - 32px);
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .group_members>p {
            font-size: 13px;
            line-height: 16px;
            color: rgb(33, 33, 33);
        }

        .form_item_value {
            font-size: 14px;
            /* height: 19px; */
            letter-spacing: 0;
            color: rgb(208, 208, 208);
            max-width: 200px;
            white-space: pre;
            overflow: hidden;
            text-overflow: ellipsis;
        }

        .report {
            margin-top: 12px;
            background-color: rgb(255, 255, 255);
            height: 56px;
            width: 100%;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .report p {
            margin-left: 16px;
        }

        .report>.arrow {
            margin-right: 16px;
        }

        .switch {
            margin-right: 16px;
        }

        .members {
            height: 92px;
            width: 100%;
            padding: 8px 0 0px;
            display: flex;
            overflow: hidden;
        }

        .member_li {
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            padding: 10px 9px;
            box-sizing: border-box;
            width: 20%;
        }

        .member_li>img {
            width: 52px;
            height: 52px;
            border-radius: 50%;
            background-image: url('../image/profile_avator.png');
            background-repeat: no-repeat;
            background-size: contain;
        }

        .member_li>p {
            font-size: 11px;
            margin-top: 6px;
            line-height: 15px;
            color: #505050;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            max-width: 100%;
        }

        .share_weixin {
            width: calc(100% - 40px);
            padding: 10px 20px 16px;
            height: 40px;
        }

        .share_button {
            width: 100%;
            height: 100%;
            border-radius: 22px;
            font-size: 14px;
            letter-spacing: 0;
            color: rgb(34, 34, 34);
            border: none;
            outline: none;
            border: 1px solid rgba(34, 34, 34, 0.08);
            background-color: rgb(255, 255, 255);
            display: flex;
            justify-content: center;
            align-items: center;
        }

        .chooseBtn {
            display: none;
        }

        .choose-label {
            width: 51px;
            height: 31px;
            display: inline-block;
            border-radius: 20px;
            position: relative;
            background-color: rgba(120, 120, 120, 0.16);
            overflow: hidden;
        }

        .choose-label:before {
            content: '';
            position: absolute;
            left: 2px;
            top: 2px;
            width: 27px;
            height: 27px;
            display: inline-block;
            border-radius: 20px;
            background-color: #fff;
            z-index: 20;
            -webkit-transition: all 0.5s;
            transition: all 0.1s linear;
        }

        .chooseBtn:checked+label.choose-label:before {
            left: 22px;
        }

        .chooseBtn:checked+label.choose-label {
            background-color: rgb(215, 77, 55);
        }

        .bottom_button {
            width: 100%;
            margin: 47px 0 56px;
            background-color: rgb(255, 255, 255);
            color: rgb(250, 0, 0);
            height: 56px;
            line-height: 56px;
            text-align: center;
        }

        .item_img {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            background-image: url('../image/profile_avator.png');
            background-repeat: no-repeat;
            background-size: contain;
        }

        .item_info {
            /* max-width: ; */
            display: flex;
            align-items: center;
            margin-left: 15px;

        }

        .introduction {
            max-width: 220px;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            word-break: break-all;
        }

        .share_icon {
            width: 20px;
            height: 20px;
            margin-right: 4px;
        }

        .quit_wrap {
            position: fixed;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.4);
            z-index: 999;
        }

        .quit_wrap>div {
            position: fixed;
            width: calc(100% - 16px);
            bottom: 8px;
            margin: 0 8px;
        }

        .quit_wrap>div>button {
            width: 100%;
            height: 56px;
            outline: none;
            border: none;
            font-size: 16px;
            border-radius: 10px;
            color: #0276e8;
        }

        .quit_wrap>div>button:first-child {
            background-color: rgb(255, 255, 255);
            color: rgb(33, 33, 33);
            margin-bottom: 8px;
        }

        .quit_wrap>div>button:last-child {
            color: rgb(255, 255, 255);
            background: linear-gradient(to right, rgb(196, 45, 21), rgb(212, 104, 19));
        }

        .hidden {
            display: none;
        }

        .copyid {
            -webkit-user-select: text;
            -moz-user-select: text;
            -ms-user-select: text;
            user-select: text;
        }
    </style>
</head>

<body>
    <div class="container_wrap">
        <div class="header">
            <div class="header_top" onclick="jumpToGroupDetail()">
                <div>
                    <img class="groupimg" alt="">
                    <div class="header_content">
                    </div>
                </div>
                <img class="arrow" src="../image/arrow.png" alt="">

            </div>
            <div class="group_members" onclick="handleViewMember()">
                <div class="group_members_top">
                    <p>群成员 <span class="group_members_count"></span></p>
                    <img class="arrow" src="../image/arrow.png" alt="">
                </div>
                <ul class="members">

                </ul>
            </div>
            <div class="share_weixin">
                <button class="share_button" onclick="handleShareWechat()">
                    <img src="../image/share.png" class="share_icon" alt="">向微信好友分享群聊</button>
            </div>
        </div>
        <ul class="form">
            <li class="form_item owner_item" data-edit=false data-value='name' onclick="handleEdit(event)">
                <p>群名称</p>
                <p class="item_info">
                    <span class="form_item_value"></span>
                    <img class="arrow hidden" src="../image/arrow.png" alt="">
                </p>
            </li>
            <li class="form_item owner_item" data-edit=false data-value='avatars' onclick="handleEdit(event)">
                <p>群头像</p>
                <p class="item_info">
                    <img class="form_item_value item_img"></img>
                    <img class="arrow hidden" src="../image/arrow.png" alt="">
                </p>
            </li>
            <li class="form_item owner_item" data-edit=false data-value='description' onclick="handleEdit(event)">
                <p>群简介</p>
                <p class="item_info">
                    <span class="form_item_value introduction"></span>
                    <img class="arrow hidden" src="../image/arrow.png" alt="">
                </p>
            </li>
            <li class="form_item owner_item" data-edit=false data-value='publicId'>
                <p>群ID</p>
                <p><span class="form_item_value copyid" style="margin-right: 0;" ontouchstart="handleTouchStart(event)"
                        ontouchmove="handleTouchmove()" ontouchend="handleTouchend()"></span>
                </p>
            </li>
            <li class="form_item nick_name" data-edit=true data-value='nickname' onclick="handleEdit(event)">
                <p>本群的昵称</p>
                <p class="item_info">
                    <span class="form_item_value"></span>
                    <img class="arrow" src="../image/arrow.png" alt="">
                </p>
            </li>
        </ul>
        <div class="report report_item" onclick="handleReport()">
            <p>举报</p>
            <img class="arrow" src="../image/arrow.png" alt="">
        </div>

        <div class="report">
            <p>消息免打扰</p>
            <div class="switch" onclick="handleMuteStatus(event)">
                <input type="checkbox" name="sex" id="male" class="chooseBtn" />
                <label for="male" class="choose-label">
                </label>
            </div>
        </div>
        <div class="bottom_button" onclick="handleDeleteQuit(event)"></div>
        <div class="quit_wrap hidden" onclick="handlepopup(event)">
            <div>
                <button class="confirm_button" data-value="confirm">确定</button>
                <button class="report_cancel" data-value="cancel">取消</button>
            </div>
        </div>
    </div>
    <script src="../utils/jsbridge.js?path=cp/utils/jsbridge.js"></script>
    <script src="../utils/util.js?path=cp/utils/util.js"></script>
    <script src="../utils/zepto.js?path=cp/utils/zepto.js"></script>
    <script src="../utils/ajax.js?path=cp/utils/ajax.js"></script>
    <script src="../utils/common.js?path=cp/utils/common.js"></script>

    <script type="text/javascript">
        "use strict";
        let settingInfoData = {};
        let chatGroupMembers = [];
        window.doWhenGetToken = new Function()

        let headerContent = document.querySelector('.header_content');
        let groupImg = document.querySelector('.groupimg');
        let li_item = document.getElementsByClassName('owner_item');
        let ul = document.querySelector('.form');
        let members = document.querySelector('.members');
        let memberCount = document.querySelector('.group_members_count');
        let buttonQuit = document.querySelector('.bottom_button');
        let switchWrap = document.querySelector('.switch');
        let quitPopup = document.querySelector('.quit_wrap');
        let groupid = getUrlParams(window.location.search.slice(1)).groupId;
        let nickName = document.querySelector('.nick_name');
        let imgItem = document.querySelector('.item_img');
        let report = document.querySelector('.report_item');
        let copyId = document.querySelector('.copyid');
        let conversionid;
        let currentUserid;
        let checkboxStatus;
        // 部分手机双击会跳转两次页面
        let currentPage_member = false;
        let firstRender = true;
        let newAvatar;
        let descriptionInfo = {};
        let shareUrl;
        let nicknamevalue;
        let timeOutEvent = 0;
        let originMembers;
        let pageIDMap = {
            pageId: 'p_group_chat_settings',
            isOwnerPopup: 'p_dissolution_group',
            leavePopup: 'p_leave_group_popup'
        }

        function trackCurPD(pageId) {
            isAndroid && trackNew({ pageId: pageId, type: 'PD' });
        }

        function handleTouchStart(event) {
            if (isAndroid) {
                timeOutEvent = setTimeout(() => {
                    longPress(event)
                }, 500);
            }
        }
        function longPress(e) {
            clearTimeout(timeOutEvent);
            if (isAndroid && document.execCommand('copy')) {
                const input = document.createElement('input');
                document.body.appendChild(input);
                input.setAttribute('value', copyId.innerText);
                input.select();
                if (document.execCommand('copy')) {
                    document.execCommand('copy');
                    showToast({
                        context: '复制成功'
                    })
                }
                document.body.removeChild(input);
            } else {
                console.log('ios');
            }
        }

        function handleTouchmove() {
            clearTimeout(timeOutEvent); //清除定时器
            return false;
        }

        function handleTouchend() {
            clearTimeout(timeOutEvent); //清除定时器
            return false;
        }


        //  判断当前用户是否是群主
        getUserInfo().then(function (res) {
            currentUserid = res.userId;
        });

        function setGroupSettingInfo(result) {
            //这里接收值
            let data = JSON.parse(result).data;
            settingInfoData = data.chatGroup;
            renderForm(settingInfoData);

            // //判断是否是群主
            if (firstRender) {
                console.log({
                    groupchat_id: groupid, //群聊的id
                    is_anonymou_group: settingInfoData.type === 'anonymous' ? '1' : '0'//是否是匿名群，1是，0不是
                })
                // PV埋点
                trackSettingPV();
                switchWrap.firstElementChild.checked = data.muted;
                if (settingInfoData.ownerUserId === currentUserid) {
                    report.classList.add('hidden');
                    buttonQuit.innerHTML = '解散群组';
                } else {
                    buttonQuit.innerHTML = '删除并退出';
                }
                //端返回群设置信息和群成员的bridge顺序不能保证，所以第一次需要判断所有数据是否都已经获取到
                if (originMembers && originMembers.length) {
                    // renderMember(chatGroupMembers);
                    chatGroupMembers = handleGroupOwnerFirstPlace(originMembers, settingInfoData.ownerUserId);
                    renderMember(chatGroupMembers);
                }
            }

            firstRender = false;
            getWeChatShareInviteUrl({
                groupPublicId: settingInfoData.publicId,
                setWeChatShareInviteUrl: setWeChatShareInviteUrl
            })
        }

        const getWeChatShareInviteUrl = function (params) {
            const fnName = 'getWeChatShareInviteUrl';
            let type = params.type;
            const res = triggerAction({
                actionType: fnName,
                restParams: {
                    groupPublicId: params.groupPublicId,
                    setWeChatShareInviteUrl: params.setWeChatShareInviteUrl
                }
            })
        }

        function setWeChatShareInviteUrl(result) {
            shareUrl = `${result}?pageId=Group_Detail&hideNavigationBar=1&hideNotch=1`;
        }

        const getGroupSettingInfo = function (params) {
            const fnName = 'getGroupSettingInfo';
            const setGroupSettingInfo = params.setGroupSettingInfo;
            const res = triggerAction({
                actionType: fnName,
                restParams: {
                    groupid: groupid,
                    setGroupSettingInfo: setGroupSettingInfo
                }
            })
        }

        function setGroupMember(result) {
            originMembers = JSON.parse(result).chatGroupMembers; //原始的值，因为暂时没有获取到群主的信息，所以暂存起来
            if (settingInfoData && settingInfoData.ownerUserId) {
                let data = handleGroupOwnerFirstPlace(JSON.parse(result).chatGroupMembers, settingInfoData.ownerUserId);
                if (JSON.stringify(data) === JSON.stringify(chatGroupMembers)) {
                    return false;
                }
                chatGroupMembers = data;
                renderMember(chatGroupMembers);
            }
        }

        const getGroupConversionId = function (params) {
            const fnName = 'getGroupConversionId';
            const setGroupConversionId = params.setGroupConversionId;
            const res = triggerAction({
                actionType: fnName,
                restParams: {
                    groupid: groupid,
                    setGroupConversionId: setGroupConversionId
                }
            })
        }

        function setGroupConversionId(id) {
            conversionid = id;
        }

        const getGroupMember = function (params) {
            const fnName = 'getGroupMember';
            const setGroupMember = params.setGroupMember;
            const res = triggerAction({
                actionType: fnName,
                restParams: {
                    groupid: groupid,
                    setGroupMember: setGroupMember
                }
            })
        }

        const getGroupDescriptionInfo = function (params) {
            const fnName = 'getGroupDescriptionInfo';
            const setGroupDescriptionInfo = params.setGroupDescriptionInfo;
            const res = triggerAction({
                actionType: fnName,
                restParams: {
                    setGroupDescriptionInfo: setGroupDescriptionInfo
                }
            })
        }

        function setGroupDescriptionInfo(result) {
            if (JSON.parse(result)) {
                descriptionInfo.groupNameEditTip = JSON.parse(result).groupNameEditTip;
                descriptionInfo.groupDescriptionEditTip = JSON.parse(result).groupDescriptionEditTip;

            }
        }

        getGroupSettingInfo({
            setGroupSettingInfo: setGroupSettingInfo
        })
        getGroupConversionId({
            setGroupConversionId
        })
        getGroupMember({
            setGroupMember: setGroupMember
        })

        getGroupDescriptionInfo({
            setGroupDescriptionInfo: setGroupDescriptionInfo
        })

        // 跳转到群详情页面【无网络判断】
        function jumpToGroupDetail() {
            getInternetStatus(function () {
                trackPD(pageIDMap.pageId);
                return openOfflineWebview(`cp/groupSettings/groupDetail.html?groupId=${groupid}&statusValue=2&currentUserId=${currentUserid}&pageId=Group_Detail&hideNavigationBar=1&hideNotch=1`);
            });
        }

        // 跳转至群成员列表页面【无网络判断】
        function handleViewMember() {
            !currentPage_member && getInternetStatus(function () {
                trackMC(pageIDMap.pageId, 'e_group_members', { groupchat_id: groupid });
                currentPage_member = true;
                //这里跳转到查看群成员页面
                let obj = {
                    members: chatGroupMembers,
                    groupid: groupid,
                    groupOwner: settingInfoData.ownerUserId === currentUserid,
                    type: settingInfoData.type,
                    currentUserid: currentUserid,
                    conversationId: conversionid
                }
                window.localStorage.setItem('membersInfo', JSON.stringify(obj));
                trackPD(pageIDMap.pageId);
                openOfflineWebview(`cp/groupMember/member.html?type=${settingInfoData.type}&groupOwner=${obj.groupOwner}&groupid=${groupid}&currentUserid=${currentUserid}&conversationId=${conversionid}&pageId=Group_member&hideNavigationBar=1&hideNotch=1`);
                // openOfflineWebview(`cp/groupMember/member.html?data=${query}&pageId=Group_member&hideNavigationBar=1&hideNotch=1`);
            })
        }

        function handleReport() {
            //这里跳转到举报页面
            getNetworkEnv((env) => {
                let baseUrl = (env === 'online') ? 'https://m.tantanapp.com/' : 'http://m.staging2.p1staff.com/';
                openWebview(`${baseUrl}middle-platform/groupchat_report/${groupid}`);

            })
        }

        // 解散 / 退出 群聊【无网络判断】
        function handleDeleteQuit(e) {
            getInternetStatus(function () {
                quitPopup.classList.remove('hidden');
                trackPD(pageIDMap.pageId);
                // trackCurPD(pageIDMap.pageId)
                if (buttonQuit.innerText === "解散群组") {
                    //当前是群主
                    trackMC(pageIDMap.pageId, 'e_dissolution_group', { groupchat_id: groupid })//群聊的id
                } else {
                    //当前不是群主
                    trackNew({
                        pageId: pageIDMap.pageId,
                        eid: 'e_group_chat_delete_and_leave',
                        type: 'MC'
                    })
                }
                if (buttonQuit.innerText === "解散群组") {
                    //当前是群主
                    const extras = {
                        groupchat_id: groupid, //群聊的id
                        is_anonymou_group: settingInfoData.type === 'anonymous' ? '1' : '0'//是否是匿名群，1是，0不是
                    };
                    isAndroid
                        ? trackNew({
                            pageId: pageIDMap.isOwnerPopup,
                            type: 'PV',
                            extras: extras
                        })
                        : setWebviewPageID({ pageID: pageIDMap.isOwnerPopup, extras: extras })
                } else {
                    //当前不是群主
                    const extras = {
                        is_anonymou_group: settingInfoData.type === 'anonymous' ? '1' : '0'
                    };
                    isAndroid
                        ? trackNew({
                            pageId: pageIDMap.leavePopup,
                            type: 'PV',
                            extras: extras
                        })
                        : setWebviewPageID({ pageID: pageIDMap.leavePopup, extras: extras })
                }
            })
        }

        const quitCurrentGroup = function (params) {
            const fnName = 'quitCurrentGroup';
            const setQuitCurrentGroup = params.setQuitCurrentGroup;
            const res = triggerAction({
                actionType: fnName,
                restParams: {
                    groupid: groupid,
                    setQuitCurrentGroup: setQuitCurrentGroup
                }
            })
        }

        function setQuitCurrentGroup(result) {
            //如果退群成功，退出当前页面
            //埋点
            if (result === '0') {
                closeWebview();
            }
        }

        function handlepopup(e) {
            const isConfirm = e.target.dataset.value === 'confirm';
            if (buttonQuit.innerText === "解散群组") {
                //当前用户是群主
                trackMC(pageIDMap.isOwnerPopup, 'e_group_dissolution', { is_confirm: isConfirm ? '1' : '2' });
                trackCurPD(pageIDMap.isOwnerPopup)
                if (!isConfirm) {
                    quitPopup.classList.add('hidden');
                    trackSettingPV();
                    return false;
                }
            } else {
                // 当前不是群主
                if (!isConfirm) {
                    //用户点了取消
                    trackMC(pageIDMap.leavePopup, 'e_cancel_leave_group', { groupchat_id: groupid });
                    quitPopup.classList.add('hidden');
                    trackCurPD(pageIDMap.leavePopup)
                    trackSettingPV();
                    return false;
                }
                trackMC(pageIDMap.leavePopup, 'e_confirm_leave_group', { groupchat_id: groupid });
                trackCurPD(pageIDMap.leavePopup)
            }
            trackSettingPV();
            quitPopup.classList.add('hidden');
            quitCurrentGroup({
                setQuitCurrentGroup: setQuitCurrentGroup
            })
        }

        function setInternetStatus(result) {
            if (result === '1') { //result为1是没有网络，0是有网络
                showToast({
                    context: '没有网络信号，请再试一次',
                    duration: 2000
                })
            }
        }

        // 消息免打扰【无网络判断】
        function handleMuteStatus(e) {
            let ele = e.target;
            if (ele.tagName === 'INPUT') return;
            while (ele !== switchWrap) {
                ele = ele.parentElement;
            }
            checkboxStatus = !ele.firstElementChild.checked;
            getInternetStatus(function () {
                trackMC(pageIDMap.pageId, 'e_group_chat_mute_notification_button', { groupchat_id: groupid, is_open: checkboxStatus ? '1' : '0' }); //0代表否，1代表是
                //消息免打扰
                switchDisturb({
                    setSwitchDisturb: setSwitchDisturb,
                    status: checkboxStatus
                })
            }, function () {
                ele.firstElementChild.checked = !ele.firstElementChild.checked;
            })
        }

        function setSwitchDisturb(result) {
            if (result === '0') {
                //切换成功了
                switchWrap.firstElementChild.checked = checkboxStatus;
            } else if (result === '1') {
                //切换失败了
                switchWrap.firstElementChild.checked = !checkboxStatus;
            }
        }

        const switchDisturb = function (params) {
            const fnName = 'switchDisturb';
            const res = triggerAction({
                actionType: fnName,
                restParams: {
                    conversionid: conversionid,
                    muted: params.status,
                    setSwitchDisturb: params.setSwitchDisturb
                }
            })
        }

        // 跳转至编辑页面
        function handleEdit(e) {
            getInternetStatus(function () {
                trackPD(pageIDMap.pageId);
                let ele = e.target;
                while (ele.tagName !== 'LI') {
                    if (ele === ul) {
                        ele = null;
                        break;
                    }
                    ele = ele.parentNode;
                }
                if (ele.dataset.edit === 'false') return;
                const itemdesc = ele.dataset.value;
                let groupOwnerAvatar = chatGroupMembers.filter(item => item.userId === currentUserid);
                let temp = {
                    groupid: groupid,
                    type: itemdesc,
                    isanonymous: settingInfoData.type,
                    value: itemdesc === 'nickname' ? nicknamevalue : settingInfoData[itemdesc],
                    avatar: itemdesc === 'nickname' ? groupOwnerAvatar && groupOwnerAvatar[0] && groupOwnerAvatar[0].avatar : '',
                    conversationid: conversionid,
                    descriptionInfo: descriptionInfo
                };
                switch (itemdesc) {
                    //这里的type是判断当前群是否是匿名群
                    case 'name':
                        trackMC(pageIDMap.pageId, 'e_alter_group_name', { groupchat_id: groupid });
                        openOfflineWebview(`cp/groupSettings/editInfo.html?params=${encodeURIComponent(JSON.stringify(temp))}&pageId=Group_editInfo&hideNavigationBar=1&hideNotch=1`)
                        break;
                    case 'description':
                        trackMC(pageIDMap.pageId, 'e_alter_group_state', { groupchat_id: groupid });
                        openOfflineWebview(`cp/groupSettings/editInfo.html?params=${encodeURIComponent(JSON.stringify(temp))}&pageId=Group_editInfo&hideNavigationBar=1&hideNotch=1`)
                        break;
                    case 'nickname':
                        trackMC(pageIDMap.pageId, 'e_my_alias_in_group');
                        openOfflineWebview(`cp/groupSettings/editInfo.html?params=${encodeURIComponent(JSON.stringify(temp))}&pageId=Group_editInfo&hideNavigationBar=1&hideNotch=1`)
                        break
                    case 'avatars':
                        //修改群头像
                        handleUploadImage();

                        break;
                }
            })
        }




        function handleUploadImage() {
            trackMC(pageIDMap.pageId, 'e_alter_group_photo', { groupchat_id: groupid })
            trackPD(pageIDMap.pageId)
            triggerAction({
                actionType: 'imagePickerPhoto',
                restParams: {
                    success: function (localUrl, imgArr, error) {
                        trackSettingPV()
                        try {
                            if (isAndroid) {
                                newAvatar = {
                                    localUrl: localUrl,
                                    imgArr: JSON.parse(imgArr)
                                }
                            } else {
                                const args = JSON.parse(localUrl);
                                newAvatar = {
                                    localUrl: `data:image/png;base64,${args.base64Image}`,
                                    imgArr: [args.pictureInfo]
                                }
                            }
                            modifyGroupInfo({
                                conversationid: conversionid,
                                type: 'avatar',
                                groupid: groupid,
                                avatar: newAvatar.imgArr || [],
                                setModifyGroupInfo: setModifyGroupInfo
                            });
                            if (error) {
                                //这里需要弹一个toast，图片上传失败
                                showToast({
                                    context: '图片上传失败',
                                    duration: 2000
                                })
                            }
                        } catch (e) {
                            if (error) {
                                //这里需要弹一个toast，图片上传失败
                                showToast({
                                    context: '图片上传失败',
                                    duration: 2000
                                })
                            }
                        }
                    },
                }
            })
        }

        function setModifyGroupInfo(data) {
            if (JSON.parse(data).status === '1') { // 0是修改群头像成功了
                //修改群头像失败了
                !isAndroid && showToast({
                    context: JSON.parse(data).message,
                    duration: 2000
                })
            } else {
                const url = newAvatar.imgArr && newAvatar.imgArr[0] && newAvatar.imgArr[0].url;
                imgItem.src = newAvatar.localUrl;
                groupImg.src = newAvatar.localUrl;
            }
        }

        const modifyGroupInfo = function (params) {
            const fnName = 'modifyGroupInfo';
            let type = params.type;
            const res = triggerAction({
                actionType: fnName,
                restParams: {
                    groupid: params.groupid,
                    avatar: isAndroid ? `{${params.type}: ${JSON.stringify(params[params.type])}}` : params[params.type],
                    conversationid: params.conversationid,
                    setModifyGroupInfo: params.setModifyGroupInfo
                }
            })
        }

        function trackMC(pid, eid, extra) {
            trackNew({
                pageId: pid,
                eid: eid,
                type: 'MC',
                extras: extra
            })
        }

        function trackSettingPV() {
            console.log('trackSettingPV');
            const extras = {
                groupchat_id: groupid, //群聊的id
                is_anonymou_group: settingInfoData.type === 'anonymous' ? '1' : '0'//是否是匿名群，1是，0不是
            }
            const flag = firstRender || isiOS
            flag
                ? setWebviewPageID({
                    pageID: pageIDMap.pageId,
                    extras: extras
                })
                : trackNew({
                    pageId: pageIDMap.pageId,
                    type: 'PV',
                    extras: extras
                })
        }

        function renderMember(data) {
            let li = '';
            data.forEach(function (element, index) {
                switch (settingInfoData.type) {
                    case 'anonymous':
                        getAnonymityAvatarById(element, (function (item) {
                            return function (url) {
                                if (item.userId === currentUserid) {
                                    nicknamevalue = item.nickName || item.userName;
                                    nickName.querySelector('.form_item_value').innerHTML = nicknamevalue;
                                }
                                const isLast = index === 5 || index === data.length - 1;
                                if (index < 5) {
                                    li += `<li class="member_li">
                                        <img src=${url} >
                                        <p>${item.nickName || item.userName}</p>
                                    </li>`
                                }
                                if (isLast) {
                                    members.innerHTML = li;
                                }
                            }
                        })(element))
                        break;
                    default:
                        if (element.userId === currentUserid) {
                            nicknamevalue = element.nickName || element.userName;
                            nickName.querySelector('.form_item_value').innerText = nicknamevalue;
                        }
                        if (index < 5) {
                            li += `<li class="member_li">
                                <img src=${element.avatar} >
                                <p>${element.nickName || element.userName}</p>
                            </li>`
                        }
                        break;
                }
            })
            members.innerHTML = li;
        }

        function renderForm(data) {
            groupImg.src = data && data.avatars && data.avatars[0] && data.avatars[0].url || jsbridge_baseImgUrl;
            let content = `
                <div class="header_content_title">
                    <span>${data.name}</span>
                    ${data.category && data.category.name && data.category.name !== '其他' ?
                    `<span style="color: ${data.category.textColor}; margin-left: 6px; border-radius: 4px; padding: 2px 5px; font-size: 11px; background-color: ${data.category.backgroundColor}">${data.category.name}</span>`
                    : ''}
                </div>
                <p class="header_content_desc">${data.description}</p>
            `
            headerContent.innerHTML = content;
            memberCount.innerText = `${settingInfoData.memberCount}/${settingInfoData.memberLimit}`
            Array.prototype.slice.call(li_item).forEach(function (item) {
                let arrow = item.querySelector('.arrow');
                let element = item.querySelector('.form_item_value');
                if (settingInfoData.ownerUserId === currentUserid) {
                    item.dataset.edit = true;//如果是群主则可以更改群简介等信息，显示右边的箭头
                    arrow && arrow.classList.remove('hidden');
                }
                const imgUrl = data && data[item.dataset.value] && data[item.dataset.value][0] && data[item.dataset.value][0].url || jsbridge_baseImgUrl;
                // 头像是赋值图片，其他的直接赋值文案，
                item.dataset.value === 'avatars' ? element.src = imgUrl :
                    element.innerHTML = data[item.dataset.value]

                // description只显示第一行
                if (item.dataset.value === 'description' && data[item.dataset.value].split('\n').length > 1) {
                    element.innerHTML = `${data['description'].split('\n')[0]}...`
                }
            });

        }

        // 分享微信【无网络判断】
        function handleShareWechat() {
            getInternetStatus(function () {
                trackMC(pageIDMap.pageId, 'e_invite_friends_to_group', { groupchat_id: groupid })//群聊的id
                share({
                    channel: 'wx',
                    url: shareUrl,
                    title: '来自探探群聊',
                    imgUrl: settingInfoData.avatars && settingInfoData.avatars[0] && settingInfoData.avatars[0].url ? settingInfoData.avatars[0].url : 'https://auto.tancdn.com/v1/images/eyJpZCI6Ilg1UjVZQUtWUkRTNk1FRlhJTlRHVjVZR0JGTjZTSTA3IiwidyI6MTQ4LCJoIjoxNDgsImQiOjAsIm10IjoiaW1hZ2UvanBlZyIsImRoIjozNzAyMzg4NDg3NDA5NjU2MTE1fQ.png',
                    description: `我加入了探探群聊【${settingInfoData.name}】，快加入一起聊天吧`,
                    successHandler: function () { },
                    errorHandler: function () {
                        //端会自动弹toast
                        // showToast({
                        //     context: '分享失败了',
                        //     duration: 2000
                        // })
                    }
                })
            })
        }


        document.addEventListener("visibilitychange", function () {
            if (document.visibilityState === 'visible') {
                currentPage_member = false;
                isAndroid && trackSettingPV();
                window.localStorage.removeItem('membersInfo');
                if (isiOS) {
                    getGroupMember({
                        setGroupMember: setGroupMember
                    })
                    getGroupSettingInfo({
                        setGroupSettingInfo: setGroupSettingInfo
                    })
                }
            }
        });


        function hanldeTouchMove(event) {
            if (window.getComputedStyle(quitPopup, null).display !== 'none') {
                if (event.cancelable) {
                    // 判断默认行为是否已经被禁用
                    if (!event.defaultPrevented) {
                        event.preventDefault();
                    }
                }
            }
        }

        window.onload = function () {
            setNavigation({
                title: '群聊设置',
                rightText: ''
            })
            document.body.addEventListener('touchstart', function () {
                window.getSelection().removeAllRanges();
            })
            if (isiOS) {
                getTabBarHeight(function (res) {
                    if (parseInt(res) > 60) {
                        quitPopup.firstElementChild.style.bottom = '42px';
                        //底部导航栏比较长,bottom距离底部为44px
                    }
                })
                document.body.addEventListener('touchmove', hanldeTouchMove, { passive: false });

            }
        }

    </script>
</body>

</html>