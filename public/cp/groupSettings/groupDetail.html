<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width,minimum-scale=1.0,maximum-scale=1.0,user-scalable=no">

    <title>群聊详情</title>
    <link href="../utils/common.css?path=cp/utils/common.css" rel="stylesheet" type="text/css" />
    <style>
        body,
        html {
            margin: 0;
            padding: 0;
            height: 100%;
            position: relative;
        }

        body {
            display: none;
            overflow: hidden;
        }

        ul,
        p {
            margin: 0;
            padding: 0;
        }

        .navigation_bar {
            width: 100%;
            background-color: #ffffff;
            display: flex;
            justify-content: space-between;
            height: 56px;
            align-items: center;
            position: fixed;
            top: 0;
            z-index: 9999;
        }

        .navigation_bar>img {
            flex-shrink: 0;
        }

        .navigation_bar>img:first-child {
            width: 12px;
            height: 24px;
            margin-left: 20px;
        }

        .navigation_bar .nav-title {
            flex: 1;
            overflow: hidden;
            text-overflow: ellipsis;
            text-align: center;
            font-size: 18px;
            font-weight: 400;
            color: rgb(34, 34, 34);
            white-space: nowrap;
        }

        .navigation_bar>img:last-child {
            width: 24px;
            height: 24px;
            margin-right: 16px;
        }

        .container_wrap {
            /* background-color: rgb(247, 247, 247); */
            user-select: none;
            -webkit-user-select: none;
            /*webkit浏览器*/
            -moz-user-select: none;
            /*火狐*/
            -ms-user-select: none;
            /*IE10*/
            overflow: hidden;
        }

        .content {
            height: 100vh;
            overflow: auto;
        }

        .header {
            width: calc(100% - 32px);
            padding: 10px 16px 24px 16px;
            display: flex;
            align-items: center;
            background-color: rgb(255, 255, 255);
            margin-top: 50px;
        }

        .header>img {
            height: 76px;
            width: 76px;
            margin-right: 16px;
            border-radius: 50%;
            background-image: url('../image/profile_avator.png');
            background-repeat: no-repeat;
            background-size: contain;
        }

        .header_content {
            display: flex;
            flex-direction: column;
        }

        .header_content_title {
            font-size: 20px;
            font-weight: 400;
            color: rgb(34, 34, 34);
        }

        .header_content_label {
            font-size: 11px;
            color: rgb(239, 125, 55);
            padding: 2px 5px;
            margin-right: 6px;
            border-radius: 4px;
            background-color: rgb(255, 241, 200);
        }

        .header_content_id,
        .header_content_id_box {
            font-size: 14px;
            line-height: 17px;
            color: rgb(144, 144, 144);
        }

        .header_content_id {
            -webkit-user-select: text;
            -moz-user-select: text;
            -ms-user-select: text;
            user-select: text;
        }

        .member {
            width: 100%;
            height: 132px;
            background-color: rgb(255, 255, 255);

        }

        .member_line {
            font-size: 15px;
            color: rgb(34, 34, 34);
            margin-left: 16px;
        }

        .list {
            display: flex;
            padding: 14px 20px 16px 26px;
            justify-content: space-between;
            list-style: none;
        }

        .list_item {
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
        }

        .list_item>p {
            font-size: 12px;
            color: rgb(34, 34, 34);
            margin-top: 8px;
        }

        .label {
            width: calc(100% - 32px);
            padding: 16px 16px 12px;
            /* margin: 12px 0; */
            background-color: rgb(255, 255, 255);
        }

        .label>P {
            font-size: 15px;
            color: rgb(34, 34, 34);
            margin-bottom: 10px;
        }

        .label_ul {
            display: flex;
            list-style: none;
        }

        .li_label {
            font-size: 13px;
            color: rgb(117, 117, 117);
            border: 1px solid rgb(230, 230, 230);
            padding: 5px 10px;
            border-radius: 15px;
            margin-right: 10px;
            font-weight: bold;
            opacity: 0.7;
        }

        .gapline {
            height: 12px;
            width: 100%;
            background-color: rgb(247, 247, 247);
            display: block;
        }

        .owner-box {
            width: 100%;
        }

        .owner {
            padding: 12px 16px 12px;
            /* margin-bottom: 12px; */
            display: flex;
            align-items: center;
            justify-content: space-between;
            background-color: rgb(255, 255, 255);
        }

        .owner>p {
            font-size: 15px;
            color: rgb(80, 80, 80);
            white-space: nowrap;
            margin-right: 40px;
        }

        .group_info {
            display: flex;
            align-items: center;
            font-size: 13px;
            color: rgb(34, 34, 34);
            overflow: hidden;
        }

        .owner_name {
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }

        .group_info>img:first-child {
            width: 28px;
            height: 28px;
            margin-right: 8px;
            border-radius: 50%;
            flex-shrink: 0;
        }

        .intro {
            background-color: rgb(255, 255, 255);
            width: calc(100% - 32px);
            padding: 12px 16px;
            flex: 1;
        }

        .intro :first-child {
            font-size: 15px;
            color: rgb(34, 34, 34);
            margin-bottom: 6px;
        }

        .intro_desc {
            font-size: 15px;
            line-height: 26px;
            color: rgb(144, 144, 144);
            padding-bottom: 64px;
            word-break: break-all;
            white-space: pre-line;
        }

        .bottom {
            width: 100%;
            position: fixed;
            bottom: 0;
            background-color: rgb(255, 255, 255);
            text-align: center;
            padding: 12px 0 10px 0;
        }

        .arrow {
            width: 6px;
            height: 10px;
            margin-left: 8px;
        }

        .realname_icon {
            width: 16px;
            height: 16px;
            margin-left: 5px;
            flex-shrink: 0;
        }

        .bottom>button {
            width: calc(100% - 40px);
            height: 44px;
            border-radius: 24px;
            color: rgb(255, 255, 255);
            font-size: 14px;
            background-color: rgb(255, 92, 49);
            border: none;
            outline: none;
        }

        .bottom>textarea {
            resize: none;
            line-height: 18px;
            font-size: 15px;
            background-color: rgb(247, 247, 247);
            border: none;
            height: 60px;
            padding: 8px 8.5px;
            outline: none;
            border-radius: 6px;
            margin-bottom: 5px;
            width: calc(100% - 23px);
            box-sizing: border-box;
            height: 80px;
        }

        .bottom>textarea::placeholder {
            color: rgb(208, 208, 208);
            line-height: 18px;
            font-size: 15px;
        }

        .report_wrap {
            position: fixed;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.7);
            z-index: 99;
            top: 0;
        }

        .report_wrap>div {
            position: fixed;
            width: calc(100% - 16px);
            bottom: 8px;
            margin: 0 8px;
        }

        .report_wrap>div>button {
            width: 100%;
            height: 56px;
            border-radius: 10px;
            outline: none;
            border: none;
            font-size: 16px;
        }

        .report_button {
            background-color: rgb(255, 255, 255);
            margin-bottom: 8px;
            color: rgb(33, 33, 33);
        }

        .report_cancel {
            color: rgb(255, 255, 255);
            background: linear-gradient(to left, rgb(212, 104, 19), rgb(196, 45, 22));
        }

        .mycircle {
            fill: rgba(0, 0, 0, 0);
            stroke-width: 4px;
            stroke-dasharray: 100 500;
        }

        .hidden {
            display: none;
        }
    </style>
</head>

<body>
    <div class="container_wrap">
        <div class="navigation_bar">
            <img class="close_arrow" src="../image/backarrow_ios.png" alt="" onclick="closeWebview()">
            <div class="nav-title"></div>
            <img class="report_dots hidden" src="../image/more.png" alt="" onclick="showReportPage()">
        </div>
        <div class="content">
            <div class="header">
                <img class="group_avator" src="" onerror="this.src='../image/profile_avator.png';this.onerror=null" />
                <div class="header_content">
                    <p class="header_content_title"></p>
                    <div>
                        <span class="header_content_label"></span>
                        <span class="header_content_id_box">
                            ID:
                            <span class="header_content_id" ontouchstart="handleTouchStart(event)"
                                ontouchmove="handleTouchmove()" ontouchend="handleTouchend()"></span>
                        </span>
                    </div>
                </div>
            </div>
            <div class="member">
                <div class="member_line">成员分布 <span class="membercount"></span></div>
                <ul class="list">

                </ul>
            </div>
            <!-- 间隔线 -->
            <span class="gapline"></span>
            <div class="label">
                <p>群标签</p>
                <ul class="label_ul">

                </ul>
            </div>
            <span class="gapline"></span>
            <div class="owner-box hidden">
                <div class="owner" onclick="handleJumpPersonProfile(event)">
                    <p>群主</p>
                    <div class="group_info">
                        <img src="" alt="">
                        <span class="owner_name"></span>
                        <img class="realname_icon hidden" src="" alt="">
                        <img class="arrow" src="../image/arrow.png" alt="">
                    </div>
                </div>
                <span class="gapline"></span>
            </div>
            <div class="intro" onclick="hanldeIntroClick(event)">
                <p>群简介</p>
                <p class="intro_desc"></p>
            </div>
        </div>

        <div class="bottom">
            <textarea placeholder="请填写进群理由；自我描述越丰富，越容易通过加群申请；" onclick="handleTextarea(event)"
                onfocus="hanldeTextareaFocus()" onblur="handleTextareaBlur()"></textarea>
            <button onclick="handleApplyJoin(event)" class="bottom_apply"></button>
        </div>
        <div class="report_wrap hidden" onclick="handleReport(event)">
            <div>
                <button class="report_button">举报</button>
                <button class="report_cancel">取消</button>
            </div>
        </div>
    </div>
    <script src="../utils/jsbridge.js?path=cp/utils/jsbridge.js"></script>
    <script src="../utils/util.js?path=cp/utils/util.js"></script>
    <script src="../utils/zepto.js?path=cp/utils/zepto.js"></script>
    <script src="../utils/ajax.js?path=cp/utils/ajax.js"></script>
    <script src="../utils/common.js?path=cp/utils/common.js"></script>
    <script src="../utils/zepto.js?path=cp/utils/zepto.js"></script>
    <script type="text/javascript">
        "use strict";
        const pageID = 'p_group_information';
        let query = getUrlParams(window.location.search.slice(1));
        let detailData = {};
        let memberPercent = [];
        let owner = {};
        let currentUserid = query.currentUserId;
        let iLastTouch = null;
        let timeOutEvent = 0;
        let initialHeight = window.innerHeight;

        let ulList = document.querySelector('.list');
        let ulLabel = document.querySelector('.label_ul');
        let groupOwner = document.querySelector('.owner-box');
        let introDesc = document.querySelector('.intro_desc');
        let header = document.querySelector('.header_content');
        let groupIDEle = header.querySelector('.header_content_id');
        let groupAvator = document.querySelector('.group_avator');
        let reportWrap = document.querySelector('.report_wrap')
        let reportButton = document.querySelector('.report_button');
        let cancelButton = document.querySelector('.report_cancel');
        let bottom = document.querySelector('.bottom');
        let containerWrap = document.querySelector('.container_wrap');
        let contentEle = document.querySelector('.content');
        let reportDots = document.querySelector('.report_dots');
        let closeArrow = document.querySelector('.close_arrow');
        let navTitle = document.querySelector('.nav-title');
        let previousValue = '';
        let pasteVal = '';
        let cpLock = true;

        function hanldeTextareaFocus(event) {
            if (isiOS) {
                setTimeout(() => {
                    navTitle.innerHTML = detailData.name;
                    closeArrow.parentElement.style.top = (initialHeight - window.innerHeight) + 'px';
                }, 200)
            }
        }

        function handleTextareaBlur() {
            let title;
            if (contentEle.scrollTop >= 60) {
                title = detailData.name
            } else {
                title = ' '
            }
            navTitle.innerHTML = title;
            if (isiOS) {
                closeArrow.parentElement.style.top = '0px';
            }
        }

        function handleTouchStart(event) {
            if (isAndroid) {
                timeOutEvent = setTimeout(() => {
                    longPress(event)
                }, 500);
            }
        }

        function handleTouchmove() {
            clearTimeout(timeOutEvent); //清除定时器
            return false;
        }

        function handleTouchend() {
            clearTimeout(timeOutEvent); //清除定时器
            return false;
        }

        function longPress(e) {
            clearTimeout(timeOutEvent);
            if (isAndroid && document.execCommand('copy')) {
                const input = document.createElement('input');
                document.body.appendChild(input);
                input.setAttribute('value', detailData.publicId);
                input.select();
                if (document.execCommand('copy')) {
                    document.execCommand('copy');
                    showToast({
                        context: '复制成功'
                    })
                }
                document.body.removeChild(input);
            } else {
                console.log('ios');
            }
        }

        if (isAndroid) {
            closeArrow.src = '../image/backarrow_android.png';
            closeArrow.style.width = '24px';
            closeArrow.parentElement.style.height = `56px`;
            header.parentElement.style.marginTop = '56px';
        } else {
            getTabBarHeight(function (res) {
                if (parseInt(res) > 60) {
                    bottom.style.paddingBottom = '44px';
                    reportWrap.firstElementChild.style.bottom = '42px';
                    //底部导航栏比较长,bottom距离底部为44px
                }
                // 否则按照默认的10px
                // ios点击空白区域input不失焦
                document.body.addEventListener('touchend', handleIOSBlur);
            })
        }
        window.ontouchstart = function (e) {
            if (!bottom.firstElementChild.classList.contains('hidden') && !e.target.classList.contains('bottom_apply')) {
                bottom.firstElementChild.blur();
            }
        };

        // 解决ios 双击页面自动滚动的bug
        function hanldeIntroClick(event) {
            var a = new Date().getTime();
            iLastTouch = iLastTouch || a + 1;
            var c = a - iLastTouch;
            if (c < 500 && c > 0) {
                event.preventDefault();
                return false;
            }
            iLastTouch = a
        }

        getStatusBar({
            setStatusBar: setStatusBar
        })
        //设置电量栏状态(只处理ios,ios的电量栏是透明的，单独处理，安卓正常)
        function setStatusBar(result) {
            let navigationBar = closeArrow.parentElement;
            if (!isAndroid) {
                navigationBar.style.paddingTop = `${result}px`;
                navigationBar.style.height = '44px';
                header.parentElement.style.marginTop = `${parseInt(window.getComputedStyle(navigationBar, null).paddingTop) + parseInt(window.getComputedStyle(navigationBar, null).height)}px`;
            }
        }

        function handleIOSBlur(e) {
            if (e.target.nodeName != 'TEXTAREA') {
                if (!bottom.firstElementChild.classList.contains('hidden')) {
                    bottom.firstElementChild.blur();
                }
            }
        }


        // 渲染群成员百分比列表
        function renderMemberSvgList() {
            let li = '';
            for (let i = 0; i < memberPercent.length; i++) {
                li += `
                <li class="list_item" data-percent=${memberPercent[i].percentage}>
                    <svg width="56" height="56"  viewBox="0 0 50 50"
                    xmlns="http://www.w3.org/2000/svg">
                    <circle cx="26"  cy="26"   r = "22"
                        fill="none"  stroke="rgb(238, 238, 238)" stroke-width="2">
                    </circle>
                    <circle cx="0"  cy="0"   r = "22" class="mycircle" id="progressRound"
                        stroke=${memberPercent[i].backgroundColor}
                        transform="translate(26,26) rotate(-90)" stroke-linecap="round">
                    </circle>
                </svg>
                <p>${memberPercent[i].description}</p>
                </li>
                `
            }
            ulList.innerHTML = li;
            Array.prototype.slice.call(ulList.children).forEach(function (item, index) {
                setPercentValue(item.querySelector('#progressRound'), item.dataset.percent);
            })
        }

        // 渲染标签列表
        function renderLabelList(arr) {
            let li = ''
            arr.forEach(function (item) {
                li += `<li class="li_label" style="color: ${item.textColor};backgroundColor: ${item.backgroundColor}">${item.name}</li>`;
            })
            ulLabel.innerHTML = li;
        }

        // 渲染群主信息
        function renderGroupOwner() {
            if (detailData.avatars && detailData.avatars[0] && detailData.avatars[0].url) {
                groupAvator.src = detailData.avatars[0].url;
            }

            ulList.parentElement.querySelector('.membercount').innerHTML = `${detailData.memberCount}/${detailData.memberLimit}`;

            introDesc.innerHTML = detailData.description;
            header.querySelector('.header_content_title').innerHTML = detailData.name;
            let label = header.querySelector('.header_content_label');
            // 渲染标签
            if (detailData.category.name && detailData.category.name !== '其他') {
                // 其他标签不显示
                label.innerHTML = detailData.category.name;
                label.style.backgroundColor = detailData.category.backgroundColor;
                label.style.color = detailData.category.textColor;
            } else {
                label.style.display = 'none';
            }

            header.querySelector('.header_content_id').innerHTML = `${detailData.publicId}`;
            const ownerInfo = owner[0];
            //如果不是群主的话，需要显示群主的信息
            if (ownerInfo && ownerInfo.id !== currentUserid) {
                groupOwner.classList.remove('hidden');
                reportDots.classList.remove('hidden');
                // 需要判断是否匿名 detailData.type === 'anonymous'
                if (detailData.type === 'anonymous') {
                    getAnonymityAvatarById({ userId: ownerInfo.id, gender: ownerInfo.gender }, function (url) {
                        groupOwner.getElementsByTagName('img')[0].src = url;
                    })
                } else {
                    groupOwner.getElementsByTagName('img')[0].src = ownerInfo.pictures[0].url;
                }
                groupOwner.querySelector('.owner_name').innerHTML = ownerInfo.name;
                handleUserAuthenticationStatus(ownerInfo.id);
            }
        }

        // 判断用户的认证状态
        function handleUserAuthenticationStatus(ownerId) {
            let icon = groupOwner.querySelector('.realname_icon');
            if (owner[0].verifications.picture.verified) {
                icon.classList.remove('hidden');
                // 如果这两个都是true， 是真人实名认证, 如果只有picture.verified为true，就是真实头像认证
                owner[0].verifications.idCard.verified ? icon.src = '../image/verified_avator.png' : icon.src = '../image/real_avatar.png';
            }
        }

        // 举报弹窗
        function handleReport(e) {
            switch (e.target) {
                case reportButton:
                    getNetworkEnv((env) => {
                        let baseUrl = (env === 'online') ? 'https://m.tantanapp.com/' : 'http://m.staging2.p1staff.com/';
                        openWebview(`${baseUrl}middle-platform/groupchat_report/${detailData.id}`);
                    })
                    break;
                case cancelButton:
                    reportWrap.classList.add('hidden');
                    break;
                default:
                    reportWrap.classList.add('hidden');
                    break;
            }
        }

        // 【打点】点击输入进群理由输入框
        function handleTextarea(e) {
            trackNew({
                pageId: pageID,
                eid: 'e_apply_add_group_reason',
                type: 'MC',
                extras: {
                    groupchat_id: detailData.id, //群聊的id
                }
            })
        }

        // 跳转到个人信息页面
        function handleJumpPersonProfile(e) {
            if (!e.target.classList.contains('hidden')) {
                trackPD(pageID);
                openGroupMemberProfile({
                    userId: owner[0].id,
                    anonymity: detailData.type === "anonymous" ? true : false
                })
            }
        }

        // 设置百分比
        function setPercentValue(progressRound, percent) {
            if (percent === '0') {
                progressRound.classList.add('hidden');
                return;
            }
            let jindu = 0;
            let jinduLength = Math.PI * 2;
            let goFun = function () {
                jindu += 1;
                let strokeLength = jinduLength * jindu;
                // 更改环形样式，控制进度变化：实线段长度。
                progressRound.style.strokeDasharray = strokeLength + " 1000";
                // 如果进度为 100 ，那么终止计时器。
                //这里22是半径，因为Math.PI * 2 * r是整个圆
                if (jindu === parseInt(22 * percent)) {
                    clearInterval(myset);
                }
            };
            // 启动计时器
            let myset = setInterval(function () {
                goFun();
            }, 30);
        }

        // 展示举报弹窗
        function showReportPage() {
            if (detailData.ownerUserId !== currentUserid) {
                reportWrap.classList.remove('hidden');
            }
        }

        // 点击申请群聊
        function handleApplyJoin(e) {
            //进入群聊埋点
            if (e.target.innerText === '进入群聊') {
                trackNew({
                    pageId: pageID,
                    eid: 'e_enter_groupchat',
                    type: 'MC',
                    extras: {
                        groupchat_id: detailData.id, //群聊的id
                        is_anonymou_group: detailData.type === 'anonymous' ? '1' : '0' //是否匿名群， 1是匿名，0 是实名
                    }
                })
                openWebview(`tantanapp://group/chat?groupId=${detailData.id}`);
            } else if (e.target.innerText === '申请加群') {
                //申请加群埋点
                groupApply(detailData, '0', detailData.id, bottom.getElementsByTagName('textarea')[0].value, e.target);
                trackNew({
                    pageId: pageID,
                    eid: 'e_apply_add_group_button',
                    type: 'MC',
                    extras: {
                        groupchat_id: detailData.id, //群聊的id
                        is_anonymou_group: detailData.type === 'anonymous' ? '1' : '0' //是否匿名群， 1是匿名，0 是实名
                    }
                })
            }
        }

        function handleTextareaInput(e) {
            let value = e.target.value;
            // 处理删除
            if (e.inputType === 'deleteContentBackward') previousValue = value;
            // 处理粘贴
            if (e.inputType === 'insertFromPaste') e.pasteData = pasteVal;
            if (value.length && (e.data || e.pasteData)) {
                const checkLenRes = handleIosTextLength(e, previousValue, 100);
                //ios maxlength失效，js手动控制最大长度
                if (!checkLenRes) {
                    previousValue = value;
                } else {
                    previousValue = checkLenRes;
                }
            }
            if (!value.length) {
                e.target.placeholder = '请填写进群理由；自我描述越丰富，越容易通过加群申请；'
            }
        }

        // 底部按钮文案展示
        function handleBottomButtonText(status, text) {
            if (status === 'chat') {
                introDesc.parentNode.style.paddingBottom = '64px';
                bottom.querySelector('textarea').classList.add('hidden');
                let button = bottom.querySelector('button');
                if (text === '已申请') {
                    button.style.backgroundColor = 'rgb(255, 255, 255)';
                    button.style.color = 'rgb(208, 208, 208)';
                }
                button.innerHTML = text;
            } else if (status === 'join') {
                introDesc.parentNode.style.paddingBottom = '156px';
                bottom.style.borderRadius = '16px 16px 0 0';
                bottom.style.borderShadows = 'rgb(0, 0, 0, 0.08)';
                bottom.getElementsByTagName('button')[0].innerHTML = text;
            }
        }

        // 页面按钮展示 申请加群 / 已申请 / 进入群聊
        function judgePageOrigin(statusValue) {
            switch (+statusValue) {
                case 0:
                    handleBottomButtonText('join', '申请加群')
                    break;
                case 1:
                    handleBottomButtonText('chat', '已申请');
                    break;
                case 2:
                    handleBottomButtonText('chat', '进入群聊');
                    trackNew({
                        pageId: pageID,
                        eid: 'e_enter_groupchat',
                        type: 'MV',
                        extras: {
                            groupchat_id: detailData.id, //群聊的id
                            is_anonymou_group: detailData.type === 'anonymous' ? '1' : '0' //是否匿名群， 1是匿名，0 是实名
                        }
                    })
                    break;
            }
        }

        window.onload = function () {
            $('body').hide();

            document.body.addEventListener('touchstart', function () {
                // window.getSelection().removeAllRanges();
            })
            contentEle.addEventListener('scroll', function (event) {
                event.stopPropagation();
                const scrollTop = this.scrollTop;
                let title;
                if (scrollTop >= 60) {
                    title = detailData.name
                } else {
                    title = ' '
                }
                navTitle.innerHTML = title;
            }, { passive: false })
            bottom.addEventListener('touchmove', function (event) {
                event.preventDefault();
                event.stopPropagation();
            }, { passive: false })

            getInternetStatus(function () {
                ajax({
                    url: `/v3/chat-groups/${query.groupId}?with=members,users,group-applies`,
                    method: 'GET'
                }).then(res => {
                    if (res && res.meta && res.meta.code === 200) {
                        detailData = res.data.chatGroups[0];
                        owner = res.data.users.filter(function (_) {
                            if (_.id === res.data.chatGroups[0].ownerUserId) {
                                return _;
                            }
                        });
                        //该点未打上
                        setWebviewPageID({
                            pageID: pageID,
                            extras: {
                                groupchat_id: detailData.id //群聊的id
                            }
                        })
                        memberPercent = detailData.memberStatistics;
                        query.statusValue = getGroupStatusValue(res.data.chatGroups[0], res.data);
                        judgePageOrigin(query.statusValue);
                        renderMemberSvgList();
                        renderLabelList(detailData.tags || []);
                        renderGroupOwner();
                        hideNavigationBar();
                        $('body').show();
                    }
                })
            })
            setTimeout(function () {
                const input = document.getElementsByTagName('textarea')[0];
                input.addEventListener('compositionstart', function () {
                    cpLock = false;
                });
                input.addEventListener('compositionend', function () {
                    cpLock = true;
                });
                input.addEventListener('touchmove', function (event) {
                    document.activeElement !== input && event.preventDefault();
                    event.stopPropagation();
                }, { passive: false })
                input.addEventListener('input', function (event) {
                    setTimeout(function () {
                        if (isiOS && !cpLock) return;
                        handleTextareaInput(event)
                    }, 20)
                })
                input.addEventListener('paste', function (event) {
                    if (isAndroid && event.clipboardData || event.originalEvent) {
                        let clipboardData = (event.clipboardData || window.clipboardData);
                        pasteVal = clipboardData.getData('text');
                        // event.preventDefault();
                    }
                })
            }, 100)
        }

        document.addEventListener("visibilitychange", function () {
            if (document.visibilityState === 'visible') {
                isAndroid && setWebviewPageID({ pageID: pageID });
                reportWrap.classList.add('hidden');
            } else {
                isiOS && document.body.removeEventListener('touchend', handleIOSBlur);
            }
        });

    </script>
</body>

</html>