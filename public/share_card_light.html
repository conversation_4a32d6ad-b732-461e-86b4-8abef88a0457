<!DOCTYPE html>
<html lang="en">

<head>
  <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
  <title>卡片分享</title>
  <meta name="viewport" content="width=device-width,minimum-scale=1.0,maximum-scale=1.0,user-scalable=no">
  <meta name="description"
        content="探探是一个基于大数据智能推荐，给你推送身边与你匹配的人，帮助你结识互有好感的新朋友的社交APP向右滑喜欢，向左滑无感，翻动探探推送的照片，寻找与你最近的缘分。如果你喜欢的人也喜欢你，恭喜，一见钟情！在探探，只有相互喜欢的人才可以开始聊天，眼缘不错，趣味相投，向陌生人尴尬的搭讪说再见吧。">
  <meta name="twitter:card" content="卡片分享">
  <meta name="twitter:description"
        content="探探是一个基于大数据智能推荐，给你推送身边与你匹配的人，帮助你结识互有好感的新朋友的社交APP向右滑喜欢，向左滑无感，翻动探探推送的照片，寻找与你最近的缘分。如果你喜欢的人也喜欢你，恭喜，一见钟情！在探探，只有相互喜欢的人才可以开始聊天，眼缘不错，趣味相投，向陌生人尴尬的搭讪说再见吧。">
  <meta name="twitter:image" content="https://tantanapp.com/img/d1ebc7c5.shared.jpg">
  <meta property="og:title" content="卡片分享">
  <meta property="og:description"
        content="探探是一个基于大数据智能推荐，给你推送身边与你匹配的人，帮助你结识互有好感的新朋友的社交APP向右滑喜欢，向左滑无感，翻动探探推送的照片，寻找与你最近的缘分。如果你喜欢的人也喜欢你，恭喜，一见钟情！在探探，只有相互喜欢的人才可以开始聊天，眼缘不错，趣味相投，向陌生人尴尬的搭讪说再见吧。">
  <meta property="og:image" content="https://tantanapp.com/img/d1ebc7c5.shared.jpg">
  <meta property="og:product" content="卡片分享:product">
  <meta itemprop="name" content="卡片分享"/>
  <meta name="description" itemprop="description"
        content="探探是一个基于大数据智能推荐，给你推送身边与你匹配的人，帮助你结识互有好感的新朋友的社交APP向右滑喜欢，向左滑无感，翻动探探推送的照片，寻找与你最近的缘分。如果你喜欢的人也喜欢你，恭喜，一见钟情！在探探，只有相互喜欢的人才可以开始聊天，眼缘不错，趣味相投，向陌生人尴尬的搭讪说再见吧。"/>
  <style type="text/css">
    /*
    * Prefixed by https://autoprefixer.github.io
    * PostCSS: v7.0.29,
    * Autoprefixer: v9.7.6
    * Browsers: last 4 version
    */

    * {
      margin: 0;
      padding: 0;
      font-size: 0.16rem;
    }

    a {
      -webkit-tap-highlight-color: rgba(255, 255, 255, 0);
      /* 去掉点击a标签蓝色覆盖层 */
    }

    .content {
      padding: 0 0.7rem;
      color: #999999;
      position: fixed;
      bottom: 10%;
      text-align: center;
    }

    .mask {
      position: absolute;
      left: 0;
      top: 0;
      right: 0;
      bottom: 0;
      background-color: rgba(0, 0, 0, 0.7);
      z-index: 1;
      display: none;
    }

    .img {
      position: absolute;
      right: 0.2rem;
      top: 0.1rem;
      width: 2.2rem;
      height: 1.4rem;
    }

    .buttonImg {
      width: 100%;
    }

    .mask {
      position: absolute;
      left: 0;
      top: 0;
      right: 0;
      bottom: 0;
      background-color: rgba(0, 0, 0, 0.7);
      z-index: 1;
      display: none;
    }

    .img {
      position: absolute;
      right: 0.2rem;
      top: 0.1rem;
      width: 2.2rem;
      height: 1.4rem;
    }
  </style>
  <!-- Global site tag (gtag.js) - Google Analytics -->
  <script async src="https://www.googletagmanager.com/gtag/js?id=UA-109302778-22"></script>
  <script>
    window.dataLayer = window.dataLayer || [];

    function gtag() {
      dataLayer.push(arguments);
    }

    gtag('js', new Date());
    gtag('config', 'UA-109302778-22');
  </script>
  <!-- <script>
      ; (function () {
          var src = 'https://auto.tancdn.com/v1/raw/d39d1ccb-9e30-4bef-ab0e-7e35058716c40300.js';
          document.write('<scr' + 'ipt src="' + src + '"></scr' + 'ipt>');
          document.write('<scr' + 'ipt>eruda.init();</scr' + 'ipt>');
      })();
  </script> -->
</head>
<body>
<div class="mask">
  <img class="img"
       src="https://auto.tancdn.com/v1/images/eyJpZCI6IkNZWEE0UVhSQkNUUUVUVkdXQTI3NlhKTktXVDVaRjA1IiwidyI6NTEyLCJoIjoyODAsImQiOjAsIm10IjoiaW1hZ2UvanBlZyIsImRoIjo3Mzk2MTAyNjQ2Mjk5NDE5MjY0LCJhYiI6MH0.png">
</div>
<div>
  <div style="width:40%; margin: 0 auto; padding-top: 1rem; padding-bottom: 1rem;">
    <img width="100%"
         src="https://auto.tancdn.com/v1/images/eyJpZCI6IkdMT1FQVDNMVVNNUVYzSEZYSzJUS0FTQUFQSU1BVDA3IiwidyI6MzMxLCJoIjozMzEsImQiOjAsIm10IjoiaW1hZ2UvanBlZyIsImRoIjoxMDQxNjk0MjI2NjU3MjkxOTQwMX0.png"
         alt="tantan">
  </div>
  <a href="https://tantanapp.com/tantan_scp.apk">
    <div class="content">
      <img class="buttonImg"
           src="https://auto.tancdn.com/v1/images/eyJpZCI6IjZLTkVKRUNNWk9MTFZHR0hXR0xCRVkyS1g3VjJVUzA1IiwidyI6NTAzLCJoIjozMjcsImQiOjAsIm10IjoiaW1hZ2UvanBlZyIsImRoIjoxNTY2MDY0NzU4MTgwNDQ3MDYyOSwiYWIiOjB9.png">
    </div>
  </a>
</div>
<script>
  function parseQuery(search) {
    let query = Object.create(null)
    search = search || window.location.search;
    let paris = search.replace('?', '').split('&')
    paris.forEach(item => {
      let arr = item.split('=')
      query[arr[0]] = arr[1]
    })
    return query;
  }

  // 判断是否是微信环境
  function isWeixinBrowser() {
    var ua = typeof navigator !== 'undefined' ? navigator.userAgent.toLowerCase() : '';
    console.log('weixin', (/micromessenger/.test(ua)))
    return (/micromessenger/.test(ua));
  }

  if (isWeixinBrowser()) {
    document.getElementsByClassName('mask')[0].style.display = 'block'
  }

  var uid = parseQuery().uid
  var appUrl = 'tantanapp://shareCard?uid=' + uid
  var isiOS = !!navigator.userAgent.match(/\(i[^;]+;( U;)? CPU.+Mac OS X/);

  location.href = appUrl;
  console.log('location', appUrl)

  if (isiOS) {
    var iOSUrl = "https://itunes.apple.com/cn/app/tantan/id861891048?mt=8";
    document.getElementsByTagName("a")[0].setAttribute('href', iOSUrl);
    if (!isWeixinBrowser()) {
      setTimeout(function () {
        location.href = iOSUrl;
      }, 9000)
    }
  } else {
    var androidUrl = "https://apk-light.tancdn.com/light-redpocketshare.apk";
    document.getElementsByTagName("a")[0].setAttribute('href', androidUrl);
    if (!isWeixinBrowser()) {
      setTimeout(function () {
        location.href = androidUrl;
      }, 9000)
    }
  }
</script>
</body>
</html>
