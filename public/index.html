<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="utf-8"/>
  <meta name="viewport"
        content="width=device-width,initial-scale=1.0,maixmum-scale=1.0,minimum-scale=1.0,user-scalable=no,viewport-fit=cover">
  <meta name=" theme-color" content="#000000"/>
  <title>邀请你加入探探</title>
  <script type="text/javascript">
    (function () {
      window.onresize = setRem;

      function setRem() {
        console.log(document.documentElement.offsetWidth);
        const docEle = document.documentElement;
        docEle.style.fontSize = (document.documentElement.offsetWidth / (375 / 100)) + 'px';
        docEle.style.lineHeight = '20px';
      }

      setRem();
      const blockList = ["/activity/test"];
      if (
        (!~blockList.indexOf(window.location.pathname) &&
          ~window.location.hostname.indexOf("staging2")) ||
        /devtool=1/.test(window.location.search) ||
        ~window.location.hostname.indexOf('10.')
      ) {
        var src =
          "https://auto.tancdn.com/v1/raw/d39d1ccb-9e30-4bef-ab0e-7e35058716c40300.js";
        document.write("<scr" + 'ipt src="' + src + '"></scr' + "ipt>");
        document.write("<scr" + "ipt>eruda.init();</scr" + "ipt>");
      }
    })();
    window.doWhenGetToken = new Function()
  </script>
</head>

<body>
<noscript>You need to enable JavaScript to run this app.</noscript>
<div id="root"></div>
<!--
  This HTML file is a template.
  If you open it directly in the browser, you will see an empty page.

  You can add webfonts, meta tags, or analytics to this file.
  The build step will place the bundled scripts into the <body> tag.

  To begin the development, run `npm start` or `yarn start`.
  To create a production bundle, use `npm run build` or `yarn build`.
-->
</body>
</html>
