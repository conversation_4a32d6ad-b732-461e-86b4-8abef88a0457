<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="utf-8" />
  <meta name=" theme-color" content="#000000" />
  <meta name="viewport"
    content="width=device-width,initial-scale=1.0,maixmum-scale=1.0,minimum-scale=1.0,user-scalable=no,viewport-fit=cover">
  <title>会员成长等级说明</title>
  <!-- reset css -->
  <style>
    * {
      box-sizing: border-box;
      -webkit-box-sizing: border-box;
      -moz-box-sizing: border-box;
      user-select: none;
    }

    html,
    body {
      margin: 0;
      padding: 0;
      font-family: sans-serif;
      -webkit-font-smoothing: antialiased;
    }
    body {
      padding-bottom: 24px;
    }
  </style>

  <!-- table css -->
  <style>
    .fl-table {
      display: block;
      width: 100%;
      width: 100%;
      max-width: 100%;
      font-size: 14px;
      font-weight: normal;
      border: none;
      white-space: nowrap;
      border-collapse: collapse;
    }

    .fl-table thead,
    .fl-table tbody,
    .fl-table thead th {
      display: block;
    }
    .fl-table thead {
      float: left;
    }

    .fl-table tbody {
      width: auto;
      position: relative;
      overflow-x: auto;
    }

    .fl-table td,
    .fl-table th {
      overflow: hidden;
      vertical-align: middle;
      box-sizing: border-box;
      text-overflow: ellipsis;
    }

    .fl-table thead th {
      text-align: left;
    }

    .fl-table tbody tr {
      display: table-cell;
    }

    .fl-table thead th,
    .fl-table tbody td {
      display: block;
      height: 42px;
      line-height: 42px;
      padding-right: 18px;
      padding-left: 18px;
      border-right: 1px solid rgb(242, 218, 220);
      border-bottom: 1px solid rgb(242, 218, 220);
    }

    /* .fl-table thead tr:last-child th, */
    .fl-table tbody tr:last-child td {
      border-right: none;
    }

    .fl-table thead th:last-child,
    .fl-table tbody td:last-child {
      border-bottom: none;
    }

    .fl-table thead .thead,
    .fl-table tbody .thead {
      font-weight: 600;
      background-color: rgb(251, 235, 235);
    }

    .fl-table thead tr th:nth-child(even),
    .fl-table tbody tr td:nth-child(even) {
      background-color: rgb(254, 250, 251);;
    }
  </style>

  <!-- css -->
  <style>
    .container {
      padding: 0 20px;
    }

    .formula {
      margin-bottom: 18px;
    }

    .formula-result {
      display: inline-block;
      width: 44px;
      height: 44px;
      line-height: 44px;
      font-size: 12px;
      font-weight: 500;
      text-align: center;
      border-radius: 50%;
      background-color: rgb(251, 235, 235);
    }

    .formula-content {
      display: inline-block;
      width: 77%;
      width: calc(100% - 64px);
      height: 44px;
      line-height: 44px;
      font-size: 12px;
      font-weight: 500;
      text-align: center;
      border-radius: 26px;
      background-color: rgb(251, 235, 235);
    }

    .formula-item {
      vertical-align: middle;
    }
    
    .table.source,
    .table.rights {
      position: relative;
      border-radius: 12px;
      overflow: hidden;
      border: 1px solid rgb(242, 218, 220);
    }

    .table.source tbody,
    .table.rights tbody {
      border-top-right-radius: 12px;
      border-bottom-right-radius: 12px;
    }

    .table.source tr:first-child {
      min-width: 140px;
    }

    .table.source tr:last-child {
      width: 100%;
    }

    .table.source tbody tr:first-child {
      font-weight: 600;
    }

    .table.rights thead th {
      min-width: 130px;
    }

    .table.rights tr:last-child {
      width: 100%;
    }

    .table.rights thead {
      font-weight: 600;
    }

    .table.rights .thead,
    .table.rights thead th:first-child {
      height: 60px;
      line-height: 60px;
    }

    .table.rights .thead.br {
      line-height: 22px;
      padding-top: 10px;
    }
    
    .desc {
      font-size: 13px;
    }

    .icon {
      display: inline-block;
      width: 8px;
      height: 10px;
      vertical-align: middle;
      background-color: red;
    }
    .icon.eq {
      background: center / cover no-repeat url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAADAAAAAwCAYAAABXAvmHAAABCUlEQVRoQ+1YQQrCQAxMFOoPPPYmFP2BXnyFj/Qdgn5AEbztM3owoqK0HkxpsrS7TK9NJslM0t2UKfGHE8+fUMDQCkIBKGBkAC1kJNDsnp8Ct7JY3kW2d+a5mR5HAGYJU+HTItSXJmxLgVfyRHshWjnGdoNiovOEaNcsolXAtZwdiGTjFjEO0LEK9foD/VNAIXFi+qJWof7mjQJ8ue2GlrMCiQ9x8p/RZwcmfZB1G6FxWeV3FxoXv3o2UEDnKK4FFIjLr46enwJJH2TJXyWwkekz52KR8z6AndilRTSQPy2EjUwjz/Re/bGFjczEbz/n/O5C/XgYzgsKDMf9OzIUgAJGBtBCRgLN7g8t6ogxeMpYzwAAAABJRU5ErkJggg==);
    }

    .icon.sum {
      background: center / cover no-repeat url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAADAAAAAwCAYAAABXAvmHAAACPUlEQVRoQ+1ZMW/TQBh9ZztOkCokBmglEisFR3HJBHMHWBjaEYkf0Q2kDt3KVomFuT+hVB1QYWWgA38AUuEE0muqlk5IIJpebF/lIVWcOmmcu0MtOo9x7n3vfd+7z9/ZBNf8ItecP7SAtAr6d28UAyt4aXDylAMdDmzPUbaqotrSK/DlXsExg2gvjeyJwW49bOGXTCHSBdQde5sAi+kk+RuPdl9cWQGNYt4NDO4PI0iAZpUy98oKqBetx8QwPo4i6FEmtepSwbSACbylK9CfNG0hbaEJMqAtJJg03YW0hVRZyHdxM+hYj7LiXzYL8Sh6khWzYAWfZ1vopK27sAd8x34QAq8BLGQNpPD/vwG8KxBrubz397A/TkJA4/7UnaDLfiokIgw9OM0mBOw69iaAZ8JRFAJw4FX/8TQhoO7kjgjItML4wtCEY6u6z86TnKxAKXcMQm4LR1EL8Naj7HkvxICF8p8APq82vhg6B9bmKFtJFTDOOCwWXny1GZqlysFJO1VA/GPcRiNggwM18XDyEDjwPrKMpdr3Dh3aRns3fpRRYLDLURDNZKWg4kGWg9l226eNsR5kWQnrWUgkY4Dcl7vjNAH9XmigYvpAozex3sSCGdAWEkyg7kL/lYWaxXyla/BvI1zR8iibFXRNYrlUC/ku8iGz429kpSEkP3iUDfkAOJksqQJiCqPmIROoVSj7OhnV9FXSBfQORSHIOoAqRxQSkB0rZy+5zT/HMsnHWEoEyCY5Ck8L+JfZTot1Bv447DGVYvfhAAAAAElFTkSuQmCC);
    }

    .icon.less {
      background: center / cover no-repeat url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAADAAAAAwCAYAAABXAvmHAAABA0lEQVRoQ+2VMQ6CQBBF52MC1lorsfMGegfhtHgH8QZKYzyA1jYyJhR2GpIPq5t8aubPzHubXVjkHyKf37TArw3KgAyQBHSESIB0uQzQCMkAGSAB0uUyQCMkA2SABEiXywCNkAzoDJzy6crcSzefkXlByieGWwvs19fHBc0yK1rzKkjnYZt4ApQ452ntbpths8Okwa3GaZl6mHbjdNEC43Dtnxq/gfMiPThs23/n//kTsCOaPNu13l2j0b3KiaF8P2SJe/E0n/8P38+TwHA3oOoeshgG/jajFvi1QRmQAZKAjhAJkC6XARohGSADJEC6XAZohGSADJAA6XIZoBGSAdEbeAF0BzE8RRxoBwAAAABJRU5ErkJggg==);
    }
  </style>
</head>

<body>
  <div id="container" class="container">
    <section class="source-content">
      <h3 class="title">电力值获取说明</h3>
      <div class="formula">
        <span class="formula-result">电力值</span>
        <i class="icon eq"></i>
        <div class="formula-content">
          <span class="formula-item">开通电力值</span>
          <i class="icon sum"></i>
          <span class="formula-item">任务电力值</span>
          <i class="icon less"></i>
          <span class="formula-item">会员下降值</span>
        </div>
      </div>

      <div class="table source">
        <table class="fl-table">
          <tr>
            <td class="thead">会员种类</td>
            <td>SVIP会员-订阅</td>
            <td>SVIP会员-非订阅</td>
            <td>VIP会员-订阅</td>
            <td>VIP会员-非订阅</td>
          </tr>
          <tr>
            <td class="thead">获取电力值</td>
            <td>+93/月 +150/续约每月</td>
            <td>+93/月</td>
            <td>+31/月 +150/续约每月</td>
            <td>+31/月</td>
          </tr>
        </table>
      </div>
    </section>
    <section class="rights-content">
      <h3 class="title">等级权益</h3>
      <div class="table rights">
        <table class="fl-table">
          <thead>
            <tr>
              <th class="thead">电力值</th>
              <th>100至200</th>
              <th>200至400</th>
              <th>400至800</th>
              <th>800至1300</th>
              <th>1300至1800</th>
              <th>1800至2400</th>
              <th>2400至10000</th>
              <th>大于10000</th>
              <th>小于100</th>
            </tr>
          </thead>
          <tbody>
            <tr>
              <td class="thead">额外福利-加速配对</td>
              <td>2倍加速配对</td>
              <td>3倍加速配对</td>
              <td>4倍加速配对</td>
              <td>5倍加速配对</td>
              <td>6倍加速配对</td>
              <td>7倍加速配对</td>
              <td>8倍加速配对</td>
              <td>9倍加速配对</td>
              <td>1.5倍加速配对</td>
            </tr>
            <tr>
              <td class="thead br">其他额外权益<br/>( 升级后赠送，31天后不使用则失效，不可累计 )</td>
              <td>1次超级喜欢/月</td>
              <td>1次超级喜欢/月</td>
              <td>1次超级喜欢/月+1次优先推荐/月</td>
              <td>1次超级喜欢/月+1次优先推荐/月</td>
              <td>1次超级喜欢/月+2次优先推荐/月</td>
              <td>1次超级喜欢/月+2次优先推荐/月</td>
              <td>1次超级喜欢/月+2次优先推荐/月</td>
              <td>1次超级喜欢/月+3次优先推荐/月</td>
              <td>-</td>
            </tr>
          <tbody>
        </table>
      </div>
      <p class="desc">
        注：电力值及相应的会员等级权益，仅限VIP会员/SVIP超级会员享有，会员到期后将会失效
      </p>
    </section>
  </div>
</body>

</html>