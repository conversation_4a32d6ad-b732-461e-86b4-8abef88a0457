<!--
 * @Author: your name
 * @Date: 2021-05-28 15:13:32
 * @LastEditTime: 2021-06-07 20:21:42
 * @LastEditors: Please set LastEditors
 * @Description: In User Settings Edit
 * @FilePath: /tantan-frontend-commerce/README.md
-->
# 探探商业化 h5

### 开发

`npm run start`

### bundle 分析

### 代码行数分析

`find . -name "*.tsx"|xargs cat|grep -v ^$|wc -l`

### 打包

`npm run build-test` 打包测试环境包
`npm run build` 打包生产环境包

### 项目地址

staging：http://m.staging2.p1staff.com/commerce/

线上：https://m.tantanapp.com/commerce/

部署地址：https://tcp.p1staff.com/workbench/delivery_process_details/4655/dashboard


#### 群聊

群聊h5重构项目（使用离线缓存），打包上传cdn单独使用分支，feature/offline-cache（makefile文件不同，切换容易出错）


## 其他

安卓h5弹窗：项目在feature/buypopup_static这个分支上面（未合到master）

grouph5应该迁移到cp里面


