const { createProxyMiddleware } = require('http-proxy-middleware');
module.exports = function (app) {
    app.use(
        createProxyMiddleware('/report',{
            target : 'http://h5report.staging2.p1staff.com/',
            changeOrigin : true,  // 设置跨域请求
        }),
        createProxyMiddleware('/v2/confirmation-code', {
            target : 'http://core.staging2.p1staff.com/',
            changeOrigin : true,  // 设置跨域请求
        }),
        createProxyMiddleware('/debug/phone', {
            target : 'http://passport.staging2.p1staff.com/',
            changeOrigin : true,  // 设置跨域请求
        }),
    );
};
