import React from 'react'
import { Toast } from './components/antd/Antd'
import { Error } from './routes/error/Error'

interface MyProps {
  children: React.ReactNode
}
interface Mystate {
  hasError: boolean
}
class ErrorBoundary extends React.Component<MyProps, Mystate> {
  constructor(props: MyProps) {
    super(props)
    this.state = {
      hasError: false,
    }
  }

  static getDerivedStateFromError() {
    // 更新 state 使下一次渲染能够显示降级后的 UI
    return { hasError: true }
  }

  render() {
    if (this.state.hasError) {
      Toast.info('出错了~~', 1)
      return <Error />
    }

    return this.props.children
  }
}

export default ErrorBoundary
