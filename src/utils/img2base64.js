export const img2base64 = (url, callback) => {
  var img = new Image()
  img.crossOrigin = 'anonymous'

  img.onload = function () {
    const canvas = document.createElement('canvas'),
    ctx = canvas.getContext('2d');

    canvas.height = img.naturalHeight;
    canvas.width = img.naturalWidth;
    ctx.drawImage(img, 0, 0);

    // Unfortunately, we cannot keep the original image type, so all images will be converted to PNG
    // For this reason, we cannot get the original Base64 string
    const uri = canvas.toDataURL('image/png')
    callback(uri)
  };

  // If you are loading images from a remote server, be sure to configure “Access-Control-Allow-Origin”
  // For example, the following image can be loaded from anywhere.
  img.src = url
}