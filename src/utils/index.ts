export const parseQuery = (str: string) => {
  let qs = str;
  if (qs.length <= 1) {
    return {};
  }
  qs = qs.slice(1);
  const split = qs.split("&");
  const result: any = {};

  split.forEach((s) => {
    const [key, value] = s.split("=");
    if (key) {
      result[key] = value && decodeURIComponent(value);
    }
  });
  return result;
};

export function urlParams(url: string) {
  let match;
  const pl = /\+/g; // Regex for replacing addition symbol with a space
  const search = /([^&=]+)=?([^&]*)/g;
  const decode = (s: string) => {
    return decodeURIComponent(s.replace(pl, " "));
  };

  let obj = {} as any;
  /* eslint-disable */
  // @ts-ignore
  while ((match = search.exec(url))) {
    obj[decode(match[1])] = decode(match[2]);
  }
  /* eslint-enable */
  return obj;
}

export const formatDate = (date: string) => {
  const d = new Date(Number(date));
  var month: any = d.getMonth() + 1;
  var day: any = d.getDate();

  month = (month < 10 ? "0" : "") + month;
  day = (day < 10 ? "0" : "") + day;

  var str = d.getFullYear() + "年" + month + "月" + day + "日";

  return str;
};

export const formatDateEn = (data: string) => {
  const dt = new Date(parseInt(data));
  const m = ["January", "February", "March", "April", "May", "June", "July", "August", "September", "October", "November", "December"];
  const mn = dt.getMonth();
  const dn = dt.getDate();
  return m[mn] + " " + dn + ", " + dt.getFullYear();
}

export const makeRandomCode = (length: number) => {
  var result = "";
  var characters =
    "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789";
  var charactersLength = characters.length;
  for (var i = 0; i < length; i++) {
    result += characters.charAt(Math.floor(Math.random() * charactersLength));
  }
  return result;
};
