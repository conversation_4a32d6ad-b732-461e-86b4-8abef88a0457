import {share} from "./bridge";
import {Toast} from "../components/antd/Antd";

export function shareImage(params) {
  const {pic, title, platform, desc} = params

  return new Promise((resolve, reject) => {
    try {
      share({
        channel: platform,
        url: `${window.location.origin}/commerce/annual/report/shared`,
        title: title,
        imgUrl: pic,
        description: desc,
        successHandler: () => {
          resolve('success')
          console.log('分享成功了')
        },
        errorHandler: () => {
          Toast.info('分享失败了', 0.5)
          reject('error')
        }
      })
    } catch (err) {
      Toast.info('分享失败了')
      reject('error')
    }
  })
}
