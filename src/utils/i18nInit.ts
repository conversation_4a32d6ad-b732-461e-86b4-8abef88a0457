import i18n from "i18next";
import {initReactI18next} from "react-i18next";
import {getSystemInfo} from "./bridge";

export type Language = "zh-Hans" | "en" | "zh-Hant" | "id" | "tr" | "ko" | "ja";

const zh = require("../utils/locales/locales/tantan-h5-json-archive/locales/zh/tantan-h5-zh.json");
const en = require("../utils/locales/locales/tantan-h5-json-archive/locales/en-US/tantan-h5-en-US.json");
const id = require("../utils/locales/locales/tantan-h5-json-archive/locales/id-ID/tantan-h5-id-ID.json");
const ja = require("../utils/locales/locales/tantan-h5-json-archive/locales/ja/tantan-h5-ja.json");
const ko = require("../utils/locales/locales/tantan-h5-json-archive/locales/ko/tantan-h5-ko.json");
const zh_TW = require("../utils/locales/locales/tantan-h5-json-archive/locales/zh-TW/tantan-h5-zh-TW.json");

export async function i18nInit() {
  let language;
  try {
    language = (await getSystemInfo()).language;
  } catch (e) {
    language = "en";
  }
  console.log("language: ", language);
  await i18n
    .use(initReactI18next) // passes i18n down to react-i18next
    .init({
      resources: {
        "zh-Hans": {translation: zh},
        en: {translation: en},
        id: {translation: en},
        ja: {translation: en},
        ko: {translation: en},
        "zh-Hant": {translation: en},
      },
      lng: language ? language : "zh-Hans",
      fallbackLng: "zh-Hans",
      react: {
        wait: true,
      },
    });
}
