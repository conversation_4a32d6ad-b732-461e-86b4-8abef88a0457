import {ajax} from "./ajax";
import {reportUrl} from "./config";
import {makeRandomCode} from "./index";
import {PVItem} from "../routes/annual/Report/constant";
import {trackNew} from "./bridge";

export const deviceID = localStorage.getItem('deviceID') || makeRandomCode(20);

export const track = (type: string, pid?: string, eid?: string, extra?: {[key: string]: any}) => {
  let params = {
    userId: '',
    deviceId: deviceID,
    content: JSON.stringify({
      pid, tt: type, time: Date.now(), eid, extra
    })
  }
  ajax.post(reportUrl, params)
    .then(res => {
      console.log(pid, type);
    })
}

export const trackPV = (pv: PVItem) => {
  if (pv.flag) return;
  pv.flag = !pv.flag;
  trackNew({
    pageId: pv.key,
    type: 'PV'
  })
}

export function getQueryVariable(key: string): string {
  let query = window.location.search.substring(1);
  let lets = query.split("&");
  for (let i = 0; i < lets.length; i++) {
    let pair = lets[i].split("=");
    if (pair[0] == key) {
      return pair[1];
    }
  }
  return '';
}

export function compare(curV: string, reqV: string) {
  if (curV && reqV) {
    //将两个版本号拆成数字
    var arr1 = curV.split('.'),
      arr2 = reqV.split('.');
    var minLength = Math.min(arr1.length, arr2.length),
      position = 0,
      diff = 0;
    //依次比较版本号每一位大小，当对比得出结果后跳出循环（后文有简单介绍）
    while (position < minLength && ((diff = parseInt(arr1[position]) - parseInt(arr2[position])) == 0)) {
      position++;
    }
    console.log('diff', diff);

    diff = (diff != 0) ? diff : (arr1.length - arr2.length);
    //若curV大于reqV，则返回true
    return diff >= 0;
  } else {
    //输入为空
    console.log("版本号不能为空");
    return false;
  }
}

export function obj2str(obj: any): string {
  if (typeof obj !== "object") return '';
  return JSON.stringify(obj, null, 2)
}
