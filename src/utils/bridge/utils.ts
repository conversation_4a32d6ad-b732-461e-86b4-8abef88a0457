declare global {
    interface Window {
        tantan: any;
        webkit: any;
        [key: string]: any,
    }
}

export const genUuid = () => {
    const d = +new Date();
    const range = (Math.random() as any).toFixed(3) * 1000;
    return `${d}_${range}`;
}

const ua = typeof navigator !== 'undefined' ? navigator.userAgent.toLowerCase() : '';
// iPhone
export const isiOS = ~ua.indexOf('iphone') || ua === 'tantan-ios'

// 安卓
export const isAndroid = ~ua.indexOf('android');

// 判断是否是微信环境
export const isWeixinBrowser = (): boolean => {
    var ua = typeof navigator !== 'undefined' ? navigator.userAgent.toLowerCase() : '';
    console.log('weixin', (/micromessenger/.test(ua)))
    return (/micromessenger/.test(ua));
}

export const throwErr = (errorMsg: string) => {
    throw errorMsg;
}

export const checkIsFn = (fnArray: Array<any>) => {
    if (!Array.isArray(fnArray)) {
        throwErr('param must be array')
    }
    return fnArray.every(fn => fn && typeof fn === 'function');
}

export const android_method_checker = (method: string) => {
    return (
        isAndroid &&
        typeof window.tantan !== "undefined" &&
        typeof window.tantan[method] === "function"
    );
}

export const ios_method_checker = (method: string) => {
    return isiOS
        && window.webkit
        && window.webkit.messageHandlers
        && window.webkit.messageHandlers[method]
        && typeof window.webkit.messageHandlers[method].postMessage === 'function'
}

export const oldIOS_method_checker = (method: string) => {
    return isiOS && typeof window[method] === "function";
}

export const noop = () => {};

export const handleAndroidParamArr = (array: Array<any> = []) => array.map((item: any) => {
    const resultMap: { type: string, value: any, isArray?: boolean } = {
        type: 'String',
        value: item,
    };
    if (typeof item === 'number') {
        resultMap.type = 'Integer'
        if (/^\d+\.\d+$/.test(String(item))) {
            resultMap.type = 'Double'
        }
    }
    if (typeof item === 'boolean') {
        resultMap.type = 'Boolean';
    }
    if (Object.prototype.toString.call(item) === '[object Array]') {
        resultMap.isArray = true;
    }
    return resultMap;
})

export const customConsole = (text: string) => {
    console.warn(`%c${text}`, 'color: red;font-style: italic;font-size: 0.2rem')
}

export const isJsonString = (str: string) => {
    try {
        if (typeof JSON.parse(str) == "object") {
            return true;
        }
    } catch(e) {
    }
    return false;

}
