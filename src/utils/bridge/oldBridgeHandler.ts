import {
    saveBean, closeWebviewOld, refreshLiveAuth, jumpRecharge, refreshGuildAuth, jumpToCover,
    onTeenModeEnable, verifyTeenModePassword, closeLiveCampaignDialog,
    refreshUserCounters, jumpToLiveAnchor, action, jumpToProfile, jumpToRoom, onRedPacketOpen,
    subscribeCampaign, showRechargeDialog, liveGiftDialogController, liveNewUserRedPacketController,
    jumpToTopicAggregationAct, jumpToTopicVoteAggregationAct, jumpToProfileAct, startMessagesAct,
    jumpToSeeOrBuySee, registerBarRight, unRegisterBarRight, imagePicker, setTitle, campaignController,
    track, follow, jumpToTopic, jumpTantanxDownload,
} from "./oldBridge";

const oldBridge = (methodName: string, restParam?: any) => {
    switch (methodName) {
        case 'saveBean':
            return saveBean();
        case 'closeWebview':
            return closeWebviewOld();
        case 'refreshLiveAuth':
            return refreshLiveAuth();
        case 'jumpRecharge':
            return jumpRecharge(restParam)
        case 'refreshGuildAuth':
            return refreshGuildAuth();
        case 'jumpToCover':
            return jumpToCover();
        case 'onTeenModeEnable':
            return onTeenModeEnable(restParam.enable);
        case 'verifyTeenModePassword':
            const { verified, scenes } = restParam;
            return verifyTeenModePassword(verified, scenes);
        case 'closeLiveCampaignDialog':
            return closeLiveCampaignDialog();
        case 'refreshUserCounters':
            return refreshUserCounters();
        case 'jumpToLiveAnchor':
            return jumpToLiveAnchor();
        case 'action':
            return action(restParam.schema);
        case 'jumpToProfile':
            const { userId, from } = restParam;
            return jumpToProfile(userId, from);
        case 'jumpToRoom':
            const { liveId, roomId, source } = restParam;
            return jumpToRoom(liveId, roomId, source)
        case 'onRedPacketOpen':
            return onRedPacketOpen(restParam.ismarqueeredpacket);
        case 'subscribeCampaign':
            return subscribeCampaign(restParam)
        case 'showRechargeDialog':
            return showRechargeDialog();
        case 'liveGiftDialogController':
            let { type, content, callBack } = restParam;
            return liveGiftDialogController(type, content, callBack);
        case 'liveNewUserRedPacketController':
            return liveNewUserRedPacketController(restParam.type, restParam.content, restParam.callBack);
        case 'jumpToTopicAggregationAct':
            return jumpToTopicAggregationAct(restParam.id);
        case 'jumpToTopicVoteAggregationAct':
            return jumpToTopicVoteAggregationAct(restParam.id, restParam.topicOwnerId);
        case 'jumpToTopic':
            return jumpToTopic(restParam);
        case 'jumpToProfileAct':
            return jumpToProfileAct(restParam.userId, restParam.from);
        case 'startMessagesAct':
            return startMessagesAct(restParam.id, restParam.from);
        case 'jumpToSeeOrBuySee':
            return jumpToSeeOrBuySee();
        case 'registerBarRight':
            return registerBarRight(restParam.key, restParam.callback);
        case 'unRegisterBarRight':
            return unRegisterBarRight();
        case 'imagePicker':
            return imagePicker(restParam.success);
        case 'setTitle':
            return setTitle(restParam);
        case 'campaignController':
            return campaignController(restParam.action, restParam.content, restParam.callBack)
        case 'track':
            return track(restParam.key, restParam.params);
        case 'follow':
            return follow(restParam);
        case 'jumpTantanxDownload':
            return jumpTantanxDownload()
        default:
            return {};
    }
}

export default oldBridge;
