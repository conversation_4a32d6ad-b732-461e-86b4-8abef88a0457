import {android_method_checker, oldIOS_method_checker, ios_method_checker, noop, genUuid } from "./utils";
import {TantanBean} from "./interface";


/** 1. 获取设备基础信息 */
export async function saveBean() {
    if (android_method_checker("saveBean")) {
        const res = JSON.parse(window.tantan.saveBean());
        if (res.versionCode) {
            res.appVersion = res.versionCode;
        }
        if (res.platform) {
            res.os = res.platform
        }
        return Promise.resolve(res)
    } else if (oldIOS_method_checker("saveBean")) {
        return window.saveBean() as TantanBean;
    } else if (ios_method_checker('saveBean')) {
        return new Promise<TantanBean>((resolve, reject) => {
            const uid = genUuid();
            window[`saveBeanSuccess${uid}`] = (res: string) => {
                const result = JSON.parse(res);
                if (result.versionCode) {
                    result.appVersion = result.versionCode;
                }
                if (result.platform) {
                    result.os = result.platform
                }
                resolve(result)
            }
            window[`saveBeanFail${uid}`] = (err: any) => { reject(err) }
            window.webkit.messageHandlers.saveBean.postMessage({
                success: `saveBeanSuccess${uid}`,
                fail: `saveBeanFail${uid}`,
            })
        })
    }
    console.info("无法获取设备信息: saveBean", 0.5);
    return {
        schemeVersion: "",
        deviceId: "",
        language: "",
        userId: "10086",
        versionCode: ""
    } as TantanBean;
}

/** 2. 关闭窗口 */
export function closeWebviewOld(): void {
    if (android_method_checker("closeWebview")) {
        window.tantan.closeWebview();
    } else if (oldIOS_method_checker("closeWebview")) {
        window.closeWebview();
    }  else {
        console.info("无法关闭当前页面", 0.5);
    }
}


/**
 * 4. 打点
 * @param key 事件名
 * @param params 事件参数对
 */
export function track(key: string, params: { [k: string]: string }): void {
    let kvs: any = [];
    if (Object.prototype.toString.call(params) === '[object Object]') {
        for (let key in params) {
            kvs = kvs.concat(String(key), String(params[key]));
        }
    } else if (Object.prototype.toString.call(params) === '[object Array]') {
        kvs = params;
    }
    if (android_method_checker("track")) {
        window.tantan.track(key, kvs);
    } else if (oldIOS_method_checker("trackEvent")) {
        window.trackEvent(key, kvs);
    } else if (ios_method_checker('trackEvent')) {
        window.webkit.messageHandlers.trackEvent.postMessage({
            key,
            params: kvs,
        })
    } else {
        console.info("无法打点", 0.5);
    }
}


/** 7. 刷新客户端直播个人主播认证信息 */
export function refreshLiveAuth() {
    if (android_method_checker("refreshLiveAuth")) {
        window.tantan.refreshLiveAuth();
    } else if (oldIOS_method_checker("refreshLiveAuth")) {
        window.refreshLiveAuth();
    } else if (ios_method_checker('refreshLiveAuth')) {
        return window.webkit.messageHandlers.refreshLiveAuth.postMessage({})
    } else {
        console.info("无法刷新认证信息", 0.5);
    }
}

/** 8. 跳转至充值页面 */
export function jumpRecharge(
    { successRecharge }: { successRecharge: () => void }
) {
    const time = Math.floor(Math.random() * 100)
    if (android_method_checker("jumpRecharge")) {
        window[`successRecharge${time}`] = successRecharge;
        if (!successRecharge) {
           return window.tantan.jumpRecharge()
        }
        window.tantan.jumpRecharge(`successRecharge${time}`);
    } else if (oldIOS_method_checker("jumpRecharge")) {
        window.jumpRecharge(successRecharge);
    } else if (ios_method_checker('jumpRecharge')) {
        return new Promise<string>((resolve, reject) => {
            window[`successRecharge${time}`] = (res: string) => {
                successRecharge()
                resolve(res)
            }
            window.webkit.messageHandlers.jumpRecharge.postMessage({
                success: `successRecharge${time}`
            })
        })
    } else {
        console.info("无法跳转至充值界面", 0.5);
    }
}



/** 10. 刷新客户端直播公会主播认证信息 */
export function refreshGuildAuth() {
    if (android_method_checker("refreshGuildAuth")) {
        window.tantan.refreshGuildAuth();
    } else if (oldIOS_method_checker("refreshGuildAuth")) {
        window.refreshGuildAuth();
    } else if (ios_method_checker('refreshGuildAuth')) {
        return window.webkit.messageHandlers.refreshGuildAuth.postMessage({})
    } else {
        console.info("无法刷新认证信息", 0.5);
    }
}


/** 11. 跳转去封面 */
export function jumpToCover() {
    if (android_method_checker("jumpToCover")) {
        window.tantan.jumpToCover();
    } else if (oldIOS_method_checker("jumpToCover")) {
        window.jumpToCover();
    } else if (ios_method_checker('jumpToCover')) {
        return window.webkit.messageHandlers.jumpToCover.postMessage({})
    } else {
        console.info("无法跳转去封面", 0.5);
    }
}


/** 12. 刷新客户端青少年模式状态 */
export function onTeenModeEnable(enable: boolean) {
    if (android_method_checker("onTeenModeEnable")) {
        window.tantan.onTeenModeEnable(enable);
    } else if (oldIOS_method_checker("onTeenModeEnable")) {
        window.onTeenModeEnable(enable);
    } else if (ios_method_checker('onTeenModeEnable')) {
        return window.webkit.messageHandlers.onTeenModeEnable.postMessage({ enable })
    } else {
        console.info("无法刷新青少年模式状态", 0.5);
    }
}

/** 13. 客户端青少年模式输入密码回调 */
export function verifyTeenModePassword(verified: boolean, scenes: number) {
    if (android_method_checker("verifyTeenModePassword")) {
        window.tantan.verifyTeenModePassword(verified, scenes);
    } else if (oldIOS_method_checker("verifyTeenModePassword")) {
        window.verifyTeenModePassword(verified, scenes);
    } else if (ios_method_checker('verifyTeenModePassword')) {
        return window.webkit.messageHandlers.verifyTeenModePassword.postMessage({ verified, scenes })
    } else {
        console.info("无法回调客户端青少年模式输入密码", 0.5);
    }
}

/** 14. 弹窗跳转到充值页面 */
export function dialogJumpRecharge(
    { successRecharge }: { successRecharge: () => void } = {
        successRecharge: noop
    }
) {
    if (android_method_checker("jumpRecharge")) {
        window.tantan.jumpRecharge();
    } else if (oldIOS_method_checker("jumpRecharge")) {
        window.jumpRecharge(successRecharge);
    } else if (ios_method_checker('jumpRecharge')) {
        return new Promise((resolve, reject) => {
            const time = Math.floor(Math.random() * 100)
            window[`jumpRechargeSuccess${time}`] = (res: string) => {
                successRecharge()
                resolve(res)
            }
            window[`jumpRechargeSuccess${time}`] = (err: any) => { reject(err) }
            window.webkit.messageHandlers.jumpRecharge.postMessage({
                success: `jumpRechargeSuccess${time}`,
                fail: `jumpRechargeSuccess${time}`,
            })
        })
    } else {
        console.info("无法跳转至充值界面", 0.5);
    }
}

/** 关闭弹窗 */
export function closeLiveCampaignDialog() {
    if (android_method_checker("closeLiveCampaignDialog")) {
        window.tantan.closeLiveCampaignDialog();
    } else if (oldIOS_method_checker("closeLiveCampaignDialog")) {
        window.closeLiveCampaignDialog();
    } else if (ios_method_checker('closeLiveCampaignDialog')) {
        return window.webkit.messageHandlers.closeLiveCampaignDialog.postMessage({})
    } else {
        console.info("关闭弹窗", 0.5);
    }
}



/**
 * 刷新用户信息，用于vip合作运营活动页面用户抽取到"超级曝光"礼物时调用(客户端要求)
 */
export function refreshUserCounters() {
    if (android_method_checker("refreshUserCounters")) {
        window.tantan.refreshUserCounters();
    } else if (oldIOS_method_checker("refreshUserCounters")) {
        window.refreshUserCounters();
    } else if (ios_method_checker('refreshUserCounters')) {
        return window.webkit.messageHandlers.refreshUserCounters.postMessage({})
    } else {
        console.info("无法刷新用户信息", 0.5);
    }
}

/**
 * 该方法会根据主播是否通过认证进行页面的二次跳转(不关闭当前页),通过认证跳转直播预览页，未通过跳转认证页
 */
export function jumpToLiveAnchor() {
    if (android_method_checker("jumpToLiveAnchor")) {
        window.tantan.jumpToLiveAnchor();
    } else if (oldIOS_method_checker("jumpToLiveAnchor")) {
        window.jumpToLiveAnchor();
    } else if (ios_method_checker('jumpToLiveAnchor')) {
        return window.webkit.messageHandlers.jumpToLiveAnchor.postMessage({})
    } else {
        console.info("当前版本无法直接开播，请返回小助手页面点击开播", 0.5);
    }
}


/**
 * 该方法用于调用schema
 */
export function action(schema: string) {
    if (android_method_checker("action")) {
        window.tantan.action(schema);
    } else if (oldIOS_method_checker("action")) {
        window.action(schema);
    } else if (ios_method_checker('action')) {
        return window.webkit.messageHandlers.action.postMessage({ url: schema })
    } else {
        console.info("无法调用action", 0.5);
    }
}


/**
 * 跳转到profile页面
 */
export function jumpToProfile(userId: string, from: string) {
    if (android_method_checker("jumpToProfile")) {
        window.tantan.jumpToProfile(userId, from);
    } else if (oldIOS_method_checker("jumpToProfile")) {
        window.jumpToProfile(userId, from);
    } else if (ios_method_checker('jumpToProfile')) {
        return window.webkit.messageHandlers.jumpToProfile.postMessage({ userId, from })
    } else {
        console.info("跳转到profile页面失败", 0.5);
    }
}

/**
 * 跳转到直播房间页面
 */
export function jumpToRoom(liveId: string, roomId: string, source: string) {
    if (android_method_checker("jumpToRoom")) {
        window.tantan.jumpToRoom(liveId, roomId, source);
    } else if (oldIOS_method_checker("jumpToRoom")) {
        window.jumpToRoom(liveId, roomId, source);
    } else if (ios_method_checker('jumpToRoom')) {
        return window.webkit.messageHandlers.jumpToRoom.postMessage({ liveId, roomId, source })
    } else {
        console.info("跳转到直播房间页面失败", 0.5);
    }
}




/**
 * 通知客户端红包已抢
 */
export function onRedPacketOpen(ismarqueeredpacket: boolean) {
    if (android_method_checker("onRedPacketOpen")) {
        window.tantan.onRedPacketOpen(ismarqueeredpacket);
    } else if (oldIOS_method_checker("onRedPacketOpen")) {
        window.onRedPacketOpen(ismarqueeredpacket);
    } else if (ios_method_checker('onRedPacketOpen')) {
        return window.webkit.messageHandlers.onRedPacketOpen.postMessage({ ismarqueeredpacket })
    } else {
        console.info("无法调用onRedPacketOpen", 0.5);
    }
}


/**
 *
 */
interface ISubscribeParams {
    subscribeType: string,
    successHandler?: (res: string) => void,
    subscribeHandler: (res: string) => void,
}
export function subscribeCampaign(params: ISubscribeParams) {
    const { subscribeType, subscribeHandler, successHandler = noop } = params
    const time = Math.floor(Math.random() * 100)
    if (android_method_checker("subscribeCampaign")) {
        window[`successHandler${time}`] = successHandler;
        window[`handler${time}`] = subscribeHandler;
        window.tantan.subscribeCampaign(subscribeType, `successHandler${time}`, `handler${time}`);
        return true;
    } else if (oldIOS_method_checker("subscribeCampaign")) {
        window.subscribeCampaign(subscribeType, successHandler, subscribeHandler);
        return true;
    } else if (ios_method_checker('subscribeCampaign')) {
        const time = Math.floor(Math.random() * 100)
        window[`successHandler${time}`] = successHandler;
        window[`subscribeHandler${time}`] = subscribeHandler;
        window.webkit.messageHandlers.subscribeCampaign.postMessage({
            subscribeType,
            successHandler: `successHandler${time}`,
            subscribeHandler: `subscribeHandler${time}`,
        })
        return true;
    } else {
        console.info("无法订阅", 0.5);
        return false;
    }
}

/**
 * 调起充值面板
 */
export function showRechargeDialog() {
    if (android_method_checker("showRechargeDialog")) {
        window.tantan.showRechargeDialog();
    } else if (oldIOS_method_checker("showRechargeDialog")) {
        window.showRechargeDialog();
    } else if (ios_method_checker('showRechargeDialog')) {
        return window.webkit.messageHandlers.showRechargeDialog.postMessage({})
    } else {
        console.info("无法刷新用户信息", 0.5);
    }
}

/**
 * 直播间送礼面板控制
 */
export function liveGiftDialogController(
    type: 'showLiveGiftBoard' | 'sendGift',
    content = '',
    callBack = noop,
) {
    const time = Math.floor(Math.random() * 100)
    window[`liveGiftDialogControllerCallBack${time}`] = callBack;
    if (android_method_checker("liveGiftDialogController")) {
        window.tantan.liveGiftDialogController(type, content, `liveGiftDialogControllerCallBack${time}`);
    } else if (oldIOS_method_checker("liveGiftDialogController")) {
        window.liveGiftDialogController(type, content, callBack);
    } else if (ios_method_checker('liveGiftDialogController')) {
        return window.webkit.messageHandlers.liveGiftDialogController.postMessage({
            type,
            content,
            callBack: `liveGiftDialogControllerCallBack${time}`
        })
    } else {
        console.info("无法调用liveGiftDialogController", 0.5);
    }
}

/** 新手红包相关操作
 * type: startCountDown 开始倒计时
 * type: getStatusAndSeconds 提供观看直播秒数的
 * type: refreshStatus 刷新红包状态（消失/刷新状态）
 */

export function liveNewUserRedPacketController(
    type: 'startCountDown' | 'getStatusAndSeconds' | 'refreshStatus',
    content: string,
    callBack?: (status: string, second: string) => void,
) {
    console.log('count', type, content)
    const time = Math.floor(Math.random() * 100)
    window[`liveNewUserRedPacketControllerCallBack${time}`] = callBack;
    if (android_method_checker("liveNewUserRedPacketController")) {
        window.tantan.liveNewUserRedPacketController(type, content, `liveNewUserRedPacketControllerCallBack${time}`);
    } else if (oldIOS_method_checker("liveNewUserRedPacketController")) {
        window.liveNewUserRedPacketController(type, content, callBack);
    } else if (ios_method_checker('liveNewUserRedPacketController')) {
        window.webkit.messageHandlers.liveNewUserRedPacketController.postMessage({
            type,
            content,
            callBack: `liveNewUserRedPacketControllerCallBack${time}`
        })
    } else {
        console.info("无法调用liveNewUserRedPacketController", 0.5);
    }
}


/**
 * ios中jumpToTopicAggregationAct和jumpToTopicVoteAggregationAct均通过jumpToTopic实现
 */
interface ITopicParam {
    topicID: number,
    from: string
}
export function jumpToTopic(params: ITopicParam) {
    const { topicID, from } = params
    if (ios_method_checker('jumpToTopic')) {
        return window.webkit.messageHandlers.jumpToTopic.postMessage({ topicID, from })
    }
    // if (oldIOS_method_checker("jumpToTopicAggregationAct")) {
    //     window.jumpToTopicAggregationAct(topicID, from);
    // }
    // if (oldIOS_method_checker("jumpToTopicVoteAggregationAct")) {
    //     window.jumpToTopicVoteAggregationAct(id, topicOwnerId, '-1');
    // }
}

export const jumpTantanxDownload = () => {
    if (android_method_checker("jumpTantanxDownload")) {
        window.tantan.jumpTantanxDownload();
    } else if (oldIOS_method_checker("jumpTantanxDownload")) {
        window.jumpTantanxDownload();
    } else if (ios_method_checker('jumpTantanxDownload')) {
        return window.webkit.messageHandlers.jumpTantanxDownload.postMessage({ })
    } else {
        console.info("can't jumpTantanxDownload", 0.5);
    }
}


// 跳转每日话题
export function jumpToTopicAggregationAct(id: number) {
    if (android_method_checker("jumpToTopicAggregationAct")) {
        window.tantan.jumpToTopicAggregationAct(id);
    } else {
        console.info("跳转到每日话题页面失败", 0.5);
    }
}
// 跳转投票
export function jumpToTopicVoteAggregationAct(id: number, topicOwnerId: number) {
    if (android_method_checker("jumpToTopicVoteAggregationAct")) {
        window.tantan.jumpToTopicVoteAggregationAct(id, topicOwnerId, '-1');
    } else {
        console.info("跳转投票失败", 0.5);
    }
}

// 跳转个人资料页
export function jumpToProfileAct(userId: number, from: string) {
    if (android_method_checker("jumpToProfileAct")) {
        window.tantan.jumpToProfileAct(userId, from);
    } else if (oldIOS_method_checker("jumpToProfileAct")) {
        window.jumpToProfileAct(userId, from);
    } else if (ios_method_checker('jumpToProfileAct')) {
        return window.webkit.messageHandlers.jumpToProfileAct.postMessage({ userId, from })
    } else {
        console.info("跳转到每日话题页面失败", 0.5);
    }
}

// 跳转聊天页
export function startMessagesAct(id: number, from: string) {
    // 114093和115521是好友
    if (android_method_checker("startMessagesAct")) {
        window.tantan.startMessagesAct(id, from);
    } else if (oldIOS_method_checker("startMessagesAct")) {
        window.startMessagesAct(id, from);
    } else if (ios_method_checker('action')) {
        return window.webkit.messageHandlers.action.postMessage({ url: `tantanapp://chat?uid=${id}&from=${from}` })
    } else if (ios_method_checker('openWebview') && ios_method_checker('jumpWebview')) { // 增加jumpWebview的判断用来辨识是否为新版本
        return window.webkit.messageHandlers.openWebview.postMessage({ url: `tantanapp://chat?uid=${id}&from=${from}` })
    } else {
        console.info("跳转到每日话题页面失败", 0.5);
    }
}


export function jumpToSeeOrBuySee() {
    const fnName = 'jumpToSeeOrBuySee';
    if (android_method_checker(fnName)) {
        window.tantan[fnName]();
    } else if (oldIOS_method_checker(fnName)) {
        window[fnName]();
    } else if (ios_method_checker(fnName)) {
        return window.webkit.messageHandlers[fnName].postMessage({})
    } else {
        console.info("跳转至see页面失败", 0.5);
    }
}


/**
 * 自定义右上角按钮
 */
export const registerBarRight = (method: string = 'mytantan', cb: () => void) => {
    const fnName = 'registerBarRight';
    const uid = genUuid();
    window[`registerBarRight${uid}`] = cb;
    if (android_method_checker('registerBarRight')) {
        window.tantan[fnName](method, `registerBarRight${uid}`)
    } else if (oldIOS_method_checker(fnName)) {
        window[fnName](method, `registerBarRight${uid}`)
    } else if (ios_method_checker(fnName)) {
        window.webkit.messageHandlers[fnName].postMessage({
            key: method,
            callback: `registerBarRight${uid}`,
        })
    }
};



export function unRegisterBarRight() {
    const fnName = 'unRegisterBarRight'
    if (android_method_checker(fnName)) {
        window.tantan[fnName]();
    } else if (oldIOS_method_checker(fnName)) {
        window[fnName]();
    } else if (ios_method_checker(fnName)) {
        return window.webkit.messageHandlers[fnName].postMessage({})
    } else {
        console.info("无法卸载右上角按钮", 0.5);
    }
}

/**
 * 选取图片
 *
 * 1、如果 backMethod 为空，默认回调 adtp。
 * 2、如果图片上传成功，error 为 undefined
 * 3、如果用户取消操作，error 为 canceled
 * 4、如果用户选取的格式不支持，error 为 unknown
 * 5、如果上传图片失败，error 为对应的错误信息
 *
 */
export function imagePicker(callBack: (url: string, error: string) => void) {
    const time = Math.floor(Math.random() * 100)
    if (android_method_checker("imagePicker")) {
        window[`imagePickerCallBack${time}`] = callBack;
        window.tantan.imagePicker(`imagePickerCallBack${time}`);
    } else if (oldIOS_method_checker("imagePicker")) {
        window.imagePicker(callBack);
    } else if (ios_method_checker('imagePicker')) {
        window[`imagePickerCallBack${time}`] = callBack;
        window.webkit.messageHandlers.imagePicker.postMessage({
            success: `imagePickerCallBack${time}`
        })
    } else {
        console.info("无法选取图片", 0.5);
    }
}

/**
 * 设置导航栏标题
 * 注: newIos条件下容器会捕获页面的document.title属性并设置为导航栏标题
 * @param title
 */
export function setTitle(title: string) {
    const fnName = 'setTitle';
    if (android_method_checker(fnName)) {
        window.tantan[fnName](title)
    } else if (oldIOS_method_checker(fnName)) {
        window[fnName](title)
    }
    return '黑钻会员'
}


/**
 * 打开活动落地页or用户资料卡
 * 1，弹起用户资料卡片（action="showUserProfileCard" , content=userId）
 * 2，弹出某个活动(action="alertCampaign", content=活动 id)
 * @param action
 * @param content
 * @Param callBack
 */
export function campaignController(action: string, content: string | number, callBack?: (v: any) => void) {
    const uid = genUuid();
    const fnName = 'campaignController';
    window[`${fnName}CallBack${uid}`] = callBack;
    if (android_method_checker(fnName)) {
        window.tantan[fnName](action, content, `${fnName}CallBack${uid}`);
    } else if (oldIOS_method_checker(fnName)) {
        window[fnName](action, content, callBack)
    } else if (ios_method_checker(fnName)) {
        window.webkit.messageHandlers[fnName].postMessage({
            action,
            content,
            callBack: `${fnName}CallBack${uid}`,
        })
    } else {
        console.info("campaignController无法调用", 0.5);
    }
}


/**
 * 关注
 */
export function follow( params: {  otherUid: string,
                           source: string,
                            liveId: string,
                            success: () => void,
                            fail: () => void,
                            isFollow?: boolean,
                        }
                       ) {
    const uid = genUuid();
    const fnName = 'follow';
    const { otherUid, source, liveId, success, fail, isFollow } = params;
    const isNewFollow = Object.keys(params).indexOf('isFollow') > -1; // 新的follow方法在客户端函数名相同，参数多了isFollow
    console.log(`is newFollow ${isNewFollow}`);
    if (android_method_checker(fnName)) {
        window[`successFollow${uid}`] = success;
        window[`failFollow${uid}`] = fail;
        if (isNewFollow) {
            window.tantan.follow(otherUid, source, liveId, `successFollow${uid}`, `failFollow${uid}`);
        } else {
            window.tantan.follow(otherUid, source, liveId, `successFollow${uid}`, `failFollow${uid}`, isFollow);
        }
    } else if (oldIOS_method_checker(fnName)) {
        window[fnName](otherUid, source, liveId, `successFollow${uid}`, `failFollow${uid}`);
    } else if (ios_method_checker('follow')) {
        return new Promise((resolve, reject) => {
            window[`followSuccess${uid}`] = (res: string) => {
                success();
                resolve(res)
            }
            window[`followFailed${uid}`] = (err: any) => {
                fail();
                reject(err)
            };
            const iosParams: any = {
                otherUid,
                source,
                liveId,
                success: `followSuccess${uid}`,
                fail: `followFailed${uid}`,
            };
            if (isNewFollow) {
                iosParams.isFollow = isFollow;
            }
            window.webkit.messageHandlers[fnName].postMessage(iosParams)
        })
    } else {
        console.info("无法关注", 0.5);
    }
}


/**
 * 关注
 */
export function followAndUnfollow(otherUid: string, source: string, liveId: string,
                       successHandler: () => void  = noop,
                       failHandler: () => void = noop,
                       isFollow: boolean
) {
    const uid = genUuid();
    const fnName = 'follow';
    if (android_method_checker(fnName)) {
        window[`successFollow${uid}`] = successHandler;
        window[`failFollow${uid}`] = failHandler;
        window.tantan.follow(otherUid, source, liveId, `successFollow${uid}`, `failFollow${uid}`, isFollow);
    } else if (oldIOS_method_checker(fnName)) {
        window[fnName](otherUid, source, liveId, `successFollow${uid}`, `failFollow${uid}`, isFollow);
    } else if (ios_method_checker(fnName)) {
        return new Promise((resolve, reject) => {
            window[`followSuccess${uid}`] = (res: string) => {
                successHandler()
                resolve(res)
            }
            window[`followFailed${uid}`] = (err: any) => {
                failHandler()
                reject(err)
            }
            window.webkit.messageHandlers[fnName].postMessage({
                otherUid,
                source,
                liveId,
                success: `followSuccess${uid}`,
                fail: `followFailed${uid}`,
                isFollow,
            })
        })
    } else {
        console.info("无法关注", 0.5);
    }
}
