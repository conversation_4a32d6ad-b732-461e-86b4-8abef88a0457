export interface IBaseHandler {
  successHandler?: () => any, // 设置成功的回调函数
  errorHandler?: () => any, //设置失败的回调函数
}

export interface IUserInfo {
  userId: string,
  userName: string,
  token: string,
  gender: string,
  avatarUrl: string,
  zodiac: string,
  age: number,
  phoneNumber: string
}

type channelType = '' | 'mo' | 'wx' | 'wb' | 'qq' | 'qz'

export interface IShareBaseParam {
  url: string,
  title: string,
  imgUrl: string,
  description?: string,
  // successHandler?: () => void,
  // errorHandler?: () => void,
}

export interface IShareImageParam {
  pic: string,
  platform: channelType
}

export interface IShareParam extends IShareBaseParam {
  channel: channelType,
}

export interface INativeShareParam extends IShareBaseParam {
  modalTitle: string,
  channels: channelType,
}

export interface ISystemInfo {
  os: string, //（操作系统）
  osVersion: string, //（操作系统版本）
  brand: string, //（设备品牌）
  model: string,//（设备型号）
  imei: string, //（移动设备识别码）
  pixelRatio: number,//（设备像素比）
  screenWidth: number, //（屏幕宽度）
  screenHeight: number, //（屏幕高度）
  windowWidth: number, //（可使用窗口宽度）
  windowHeight: number, //（可使用窗口高度）
  appVersion: string, //（软件版本）
  sdkVersion: string,// （JSBridge版本）
  versionCode: string,// 软件版本 => 取appVersion的值
  language: string, // 语言版本
  browserVersion: string, // webview版本
  deviceId: string, // 设备token
  oneId: string, //设备唯一ID
  platform: string // 操作系统 => 取os的值
  token: string //
}

export interface ICityInfo {
  cityName: string,
  cityId: number,
}


export interface INavigationParams {
  title: string,            // 导航栏标题
  handler?: () => any,      // 导航栏点击的handler
  leftImgUrl?: string,      // 导航栏左侧按钮的图片url
  leftText?: string,        // 导航栏左侧按钮的文案(文案or图片两者只能存在一个)
  leftHandler?: () => any,  // 导航栏左侧点击的handler
  rightImgUrl?: string,     // 导航栏右侧按钮的图片url
  rightText?: string,       // 导航栏右侧侧按钮的文案(文案or图片两者只能存在一个)
  rightHandler?: () => any, // 导航栏右侧点击的handler
  skipBack?: boolean,       // 是否跳过默认返回功能，false：默认返回，true：阻止返回事件
}

export interface INavigationTitleParams extends IBaseHandler {
  title: string, // (标题)
  handler?: () => any, // (点击标题后的回调)
}

export interface INavButtonParams {
  imgUrl?: string, //（图标的url）
  text?: string, //（文案）
  handler?: () => any, // (点击按钮后的回调)
}


export interface IToastParams {
  context: string,
  duration?: number,
}

export interface IStorageParams {
  key: string, // 键名
  data: string | number | boolean, // 存储的数据
}

export interface IStorageRemoveParams {
  key: string, // 键名
}


export interface IActionParams {
  actionType: string,
  restParams?: any,
}

export interface ISubscribeParams {
  subscribeType: string,
  subscribeHandler: () => any,
}


export interface TantanBean {
  /** 协议版本 */
  schemeVersion: string;
  /** 设备id */
  deviceId: string;
  /** 语言 */
  language: string;
  /** 用户ID */
  userId: string;
  /** 客户端版本 */
  versionCode: string;
}

export interface ZhiMa {
  /** 身份证姓名 */
  name: string;
  /** 身份证号 */
  id: string;
  /** 绑定成功回调函数 */
  successZhima: () => void;
  /** 绑定失败回调函数 */
  failZhima: (res: any) => void;
}

export enum AlipayError {
  PUGWalletServiceInvalidParametersError = 41901,
  PUGWalletServiceBalanceNotEnoughError,
  PUGWalletServiceNoZhimaAuthError,
  PUGWalletServiceWithdrawLimitedError,
  PUGWalletServiceAlreadyAlipayAuthError,
  PUGWalletServiceAlipayAuthBindByOthersError,
  PUGWalletServiceUnsupportedIntentError,
  PUGWalletServiceAlreadyZhiMaAuthedError,
  PUGWalletServiceZhimaAuthBindByOthersError,
  PUGWalletServiceNoAlipayAuthError,
  PUGWalletServiceZhiMaAuthFailedError,
  PUGWalletServiceMetaCertificateInitFailed,
  PUGWalletServiceIsInBlacklist = 41917,
  PUGWalletServiceUnknownError = 1000000,
  PUGWalletServiceAlipayNotInstalledError,
  NetworkError = 1,
}

export interface IAlipayParams {
  alipayFlag?: boolean;
  /** 绑定成功回调函数 */
  successAlipay: () => void;
  /** 绑定失败回调函数 */
  failAlipay: (res: string) => void;
}

export interface IDeviceNotchInfo {
  isNotch: boolean,
  notchHeight: number,
  statusBarHeight: number,
}

export interface IShareMoment {
  data: string,
  type: 'img' | 'text'
}
