import {
  android_method_checker,
  ios_method_checker,
  genUuid,
  throwErr,
  checkIsFn,
  oldIOS_method_checker,
  handleAndroidParamArr,
  customConsole,
  isAndroid, isiOS,
} from "./utils"
import {
  IUserInfo, ISystemInfo, ICityInfo, IShareParam,
  INavigationParams, INavigationTitleParams, INavButtonParams,
  IToastParams, IStorageParams, IStorageRemoveParams, IActionParams, ISubscribeParams,
  ZhiMa, INativeShareParam, IAlipayParams, IShareImageParam, IDeviceNotchInfo, IShareMoment,
} from './interface'
import oldBridgeHandler from './oldBridgeHandler'

const androidMethodAccessible = (methodName: string) => {
  return window && window.tantan && typeof window.tantan.canIUse === 'function' && window.tantan.canIUse(methodName)
}

/**
 * 获取用户信息
 */
export const getUserInfo = (): Promise<IUserInfo> => {
  const fnName = 'getUserInfo'
  if (androidMethodAccessible(fnName)) {
    try {
      const sourceRes = window.tantan.dispatch(fnName, JSON.stringify([]))
      const res = sourceRes ? JSON.parse(sourceRes) : {} // 未登录情况下安卓返回空串
      return Object.keys(res).length === 0 ? Promise.reject('unknown user') : Promise.resolve(res as IUserInfo)
    } catch (e) {
      console.error('internal error')
    }
  }
  if (ios_method_checker(fnName)) {
    const uid = genUuid()
    return new Promise<IUserInfo>((resolve, reject) => {
      window[`${fnName}Success${uid}`] = (res: string) => {
        resolve(JSON.parse(res) as IUserInfo)
      }
      window[`${fnName}Fail${uid}`] = () => {
        reject('unknown user')
      }
      window.webkit.messageHandlers.getUserInfo.postMessage({
        success: `${fnName}Success${uid}`,
        fail: `${fnName}Fail${uid}`,
      })
    })
  }
  // 若为旧版sdk，则调用saveBean方法
  if (android_method_checker('saveBean') || ios_method_checker('saveBean') || oldIOS_method_checker('saveBean')) {
    return oldBridgeHandler('saveBean')
  }
  customConsole('no method matched for getUserInfo')
  return Promise.resolve({
    userId: "10086",
    userName: ''
  } as IUserInfo)
}


/** AB分组的header头 */
export const getAbHeader = (): Promise<string> => {
  const fnName = 'getAbHeader'
  if (isAndroid) {
    if (androidMethodAccessible(fnName)) {
      const res = window.tantan.dispatch(fnName, JSON.stringify([]))
      return Promise.resolve(res as string)
    } else if (android_method_checker(fnName)) {
      return Promise.resolve(window.tantan[fnName]() as string)
    }
  }
  if (oldIOS_method_checker(fnName)) {
    return Promise.resolve(window[fnName]() as string)
  }
  if (ios_method_checker(fnName)) {
    console.log('enter into method getAbHeader')
    return new Promise<string>((resolve, reject) => {
      const uid = genUuid()
      window[`${fnName}Success${uid}`] = (res: string) => {
        resolve(res)
      }
      window[`${fnName}Fail${uid}`] = (err: any) => {
        reject(err)
      }
      window.webkit.messageHandlers[fnName].postMessage({
        success: `${fnName}Success${uid}`,
        fail: `${fnName}Fail${uid}`,
      })
    })
  }
  return Promise.reject(`no method matched for ${fnName}`)
}


/**
 * 获取系统信息
 */
export const getSystemInfo = (): Promise<ISystemInfo> => {
  const fnName = 'getSystemInfo'
  if (androidMethodAccessible(fnName)) {
    const res = JSON.parse(window.tantan.dispatch(fnName, JSON.stringify([])))
    if (res.appVersion) {
      res.versionCode = res.appVersion
    }
    if (res.sdkVersion) {
      res.schemeVersion = res.sdkVersion
    }
    if (res.os) {
      res.platform = res.os
    }
    return Promise.resolve(typeof res === 'object' ? res as ISystemInfo : {} as ISystemInfo)
  }
  if (ios_method_checker(fnName)) {
    return new Promise((resolve, reject) => {
      const uid = genUuid()
      window[`${fnName}Success${uid}`] = (res: string) => {
        const result = JSON.parse(res)
        if (result.appVersion) {
          result.versionCode = result.appVersion
        }
        if (result.sdkVersion) {
          result.schemeVersion = result.sdkVersion
        }
        if (result.os) {
          result.platform = result.os
        }
        resolve(result as ISystemInfo)
      }
      window[`${fnName}Fail${uid}`] = (err: any) => {
        reject(err)
      }
      window.webkit.messageHandlers[fnName].postMessage({
        success: `${fnName}Success${uid}`,
        fail: `${fnName}Fail${uid}`,
      })
    })
  }
  // 若为旧版sdk，则调用saveBean方法
  if (android_method_checker('saveBean') || ios_method_checker('saveBean') || oldIOS_method_checker('saveBean')) {
    return oldBridgeHandler('saveBean') as Promise<ISystemInfo>
  }
  return Promise.reject(`no method matched for ${fnName}`)
}

/**
 * 获取网络信息
 */
export const getNetworkInfo = (): Promise<'wifi' | '5g' | '4g' | '3g' | '2g' | 'unknown' | 'NotReachable' | 'No connection' | 'offline' | 'none'> => {
  const fnName = 'getNetworkInfo'
  if (androidMethodAccessible(fnName)) {
    return Promise.resolve(window.tantan.dispatch(fnName, JSON.stringify([])))
  }
  if (ios_method_checker(fnName)) {
    return new Promise((resolve, reject) => {
      const uid = genUuid()
      window[`${fnName}Success${uid}`] = (res: string) => {
        const parsed = JSON.parse(res)
        parsed.networkType && resolve(parsed.networkType)
      }
      window[`${fnName}Fail${uid}`] = (err: any) => {
        reject(err)
      }
      window.webkit.messageHandlers[fnName].postMessage({
        success: `${fnName}Success${uid}`,
        fail: `${fnName}Fail${uid}`,
      })
    })
  }
  return Promise.reject(`no method matched for ${fnName}`)
}

/**
 * 新开webview
 * @param urlOrSchema url地址或schema地址
 * @param title webview标题
 */
export const openWebview = (urlOrSchema: string, title: string = ''): void => {
  if (!urlOrSchema) {
    throwErr('param url is REQUIRED!')
  }
  const fnName = 'openWebview'
  if (androidMethodAccessible(fnName)) {
    return window.tantan.dispatch(fnName, JSON.stringify(handleAndroidParamArr([urlOrSchema, title])))
  }
  if (ios_method_checker(fnName) && ios_method_checker('jumpWebview')) { // 新老版本都存在openwebview的情况，故增加用是否存在jumpWebview来区分是否为新版,若后续需要更精细化的区分，则调用获取系统信息来判断版本号
    const paramBody = /http/g.test(urlOrSchema) ? {url: urlOrSchema, title} : {schema: urlOrSchema, title}
    return window.webkit.messageHandlers[fnName].postMessage(paramBody)
  }
  // 兼容老版本没有openWebview，针对指定的schema调用老版的bridge方法
  // const migrateSchemas = [
  //     'tantanapp://profile/edit',
  //     'tantanapp://vip/vip/buy',
  //     'tantanapp://vip/boost/buy',
  //     'tantan://liveCover',
  // ]
  if ((android_method_checker('action') || ios_method_checker('action') || oldIOS_method_checker('action'))) {
    oldBridgeHandler('action', {schema: urlOrSchema})
  }
  customConsole('no method matched for openWebview')
}

/**
 * 新开webview同时关闭当前webview
 * @param urlOrSchema url地址或schema地址
 * @param title webview标题
 */
export const jumpWebview = (urlOrSchema: string, title: string = ''): void => {
  if (!urlOrSchema) {
    throwErr('param url is REQUIRED!')
  }
  const fnName = 'jumpWebview'
  if (androidMethodAccessible(fnName)) {
    return window.tantan.dispatch(fnName, JSON.stringify(handleAndroidParamArr([urlOrSchema, title])))
  }
  if (ios_method_checker(fnName)) {
    const paramBody = /http/g.test(urlOrSchema) ? {url: urlOrSchema, title} : {schema: urlOrSchema, title}
    window.webkit.messageHandlers[fnName].postMessage(paramBody)
  }
  customConsole('no method matched for jumpWebview')
}


/**
 * 关闭当前webview
 */
export const closeWebview = (): void => {
  const fnName = 'closeWebview'
  if (androidMethodAccessible(fnName)) {
    return window.tantan.dispatch(fnName, JSON.stringify([]))
  }
  if (ios_method_checker(fnName)) {
    window.webkit.messageHandlers[fnName].postMessage({})
    return
  }
  if (android_method_checker(fnName) || oldIOS_method_checker(fnName)) {
    return oldBridgeHandler(fnName)
  }
  customConsole(`no method matched for ${fnName}`)
}


/**
 * 关闭当前webview(弹窗类)
 */
export const closeDialogWebview = (): void => {
  const fnName = 'closeDialogWebview'
  if (androidMethodAccessible(fnName)) {
    return window.tantan.dispatch(fnName, JSON.stringify([]))
  }
  if (ios_method_checker(fnName)) {
    return window.webkit.messageHandlers[fnName].postMessage({})
  }
  if (android_method_checker('closeLiveCampaignDialog') || ios_method_checker('closeLiveCampaignDialog')) {
    return oldBridgeHandler('closeLiveCampaignDialog')
  }
  customConsole(`no method matched for ${fnName}`)

}


/**
 * 获取城市信息
 */
export const getCityInfo = (): Promise<ICityInfo> => {
  const fnName = 'getCityInfo'
  if (androidMethodAccessible(fnName)) {
    const res = JSON.parse(window.tantan.dispatch(fnName, JSON.stringify([])))
    return Promise.resolve(typeof res === 'object' ? res : {}) as Promise<ICityInfo>
  }
  if (ios_method_checker(fnName)) {
    return new Promise((resolve, reject) => {
      const uid = genUuid()
      window[`${fnName}Success${uid}`] = (res: string) => {
        resolve(JSON.parse(res))
      }
      window[`${fnName}Fail${uid}`] = (err: any) => {
        reject(err)
      }
      window.webkit.messageHandlers[fnName].postMessage({
        success: `${fnName}Success${uid}`,
        fail: `${fnName}Fail${uid}`
      })
    })
  }
  return Promise.reject(`no method matched for ${fnName}`)
}


/**
 * 分享（需要指定分享渠道）
 * qq渠道的share的img有大小限制，估计是50kb，大了会调用失败
 * @param params
 */
export const share = (params: IShareParam): Promise<any> => {
  const {url, title = '', description = '', imgUrl, channel} = params;
  if (!channel) {
    throw 'param is invalid'
  }
  const fnName = 'share';
  if (isAndroid) {
    const uid = genUuid();
    if (androidMethodAccessible(fnName)) {
      return new Promise<void>((resolve, reject) => {
        window[`success${uid}`] = () => {
          resolve();
        };
        window[`error${uid}`] = (e: any) => {
          reject(e)
        };
        const paramArr = JSON.stringify(handleAndroidParamArr([url, title, description, imgUrl, channel, `success${uid}`, `error${uid}`]))
        window.tantan.dispatch(fnName, paramArr)
      })
    } else if (android_method_checker(fnName)) {
      return new Promise<void>((resolve, reject) => {
        window[`success${uid}`] = () => {
          resolve();
        };
        window.tantan.share(url, title, description, imgUrl, channel, `success${uid}`)
      })
    }
  }
  if (ios_method_checker(fnName)) {
    return new Promise((resolve, reject) => {
      console.log('share');
      const uid = genUuid();
      window[`success${uid}`] = () => {
        resolve()
      };
      window[`error${uid}`] = (e: any) => {
        reject(e)
      };
      let img = imgUrl;
      if (url && channel === 'wb') img = '';
      console.log({
        url,
        title,
        shareTitle: title,
        description,
        imgUrl: img,
        pic: img,
        channel,
        success: `success${uid}`,
        fail: `error${uid}`
      })
      window.webkit.messageHandlers[fnName].postMessage({
        url,
        title,
        shareTitle: title,
        description,
        imgUrl: img,
        pic: img,
        channel,
        success: `success${uid}`,
        fail: `error${uid}`
      });
    })

  }
  return Promise.reject(`no method matched for ${fnName}`)
};


/**
 * 弹窗分享
 */
export const nativeShare = (params: INativeShareParam): Promise<void> => {
  const {url, title = '', description = '', imgUrl, modalTitle = '', channels = ''} = params
  if (!url) {
    throwErr('param is invalid')
  }
  const fnName = 'nativeShare'
  const uid = genUuid()

  if (androidMethodAccessible(fnName)) {
    return new Promise((resolve, reject) => {
      window[`success${uid}`] = () => {
        resolve()
      }
      window[`error${uid}`] = () => {
        reject()
      }
      const paramArr = JSON.stringify(handleAndroidParamArr([url, title, description, imgUrl, `success${uid}`, `error${uid}`, modalTitle, channels]))
      window.tantan.dispatch(fnName, paramArr)
    })

  }
  if (ios_method_checker(fnName)) {
    return new Promise((resolve, reject) => {
      window[`success${uid}`] = () => {
        resolve()
      }
      window[`error${uid}`] = () => {
        reject()
      }
      window.webkit.messageHandlers[fnName].postMessage({
        url,
        title,
        shareTitle: title,
        description,
        imgUrl,
        pic: imgUrl,
        success: `success${uid}`,
        fail: `error${uid}`,
        modalTitle,
        dialogTitle: modalTitle,
        channels,
        platform: channels,
      })
    })

  }
  return Promise.reject(`no method matched for ${fnName}`)
}


/**
 * 设置导航栏
 */
export const setNavigation = (params: INavigationParams): Promise<void> => {
  const {
    title = '',
    handler,
    leftImgUrl = '',
    leftText = '',
    skipBack = false,
    leftHandler,
    rightImgUrl = '',
    rightText = '',
    rightHandler
  } = params
  if (!checkIsFn([handler || Function.prototype, leftHandler || Function.prototype, rightHandler || Function.prototype])) {
    return throwErr('handler must be function')
  }
  if (!title) {
    return throwErr('param title is REQUIRED')
  }
  const fnName = 'setNavigation'
  const uid = genUuid()
  window[`leftHandler${uid}`] = leftHandler
  window[`rightHandler${uid}`] = rightHandler
  window[`handler${uid}`] = handler
  if (androidMethodAccessible(fnName)) {
    return new Promise((resolve, reject) => {
      window[`success${uid}`] = () => {
        resolve()
      }
      window[`error${uid}`] = () => {
        reject()
      }
      const paramArr = JSON.stringify(handleAndroidParamArr([title, `handler${uid}`, leftImgUrl, leftText && leftImgUrl ? '' : leftText, `leftHandler${uid}`, rightImgUrl, rightText && rightImgUrl ? '' : rightText, `rightHandler${uid}`, `success${uid}`, `error${uid}`, skipBack]))
      window.tantan.dispatch(fnName, paramArr)
    })
  }
  if (ios_method_checker(fnName)) {
    return new Promise((resolve, reject) => {
      window[`success${uid}`] = () => {
        resolve()
      }
      window[`error${uid}`] = () => {
        reject()
      }
      window.webkit.messageHandlers[fnName].postMessage({
        title,
        handler: `handler${uid}`,
        leftImgUrl,
        leftText: leftText && leftImgUrl ? '' : leftText,
        leftHandler: `leftHandler${uid}`,
        rightImgUrl,
        rightText: rightText && rightImgUrl ? '' : rightText,
        rightHandler: `rightHandler${uid}`,
        skipBack,
        success: `success${uid}`,
        fail: `error${uid}`
      })
    })

  }
  return Promise.reject(`no method matched for ${fnName}`)
}

/**
 * 设置导航栏标题
 * @param params
 */
export const setNavigationTitle = (params: INavigationTitleParams): Promise<void> => {
  const {title = '', handler = Function.prototype} = params
  if (!checkIsFn([handler])) {
    return throwErr('handler must be function')
  }
  if (!title) {
    return throwErr('param title is REQUIRED')
  }
  const fnName = 'setNavigationTitle'
  const uid = genUuid()
  window[`handler${uid}`] = handler
  if (androidMethodAccessible(fnName)) {
    return new Promise((resolve, reject) => {
      window[`success${uid}`] = () => {
        resolve()
      }
      window[`error${uid}`] = () => {
        reject()
      }
      const paramArr = JSON.stringify(handleAndroidParamArr([title, `handler${uid}`, `success${uid}`, `error${uid}`]))
      window.tantan.dispatch(fnName, paramArr)
    })
  }
  if (ios_method_checker(fnName)) {
    return new Promise((resolve, reject) => {
      window[`success${uid}`] = () => {
        resolve()
      }
      window[`error${uid}`] = () => {
        reject()
      }
      window.webkit.messageHandlers[fnName].postMessage({
        title,
        handler: `handler${uid}`,
        success: `success${uid}`,
        fail: `error${uid}`
      })
    })

  }
  if (android_method_checker('setTitle') || oldIOS_method_checker('setTitle')) {
    oldBridgeHandler('setTitle', title)
  }
  return Promise.reject(`no method matched for ${fnName}`)
}

/**
 * 隐藏导航栏
 */
export const hideNavigation = (): Promise<void> => {
  const fnName = 'hideNavigation'
  const uid = genUuid()
  if (androidMethodAccessible(fnName)) {
    return new Promise((resolve, reject) => {
      window[`success${uid}`] = () => {
        resolve()
      }
      window[`error${uid}`] = () => {
        reject()
      }
      const paramArr = JSON.stringify(handleAndroidParamArr([`success${uid}`, `error${uid}`]))
      window.tantan.dispatch(fnName, paramArr)
    })

  }
  if (ios_method_checker(fnName)) {
    return new Promise((resolve, reject) => {
      window[`success${uid}`] = () => {
        resolve()
      }
      window[`error${uid}`] = () => {
        reject()
      }
      window.webkit.messageHandlers[fnName].postMessage({
        success: `success${uid}`,
        fail: `error${uid}`
      })
    })
  }
  return Promise.reject(`no method matched for ${fnName}`)
}

/**
 * 设置导航栏左侧按钮
 * @param params
 */
export const setNavLeftButton = (params: INavButtonParams): Promise<void> => {
  const {text = '', imgUrl = '', handler = Function.prototype} = params
  if (!checkIsFn([handler])) {
    return throwErr('handler must be function')
  }
  if (!text && !imgUrl) {
    throwErr('param missing')
  }
  const fnName = 'setNavLeftButton'
  const uid = genUuid()
  window[`handler${uid}`] = handler
  if (androidMethodAccessible(fnName)) {
    return new Promise((resolve, reject) => {
      window[`success${uid}`] = () => {
        resolve()
      }
      window[`error${uid}`] = () => {
        reject()
      }
      const paramArr = JSON.stringify(handleAndroidParamArr([!text && imgUrl ? imgUrl : '', text, `handler${uid}`, `success${uid}`, `error${uid}`]))
      window.tantan.dispatch(fnName, paramArr)
    })
  }
  if (ios_method_checker(fnName)) {
    return new Promise((resolve, reject) => {
      window[`success${uid}`] = () => {
        resolve()
      }
      window[`error${uid}`] = () => {
        reject()
      }
      window.webkit.messageHandlers[fnName].postMessage({
        text,
        imgUrl,
        handler: `handler${uid}`,
        success: `success${uid}`,
        fail: `error${uid}`
      })
    })

  }
  return Promise.reject(`no method matched for ${fnName}`)
}

/**
 * 设置导航栏右侧按钮
 * @param params
 */
export const setNavRightButton = (params: INavButtonParams): Promise<void> => {
  const {text = '', imgUrl = '', handler = Function.prototype} = params
  if (!checkIsFn([handler])) {
    return throwErr('handler must be function')
  }
  if (!text && !imgUrl) {
    throwErr('param missing')
  }
  const fnName = 'setNavRightButton'
  const uid = genUuid()
  window[`handler${uid}`] = handler
  if (androidMethodAccessible(fnName)) {
    return new Promise((resolve, reject) => {
      window[`success${uid}`] = () => {
        resolve()
      }
      window[`error${uid}`] = () => {
        reject()
      }
      const paramArr = JSON.stringify(handleAndroidParamArr([!text && imgUrl ? imgUrl : '', text, `handler${uid}`, `success${uid}`, `error${uid}`]))
      window.tantan.dispatch(fnName, paramArr)
    })
  }
  if (ios_method_checker(fnName)) {
    return new Promise((resolve, reject) => {
      window[`success${uid}`] = () => {
        resolve()
      }
      window[`error${uid}`] = () => {
        reject()
      }
      window.webkit.messageHandlers[fnName].postMessage({
        text,
        imgUrl,
        handler: `handler${uid}`,
        success: `success${uid}`,
        fail: `error${uid}`
      })
    })

  }
  return Promise.reject(`no method matched for ${fnName}`)
}


/**
 * 系统原生toast提醒
 * @param params
 */
export const showToast = (params: IToastParams): void => {
  const {context, duration = 3000} = params
  if (!context) {
    return throwErr('param context is REQUIRED')
  }
  const fnName = 'showToast'
  if (androidMethodAccessible(fnName)) {
    const paramArr = JSON.stringify(handleAndroidParamArr([context, duration]))
    window.tantan.dispatch('showToast', paramArr)
    return
  }
  if (ios_method_checker(fnName)) {
    window.webkit.messageHandlers[fnName].postMessage({
      context,
      duration
    })
    return
  }
  customConsole(`no method matched for ${fnName}`)
}

/**
 * 设置指定键值的本地存储
 * @param params
 */
export const setStorage = (params: IStorageParams): Promise<void> => {
  const {key, data} = params
  const fnName = 'setStorage'
  if (!key || !data) {
    return throwErr('param key & data is REQUIRED')
  }
  const uid = genUuid()

  if (androidMethodAccessible(fnName)) {
    return new Promise((resolve, reject) => {
      window[`success${uid}`] = () => {
        resolve()
      }
      window[`error${uid}`] = () => {
        reject()
      }
      const paramArr = JSON.stringify(handleAndroidParamArr([key, data, `success${uid}`, `error${uid}`]))
      window.tantan.dispatch(fnName, paramArr)
    })

  }
  if (ios_method_checker(fnName)) {
    return new Promise((resolve, reject) => {
      window[`success${uid}`] = () => {
        resolve()
      }
      window[`error${uid}`] = () => {
        reject()
      }
      window.webkit.messageHandlers[fnName].postMessage({
        key,
        data,
        success: `success${uid}`,
        fail: `error${uid}`
      })
    })
  }
  return Promise.reject(`no method matched for ${fnName}`)
}

/**
 * 获取指定键值的本地存储
 * @param key
 */
export const getStorage = (key: string): Promise<void> => {
  const handleJsonString = (string: string) => {
    if (!string) {
      return ''
    }
    if (/^{.*}$/.test(string)) {
      return JSON.parse(string)
    }
    return string
  }
  if (!key) {
    return throwErr('param key is REQUIRED')
  }
  const fnName = 'getStorage'
  if (androidMethodAccessible(fnName)) {
    const paramArr = JSON.stringify(handleAndroidParamArr([key]))
    const res = window.tantan.dispatch('getStorage', paramArr)
    if (res === 'KEY_NOT_EXIST') {
      return Promise.reject('1')
    } else {
      return Promise.resolve(handleJsonString(res))
    }
  }
  if (ios_method_checker(fnName)) {
    return new Promise((resolve, reject) => {
      const uid = genUuid()
      window[`${fnName}Success${uid}`] = (res: string) => {
        resolve(handleJsonString(res))
      }
      window[`${fnName}Fail${uid}`] = (err: any) => {
        reject(err)
      }
      window.webkit.messageHandlers[fnName].postMessage({
        key,
        success: `${fnName}Success${uid}`,
        fail: `${fnName}Fail${uid}`
      })
    })
  }
  return Promise.reject(`no method matched for ${fnName}`)
}

/**
 * 移除指定键值的本地存储
 * @param params
 */
export const removeStorage = (params: IStorageRemoveParams): Promise<void> => {
  const {key} = params
  if (!key) {
    return throwErr('param key is REQUIRED')
  }
  const fnName = 'removeStorage'
  const uid = genUuid()
  if (androidMethodAccessible(fnName)) {
    return new Promise((resolve, reject) => {
      window[`success${uid}`] = () => {
        resolve()
      }
      window[`error${uid}`] = () => {
        reject()
      }
      const paramArr = JSON.stringify(handleAndroidParamArr([key, `success${uid}`, `error${uid}`]))
      window.tantan.dispatch(fnName, paramArr)
    })
  }
  if (ios_method_checker(fnName)) {
    return new Promise((resolve, reject) => {
      window[`success${uid}`] = () => {
        resolve()
      }
      window[`error${uid}`] = () => {
        reject()
      }
      window.webkit.messageHandlers[fnName].postMessage({
        key,
        success: `success${uid}`,
        fail: `error${uid}`
      })
    })

  }
  return Promise.reject(`no method matched for ${fnName}`)
}

/**
 * 清空本地存储
 */
export const clearStorage = (): Promise<void> => {

  const fnName = 'clearStorage'
  const uid = genUuid()

  if (androidMethodAccessible(fnName)) {
    return new Promise((resolve, reject) => {
      window[`success${uid}`] = () => {
        resolve()
      }
      window[`error${uid}`] = () => {
        reject()
      }
      const paramArr = JSON.stringify(handleAndroidParamArr([`success${uid}`, `error${uid}`]))
      window.tantan.dispatch(fnName, paramArr)
    })

  }
  if (ios_method_checker(fnName)) {
    return new Promise((resolve, reject) => {
      window[`success${uid}`] = () => {
        resolve()
      }
      window[`error${uid}`] = () => {
        reject()
      }
      window.webkit.messageHandlers[fnName].postMessage({
        success: `success${uid}`,
        fail: `error${uid}`
      })
    })

  }
  return Promise.reject(`no method matched for ${fnName}`)
}

/**
 * 客户端基于约定的消息完成对应的操作(比如刷新用户信息等)
 * @param params
 */
export const triggerAction = (params: IActionParams): any => {
  const {actionType, restParams = {}} = params
  if (!actionType) {
    return throwErr('param actionType is REQUIRED')
  }
  const fnName = 'triggerAction'
  const uid = genUuid()
  const handleRestParams = (params: any) => {
    const resParams: any = {}
    Object.keys(params).forEach(key => {
      if (typeof params[key] === 'function') {
        window[`${actionType}_${key}_${uid}`] = params[key]
        return resParams[key] = `${actionType}_${key}_${uid}`
      }
      resParams[key] = params[key]
    })
    return resParams
  }
  if (actionType === 'track' && restParams.params && Object.prototype.toString.call(restParams.params) === '[object Object]') {
    let kvs: string[] = []
    for (let key in restParams.params) {
      kvs = kvs.concat(String(key), String(restParams.params[key]))
    }
    restParams.params = kvs
  }
  if (android_method_checker('triggerAction')) {
    const filterParams = handleRestParams(restParams)
    // todo track的特殊逻辑是否要下放至业务项目
    const restParamsArr = Object.keys(filterParams).map((x) => filterParams[x])
    console.log(restParamsArr);
    const paramArr = JSON.stringify(handleAndroidParamArr(restParamsArr))
    console.log(paramArr);
    return window.tantan.triggerAction(actionType, paramArr)
  }
  if (ios_method_checker(fnName)) {
    console.log(handleRestParams(restParams))
    return window.webkit.messageHandlers[fnName].postMessage({
      actionType,
      ...handleRestParams(restParams),
    })
  }
  const migrateMethods = [
    'refreshLiveAuth', 'jumpRecharge', 'refreshGuildAuth', 'jumpToCover', 'onTeenModeEnable',
    'verifyTeenModePassword', 'dialogJumpRecharge', 'closeLiveCampaignDialog', 'refreshUserCounters',
    'jumpToLiveAnchor', 'jumpToProfile', 'jumpToRoom', 'onRedPacketOpen', 'subscribeCampaign', 'showRechargeDialog',
    'liveGiftDialogController', 'campaignController', 'track', 'jumpToSeeOrBuySee', 'unRegisterBarRight', 'registerBarRight', 'follow', 'liveNewUserRedPacketController',
    'imagePicker', 'jumpToTopicAggregationAct', 'jumpToTopicVoteAggregationAct', 'jumpToTopic', 'jumpToProfileAct', 'jumpTantanxDownload',
  ]
  if (~migrateMethods.indexOf(actionType) && (android_method_checker(actionType) || ios_method_checker(actionType) || oldIOS_method_checker(actionType))) {
    console.log('method triggerAction not exist, enter oldBridgeHandler')
    return oldBridgeHandler(actionType, restParams)
  }
  // IOS中track方法名为trackEvent
  if (actionType === 'track' && (ios_method_checker('trackEvent') || oldIOS_method_checker('trackEvent'))) {
    return oldBridgeHandler(actionType, restParams)
  }
  customConsole(`no method matched for ${fnName}`)
}

/**
 * 订阅(h5订阅指定type的消息，当事件触发时客户端调用订阅该消息的webview传入的回调函数)
 * @param params
 */
export const subscribe = (params: ISubscribeParams): Promise<void> => {
  const {subscribeType, subscribeHandler = Function.prototype} = params
  if (!subscribeType) {
    return throwErr('param subscribeType is REQUIRED')
  }
  if (!checkIsFn([subscribeHandler])) {
    return throwErr('handler must be function')
  }
  const fnName = 'subscribe'
  const uid = genUuid()

  window[`subscribeHandler${uid}`] = subscribeHandler
  if (androidMethodAccessible(fnName)) {
    return new Promise((resolve, reject) => {
      window[`success${uid}`] = () => {
        resolve()
      }
      window[`error${uid}`] = () => {
        reject()
      }
      const paramArr = JSON.stringify(handleAndroidParamArr([subscribeType, `subscribeHandler${uid}`, `success${uid}`, `error${uid}`]))
      window.tantan.dispatch(fnName, paramArr)
    })
  }
  if (ios_method_checker(fnName)) {
    return new Promise((resolve, reject) => {
      window[`success${uid}`] = () => {
        resolve()
      }
      window[`error${uid}`] = () => {
        reject()
      }
      window.webkit.messageHandlers[fnName].postMessage({
        subscribeType,
        success: `success${uid}`,
        fail: `error${uid}`,
        subscribeHandler: `subscribeHandler${uid}`,
      })
    })
  }
  return Promise.reject(`no method matched for ${fnName}`)
}

/**获取HMAC版的授权码
 * getAuthorizationHeader函数被重载, 在安卓下不可进行重命名
 */
export const getAuthorizationHeader = (url?: string, body?: any): Promise<string> => {
  const fnName = 'getAuthorizationHeader'
  if (isAndroid) {
    if (androidMethodAccessible(fnName)) {
      const paramArr = JSON.stringify(handleAndroidParamArr([url, body || '']))
      const res = window.tantan.dispatch(fnName, paramArr)
      return Promise.resolve(res)
    } else if (android_method_checker(fnName)) {
      if (body) {
        return Promise.resolve(window.tantan[fnName](url, JSON.stringify(body)))
      } else {
        return Promise.resolve(window.tantan[fnName](url))
      }
    }
  } else if (oldIOS_method_checker(fnName)) {
    if (body) {
      return window[fnName](url, JSON.stringify(body))
    } else {
      return window[fnName](url)
    }
  } else if (ios_method_checker(fnName)) {
    return new Promise((resolve, reject) => {
      const uid = genUuid()
      window[`${fnName}Success${uid}`] = (res: string) => {
        resolve(res)
      }
      window[`${fnName}Fail${uid}`] = (err: any) => {
        reject(err)
      }
      window.webkit.messageHandlers.getAuthorizationHeader.postMessage({
        url,
        body: body ? JSON.stringify(body) : undefined,
        success: `${fnName}Success${uid}`,
        fail: `${fnName}Fail${uid}`,
      })
    })
  }
  return Promise.reject(`no method matched for ${fnName}`)
}


/** 芝麻认证 */
export const bindZhimaAuth = ({name, id, successZhima, failZhima}: ZhiMa) => {
  const fnName = 'bindZhimaAuth'
  if (isAndroid) {
    window.callbackZhimaSuccess = successZhima
    window.callbackZhimaFail = failZhima
    if (androidMethodAccessible(fnName)) {
      const paramArr = JSON.stringify(handleAndroidParamArr([name, id, 'callbackZhimaSuccess', 'callbackZhimaFail']))
      window.tantan.dispatch(fnName, paramArr)
    } else if (android_method_checker(fnName)) {
      window.tantan.bindZhimaAuth(
        name,
        id,
        'callbackZhimaSuccess',
        'callbackZhimaFail'
      )
    }
  } else if (oldIOS_method_checker("bindZhimaAuth")) {
    const callbackZhimaSuccess = () => {
      successZhima()
    }
    const callbackZhimaFail = (res: any) => {
      failZhima(res)
    }
    window.bindZhimaAuth(name, id, callbackZhimaSuccess, callbackZhimaFail)
  } else if (ios_method_checker('bindZhimaAuth')) {
    window.callbackZhimaSuccess = successZhima
    window.callbackZhimaFail = failZhima
    window.webkit.messageHandlers.bindZhimaAuth.postMessage({
      name,
      num: id,
      success: 'callbackZhimaSuccess',
      fail: 'callbackZhimaFail',
    })
  } else {
    console.info("无法绑定实名认证", 0.5)
  }
}

/** 新打点 */
export function trackNew(trackData: {
  type: string
  pageId: string
  eid?: string
  extras?: { [key: string]: string }
}): void {
  const fnName = 'trackNew'
  if (isAndroid) {
    const {type, pageId, eid = '', extras} = trackData
    let kvs: string[] = []
    if (extras) {
      for (let key in extras) {
        kvs = kvs.concat(String(key), String(extras[key]))
      }
    }
    if (androidMethodAccessible(fnName)) {
      const paramsArr = handleAndroidParamArr([type, eid, pageId, kvs])
      return window.tantan.dispatch(fnName, JSON.stringify(paramsArr))
    } else if (android_method_checker(fnName)) {
      return window.tantan.trackNew(type, eid, pageId, kvs)
    }
  } else if (oldIOS_method_checker("trackNew")) {
    window.trackNew(trackData)
  } else if (ios_method_checker('trackNew')) {
    return window.webkit.messageHandlers.trackNew.postMessage(trackData)
  } else {
    console.info("无法进行新打点", 0.5)
  }
}

/** 6. 绑定支付宝 */
export function bindAlipay({successAlipay, failAlipay}: IAlipayParams) {
  const uid = genUuid()
  const fnName = 'bindAlipay'
  if (isAndroid) {
    window[`successAlipay${uid}`] = successAlipay
    window[`failAlipay${uid}`] = failAlipay
    if (androidMethodAccessible(fnName)) {
      return window.tantan.dispatch(fnName, JSON.stringify(handleAndroidParamArr([`successAlipay${uid}`, `failAlipay${uid}`])))
    } else if (android_method_checker(fnName)) {
      return window.tantan[fnName](`successAlipay${uid}`, `failAlipay${uid}`)
    }
  } else if (oldIOS_method_checker(fnName)) {
    return window[fnName](successAlipay, failAlipay)
  } else if (ios_method_checker(fnName)) {
    window[`successAlipay${uid}`] = successAlipay
    window[`failAlipay${uid}`] = failAlipay
    window.webkit.messageHandlers.bindAlipay.postMessage({
      success: `successAlipay${uid}`,
      fail: `failAlipay${uid}`,
    })
  } else {
    return console.info("无法绑定支付宝", 0.5)
  }
}

export async function getABNames(): Promise<string[]> {
  const transStringToArr = (str: string, ios: boolean) => {
    let result: string[] = []
    if (str.length > 2 && !ios) {
      result = str.slice(1, -1).split(',')
    }
    if (str.length > 2 && ios) {
      result = JSON.parse(str)
    }
    return result
  }
  const fnName = 'getABNames'
  if (isAndroid) {
    return Promise.resolve(transStringToArr(window.tantan.dispatch(fnName, JSON.stringify([])), false) as string[])
  } else if (oldIOS_method_checker(fnName)) {
    return transStringToArr(window[fnName](), true) as string[]
  } else if (ios_method_checker(fnName)) {
    return new Promise<string[]>((resolve, reject) => {
      const time = Math.floor(Math.random() * 100)
      window[`getABNamesSuccess${time}`] = (res: string) => {
        resolve(transStringToArr(res, true))
      }
      window[`getABNamesFail${time}`] = (err: any) => {
        reject(err)
      }
      window.webkit.messageHandlers.getABNames.postMessage({
        success: `getABNamesSuccess${time}`,
        fail: `getABNamesFail${time}`,
      })
    })
  }
  return Promise.reject(`no method matched for ${fnName}`)
}

// 添加新的bridge监听关闭webview事件，当前webview关闭之后的回调
export async function setWebviewPageID({pageID, extras = {}}: { pageID: string, extras?: any }) {
  const fnName = 'setWebviewPageID'
  let kvs: string[] = []
  if (extras) {
    for (let key in extras) {
      kvs = kvs.concat(String(key), String(extras[key]))
    }
  }
  if (androidMethodAccessible(fnName)) {
    const paramsArr = handleAndroidParamArr([pageID, kvs])
    return window.tantan.dispatch(fnName, JSON.stringify(paramsArr))
  }
  console.log(fnName);
  if (ios_method_checker(fnName)) {
    window.webkit.messageHandlers[fnName].postMessage({pageID, extras})
    return
  }
  if (android_method_checker(fnName) || oldIOS_method_checker(fnName)) {
    return oldBridgeHandler(fnName)
  }
  customConsole(`no method matched for ${fnName}`)
}

// 添加新的bridge返回可分享渠道。
export async function getShareChannel() {
  const fnName = 'getShareChannel'
  if (androidMethodAccessible(fnName)) {
    try {
      const sourceRes = window.tantan.dispatch(fnName, JSON.stringify([]))
      const res = sourceRes ? JSON.parse(sourceRes) : {} // 未登录情况下安卓返回空串
      return Object.keys(res).length === 0 ? Promise.reject('unknown user') : Promise.resolve(res as string[])
    } catch (e) {
      console.error('internal error')
    }
  }
  if (ios_method_checker(fnName)) {
    const uid = genUuid()
    return new Promise<string[]>((resolve, reject) => {
      window[`${fnName}Success${uid}`] = (res: string) => {
        console.log(res);
        // iOS返回 wx / qq / wb 需要我们手动添加 mo / qz
        const resArr = JSON.parse(res);
        if (resArr.includes('wx')) resArr.push('mo');
        if (resArr.includes('qq')) resArr.push('qz');
        resolve(resArr as string[])
      }
      window[`${fnName}Fail${uid}`] = () => {
        reject('unknown user')
      }
      window.webkit.messageHandlers[fnName].postMessage({
        success: `${fnName}Success${uid}`,
        fail: `${fnName}Fail${uid}`,
      })
    })
  }
  customConsole('no method matched for getShareChannel')
  // 默认旧版本app仅支持微信
  return Promise.resolve(['wx'])
}

/* jsBridge topic相关 *****************************************************************************************************************/
export const jumpTopic = (
  id: string,
  from: string,
  landingPage: string,
  topicType: "topic" | "vote" | "anonymous" | "link" | "qa",
  name: string
) => {
  if (isiOS) {
    triggerAction({
      actionType: "jumpToTopic",
      restParams: {
        topicID: id,
        from: from,
      },
    })
  } else if (isAndroid) {
    switch (topicType) {
      case "link":
        triggerAction({
          actionType: "openWebview",
          restParams: {
            url: `tantanapp://topic_webview?title=${name}&url=${landingPage}`,
            title: name,
          },
        });
        break;
      case "vote":
        triggerAction({
          actionType: "jumpToTopicVoteAggregationAct",
          restParams: {
            id: id,
            topicOwnerId: "-10010",
            momentId: "-1",
          },
        });
        break;
      case "qa":
        triggerAction({
          actionType: "jumpToQATopicAggregationAct",
          restParams: {
            topicId: id,
          },
        });
        break;
      default:
        triggerAction({
          actionType: "jumpToTopicAggregationAct",
          restParams: {
            id: id,
          },
        })
    }
  } else {
    showToast({context: "请在手机端打开该页面~"})
  }
}

/* jsBridge 分支新增 *****************************************************************************************************************/
/**
 * 获取设备状态栏信息
 */
export const getDeviceNotchInfo = (): Promise<IDeviceNotchInfo> => {
  const fnName = 'getDeviceNotchInfo';
  if (androidMethodAccessible(fnName)) {
    try {
      const res = window.tantan.dispatch(fnName, JSON.stringify([]));
      return Promise.resolve(res ? JSON.parse(res) as IDeviceNotchInfo : {} as IDeviceNotchInfo);
    } catch (e) {
      console.error('internal error')
    }
  }
  if (ios_method_checker(fnName)) {
    const uid = genUuid();
    return new Promise<IDeviceNotchInfo>((resolve, reject) => {
      window[`${fnName}Success${uid}`] = (res: string) => {
        let finalRes = res ? JSON.parse(res) : {};
        if (typeof finalRes.isNotch === 'number') {
          finalRes.isNotch = Boolean(finalRes.isNotch)
        }
        resolve(finalRes as IDeviceNotchInfo);
      };
      window[`${fnName}Fail${uid}`] = () => {
        reject('getDeviceNotchInfoFail')
      };
      window.webkit.messageHandlers[fnName].postMessage({
        success: `${fnName}Success${uid}`,
        fail: `${fnName}Fail${uid}`,
      });
    })
  }
  return Promise.reject(`no method matched for ${fnName}`)
}
/**
 * 设置状态栏背景色
 * @param color
 */
export const changeNotchBackgroundColor = (color: string) => {
  if (!color || typeof color !== 'string') {
    throwErr('param color is invalid!')
  }
  const fnName = 'changeNotchBackgroundColor';
  const resColor = color.replace('#', '')
  if (androidMethodAccessible(fnName)) {
    return window.tantan.dispatch(fnName, JSON.stringify(handleAndroidParamArr([resColor])));
  }
  if (ios_method_checker(fnName)) {
    return window.webkit.messageHandlers[fnName].postMessage({color: resColor})
  }
  customConsole(`no method matched for ${fnName}`);
}
/**
 * 设置webview背景色
 * @param color
 */
export const changeWebviewBackgroundColor = (color: string) => {
  if (!color || typeof color !== 'string') {
    throwErr('param color is invalid!')
  }
  const fnName = 'changeWebviewBackgroundColor';
  const resColor = color.replace('#', '')

  if (androidMethodAccessible(fnName)) {
    return window.tantan.dispatch(fnName, JSON.stringify(handleAndroidParamArr([resColor])));
  }
  if (ios_method_checker(fnName)) {
    return window.webkit.messageHandlers[fnName].postMessage({color: resColor})
  }
  customConsole(`no method matched for ${fnName}`);
}
/**
 * 隐藏状态栏
 */
export const hideNotch = (): void => {
  const fnName = 'hideNotch';
  if (androidMethodAccessible(fnName)) {
    return window.tantan.dispatch(fnName, JSON.stringify(handleAndroidParamArr([])));
  }
  if (ios_method_checker(fnName)) {
    return window.webkit.messageHandlers[fnName].postMessage({})
  }
  customConsole(`no method matched for ${fnName}`);
}
/**
 * 显示状态栏
 */
export const showNotch = (): void => {
  const fnName = 'showNotch';
  if (androidMethodAccessible(fnName)) {
    return window.tantan.dispatch(fnName, JSON.stringify(handleAndroidParamArr([])));
  }
  if (ios_method_checker(fnName)) {
    return window.webkit.messageHandlers[fnName].postMessage({})
  }
  customConsole(`no method matched for ${fnName}`);
}
/**
 * IOS 禁止页面回弹
 */
export const disableBounce = (): void => {
  const fnName = 'disableBounce';
  if (isiOS) {
    if (ios_method_checker(fnName)) {
      return window.webkit.messageHandlers[fnName].postMessage({})
    }
    return customConsole(`no method matched for ${fnName}`);
  }
  customConsole(`${fnName} is only used in ios`);
}

export const setOnKeyBack = (params: any): Promise<void> => {
  const {
    handler,
  } = params
  const fnName = 'setOnKeyBack'
  const uid = genUuid()
  window[`handler${uid}`] = handler
  if (androidMethodAccessible(fnName)) {
    return new Promise((resolve, reject) => {
      window[`success${uid}`] = () => {
        resolve()
      }
      window[`error${uid}`] = () => {
        reject()
      }
      const paramArr = JSON.stringify(handleAndroidParamArr([`handler${uid}`]))
      window.tantan.dispatch(fnName, paramArr)
    })
  }
  return Promise.reject(`no method matched for ${fnName}`)
}


/* V2.3.0 *****************************************************************************************************************/

const androidHandler = (androidPar: any[], fnName: string) => {
  if (androidMethodAccessible(fnName)) {
    console.log(handleAndroidParamArr(androidPar));
    window.tantan.dispatch(fnName, JSON.stringify(handleAndroidParamArr(androidPar)))
  } else if (android_method_checker(fnName)) {
    window.tantan[fnName](...androidPar)
  } else {
    throw new Error('没有这个Bridge')
  }
}

/**
 * 分享图片（需要指定分享渠道）--- 无返回值
 * iOS 分享图片复用share逻辑
 * @param params
 */
export const shareImage = (params: IShareImageParam): Promise<void> => {
  const {pic, platform} = params
  if (!pic || !platform) {
    throwErr('param is invalid')
  }
  const fnName = 'shareImage'
  return new Promise<void>((resolve, reject) => {
    try {
      if (isAndroid) {
        androidHandler([pic, platform], fnName);
      } else if (ios_method_checker('share')) {
        // iOS 复用share逻辑
        share({
          url: '',
          title: '',
          description: '',
          imgUrl: pic,
          channel: platform
        });
      }
    } catch (e) {
      reject(`no method matched for ${fnName}`)
    }
  })
}

/**
 * 保存图片
 * @param params
 */
export const imageSave = (params: { url: string }): Promise<void> => {
  const {url} = params
  const fnName = 'imageSave'
  const callbackName = `callback${genUuid()}`;
  const callbackFn = (resolve: Function, reject: Function) => ((data: 'success' | 'failed' | 'refuse') => {
    console.log(fnName, data);
    if (data !== 'success') return reject(data);
    resolve();
  })
  return new Promise((resolve, reject) => {
    window[callbackName] = callbackFn(resolve, reject);
    try {
      if (isAndroid) {
        androidHandler([url, callbackName], fnName);
      } else if (ios_method_checker(fnName)) {
        window.webkit.messageHandlers[fnName].postMessage({url, callback: callbackName})
      }
    } catch (e) {
      reject(`no method matched for ${fnName}`)
    }
  })
}

/**
 * 保存Base64图片
 * @param params
 */
export const saveBase64ImageData = (params: { data: string }): Promise<void> => {
  const {data} = params
  const fnName = 'saveBase64ImageData'
  const callbackName = `callback${genUuid()}`;
  const callbackFn = (resolve: Function, reject: Function) => ((data: 'success' | 'failed' | 'refuse') => {
    console.log(fnName, data);
    if (data !== 'success') return reject(data)
    resolve()
  })
  return new Promise<void>((resolve, reject) => {
    window[callbackName] = callbackFn(resolve, reject);
    try {
      if (isAndroid) {
        androidHandler([data, callbackName], fnName);
      } else if (ios_method_checker(fnName)) {
        window.webkit.messageHandlers[fnName].postMessage({data, callback: callbackName})
      }
    } catch (e) {
      reject(`no method matched for ${fnName}`)
    }
  })
}

export const shareFriends = (params: any): any => {
  const fnName = 'shareFriends';
  const callbackName = `callback${genUuid()}`;
  const callbackFn = (resolve: Function, reject: Function) => ((status: 'success' | 'failed' | 'refuse', userIDList: string) => {
    if (status !== 'success') return reject(status);
    if (params.callback && typeof params.callback === "function") {
      params.callback(status, userIDList)
    }
    resolve(status, userIDList);
  });
  console.log(params);
  return new Promise((resolve, reject) => {
    window[callbackName] = callbackFn(resolve, reject);
    if (isAndroid) {
      triggerAction({
        actionType: fnName,
        restParams: params
      })
      return;
    }
    if (ios_method_checker(fnName)) {
      return window.webkit.messageHandlers[fnName].postMessage({
        ...params, callback: callbackName
      });
    }
  })
}
