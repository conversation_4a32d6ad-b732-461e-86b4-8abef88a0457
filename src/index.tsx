import "react-app-polyfill/ie9";
import "react-app-polyfill/stable";
import React, {Suspense, useEffect, lazy} from "react";
import ReactDOM from "react-dom";
import "./index.css";
import 'antd-mobile/dist/antd-mobile.css'
import * as serviceWorker from "./serviceWorker";
import {BrowserRouter as Router, Route, Switch} from "react-router-dom";

import Svip from "./routes/svip/Svip";
import Kol from "./routes/kol/Kol";
import KolFeed from "./routes/kolFeed/KolFeed";
import Growth from "./routes/growth/Growth";
import CheckoutDemo from "./routes/checkoutDemo/CheckoutDemo";
import ShareLight from "./routes/shareLight/ShareLight";
import GroupOwnerInvite from './routes/groupOwner/GroupOwnerInvite';
import GroupOwnerRule from './routes/groupOwner/GroupOwnerRule';
// todo: 暂时下掉黑钻相关功能
// import DiamondInvite from './routes/diamondInvite/DiamondInvite';
// import DiamondCard from './routes/diamondInvite/DiamondCard';
// import DiamondPay from './routes/diamondInvite/DiamondPay';
// import Plane from "./routes/plane/Plane";

import {getUserInfo, getSystemInfo} from "./utils/bridge";
import {isAndroid, isIOS} from "./utils/config";
import {i18nInit} from "./utils/i18nInit";
import {Purchase} from "./routes/purchase";
import {PurchaseSuccess} from "./routes/purchase/pages/success1";
import {PurchaseSuccess2} from "./routes/purchase/pages/success2";
import {AnnualShared} from "./routes/annual/SharedWeixin";
import {AnnualReport} from "./routes/annual/Report";
import {GroupChat} from "./routes/groupChat";
import {BridgeTest} from "./routes/bridge";
import {Share} from "./routes/bridge/content/share";
import {TopicPage} from "./routes/bridge/content/topic";
import {TitleCase1} from "./routes/bridge/content/nativeStorage/case1";
import {TitleCase2} from "./routes/bridge/content/nativeStorage/case2";
import {TitleCase4} from "./routes/bridge/content/nativeStorage/case4";
import {TitleCase3} from "./routes/bridge/content/nativeStorage/case3";
import {WebviewToDeeplink} from "./routes/bridge/content/toDeeplink";
import {NativeTitlePage} from "./routes/bridge/content/nativeTitle";
import {BeforeCloseWebview} from "./routes/bridge/content/beforeCloseWebview";
import {WebviewOperations} from "./routes/bridge/content/webviewOperations";
import {GetBaseInfo} from "./routes/bridge/content/getBaseInfo";
import {EmptyPage} from "./routes/bridge/content/testPage/emptyPage";
import {ShareFriends} from "./routes/bridge/content/shareFriends";

const FriendInvite2 = lazy(() => import('./routes/friendInvite2/friendInvite2'));
const FriendInvite2Download = lazy(() => import('./routes/friendInvite2Download/friendInvite2Download'));
const FriendInvite2Rule = lazy(() => import('./routes/friendInvite2Rule/FriendInvite2Rule'));
const LittleRedbook = lazy(() => import('./routes/littleRedBook/index'));
const AndroidBridge = lazy(() => import('./routes/bridge/content/testPage/android'));


const Routers: React.FC = () => {
  return (
    <Router basename="/commerce">
      <Suspense fallback>
        <Switch>
          <Route exact path="/svip" component={Svip} />
          <Route exact path="/kol" component={Kol} />
          <Route exact path="/kolFeed" component={KolFeed} />
          <Route exact path="/growth" component={Growth} />
          <Route exact path="/checkout-demo" component={CheckoutDemo} />
          {/*针对抖音做半屏样式优化*/}
          <Route exact path="/growth2" component={(props: any) => Growth(props, 'growth2')} />
          <Route exact path="/shareLight" component={ShareLight} />
          {/*小助手购买闪聊*/}
          <Route exact path="/purchase/:type" component={Purchase} />
          <Route exact path="/purchase/success/boost" component={PurchaseSuccess2} />
          <Route exact path="/purchase/success/quickChat" component={PurchaseSuccess} />
          {/*年报 & 微信分享*/}
          <Route exact path="/annual/report" component={AnnualReport} />
          <Route exact path="/annual/report/shared" component={AnnualShared} />
          {/*群聊分享*/}
          <Route exact path="/group/share/:groupID/:userID" component={GroupChat} />
          <Route exact path="/groupowner/invite" component={GroupOwnerInvite} />
          <Route exact path="/groupowner/invite/rules" component={GroupOwnerRule} />
          {/*群聊分享*/}
          <Route exact path="/group/share/:groupID/:userID" component={GroupChat} />
          {/*黑钻推广*/}
          {/*todo: 暂时下掉黑钻相关功能*/}
          {/*<Route exact path="/diamond/enter" component={DiamondInvite} />*/}
          {/*<Route exact path="/diamond/enter/card" component={DiamondCard} />*/}
          {/*<Route exact path="/diamond/enter/pay" component={DiamondPay} />*/}
          {/* 黑钻包机活动 */}
          {/*<Route exact path="/plane" component={Plane} />*/}
          {/* 好友邀请2期 */}
          <Route exact path="/FriendInvite2" component={FriendInvite2} />
          <Route exact path="/FriendInvite2Download" component={FriendInvite2Download} />
          <Route exact path="/FriendInvite2Rule" component={FriendInvite2Rule} />
          <Route path="/littleRedbook" component={LittleRedbook} />
          {/* Bridge */}
          <Route exact path="/bridge" component={BridgeTest} />
          <Route exact path="/bridge/getUserInfo" component={GetBaseInfo} />
          <Route exact path="/bridge/share" component={Share} />
          <Route exact path="/bridge/topic" component={TopicPage} />
          <Route exact path="/bridge/storage/1" component={TitleCase1} />
          <Route exact path="/bridge/storage/2" component={TitleCase2} />
          <Route exact path="/bridge/storage/3" component={TitleCase3} />
          <Route exact path="/bridge/storage/4" component={TitleCase4} />
          <Route exact path="/bridge/todeeplink" component={WebviewToDeeplink} />
          <Route exact path="/bridge/nativeTitle" component={NativeTitlePage} />
          <Route exact path="/bridge/before/close" component={BeforeCloseWebview} />
          <Route exact path="/bridge/webview" component={WebviewOperations} />
          <Route exact path="/bridge/empty" component={EmptyPage} />
          <Route exact path="/bridge/android" component={AndroidBridge} />
          <Route exact path="/bridge/android" component={AndroidBridge} />
          <Route exact path="/bridge/ShareFriends" component={ShareFriends} />
        </Switch>
      </Suspense>
    </Router>
  );
};
/** 添加全局doWhenGetToken方法, 旧安卓版本调用 **/
(window as any).doWhenGetToken = () => {};
i18nInit().then(() => ReactDOM.render(<Routers/>, document.getElementById("root")))

// If you want your app to work offline and load faster, you can change
// unregister() to register() below. Note this comes with some pitfalls.
// Learn more about service workers: https://bit.ly/CRA-PWA
serviceWorker.unregister();
