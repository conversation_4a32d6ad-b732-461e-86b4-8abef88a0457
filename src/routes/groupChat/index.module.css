.infoBox {
  height: 0.72rem;
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
  padding: 0.12rem;
  box-sizing: border-box;
  position: relative;
}

.infoBox > div:first-child {
  position: relative;
}

img.logo {
  width: 0.48rem;
  height: 0.48rem;
}

.titleBox {
  display: inline-block;
  font-size: 0.13rem;
  color: #999;
  padding-left: 0.1rem;
  line-height: 0.18rem;
  position: absolute;
  top: 0.02rem;
}

.titleBox h1 {
  font-size: 0.19rem;
  color: #ff5c31;
  line-height: 0.26rem;
  margin: 0;
}

.downloadBtn {
  width: 1rem;
  padding: 0.08rem 0.1rem;
  border-radius: 0.11rem;
  color: #fff;
  background-image: linear-gradient(to left, #ff863e, #ff4349);
  font-size: 0.15rem;
  font-weight: 500;
  box-sizing: border-box;
  position: absolute;
  top: 50%;
  right: 0.12rem;
  margin-top: -0.18rem;
}

.downloadBtn::before {
  content: '';
  display: inline-block;
  background-image: url(https://auto.tancdn.com/v1/images/eyJpZCI6IlNXVTdLNU01S0hIM0pIMlFGVk1KQ0M1Q1I1Qk40MzA3IiwidyI6MzYsImgiOjQ4LCJkIjowLCJtdCI6ImltYWdlL2pwZWciLCJkaCI6MTU2MzMyNTUxNjU5NzI5ODYxMH0.png);
  width: 0.12rem;
  height: 0.16rem;
  background-size: cover;
  vertical-align: middle;
  margin-right: 0.05rem;
}

.contentBox {
  width: 100%;
  padding-top: 1rem;
  text-align: center;
  position: relative;
}

.avatar {
  width: 1rem;
  border-radius: 50%;
}

.groupTitle {
  color: #212121;
  font-size: 0.19rem;
  font-weight: 500;
  font-family: "PingFang SC";
  margin-top: 0.2rem;
}

.addBtn {
  height: 0.5rem;
  width: 2.24rem;
  position: absolute;
  left: 50%;
  margin-left: -1.12rem;
  margin-top: 0.35rem;
  border-radius: 0.25rem;
  background-image: linear-gradient(to left, #ff863e, #ff4349);
  line-height: 0.5rem;
  color: #fff;
  font-size: 0.17rem;
  font-weight: 500;
}
