import React, {useEffect, useState} from "react";
import style from './index.module.css'
import {isAndroid, isWeixinBrowser} from "../../utils/bridge/utils";
import {RouteComponentProps} from "react-router-dom";
import axios from "axios";
import {track} from "../../utils/utils";

type status = 'default' | 'banned' | 'dismissed' | 'disbanded'

// 分组信息
interface GroupInfo {
  id?: string;                      // 群ID
  publicId?: string;                // publicID
  name?: string;                    // 群聊名称
  avatars?: [],                     // 头像
  status?: status;                  // 状态
  memberIds?: string[]              // 成员ID
  memberCount?: number              // 成员数量
  type?: 'anonymous' | 'realname'   // 群状态
}

interface Params {
  groupID: string;
  userID: string;
}

let logoUrl = 'https://auto.tancdn.com/v1/images/eyJpZCI6IlQzNFlVVzZPNVI0Mjc0TEVYU0FBWTRPUU5MWDQ2NzA4IiwidyI6MzAwLCJoIjozMDAsImQiOjAsIm10IjoiaW1hZ2UvanBlZyIsImRoIjoxMDgzMzgxNTM0MzUwNTEwMTE5OH0.png';
let avatarUrl = logoUrl;
let url = isAndroid
  ? 'https://tantanapp.com/download/ '
  : 'https://apps.apple.com/cn/app/id861891048'
const isTestEnv = window.location.href.includes('staging2');
export const GroupChat: (props: RouteComponentProps<Params>) => JSX.Element = (props) => {
  // 展示提示蒙层
  const [showMask, setShowMask] = useState(false);
  // 群信息
  const [groupInfo, setGroupInfo] = useState<GroupInfo>({});
  // 是否已解散
  const [disbanded, setDisbanded] = useState(true)
  // 是否展示内容
  const [showContent, setShowContent] = useState(false)
  useEffect(() => {
    (async () => {
      const publicGroupID = props.match.params.groupID;
      const publicUserID = props.match.params.userID;
      const groupInfo = (await axios.get(`/v3/chat-groups/${publicGroupID}?source=share${isTestEnv ? '&user_id=22957' : ''}`)).data.data.chatGroups[0]
      const {avatars, status} = groupInfo;
      const disbanded = status === 'disbanded';
      avatarUrl = avatars && avatars.length ? avatars[0].url : logoUrl;
      setGroupInfo(groupInfo)
      setDisbanded(disbanded);
      track('PV', 'p_group_share', '', {
        if_disbanded: disbanded ? 'yes' : 'no',
        is_anonymou_group: groupInfo.type === 'anonymous' ? '1' : '0',
        groupchat_id: publicGroupID,
        user_id: publicUserID
      })
      setShowContent(true)
      if (!isWeixinBrowser() && !disbanded) {
        window.location.href = `tantanapp://join_group?groupId=${groupInfo.id}`
      }
    })()
  }, [])

  // 下载探探
  const toDownload = () => {
    track('MC', '', 'e_download_tantan', {
      groupchat_id: props.match.params.groupID,
      user_id: props.match.params.userID
    });
    if (!isAndroid && isWeixinBrowser()) {
      track('MC', '', 'e_group_share_trigger_overlay', {
        groupchat_id: props.match.params.groupID,
        user_id: props.match.params.userID
      })
      return setShowMask(true)
    }
    window.location.href = url;
  }

  // 加群
  const addGroup = () => {
    if (isWeixinBrowser()) {
      track('MC', '', 'e_group_share_trigger_overlay', {
        groupchat_id: props.match.params.groupID,
        user_id: props.match.params.userID
      })
      return setShowMask(true)
    }
    window.location.href = `tantanapp://join_group?groupId=${groupInfo.id}`
  }
  return <div>
    {showMask ? <div style={{
      position: 'fixed',
      top: 0,
      right: 0,
      bottom: 0,
      left: 0,
      backgroundColor: 'rgba(0, 0, 0, 0.7)',
      zIndex: 999999999
    }}>
      <img style={{width: '60%', position: 'absolute', right: '20px'}}
           src="https://auto.tancdn.com/v1/images/eyJpZCI6IlRDTk41RVpZRktDUUlNNURERUpHWFFKV1JUM0JCSjA3IiwidyI6NzY4LCJoIjo0MjAsImQiOjAsIm10IjoiaW1hZ2UvanBlZyIsImRoIjo3Mzk2MTAyNjQ2Mjk5NDE5MjY0fQ.png"
           alt="引导"/>
    </div> : ''}
    <div className={style.infoBox}>
      <div>
        <img className={style.logo}
             src={logoUrl}
             alt=""/>
        <div className={style.titleBox}>
          <h1>探探</h1>
          <div>超火爆社交App</div>
        </div>
      </div>
      <div className={style.downloadBtn} onClick={toDownload}>下载探探</div>
    </div>
    {showContent
      ? <div className={style.contentBox}>
        {
          disbanded ? <div style={{paddingTop: '0.5rem'}}>该群已解散，无法加入</div> : <>
            <img className={style.avatar}
                 src={avatarUrl}/>
            <div className={style.groupTitle}>{groupInfo.name} ({groupInfo.memberCount})</div>
            <div className={style.addBtn} onClick={addGroup}>加入群聊</div>
          </>
        }
      </div>
      : null
    }
  </div>
}

