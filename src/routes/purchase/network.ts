interface CommonObj {
  [key: string]: any
}

export function getAuthorizationHeader(url: string) {
  const res = window.tantan.dispatch(
    "getAuthorizationHeader",
    JSON.stringify([
      {
        type: "String",
        value: url
      }
    ])
  );
  return Promise.resolve(res);
}

const register: CommonObj = {};
let nativeCallbackID = 0;
// 向native暴露的通用回调方法 ctx 为js传递给native， native原样返回的，
// 可以传递函数句柄， 或者其他上下文信息
window.paymentCallback = (id: number, result: any) => {
  // @ts-ignore
  id = id + "";
  const handler = register[id];
  handler(result);
  delete register[id];
};

function registerCallback(handler: Function) {
  nativeCallbackID += 1;
  register[nativeCallbackID] = handler;
  return nativeCallbackID;
}

export function purchaseNew(itemId: string, purchaseChannel: string, tracker: string) {
  console.log("purchase new", itemId, purchaseChannel, tracker);
  return new Promise((resolve, reject) => {
    let resolved = false;
    let handlerId =
      registerCallback((result: string) => {
        resolved = result === "succeed";
        console.log(result);
        (resolved ? resolve : reject)(result);
      }) + "";
    window.tantan.dispatch(
      "purchaseWithTracker",
      JSON.stringify([
        {
          type: "String",
          value: handlerId
        },
        {
          type: "String",
          value: itemId
        },
        {
          type: "String",
          value: purchaseChannel
        },
        {
          type: "String",
          value: tracker
        }
      ])
    );

    // 因为navtive失败时没有回调，设置一个超时时间为60s
    window.setTimeout(() => {
      if (!resolved) {
        console.log("going to resolve failed");
        reject("failed");
      }
    }, 60000);
  });
}
