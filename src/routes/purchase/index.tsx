import React, {useEffect, useState} from "react";
import {Icon, Result} from 'antd-mobile';
import {purchaseNew} from "./network";
import s from './index.module.css'
import {statusData} from "./data";
import {getUserInfo, setNavigationTitle} from "../../utils/bridge";
import {ajax} from "../../utils/ajax";


const PURCHASE_CHANNEL = 'alipay';

export const Purchase: React.FC = (props) => {
  // @ts-ignore
  const type: 'boost' | 'quickChat' = props.match.params.type;
  const status = 'pending';
  const statusArr = Object.keys(statusData);
  const [curStatus, setCurStatus] = useState(statusData[status]);
  const getData = async (userId: string) => {
    const res = await ajax.get(`/v2/users/${userId}/link-auto-deduct/${type}`);
    const {itemID, itemTracker, orderStatus, orderIdentifier, overTime} = res.data.linkAutoDeduct;
    const item = {
      id: itemID,
      sign: itemTracker
    }
    if (overTime) {
      return setCurStatus(statusData['overTime']);
    }
    if (orderStatus === 'pending') {
      return await new Promise<void>(resolve => {
        setTimeout(async () => {
          await getData(userId);
          resolve()
        }, 3000)
      })
    }
    if (orderStatus !== 'failed') {
      if (orderStatus === 'success') {
        // @ts-ignore
        return props.history.replace(`/purchase/success/${type}`)

      }
      return setCurStatus(statusData[orderStatus || 'failed']);
    }
    purchaseNew(item.id, PURCHASE_CHANNEL, item.sign).then(_ => {
      // @ts-ignore
      props.history.replace(`/purchase/success/${type}`)
      setCurStatus(statusData['success']);
    }, _ => {
      setCurStatus(statusData['failed']);
    })
  }
  useEffect(() => {
    (async () => {
      console.log(type)
      document.title = ' ';
      // todo: 请求数据
      await setNavigationTitle({
        title: ' '
      })
      const {userId, userName} = await getUserInfo()
      console.log(userId, userName)
      // const userId = '22957';
      await getData(userId);
    })()
  }, [])
  return <div className={s.container}>
    <Result
      img={<Icon type={curStatus.icon} className={s.icon} style={{fill: curStatus.color}}/>}
      title={<div className={`${s.statusTxt} ${s[status]}`}>{curStatus.text}</div>}
    />
  </div>
}
