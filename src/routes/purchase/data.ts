interface Status {
  [key: string]: {
    icon: string;
    text: string;
    color?: string
  }
}

export const statusData: Status = {
  pending: {
    icon: 'loading',
    text: '正在支付'
  },
  success: {
    icon: 'check-circle',
    color: 'rgb(57, 214, 171)',
    text: '支付成功'
  },
  failed: {
    icon: 'cross-circle-o',
    color: '#F13642',
    text: '支付失败'
  },
  repeated: {
    icon: 'check-circle',
    color: 'rgb(57, 214, 171)',
    text: '已购买成功，请勿重复购买～'
  },
  overTime: {
    icon: 'cross-circle-o',
    color: '#F13642',
    text: '已过期'
  }
}
