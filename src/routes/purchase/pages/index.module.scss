.container {
  width: 100vw;
  height: 100vh;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding-bottom: 0.6rem;
  position: relative;
}

.titleBox {
  width: 100vw;
  height: 1.4rem;
  padding-top: 0.15rem;
  background-image: linear-gradient(to bottom, #ffffff 61%, rgba(255, 255, 255, 0));
  position: relative;
  z-index: 999999;
  box-sizing: border-box;
}

.flexBox {
  display: flex;
  align-items: center;
  justify-content: center;
}

.titleBox img {
  width: 0.76rem;
  height: 0.76rem;
  margin-right: 0.03rem;
}

.title {
  font-size: 0.18rem;
  line-height: 0.2rem;
  color: #212121;
  font-weight: bold;
}

.subtitle {
  color: #e0a030;
  font-size: 0.12rem;
  line-height: 0.2rem;
}

.subtitle span {
  font-weight: bold;
  font-size: 0.16rem;
  position: relative;
  top: 2px;
}

.circles {
  width: 100%;
  margin-top: -1rem;
  flex: 1;
  overflow: hidden;
  position: relative;
}

.avatar {
  width: 1.44rem;
  height: 1.44rem;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translateX(-0.72rem) translateY(-0.72rem);
  z-index: 2;
}

.smallAvatar {
  position: absolute;
  border-radius: 50%;
}

.avatar00 {
  width: 0.16rem;
  height: 0.16rem;
  right: 0.24rem;
  top: 0.1rem;
}

.avatar01 {
  width: 0.24rem;
  height: 0.24rem;
  right: 0.24rem;
  bottom: 0.08rem;
}

.avatar20 {
  width: 0.16rem;
  height: 0.16rem;
  left: 0.01rem;
  top: 0.8rem;
}

.avatar21 {
  width: 0.08rem;
  height: 0.08rem;
  right: 0.23rem;
  bottom: 0.5rem;
}

.avatar30 {
  width: 0.08rem;
  height: 0.08rem;
  right: 0.6rem;
  top: 0.46rem;
}

.avatar31 {
  width: 0.08rem;
  height: 0.08rem;
  left: 0.4rem;
  bottom: 0.67rem;
}

.text {
  background-color: #fff;
  height: 0.3rem;
  border-radius: 0.15rem;
  line-height: 0.3rem;
  padding: 0 0.1rem;
  white-space: nowrap;
  position: absolute;
  z-index: 9999;
  opacity: 0.8;
}

.text0 {
  color: #c3ad86;
  left: 0.8rem;
  top: 42%;
}

.text2 {
  color: #ddabab;
  right: -1rem;
  top: 30%;
}

.text3 {
  color: #a8b2b9;
  top: 68%;
  left: 0.6rem;
}

.text4 {
  color: #999;
  right: 1.58rem;
  top: 57%;
}

.button {
  width: 3.12rem;
  height: 0.48rem;
  background-image: linear-gradient(to right, #ff4349, #fe853f);
  position: absolute;
  bottom: 0;
  left: 50%;
  border-radius: 0.24rem;
  transform: translateX(-1.56rem);
  font-size: 0.16rem;
  color: #fff;
  font-weight: bold;
  line-height: 0.48rem;
  text-align: center;
}

@for $i from 0 through 4 {
  $width: 1.04rem + ( 0.72rem +  $i * $i * 0.24rem);
  .circle#{$i} {
    border: 1px solid rgba(245, 90, 67, 0.1);
    background-color: rgba(245, 90, 67, 0.02);
    width: $width;
    height: $width;
    border-radius: 50%;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translateX(-$width / 2) translateY(-$width / 2);
  }
}


.container.success2 {
  background-image: url(https://auto.tancdn.com/v1/images/eyJpZCI6Ilg1QVZVU0tDM1lYWFVHNk9OWEJBNjZIRE9NN1NQTDA2IiwidyI6MjI1MCwiaCI6MTYwOCwiZCI6MCwibXQiOiJpbWFnZS9qcGVnIiwiZGgiOjkyNTk0MDExMDg3Njg1MDI1NjB9.png),
  linear-gradient(to bottom, #ffedf5 -9%, #fff 59%);
  background-size: 100%;
  background-repeat: no-repeat;
}

.success2 .titleBox {
  height: 0.7rem;
  background: transparent;
  margin-bottom: 0.3rem;
}

.titlePink {
  text-align: center;
  color: #f465d0;
  font-size: 0.22rem;
  font-weight: bold;
  line-height: 0.35rem;
  margin-top: 0.3rem;
}

.titlePink span {
  font-family: 'Helvetica Neue';
}

.fontGrey {
  text-align: center;
  color: #999;
  font-size: 0.16rem;
  line-height: 0.23rem;
  margin-top: 0.24rem;
}

.btnPink {
  width: 2.85rem;
  height: 0.48rem;
  line-height: 0.48rem;
  text-align: center;
  font-size: 0.18rem;
  font-weight: bold;
  background-color: #ffe8f1;
  color: #f465d0;
  border-radius: 0.24rem;
  position: absolute;
  bottom: 0.4rem;
  left: 50%;
  transform: translateX(-1.425rem);
}

.numFont {
  font-family: 'Helvetica Neue';
}

.fontContent {
  font-family: ArialRounded;
  color: #f465d0;
  opacity: 0.8;
  box-sizing: border-box;
  position: absolute;
  width: 150px;
  height: 150px;
  text-align: center;
  font-weight: bold;
  font-size: 0.14rem;
  padding-top: 55px;
  top: 0;
  left: 0;
}

.fontContent span {
  font-size: 0.22rem;
}
