import React, {useEffect} from "react";
import s from './index.module.scss'
import {getRandom} from "./utils";
import {setNavigationTitle} from "../../../utils/bridge";

export const PurchaseSuccess: React.FC = () => {
  const arr = [0, 2, 3, 4];
  const configData: any = {
    0: {
      text: `距离${getRandom(1, 3, 1)}km的小姐姐发起了闪聊`,
      imgs: [
        'https://auto.tancdn.com/v1/images/eyJpZCI6IlBFTVY0MlpVV05MTFhIVlJIRllIR1BYWlFTUTdWSzA3IiwidyI6OTYsImgiOjk2LCJkIjowLCJtdCI6ImltYWdlL2pwZWciLCJkaCI6MTEwNjEzNjAyMzY0NDYxMjg5MX0.png',
        'https://auto.tancdn.com/v1/images/eyJpZCI6Ikw1QzVBRlpXR1lWVjNBUkdXSlZaUTY2MkRGRUFIVjA2IiwidyI6MTQ0LCJoIjoxNDQsImQiOjAsIm10IjoiaW1hZ2UvanBlZyIsImRoIjoyMjgzOTg3MjM0MjYxOTIyODQzfQ.png'
      ]
    },
    2: {
      text: `距离${getRandom(2, 5, 1)}km的小哥哥发起了闪聊`,
      imgs: [
        'https://auto.tancdn.com/v1/images/eyJpZCI6IlZYVTdURENSNlBUTFRQRllWQ0hGN0M1U1pOVkhOVDA2IiwidyI6OTYsImgiOjk2LCJkIjowLCJtdCI6ImltYWdlL2pwZWciLCJkaCI6MjI4MTYxNjQ2MDYzNTQ4MzY3MX0.png',
        'https://auto.tancdn.com/v1/images/eyJpZCI6IjVJVzNXUEdSVUlGSkgzS1BOTEhITkVaWUVYUk5QNzA2IiwidyI6ODY0LCJoIjo4NjQsImQiOjAsIm10IjoiaW1hZ2UvanBlZyIsImRoIjo4ODAyOTk0MzMzNTUwNTg5NTZ9.png'
      ]
    },
    3: {
      text: `附近${getRandom(100, 500)}对正在闪聊`,
      imgs: [
        'https://auto.tancdn.com/v1/images/eyJpZCI6Ik1CTk1FMzQySEFUM1hBSEVWR1hJU1ZXR1lHNDRLQTA2IiwidyI6NDgsImgiOjQ4LCJkIjowLCJtdCI6ImltYWdlL2pwZWciLCJkaCI6MzE0OTMyMDU3OTkxNjM3NzEzMX0.png',
        'https://auto.tancdn.com/v1/images/eyJpZCI6IjVNUzc3SEdUVlNZRk1VUTVMVkY1Q0paQVlSSlc3RDA2IiwidyI6NDgsImgiOjQ4LCJkIjowLCJtdCI6ImltYWdlL2pwZWciLCJkaCI6MzE2MDA2MjcyMDk0MTQwNDcxNX0.png'
      ]
    },
    4: {
      text: 'chris和cherry配对成功',
      imgs: ['', '']
    }
  }
  useEffect(() => {
    document.title = ' ';
    setNavigationTitle({
      title: ' '
    })
  }, [])
  return <div className={s.container}>
    <div className={s.titleBox}>
      <div className={s.flexBox}>
        <img
          src="https://auto.tancdn.com/v1/images/eyJpZCI6IkxKQUxFQVdYQ1pBNkhCWVdMSDZXRUUzTlY0Mk9STzA2IiwidyI6NDU2LCJoIjo0NTYsImQiOjAsIm10IjoiaW1hZ2UvanBlZyIsImRoIjoyMzA5Mzk0NjUxOTgzMDcyODY0fQ.png"
          alt="icon"/>
        <div className={s.titleContent}>
          <div className={s.title}>闪聊购买成功</div>
          <div className={s.subtitle}>获得 <span>3</span> 次</div>
        </div>
      </div>
    </div>
    <div className={s.circles}>
      {arr.map((item) =>
        <div className={s['circle' + item]}>
          <img className={`${s.smallAvatar} ${s[`avatar${item}0`]}`} src={configData[item]?.imgs[0]}/>
          <img className={`${s.smallAvatar} ${s[`avatar${item}1`]}`} src={configData[item]?.imgs[1]}/>
        </div>)}
      {
        arr.map(item => <div className={`${s.text} ${s[`text${item}`]}`}>{configData[item].text}</div>)
      }
      <img
        className={s.avatar}
        src="https://auto.tancdn.com/v1/images/eyJpZCI6IjVJVzNXUEdSVUlGSkgzS1BOTEhITkVaWUVYUk5QNzA2IiwidyI6ODY0LCJoIjo4NjQsImQiOjAsIm10IjoiaW1hZ2UvanBlZyIsImRoIjo4ODAyOTk0MzMzNTUwNTg5NTZ9.png"
        alt="avatar"/>
      <div className={s.button}>正在搜索闪聊...</div>
    </div>
  </div>
}
