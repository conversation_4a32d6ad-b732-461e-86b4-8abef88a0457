import React, {useEffect, useState} from "react";
import s from './index.module.scss'
import {ProgressCircle} from "../../../components/progressCircle";
import {closeWebview, setNavigationTitle} from "../../../utils/bridge";
import {getRandom} from "./utils";


export const numToFixed2 = (num: number): string => {
  return String(num < 10
    ? num > 0 ? `0${num}` : '00'
    : num >= 100 ? Math.floor(num / 10) : num);
}

const step = 124;
const allTime = 30 * 60 * 1000;
let mss = allTime;
let beforeTime = mss;

export const PurchaseSuccess2: React.FC = () => {
  const [minute, setMinute] = useState('30');
  const [second, setSecond] = useState('60');
  const [ms, setMs] = useState('99');
  const [percent, setPercent] = useState(0);
  const [num, setNum] = useState(getRandom(5, 15, 1))
  useEffect(() => {
    document.title = ' ';
    setNavigationTitle({
      title: ' '
    })
    let timer = setInterval(() => {
      mss = mss - step;
      setPercent(Math.ceil((allTime - mss) / allTime * 100))
      setMinute(numToFixed2(Math.floor(mss / 1000 / 60)));
      setSecond(numToFixed2(Math.floor(mss / 1000 % 60)));
      setMs(numToFixed2(mss % 1000))
      console.log(mss - beforeTime);
      if (Math.abs(beforeTime - mss) > 2500) {
        beforeTime = mss;
        setNum(getRandom(5, 15, 1))
      }
      if (mss <= 20) {
        clearInterval(timer);
        setMinute('00');
        setSecond('00');
        setMs('00');
      }
    }, step)
  }, [])
  const back = () => {
    closeWebview()
  }
  return <div className={`${s.container} ${s.success2}`}>
    <div className={s.titleBox}>
      <div className={s.flexBox}>
        <img
          src="https://auto.tancdn.com/v1/images/eyJpZCI6IlVTSFdDV1ZDQTNYWFNUTzNRNzVIQUs1S09JSjRBTTA3IiwidyI6NDU2LCJoIjo0NTYsImQiOjAsIm10IjoiaW1hZ2UvanBlZyIsImRoIjoyMzA5NDA3ODIwMTUxNDc1Mjk2fQ.png"
          alt="icon"/>
        <div className={s.title}>优先推荐购买成功</div>
      </div>
    </div>
    <ProgressCircle percent={percent}>
      <div className={s.fontContent}><span className={s.numFont}>{num}</span>倍<br/>浏览</div>
    </ProgressCircle>
    <div className={s.titlePink}>正在为你优先推荐... <br/>剩余：<span>{minute}:{second}.{ms}</span></div>
    <div className={s.fontGrey}>你的资料正被更多人看到 <br/> （附近用户较少时将适当扩大推荐范围）</div>
    <div className={s.btnPink} onClick={back}>返回</div>
  </div>;
}
