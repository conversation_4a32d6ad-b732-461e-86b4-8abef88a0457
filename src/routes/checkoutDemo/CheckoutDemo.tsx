import React, { useRef, useEffect } from "react";
import s from "./CheckoutDemo.module.css";
import "./CheckoutDemo.css";

const CheckoutDemo: React.FC = () => {
  var errors: any = {};
  errors["card-number"] = "Please enter a valid card number";
  errors["expiry-date"] = "Please enter a valid expiry date";
  errors["cvv"] = "Please enter a valid cvv code";
  console.log(window);
  const button = useRef<HTMLButtonElement>(null);
  const form = useRef<HTMLFormElement>(null);

  useEffect(() => {
    window.Frames.init({
      publicKey: "pk_test_98a563ba-4aff-4662-bedc-196d106139c1",
    });
  }, []);
  useEffect(() => {
    // 卡片校验
    window.Frames.addEventHandler(
      window.Frames.Events.CARD_VALIDATION_CHANGED,
      function (event: any) {
        console.log(11111);
        console.log("CARD_VALIDATION_CHANGED: %o", event);
        const a = window.Frames.isCardValid();
        if (button && button.current) {
          button.current.disabled = !a;
        }
      }
    );
  }, []);

  useEffect(() => {
    // 拿到token
    window.Frames.addEventHandler(
      window.Frames.Events.CARD_TOKENIZED,
      function (event: any) {
        console.log(22222);
        var el = document.querySelector(".success-payment-message");
        if (el) {
          el.innerHTML =
            "Card tokenization completed<br>" +
            'Your card token is: <span class="token">' +
            event.token +
            "</span>";
        }
      }
    );
  }, []);
  console.info(form);
  useEffect(() => {
    // 提交
    if (form && form.current) {
      form.current.addEventListener("submit", function (event) {
        console.log("submit");
        event.preventDefault();
        window.Frames.submitCard();
      });
    }
  }, [form]);
  const getErrorMessage = (event: any) => {
    if (event.isValid || event.isEmpty) {
      return "";
    }
    return errors[event.element];
  };
  useEffect(() => {
    // 校验失败
    window.Frames.addEventHandler(
      window.Frames.Events.FRAME_VALIDATION_CHANGED,
      function (event: any) {
        console.log(33333);
        console.log(event);
        var errorMessage = document.querySelector(".error-message");
        if (errorMessage && errorMessage.textContent) {
          errorMessage.textContent = getErrorMessage(event);
        }
      }
    );
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);
  return (
    <div className={s.container}>
      <div className={s.title}>checkout payment demo</div>
      <div className={s.formWrap}>
        <form
          id="payment-form"
          method="POST"
          action="https://merchant.com/charge-card"
          ref={form}
        >
          <div className={s.oneLiner}>
            <div className="card-frame"></div>
            <button ref={button} id="pay-button" disabled>
              PAY GBP 24.99
            </button>
          </div>
          <p className="error-message"></p>
          <p className="success-payment-message"></p>
        </form>
      </div>
    </div>
  );
};
export default CheckoutDemo;
