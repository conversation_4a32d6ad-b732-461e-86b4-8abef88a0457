import { url } from 'inspector';
import React, { useEffect } from 'react';
import { useHistory } from 'react-router-dom';
import { trackShare } from '../../diamondInvite/utils';
import { appUrl, getUrlParams } from '../util';



const PhoneLoginV4: React.FC = () => {
    let history = useHistory();
    const downloadUrl = getUrlParams(window?.location?.search?.slice(1))?.downloadUrl;
    useEffect(() => {
        trackShare('PV', 'p_xiaohongshu_official', '', { 'xiaohongshu_page_version': 'long' });
        window.location.href = appUrl;
    }, [])

    const jumpDownLoadGuide = () => {
        trackShare('MC', 'p_xiaohongshu_official', 'e_xiaohongshu_official_login_button', { 'xiaohongshu_page_version': 'long' });
        history.push(`./download?downloadUrl=${downloadUrl}`, { type: 'v4' });
    }
    return (
        <div style={{ overflow: 'hidden', position: 'relative'}}>
            <img style={{ width: "100%", height: "100%", 'objectFit': "cover", 'pointerEvents': "none" }} src="https://auto.tancdn.com/v1/images/eyJpZCI6IjRUUlhER1JPUEdMTk1OQlgzSUpBSjVDWExZV1VZSTA4IiwidyI6MTA4MCwiaCI6NTI1NSwiZCI6MCwibXQiOiJpbWFnZS9qcGVnIiwiZGgiOjE2MjA2NjAyNzA4NjE4NzM1MTMyfQ.png" alt="" />
            <button
                style={{
                    position: 'absolute',
                    width: '55%',
                    height: '2.7%',
                    left: '24%',
                    right: '21%',
                    top: '93.9%',
                    bottom: '3.4%',
                    borderRadius: '69px',
                    background: 'transparent',
                    border: 'none'
                }}
                onClick={jumpDownLoadGuide}
            ></button>
        </div>
    )
}

export default PhoneLoginV4;