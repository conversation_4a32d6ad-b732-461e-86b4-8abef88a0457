
@import "~sass-rem";

$rem-baseline: 90px;
.container {
    width: 100%;
    height: 100%;
    background-image: url('https://auto.tancdn.com/v1/images/eyJpZCI6IkZIRUtDQU4yNURDNkJDU0VFU1pWVVhYTzZKVU03VTA5IiwidyI6OTAwLCJoIjoxNjAwLCJkIjowLCJtdCI6ImltYWdlL2pwZWciLCJkaCI6MzM0NTQ4Mjc1Mjk0NDE3MzQ0MX0.png');
    background-size: 100% auto;
    background-repeat: no-repeat;
    background-position: center bottom 97%;
    background-attachment: local;
    overflow: hidden;

    &_top {
        box-sizing: border-box;
        width: 100%;
        padding: rem(44px 84px 125px 97px);
        img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }
    }
    &_button {
        height: rem(132px);
        width: rem(493px);
        margin-top: rem(195px);
        margin-left: rem(206px);
        border: solid rem(8px) #79b8ea;
        border-radius: rem(66px);
        background: url('https://auto.tancdn.com/v1/images/eyJpZCI6IkU3MldSSVczUzZSTVVMUTVJUzJURlkzUE8zMlBYTjA4IiwidyI6MjQ3LCJoIjo1OCwiZCI6MCwibXQiOiJpbWFnZS9qcGVnIiwiZGgiOjE0MTQ3MzkxMTg3OTk0MzAzMzEyfQ.png') no-repeat center;
        background-size:  50% 50%;
        opacity: 0.75;
        background-color: #f7f7f7;
        box-sizing: border-box;
        text-align: center;
    }
    .buttoncode {
        position: fixed;
        bottom: 0;
        font-size: 15px;
        right: 0;
        background-color: pink;
    }

    .hidden {
        display: none;
    }
}

@media screen and (max-height: 480px) {
    .container_top {
        width: 90%;
        display: flex;
        margin: 0 auto;
        align-items: center;
        justify-content: center;
        img {
            width: 95%;
        }
    }
}

@media screen and (min-height: 480px) and (max-height: 530px) {
    .container_top {
        width: 100%;
        display: flex;
        margin: 0 auto;
        align-items: center;
        justify-content: center;
        img {
            width: 95%;
        }
    }
   .container_button {
        margin-top: rem(100px);
    }
}
