import React, { useRef, useState, useContext, useEffect } from 'react';
import s from './index.module.scss';
import 'amfe-flexible/index.min.js';
import { Toast } from 'antd-mobile';
interface InputParams {
    phoneChange: Function,
    codeChange: Function,
    verifyButtonClickFn: Function,
    phoneValue: string,
    codeValue: string | '',
    buttonValue: string | '',
    imgPhoneUrl?: string,
    imgCodeUrl?: string,
    status?: string | ''
}
const ua = typeof navigator !== 'undefined' ? navigator.userAgent.toLowerCase() : '';
const isiOS = ~ua.indexOf('iphone') || ua === 'tantan-ios'

const Input: React.FC<InputParams> = (props) => {
    const inputPhoneRef = useRef<HTMLInputElement>(null);
    const codePhoneRef = useRef<HTMLInputElement>(null);
    const [focusEle, setFocusEle] = useState('phone');
    const { 
        phoneValue, 
        codeValue,
        buttonValue,
        verifyButtonClickFn,
        phoneChange,
        codeChange,
        imgPhoneUrl,
        imgCodeUrl,
        status
     } = props;
    let originHeight = document.documentElement.clientHeight || document.body.clientHeight;

    useEffect(() => {
        window.addEventListener('resize', onresize);
        document.querySelector('body')?.addEventListener('touchend', () => {
            // codePhoneRef?.current?.blur();
            // inputPhoneRef?.current?.blur();
        });
        return () => window.removeEventListener('resize', onresize);
    }, [])

    const onresize = () => {
        if (!isiOS) {
            const resizeHeight = document.documentElement.clientHeight || document.body.clientHeight;
            if (resizeHeight < originHeight) {
                // 处理安卓软键盘拉起后，输入框不再可视范围
                if(status) {
                    document.body.style.height = `${originHeight}px`;
                }
                inputPhoneRef?.current?.scrollIntoView();
            } else {
                !status && window.scrollTo({
                    top: 0,
                    behavior: "smooth"
                });
            }
        }
    }

    const phoneChangeFn = (evt: React.ChangeEvent<HTMLInputElement>) => {
        let value = evt?.target?.value.replace(/[^\d]/g, '');
        phoneChange(value);
    }
    const codeChangeFn = (evt: React.ChangeEvent<HTMLInputElement>) => {
        let value = evt?.target?.value.replace(/[^\d]/g, '');
        codeChange(value);
    }

    const codeButtonClick = () => {
        if(focusEle === 'phone') {
            inputPhoneRef?.current?.focus();
        } else if(focusEle === 'code') {
            codePhoneRef?.current?.focus();
        }
        verifyButtonClickFn();
    }

    const handleInputBlur = ()  => {
        !status && window.scrollTo(0, 0);
    }

    return (
        <div className={s.container}>
            <div className={s.container_phone}>
                <img src={
                    imgPhoneUrl ? imgPhoneUrl : "https://auto.tancdn.com/v1/images/eyJpZCI6IjRTNjNWVElFWUdSTkJDQkhGTjJRU1NJN05GRUVVTzA4IiwidyI6NDMsImgiOjc0LCJkIjowLCJtdCI6ImltYWdlL2pwZWciLCJkaCI6Mzc0MDM5MTc3NTg4NjQyMzA5MX0.png"
                } alt="" />
                <span className={s.container_phone_columnline}></span>
                <input placeholder="输入常用手机号，不错过心动机会"
                    type="text"
                    ref={inputPhoneRef}
                    value={phoneValue}
                    onChange={phoneChangeFn}
                    onFocus={() => {setFocusEle('phone')}}
                    onBlur={handleInputBlur}
                    maxLength={11} />
            </div>
            <div className={s.container_code}>
                <img src={
                    imgCodeUrl ? imgCodeUrl : "https://auto.tancdn.com/v1/images/eyJpZCI6IkhTSk5UNktBRkJRTFZCRFJDQ1BFTlJGTlFDQzNWWDA4IiwidyI6NjksImgiOjYxLCJkIjowLCJtdCI6ImltYWdlL2pwZWciLCJkaCI6MTI5NDg4ODE1MTI1NjY2OTYyMzV9.png"
                } alt="" />
                <span className={s.container_code_columnline}></span>
                <input 
                    type="text" 
                    value={codeValue} 
                    onChange={codeChangeFn}
                    maxLength={4}
                    ref={codePhoneRef}
                    onBlur={handleInputBlur}
                    onFocus={() => {setFocusEle('code')}} />
                <button onClick={codeButtonClick}>{buttonValue}</button>
            </div>
        </div>
    )
}

export default Input;