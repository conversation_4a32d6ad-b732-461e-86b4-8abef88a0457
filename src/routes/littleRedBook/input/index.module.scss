@import "~sass-rem";

$rem-baseline: 108px;

@mixin flex-style-column {
    display: flex;
    flex-direction: column;
    align-items: center;
}

@mixin boxSizing {
    box-sizing: border-box;
}


@mixin input-style {
    height: rem(85px);
    border-style: none;
    background-color: transparent;
    font-size: rem(48px);
    color: #fff;
    line-height: rem(96px);
    letter-spacing: rem(10px);
}
.container {
    z-index: 10;
    width: 100vw;
    box-sizing: border-box;
    padding: rem(0 143px 0 116px);
    @include boxSizing();
    &_phone {
        display: flex;
        height: rem(90px);
        padding: rem(0 30px 0 20px);
        border-bottom: rem(4px) solid #f7f7f7;
        &_columnline {
            width: rem(4px);
            height: rem(91px);
            margin: rem(0 46px 0 34px);
            flex-shrink: 0;
            background: #f7f7f7;
        }

        img {
            width: rem(43px);
            height: rem(74px);
        }
        input {
            @include input-style();
            flex: 1;
        }
        ::placeholder {
            color: #fff;
            font-size: rem(42px);
            letter-spacing: rem(0px);
        }
    }

    &_code {
        display: flex;
        align-items: center;
        z-index: 10;
        height: rem(90px);
        padding: rem(0 20px 0 5px);
        border-bottom: rem(4px) solid #f7f7f7;
        margin-top: rem(40px);
        &_columnline {
            width: rem(4px);
            height: rem(91px);
            margin: rem(0 46px 0 23px);
            background: #f7f7f7;
            flex-shrink: 0;
        }

        img {
            width: rem(69px);
            height: rem(61px);
        }
        input {
            @include input-style();
            max-width: rem(425px);
        }
        button {
            width: rem(223px);
            height: rem(70px);
            border: none;
            border-radius: rem(30px);
            margin-bottom: rem(25px);
            background-color: #f7f7f7;
            font-size: rem(33px);
            font-weight: bold;
            text-align: center;
            line-height: rem(70px);
            color: #f77807;
            flex-shrink: 0;
            white-space: nowrap;
        }
    }
}