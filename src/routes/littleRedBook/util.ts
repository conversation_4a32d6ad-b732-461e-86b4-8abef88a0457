
import { sendCode, verifyCode } from './service';
import { Toast } from '../../components/antd/Antd';
// import { track } from '';
import { deviceID, track  } from '../../utils/utils';
import { sha256_digest } from '../diamondInvite/sha256'
import { ajax } from '../diamondInvite/ajax';
import { Base64 } from 'js-base64';

interface ErrorType {
    [key: string]: string
}

export const getUrlParams = (params: string) => {
    const array = params.split('&');
    return array.reduce((acc: any, cur) => {
        const arrtemp = cur.split('=');
        acc[arrtemp[0]] = arrtemp[1]; 
        return acc;
    }, {})

}

const ua = typeof navigator !== 'undefined' ? navigator.userAgent.toLowerCase() : '';
export const isiOS = ~ua.indexOf('iphone') || ua === 'tantan-ios';
localStorage.setItem('deviceID', deviceID);
export const PHONE_REGEXP = /^[1][3,4,5,7,8,9][0-9]{9}$/;
export const appUrl = 'tantanapp://home';
export const ifDebug = window.location.host.includes('tantanapp.com') ? false : true;
export const countryCode = '86';

const errorStatus: ErrorType = {
    '400011': '验证码已经超时，请重新获取',
    '400013': '验证码过期',
    '400005': '无效的手机号格式',
    '400010': '验证码次数超出限制',
    '400012': '验证码次数超出限制',
    '429001': '请求太频繁，请稍后再试'
}
export const countDownFn = async (callback1: Function, callback2?: Function) => {
    let count = 59;
    callback1(count);
    let timer = setInterval(() => {
        count--;
        if(count == 0) {
            clearInterval(timer);
            callback2?.(false);
        }
        callback1(count === 0 ? '获取验证码' : count);
    }, 1000)
}

export const genHmacVersion14 = (uri: string, accessToken = '') => {
    // @ts-ignore
    const timestamp = new Date() * 1;
    const message = `${timestamp}${accessToken}${uri}`;
    const sha256 = sha256_digest(message);
    let arr = sha256.split('');
    let result = [];
    while (arr.length) {
        const item = `0x${arr.shift()}${arr.shift()}`;
        // @ts-ignore
        result.push(String.fromCharCode(parseInt(Number(item), 10)));
    }
    const base64Str = Base64.btoa(result.join(''))
    return `MAC ["14","web1.0.0","${timestamp}","${accessToken}","${base64Str}"]`
}


export const handlErrorStatus = (code: string, defaultText?: string)  => {
    Toast.info(errorStatus[code] || defaultText || '请输入正确的手机号和验证码', 1);
}