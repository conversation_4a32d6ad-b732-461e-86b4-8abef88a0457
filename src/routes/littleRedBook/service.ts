import { ajax } from '../diamondInvite/ajax';
import { genHmacVersion14, ifDebug } from './util';

const phoneDomain = ifDebug ? 'http://core.staging2.p1staff.com' : 'https://core.tantanapp.com';
const codeDomain = ifDebug ? 'http://passport.staging2.p1staff.com' : 'https://passport.tantanapp.com';

const phoneDomain_noauth = ifDebug ? 'http://growth.staging2.p1staff.com' : 'https://growth.tantanapp.com';
const codeDomain_noauth = ifDebug ? 'http://growth.staging2.p1staff.com' : 'https://growth.tantanapp.com';


export const sendCode = async (countrycode: string, phone: string): Promise<any> => {
    return await ajax.post(`${phoneDomain}/v2/confirmation-code/send`, {
        clientId: '100008',
        countryCode: parseInt(countrycode),
        mobileNumber: phone,
        language: "zh-CN",
        action: "little-red-book-verify",
        codeLength: 4,
        category: "Text",
    }, {
        headers: {
            Authorization: genHmacVersion14('/v2/confirmation-code/send'),
        }
    })
}

export const verifyCode = async (countrycode: string, code: string, phone: string) : Promise<any>=> {
    return await ajax.post(`${phoneDomain}/v2/confirmation-code/verify`, {
        clientId: '100008',
        countryCode: parseInt(countrycode),
        mobileNumber: phone,
        action: "little-red-book-verify",
        code: parseInt(code),
    }, {
        headers: {
            Authorization: genHmacVersion14('/v2/confirmation-code/verify'),
        }
    })
}

export const searchCode = async (countrycode: string, code: string, phone: string) : Promise<any>=> {
    return await ajax.post(`${codeDomain}/debug/phone/code`, {
        clientId: '100008',
        countryCode: parseInt(countrycode),
        mobileNumber: phone,
        action: 'little-red-book-verify',
    }, {
        headers: {
            Authorization: genHmacVersion14('/debug/phone/code'),
        }
    })
}

export const sendCodeNoAuth = async (phonevalue: string): Promise<any> => {
    return await ajax.post(`${phoneDomain_noauth}/v2/no_auth/red_book/send`, {
        phone: phonevalue, 
    })
}

export const verifyCodeNoAuth = async (phonevalue: string, codevalue: string): Promise<any> => {
    return await ajax.post(`${codeDomain_noauth}/v2/no_auth/red_book/verify`, {
        phone: phonevalue,
        code: codevalue
    })
}