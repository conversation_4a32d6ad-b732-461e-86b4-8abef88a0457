@import "~sass-rem";

$rem-baseline: 108px;

@mixin flex-style-column {
    display: flex;
    flex-direction: column;
    align-items: center;
}

@mixin boxSizing {
    box-sizing: border-box;
}


@mixin input-style {
    height: rem(85px);
    border-style: none;
    background-color: transparent;
    font-size: rem(42px);
    color: #fff;
    line-height: rem(96px);
    letter-spacing: rem(10px);
}
.container {
    width: 100%;
    height: 100%;
    background-image: url('https://auto.tancdn.com/v1/images/eyJpZCI6IjdBT0VGNlhRTFRGQjZRSVRVQUpSTE9WV0hOQ0pVSjA5IiwidyI6MTA4MCwiaCI6MTkxOSwiZCI6MCwibXQiOiJpbWFnZS9qcGVnIiwiZGgiOjM2NDk0MDYwMDc1Njk0NTU3MjF9.png');
    @include boxSizing();
    @include flex-style-column();
    background-size: 100% 100%;
    background-repeat: no-repeat;
    background-position: center bottom 10%;
    background-attachment: local;
    overflow: hidden;

    .bgimage {
        position: fixed;
        width: 100%;
        height: 100%;
        object-fit: cover;
        
    }
    .bgcolor {
        position: fixed;
        width: 100%;
        height: 100%;
        background-color: #000;
        opacity: 0.64;
    }

    .top {
        padding: rem(100px 0 251px 0);
        z-index: 10;
        text-align: center;
        &_logoimg {
            width: rem(114px);
            height: rem(114px);
            margin: rem(0 0 55px 0);
        }
        &_text {
            z-index: 100;
            width: rem(727px);
            margin: rem(0 108px 0 126px);
            height: rem(28px);
            background: url("https://auto.tancdn.com/v1/images/eyJpZCI6IjVBR09RV1FZQklSRlpEUFNaU082RFBIQkxQQUdSTDA5IiwidyI6NzI3LCJoIjoyOCwiZCI6MCwibXQiOiJpbWFnZS9qcGVnIiwiZGgiOjEwOTI5Nzk1Nzk5MDY4ODY0NDN9.png") center;
            background-size: 100%;
        }
        &_textimg {
            z-index: 10;
            width: rem(961px);
            height: rem(259px);
            margin: rem(203px 0 0 0);
            flex-shrink: 0;
            background: url("https://auto.tancdn.com/v1/images/eyJpZCI6Ikw1TVhCTDRDUE42RlRGUTRTVkVGSjdIVUc0NlpUNjA4IiwidyI6OTYxLCJoIjoyNTksImQiOjAsIm10IjoiaW1hZ2UvanBlZyIsImRoIjoxNDU3OTI4ODA0ODU0Mjk0NjkwfQ.png")
            no-repeat;
            background-size: 100%;
        }
    }


    
    &_inputbox {
        z-index: 999;
    }

    .bottom {
        z-index: 10;
        width: rem(595px);
        height: rem(151px);
        margin-top: rem(271px);
        border-radius: rem(73px);
        margin-bottom: rem(210px);
        flex-shrink: 0;
        background: url('https://auto.tancdn.com/v1/images/eyJpZCI6IkpWREtIWldLU1lGMkxTUEs0SDQ1U0lUWUtMREY3QzA5IiwidyI6NTk1LCJoIjoxNTEsImQiOjAsIm10IjoiaW1hZ2UvanBlZyIsImRoIjo0OTk2MDE1MTA4MTQyNDc4MTczfQ.png');
        background-size: 100%;
    }

    .buttoncode {
        position: fixed;
        bottom: 0;
        font-size: 15px;
        right: 0;
        background-color: pink;
    }

    .hidden {
        display: none;
    }
}

@media screen and (max-height: 480px) {
    .container {
        background-size: 100% 100%;
        background-position: 0% 0%;
    }
    .top {
        padding-bottom: rem(150px) !important;
        &_textimg {
            height: rem(200px);
            margin: rem(133px 0 0px 0) !important;
        }
    }
    .bottom {
        margin-top: rem(220px) !important;
    }
}
@media screen and (min-height: 480px) and (max-height: 530px) {
    .container {
        background-size: 100% 100%;
        background-position: 0% 10%;
    }
    .top {
        padding-bottom: rem(150px) !important;
        &_textimg {
            height: rem(220px);
            margin: rem(133px 0 0 0) !important;
        }
    }
    .bottom {
        margin-top: rem(180px) !important;
    }
}
