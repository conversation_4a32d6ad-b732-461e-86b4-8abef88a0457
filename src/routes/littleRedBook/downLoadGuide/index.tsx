import React, { useEffect, useRef } from 'react';
import s from './index.module.scss';
import { useLocation } from 'react-router-dom';
import 'amfe-flexible/index.min.js';
import { appUrl, ifDebug, getUrlParams } from '../util';
import { trackShare } from '../../diamondInvite/utils';
import cx from 'classnames';


interface ImageType {
    [key: string]: UrlType
}

interface UrlType {
    url: any,
    style: any,
    trackParmas: string
}


const imageUrl: ImageType = {
    'v1': {
        url: {
            bgUrl: 'https://auto.tancdn.com/v1/images/eyJpZCI6IlBYTksyTUdLQ0laMzVGSUFaU1A2N1NHSElVVFQ1TTA4IiwidyI6OTAwLCJoIjoxNjAwLCJkIjowLCJtdCI6ImltYWdlL2pwZWciLCJkaCI6MzM0NTQ4Mjc1MTU4OTgxNjI2N30.png',
            btnURl: 'https://auto.tancdn.com/v1/images/eyJpZCI6IjdBM083MklPTlZGWlZPTUE2UEZSWTQ2SjJLSjNXWDA5IiwidyI6NTU3LCJoIjoxNzAsImQiOjAsIm10IjoiaW1hZ2UvanBlZyIsImRoIjozMTU2MTY2NDE5NTc3ODgzNjg5fQ.png'
        },
        style: {
            button: {
                width: '62%',
                height: "11%",
                top: '66.2%',
                bottom: '23%',
                left: '19.2%',
                right: '19%',
                borderRadius: '92px'
            }
        },
        trackParmas: 'couple'
    },
    'v2': {
        url: {
            bgUrl: 'https://auto.tancdn.com/v1/images/eyJpZCI6IkRBNlgyS1VTMkxQU1o3NkpIWFc1QlZKNktIRU9BSjA4IiwidyI6MTA4MCwiaCI6MTkyMCwiZCI6MCwibXQiOiJpbWFnZS9qcGVnIiwiZGgiOjM2NTI4NjMyNzU0NTkxNjMyNzN9.png',
            btnURl: "https://auto.tancdn.com/v1/images/eyJpZCI6IlRYVzJXVVA2QVBRRTZFSDIzVUhIUUw1WEdOUDNOUjA4IiwidyI6NzUxLCJoIjoxODksImQiOjAsIm10IjoiaW1hZ2UvanBlZyIsImRoIjo1NTcyNTE0NjU5NDYwNDcwMDg1fQ"
        },
        style: {
            button: {
                width: '70%',
                height: '10%',
                top: '64%',
                bottom: '26%',
                left: '15%',
                right: '15%',
                borderRadius: '92px'
            }
        },
        trackParmas: 'boys'
    },
    'v3': {
        url: {
            bgUrl: 'https://auto.tancdn.com/v1/images/eyJpZCI6IkwzQzZIVEVXVFhCMlQyTkVaU1Q2NDZHMlpDVTNORzA5IiwidyI6MTgwMCwiaCI6MzIwMCwiZCI6MCwibXQiOiJpbWFnZS9qcGVnIiwiZGgiOjYxMDA4NTg3NTIzMTAzMDMwMTh9.png',
            btnURl: 'https://auto.tancdn.com/v1/images/eyJpZCI6Ik1HM0dPWkpXNTZVVVBGSFRRSFlOU1NUU0taWUFXSTA5IiwidyI6MTAyNSwiaCI6MjgxLCJkIjowLCJtdCI6ImltYWdlL2pwZWciLCJkaCI6MjU3OTQ4ODc2MzI3OTAyODgwOX0.png'
        },
        style: {
            button: {
                width: '57%',
                height: "9%",
                top: '83%',
                bottom: '9.5%',
                left: '21.5%',
                right: '21.5%',
                borderRadius: '92px'
            }
        },
        trackParmas: 'pink'
    },
    'v4': {
        url: {
            bgUrl: 'https://auto.tancdn.com/v1/images/eyJpZCI6IlRDQ1dRS0UzTFI1Q01PRkZOTkVDTklWVFM2UzJWTzA5IiwidyI6MTA4MCwiaCI6MTkyMCwiZCI6MCwibXQiOiJpbWFnZS9qcGVnIiwiZGgiOjEzODc5MzQyNzcwMjU4NzkwODQ4fQ.png',
            iconUrl: 'https://auto.tancdn.com/v1/images/eyJpZCI6IlhFQlVMREJEQ0NHRks0RTdFTzdZV0VXQlFGV0YzRzA5IiwidyI6ODcsImgiOjg3LCJkIjowLCJtdCI6ImltYWdlL2pwZWciLCJkaCI6MTA4NTA5MDc4MTYzMzMzNDg3NTB9.png',
            btnURl: 'https://auto.tancdn.com/v1/images/eyJpZCI6IjdHSkpDT0daWFJGWTRNQTVMVkpOT0dJNENSSjVUSDA4IiwidyI6NzkwLCJoIjoxOTksImQiOjAsIm10IjoiaW1hZ2UvanBlZyIsImRoIjo1NTc0MzUxNDI4NjAzMzM1ODg1fQ.png'
        },
        style: {
            button: {
                width: '72%',
                height: '10%',
                top: '84%',
                bottom: '6%',
                left: '15%',
                right: '13%',
                borderRadius: '92px'
            },
            tantanicon: {
                position: 'fixed',
                top: '2%',
                left: '3%',
                width: '8%',
                height: '5%',
                objectFit: 'contain'
            }
        },
        trackParmas: 'long'
    }
}
const ua = typeof navigator !== 'undefined' ? navigator.userAgent.toLowerCase() : '';
const isiOS = ~ua.indexOf('iphone') || ua === 'tantan-ios';


const DownLoadGuide: React.FC = () => {
    console.log(window.innerHeight, 'height');
    console.log(window.innerWidth, 'width');
    let localtionParams = useLocation();
    const imageBgRef = useRef<HTMLDivElement>(null);

    const state: any = localtionParams?.state;
    const type: string = state?.type;
    const downloadUrl = getUrlParams(window?.location?.search?.slice(1))?.downloadUrl;
    useEffect(() => {
        trackShare('PV', 'p_xiaohongshu_download', '', { 'xiaohongshu_page_version': imageUrl[type].trackParmas })
        imageBgRef.current!.style.backgroundImage = `url(${imageUrl[type].url.bgUrl})`;
    }, [])


    const downLoadTantan = () => {
        trackShare('MC', 'p_xiaohongshu_download', 'e_xiaohongshu_download_button', { 'xiaohongshu_page_version': imageUrl[type].trackParmas })
        window.location.href = appUrl;
        setTimeout(() => {
            if(isiOS) {
                window.location.href = "https://apps.apple.com/cn/app/id861891048";
            }else {
                window.location.href = ifDebug ? "https://tantanapp.com/tantan_recall.apk" : decodeURIComponent(downloadUrl);
            }

        }, 2000)
    }

    return (
        <div className={cx(s.container, s[type])}>
            <div className={s.bgimage} ref={imageBgRef}></div>
            {
                imageUrl[type].url.iconUrl ? 
                <img
                style={{
                    position: imageUrl[type].style.tantanicon.position,
                    width: imageUrl[type].style.tantanicon.width,
                    height: imageUrl[type].style.tantanicon.height,
                    top: imageUrl[type].style.tantanicon.top,
                    left: imageUrl[type].style.tantanicon.left,
                    objectFit: imageUrl[type].style.tantanicon.objectFit,
                }}
             src={(type && imageUrl[type]) && imageUrl[type].url.iconUrl} alt="" /> : null
            }
           <div 
            className={s.button}
            onClick={downLoadTantan}
            style={{
                width: imageUrl[type].style.button.width,
                height: imageUrl[type].style.button.height,
                top: imageUrl[type].style.button.top,
                bottom: imageUrl[type].style.button.bottom,
                left: imageUrl[type].style.button.left,
                right: imageUrl[type].style.button.right,
                borderRadius: imageUrl[type].style?.button?.borderRadius
            }}
           >
           <img
                src={imageUrl[type]?.url?.btnURl}
            ></img>
           </div>
        </div>
    )
}

export default DownLoadGuide;