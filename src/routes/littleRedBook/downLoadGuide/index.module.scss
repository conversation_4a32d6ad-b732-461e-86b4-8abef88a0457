

.container {
    position: relative;
    width: 100%;
    height: 100%;
    overflow: hidden;
    img {
        width: 100%;
        height: 100%;
        object-fit: cover;
        pointer-events: none;
    }
    .button {
        position: fixed;
        z-index: 9999;
        border: none;
        background: transparent;
        img {
            width: 100%;
            height: 100%;
            object-fit: contain;
        }
    }
    .bgimage {
        width: 100%;
        height: 100%;
        background-repeat: no-repeat;
        background-size: 100% 100%;
    }


    
}

 

@media screen and (min-height: 480px) and (max-height: 530px) {
    .v2 {
        .button {
            background-color: blue;
            top: 70% !important;
        }
    }
    .v4 {
        .button {
            top: 83.5%;
            height: 10.5% !important;
        }
    }
}

