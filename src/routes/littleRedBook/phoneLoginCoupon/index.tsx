import React, { useEffect, useState } from "react";
import { useHistory } from 'react-router-dom';
import { getUrlParams, PHONE_REGEXP, countryCode, countDownFn, handlErrorStatus, ifDebug, appUrl } from '../util';
import { Toast } from '../../../components/antd/Antd';
import { sendCodeNoAuth, verifyCodeNoAuth, searchCode } from '../service';

import Input from '../input/index';
import s from './index.module.scss';
import 'amfe-flexible/index.min.js';
import { trackShare } from '../../diamondInvite/utils';

const pid = 'p_xiaohongshu_freevip_official';

const PhoneLoginCoupon: React.FC = () => {

    const [phoneValue, setPhoneValue] = useState('');
    const [codeValue, setCodeValue] = useState('');
    const [buttonValue, setButtonValue] = useState<any>('获取验证码');
    const [buttonDiasble, setButtonDisable] = useState(false);
    const downloadUrl = getUrlParams(window?.location?.search?.slice(1))?.downloadUrl;
    let history = useHistory();

    useEffect(() => {
        trackShare('PV', pid);
        window.location.href = appUrl;
    }, [])

    const verifyButtonClickFn = async () => {
        // 判断当前网络

        if(!window.navigator.onLine) {
            Toast.info('网络异常', 1);
            return;
        }
        if (buttonDiasble === true) return;
        trackShare('MC', pid, 'e_xiaohongshu_freevip_verification_code_button')
        if (!phoneValue) {
            Toast.info('请输入正确的手机号', 1)
        } else if (phoneValue.length === 11 && PHONE_REGEXP.test(phoneValue)) {
            const result = await sendCodeNoAuth(phoneValue);
            switch (result?.meta?.code) {
                case 200:
                    setButtonDisable(true);
                    countDownFn(setButtonValue, setButtonDisable);
                    break;
                case 90001:
                    Toast.info('无效手机号', 1)
                    break;
                case 90003:
                    Toast.info('请求太频繁，请稍后再试', 1)
                    break;
                case 90004:
                    Toast.info('验证码次数超出限制', 1)
                    break;
                default:
                    Toast.info('请输入正确的手机号', 1)
                    break;

            }
        } else {
            Toast.info('请输入正确的手机号', 1)
        }
    }

    const jumpDownLoadGuide = async () => {
         // 判断当前网络
         if(!window.navigator.onLine) {
            Toast.info('网络异常', 1);
            return;
        }
        if (!phoneValue) {
            Toast.info('手机号或验证码错误', 1)
        } else if (!PHONE_REGEXP.test(phoneValue)) {
            Toast.info('手机号或验证码错误', 1);
        } else if (!codeValue || (codeValue && codeValue.length !== 4)) {
            Toast.info("手机号或验证码错误", 1);
        } else {
            const result = await verifyCodeNoAuth(phoneValue, codeValue);
            switch (result?.meta?.code) {
                case 200:
                    trackShare('MC', pid, 'e_xiaohongshu_freevip_claim_button');
                    history.push(`./download_coupon?downloadUrl=${downloadUrl}`, { award: result?.data?.reward });
                    break;
                case 90001:
                    Toast.info('手机号或验证码错误', 1)
                    break;
                case 90002:
                    Toast.info('手机号或验证码错误', 1)
                    break;
                case 90005:
                    Toast.info('验证码验证次数超出限制, 请10分钟后重试', 1)
                    break;
                case 90006:
                    Toast.info('验证码过期', 1)
                    break;
                default:
                    Toast.info('手机号或验证码错误', 1)
                    break;

            }
            console.log(result);
        }
    }

    return (
        <div className={s.container}>
            <div className={s.container_top}>
                {/* 探探icon */}
                <div className={s.container_top_bg}></div>
                <img className={s.container_top_icon} src="https://auto.tancdn.com/v1/images/eyJpZCI6IkRERlNINFhKR0JCTkRLR1pSNkZKVkZOSlhZVlRQNTA2IiwidyI6ODQsImgiOjg0LCJkIjowLCJtdCI6ImltYWdlL2pwZWciLCJkaCI6NjE2NzE2NDIwMzg2ODA0OTI5NH0.png" alt="" />
                {/* title */}
                <img className={s.container_top_title} src="https://auto.tancdn.com/v1/images/eyJpZCI6Ik5FREFMWklTQTZaWlNRWU1GWEdOWkJVQzNZN0ZWMjA2IiwidyI6MTAxNywiaCI6NTE0LCJkIjowLCJtdCI6ImltYWdlL2pwZWciLCJkaCI6NzY2MTM1MTA2NjM1MTExMzQ4MH0.png" alt="" />
            </div>
            <div className={s.container_context}>
                <img src="https://auto.tancdn.com/v1/images/eyJpZCI6IklYNlg2S1FLNk5ZRTdUNFdFTUU2TFRRUVJMVEJQSzA5IiwidyI6MTA4MCwiaCI6Nzk5LCJkIjowLCJtdCI6ImltYWdlL2pwZWciLCJkaCI6NTA3NzA1Njg4MDgxMzg3NjQ1MH0.png" alt="" />
            </div>
            <div className={s.container_bottom}>
                <div className={s.container_bottom_inputbox}>
                    <Input
                        phoneValue={phoneValue}
                        codeValue={codeValue}
                        buttonValue={buttonValue}
                        verifyButtonClickFn={verifyButtonClickFn}
                        phoneChange={setPhoneValue}
                        codeChange={setCodeValue}
                        status='updateStatus'
                        imgPhoneUrl="https://auto.tancdn.com/v1/images/eyJpZCI6IjZJTFBMSDJMVTQyNVJOTUc2R1VNRVZXVk9aUlBGNjA2IiwidyI6MzgsImgiOjY1LCJkIjowLCJtdCI6ImltYWdlL2pwZWciLCJkaCI6Mzc0MjY0NTc3Mzg4NDUxODQ1MX0.png"
                        imgCodeUrl="https://auto.tancdn.com/v1/images/eyJpZCI6Ilk3S1JDRFlKRU1BWFNTUUg1TFlWQVRVVFhHSzVOTzA2IiwidyI6NjEsImgiOjUzLCJkIjowLCJtdCI6ImltYWdlL2pwZWciLCJkaCI6MzcyNTM2ODc0NjgxMzEwNjIxMX0.png"
                    ></Input>
                </div>
                <div className={s.container_bottom_button} onClick={jumpDownLoadGuide}></div>
                <div className={s.container_bottom_bg}></div>
            </div>
        </div>
    )
}

export default PhoneLoginCoupon;