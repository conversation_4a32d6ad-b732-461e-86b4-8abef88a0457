@import "~sass-rem";

$rem-baseline: 108px;

.container {
    display: flex;
    flex-direction: column;
    height: 100%;
    background-image: url("https://auto.tancdn.com/v1/images/eyJpZCI6IlBBRlZZRUlGTFVNU1dJQU5NM0tDRVE3SEpBWEkyVTA5IiwidyI6MTA4MCwiaCI6MTkwOCwiZCI6MCwibXQiOiJpbWFnZS9qcGVnIiwiZGgiOjB9.png");
    background-size: 100% 100%;
    overflow: hidden;
    
    &_top {
        height: 31.5%;
        &_bg {
            width: 100%;
            position: absolute;
            // height: 31vh;
            height: 53.96%;
            background-image: url("https://auto.tancdn.com/v1/images/eyJpZCI6IlhZU05HUFNRVzZQQ0tJRTVGQk00VExLSjI2UUxCQzA2IiwidyI6MTA4MCwiaCI6MzM3LCJkIjowLCJtdCI6ImltYWdlL2pwZWciLCJkaCI6MTgwNzA5ODMzMTY2OTAxMzM2fQ.png");
            background-size: 100% auto;
            background-repeat: no-repeat;
            background-position: top;
           
        }
        &_icon {
            position: relative;
            width: rem(84px);
            height: rem(84px);
            margin-top: rem(10px);
            margin-left: rem(10px);
        }
        &_title {
            position: relative;
            box-sizing: border-box;
            width: 100%;
            // height: 27vh;
            height: 85.7%;
            padding-left: rem(26px);
            padding-right: rem(41px);
            margin-top: rem(-20px);
            object-fit: fill;
        }
    }
    &_context {
        width: 100%;
        position: relative;
        height: 41.5%;
        img {
            width: 100%;
            height: 100%;
            object-fit: fill;
        }
    }
    &_bottom {
        height: 27%;
        position: relative;
        width: 100%;
        flex: 1;
        &_inputbox {
            // height: 16vh;
            height: 62%;
            display: flex;
            align-items: center;
        }
        &_bg {
            width: 100%;
            position: absolute;
            bottom: 0;
            // height: 23vh;
            height: 85.185%;
            background-image: url('https://auto.tancdn.com/v1/images/eyJpZCI6IkRQTVJVM0MyUEFDSVMzRE5RTjRDNlRBUVhMUlhINjA2IiwidyI6MTA4MCwiaCI6NDUyLCJkIjowLCJtdCI6ImltYWdlL2pwZWciLCJkaCI6MTUwNDI3NjQ1MTM0NjE5NDQ1MTJ9.png'); 
            background-size: 100% 100%;
            pointer-events: none;
        }
        &_button {
            width: 58vw;
            margin: 0 auto;
            // height: 8vh;
            height: 29.63%;
            background-image: url("https://auto.tancdn.com/v1/images/eyJpZCI6IjdRQ1JMWTJXSVhaTFRVWFpBM05BTVJCWEJEM1QzRTA2IiwidyI6NjQxLCJoIjoxNjQsImQiOjAsIm10IjoiaW1hZ2UvanBlZyIsImRoIjo1NTcwMjA4Nzc5MjM1NDgxMjkzfQ.png");
            background-size: 100% 100%;
            background-position: center;
            background-repeat: no-repeat;
        }
    }
}
.container_bottom_inputbox > div {
    height: 72%;
    padding: 0;
    width: 66vw;
    margin-left: 15%;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    div {
        box-sizing: border-box;
        height: 42%;
        padding: 0;
        margin: 0;
        display: flex;
        align-items: center;
        border-bottom: rem(4px) solid #4b4b4b;
        span {
            display: inline-block;
            background-color: #4b4b4b;
            margin: 0;
            height: 100%;
        }
        img {
            width: 10vw;
            flex-shrink: 0;
            height: 72%;
            object-fit: contain;
        }
        input {
            height: 100%;
            padding-left: rem(45px);
            color:  #4b4b4b;
        }
        ::placeholder {
            color:  #4b4b4b;
            font-size: rem(36px);
        }
        button {
            color: #fff;
            height: 70%;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: rem(28px);
            margin-bottom: 0;
            padding: 0;
            background-color: #4b4b4b;
        }
    }
}
.container_bottom_inputbox > div >:nth-child(2) {
    input  {
        max-width: rem(330px);
    }
    img {
        height: 65%;
    }
}
