import React, { Component } from 'react';
import { Route, Switch, useRouteMatch,useParams } from "react-router-dom"
import PhoneLoginV1 from './phoneLoginV1/index';
import PhoneLoginV2 from './phoneLoginV2/index';
import PhoneLoginV3 from './phoneLoginV3/index';
import PhoneLoginV4 from './phoneLoginV4/index';
import PhoneLoginCoupon from './phoneLoginCoupon/index';
import DownLoadGuide from './downLoadGuide/index';
import DownLoadCoupon from './downloadCoupon/index';

const LittleRedbook: React.FC = () => {
    const match = useRouteMatch();
    

  return (
          <Switch>
            <Route exact path={`${match.path}/loginv1`} component={PhoneLoginV1} />
            <Route exact path={`${match.path}/loginv2`} component={PhoneLoginV2} />
            <Route exact path={`${match.path}/loginv3`} component={PhoneLoginV3} />
            <Route exact path={`${match.path}/loginv4`} component={PhoneLoginV4} />
            <Route exact path={`${match.path}/login_coupon`} component={PhoneLoginCoupon} />
            <Route exact path={`${match.path}/download`} component={DownLoadGuide} />
            <Route exact path={`${match.path}/download_coupon`} component={DownLoadCoupon} />
        </Switch>
    )
}

export default LittleRedbook;