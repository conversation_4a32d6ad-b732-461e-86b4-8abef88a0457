import React, { useEffect } from "react";
import { useLocation } from 'react-router-dom';
import 'amfe-flexible/index.min.js';
import s from './index.module.scss';
import { Toast } from '../../../components/antd/Antd';
import { ifDebug, appUrl, getUrlParams, isiOS } from '../util';
import { trackShare } from '../../diamondInvite/utils';

const pid = 'p_xiaohongshu_freevip_download';
const DownLoadCoupon: React.FC = () => {
    const downloadUrl = getUrlParams(window?.location?.search?.slice(1))?.downloadUrl;
    let localtionParams = useLocation();

    const state: any = localtionParams?.state;
    const award: string = state?.award;
    useEffect(() => {
        trackShare('PV', pid)
        trackShare('MV', pid, 'e_xiaohongshu_freevip_download_tips', {
            xiaohongshu_freevip_is_qualified: award ? 'yes' : 'no' // yes或者no
        })
        
        award ? Toast.info('恭喜，您已获得奖励资格', 5) : Toast.info('抱歉，该手机号已注册过，登录后无法获得新用户奖励', 5);
    }, [])
    const downLoadTantan = () => {
        trackShare('MC', pid, 'e_xiaohongshu_freevip_download_button')
        if(isiOS) {
            window.location.href = "https://apps.apple.com/cn/app/id861891048";
        }else {
            window.location.href = ifDebug ? "https://tantanapp.com/tantan_recall.apk" : decodeURIComponent(downloadUrl);
        }
    }

    return (
        <div className={s.container}>
            <div className={s.container_top}>
                {/* 探探icon */}
                <div className={s.container_top_bg}></div>
                <img className={s.container_top_icon} src="https://auto.tancdn.com/v1/images/eyJpZCI6IkRERlNINFhKR0JCTkRLR1pSNkZKVkZOSlhZVlRQNTA2IiwidyI6ODQsImgiOjg0LCJkIjowLCJtdCI6ImltYWdlL2pwZWciLCJkaCI6NjE2NzE2NDIwMzg2ODA0OTI5NH0.png" alt="" />
                {/* title */}
                <img className={s.container_top_title} src="https://auto.tancdn.com/v1/images/eyJpZCI6Ik5FREFMWklTQTZaWlNRWU1GWEdOWkJVQzNZN0ZWMjA2IiwidyI6MTAxNywiaCI6NTE0LCJkIjowLCJtdCI6ImltYWdlL2pwZWciLCJkaCI6NzY2MTM1MTA2NjM1MTExMzQ4MH0.png" alt="" />
            </div>
            <div className={s.container_context}>
                <img src="https://auto.tancdn.com/v1/images/eyJpZCI6IklNRVBWWlVMWEM3RlJMS1daSUhQNlNRUFdSSUFFQjA2IiwidyI6MTA4MCwiaCI6Nzk5LCJkIjowLCJtdCI6ImltYWdlL2pwZWciLCJkaCI6MTQzNTY0Mjk5NTM1OTI3MzcxOH0.png" alt="" />
            </div>
            <div className={s.container_bottom}>
                <div className={s.container_bottom_button} onClick={downLoadTantan}>
                    <img src="https://auto.tancdn.com/v1/images/eyJpZCI6IlJXM1AzNDdMN1dDRUtEVVMyWkpTWUNETVNVQlFOTTA2IiwidyI6NjQxLCJoIjoxNjQsImQiOjAsIm10IjoiaW1hZ2UvanBlZyIsImRoIjo0OTkzNjY4NTUyNzIyOTk0ODkzfQ.png" alt="" />
                </div>
            <div className={s.container_bottom_bg}></div>
            </div>
        </div>
    )
}

export default DownLoadCoupon;