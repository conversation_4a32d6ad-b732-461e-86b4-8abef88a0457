@import "~sass-rem";

$rem-baseline: 108px;

.container {
    display: flex;
    flex-direction: column;
    height: 100%;
    background-image: url("https://auto.tancdn.com/v1/images/eyJpZCI6IlBBRlZZRUlGTFVNU1dJQU5NM0tDRVE3SEpBWEkyVTA5IiwidyI6MTA4MCwiaCI6MTkwOCwiZCI6MCwibXQiOiJpbWFnZS9qcGVnIiwiZGgiOjB9.png");
    background-size: 100% 100%;
    overflow: hidden;
    
    &_top {
        height: 31.5%;
        &_bg {
            width: 100%;
            position: absolute;
            // height: 31vh;
            height: 53.96%;
            background-image: url("https://auto.tancdn.com/v1/images/eyJpZCI6IlhZU05HUFNRVzZQQ0tJRTVGQk00VExLSjI2UUxCQzA2IiwidyI6MTA4MCwiaCI6MzM3LCJkIjowLCJtdCI6ImltYWdlL2pwZWciLCJkaCI6MTgwNzA5ODMzMTY2OTAxMzM2fQ.png");
            background-size: 100% auto;
            background-repeat: no-repeat;
            background-position: top;
           
        }
        &_icon {
            position: relative;
            width: rem(84px);
            height: rem(84px);
            margin-top: rem(10px);
            margin-left: rem(10px);
        }
        &_title {
            position: relative;
            box-sizing: border-box;
            width: 100%;
            // height: 27vh;
            height: 85.7%;
            padding-left: rem(26px);
            padding-right: rem(41px);
            margin-top: rem(-20px);
            object-fit: fill;
        }
    }
    &_context {
        width: 100%;
        position: relative;
        height: 42.5%;
        img {
            width: 100%;
            height: 100%;
            object-fit: fill;
        }
    }
    &_bottom {
        height: 27%;
        position: relative;
        width: 100%;
        flex: 1;
        &_bg {
            width: 100%;
            position: absolute;
            bottom: 0;
            // height: 23vh;
            height: 85.185%;
            background-image: url('https://auto.tancdn.com/v1/images/eyJpZCI6IkRQTVJVM0MyUEFDSVMzRE5RTjRDNlRBUVhMUlhINjA2IiwidyI6MTA4MCwiaCI6NDUyLCJkIjowLCJtdCI6ImltYWdlL2pwZWciLCJkaCI6MTUwNDI3NjQ1MTM0NjE5NDQ1MTJ9.png'); 
            background-size: 100% 100%;
            pointer-events: none;
        }
        &_button {
            height: 74.07%;
            display: flex;
            align-items: center;
            justify-content: center;
            img {
                width: 58vw;
                height: 40%;
                object-fit: contain;
            }
        }
    }
}