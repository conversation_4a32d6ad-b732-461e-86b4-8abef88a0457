import React, { useEffect, useState, useRef } from 'react';
import s from './index.module.scss';
import 'amfe-flexible/index.min.js';

import { useHistory } from 'react-router-dom';
import Input from '../input/index'
import { sendCode, verifyCode, searchCode } from '../service';
import { countDownFn, ifDebug, PHONE_REGEXP, countryCode, handlErrorStatus, appUrl, getUrlParams } from '../util';
import { trackShare } from '../../diamondInvite/utils';
import { Toast } from '../../../components/antd/Antd';

const PhoneLoginV3: React.FC = () => {
    const [phoneValue, setPhoneValue] = useState('');
    const [codeValue, setCodeValue] = useState('');
    const [buttonValue, setButtonValue] = useState<any>('获取验证码');
    const [buttonDiasble, setButtonDisable] = useState(false);
    let history = useHistory();
    const downloadUrl = getUrlParams(window?.location?.search?.slice(1))?.downloadUrl;
    useEffect(() => {
        trackShare('PV', 'p_xiaohongshu_official', '', { 'xiaohongshu_page_version': 'pink' });
        window.location.href = appUrl;
    }, [])


    const verifyButtonClickFn = async () => {
        if (buttonDiasble === true) return;
        trackShare('MC', 'p_xiaohongshu_official', 'e_xiaohongshu_official_verification_code_button', { 'xiaohongshu_page_version': 'pink' });
        if (!phoneValue) {
            Toast.info('请输入手机号', 1)
        } else if (phoneValue.length === 11 && PHONE_REGEXP.test(phoneValue)) {
            const { meta: { code } } = await sendCode(countryCode, phoneValue);
            if (true && code && code === 200000) {
                setButtonDisable(true);
                countDownFn(setButtonValue, setButtonDisable);
            } else {
                handlErrorStatus(code, '请输入正确的手机号');
            }
        } else {
            Toast.info('请输入正确的手机号', 1)
        }
    }

    const jumpDownLoadGuide = async () => {
        trackShare('MC', 'p_xiaohongshu_official', 'e_xiaohongshu_official_login_button', { 'xiaohongshu_page_version': 'pink' });
        if (!phoneValue) {
            Toast.info('请输入手机号验证码', 1)
        } else if (!PHONE_REGEXP.test(phoneValue)) {
            Toast.info('手机号验证码错误', 1);
        } else if (!codeValue || (codeValue && codeValue.length !== 4)) {
            Toast.info(`${!codeValue ? '请输入验证码' : '验证码错误，请重新填写'}`, 1);
        } else {
            const { meta: { code } } = await verifyCode(countryCode, codeValue, phoneValue);
            if (code && code === 200000) {
                history.push(`./download?downloadUrl=${downloadUrl}`, { type: 'v3' });
            } else {
                handlErrorStatus(code, '验证码错误，请重新填写');
            }
        }
    }

    const getDebugCode = async () => {
        const result = await searchCode(countryCode, codeValue, phoneValue);
        Toast.info(result, 2);
    }

    return (
        <div className={s.container}>
            <img className={s.container_icon} src="https://auto.tancdn.com/v1/images/eyJpZCI6IkJVV1dBTTI0Tk9TVFdWTk4zSFRLV1lKU0VESEoySDA4IiwidyI6MjEzLCJoIjoyMTMsImQiOjAsIm10IjoiaW1hZ2UvanBlZyIsImRoIjoxMDg2ODM1OTI2NDg4OTQ0MTg2OX0.png" alt="" />
            <div className={s.container_top} >
                <img src="https://auto.tancdn.com/v1/images/eyJpZCI6IkRWNVhWRk5CQlFXTUFINzJXRTVKWFNHSkdHM1FMSTA4IiwidyI6MTQ5NywiaCI6MTI4MCwiZCI6MCwibXQiOiJpbWFnZS9qcGVnIiwiZGgiOjk4NTY3ODM4NDU4NDU2NTc3OX0.png" alt="" />
            </div>
            <div className={s.container_content}>
            <div className={s.container_content_inputbox}>
                <Input
                    phoneValue={phoneValue}
                    codeValue={codeValue}
                    buttonValue={buttonValue}
                    verifyButtonClickFn={verifyButtonClickFn}
                    phoneChange={setPhoneValue}
                    codeChange={setCodeValue}
                > </Input>
            </div>
            <div className={s.container_content_button} onClick={jumpDownLoadGuide}></div>
            <button className={`${ifDebug ? s.buttoncode : s.hidden}`} onClick={getDebugCode}>获取验证码</button>
            </div>
        </div>
    )
}

export default PhoneLoginV3;