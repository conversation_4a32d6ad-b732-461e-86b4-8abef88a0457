@import "~sass-rem";

$rem-baseline: 180px;


.container {
    width: 100%;
    height: 100%;
    background-image: url('https://auto.tancdn.com/v1/images/eyJpZCI6IjNPSlNQUFBGSlJMRElWVUxWT1kyNEhHNERORUFHTDA2IiwidyI6MTgwMCwiaCI6MzIwMCwiZCI6MCwibXQiOiJpbWFnZS9qcGVnIiwiZGgiOjU3NjU0MTY0MTYwODM5Njk2MH0.png');
    background-size: 100% auto;
    background-repeat: no-repeat;
    // background-attachment: local;
    overflow: hidden;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    &_icon {
        width: rem(213px);
        height: rem(213px);
        object-fit: cover;
        margin-top: rem(77px);
        margin-left: rem(92px);
    }
    &_top {
        box-sizing: border-box;
        width: 100%;
        height: 50vh;
        padding: rem(230px 122px 100px 173px);
        img {
            width: 100%;
            height: 100%;
            object-fit: fill;
        }
    }

    &_content {
        width: 100%;
        background-image: url('https://auto.tancdn.com/v1/images/eyJpZCI6IkJMRUEzQlpDN1VZRk1MWU43MkFYSE9XTVVPVDJWSzA2IiwidyI6MTgwMCwiaCI6MTIxMCwiZCI6MCwibXQiOiJpbWFnZS9qcGVnIiwiZGgiOjEyMzM1NDc2NjJ9.png');
        background-size: 100% 100%;
        background-repeat: no-repeat;
        background-position: 0% 0%;
        background-color:transparent;
        overflow: auto;
        flex-shrink: 0;

    }
    &_content_inputbox {
        margin-top: rem(247px);
    }
    &_content_button {
        height: rem(240px);
        width: rem(871px);
        margin-top: rem(180px);
        margin-left: rem(465px);
        margin-right: rem(465px);
        margin-bottom: rem(142px);
        border-radius: rem(119px);
        background: url('https://auto.tancdn.com/v1/images/eyJpZCI6IlVBR0lEN1FNQUVaRksyREVDWU9VRFRGTVdTWjNYVjA4IiwidyI6ODcxLCJoIjoyNDAsImQiOjAsIm10IjoiaW1hZ2UvanBlZyIsImRoIjo3OTE0MDM1NDIyODI2NDUyMDExfQ.png') no-repeat center;
        background-size:100%;
        text-align: center;
    }
    .buttoncode {
        position: fixed;
        bottom: 0;
        font-size: 15px;
        right: 0;
        background-color: pink;
    }

    .hidden {
        display: none;
    }
}

@media screen and (max-width: 320px) {
    .container_top {
        padding: rem(0px 122px 50px 247px);
    }
    .container_content_inputbox {
        margin-top: rem(150px);
    }
}

@media screen and(min-height: 480px) and (max-height: 530px) {
    .container_top {
        padding: rem(0px 122px 50px 247px);
    }
    .container_content_inputbox {
        margin-top: rem(150px);
    }
}


