.blockHeader {
  font-family: PingFangSC-Medium;
  color: rgb(153, 153, 153);
  background-color: rgb(247, 247, 247);
  padding: 0.2rem 0 0.08rem 0.18rem;
  border-bottom: 0.01rem solid rgb(247, 247, 247);
}
.topicItem {
  padding: 0 0.16rem 0 0.16rem;
}
.header {
  display: flex;
  justify-content: space-between;
}
.leftBlock {
  padding-top: 0.15rem;
  padding-bottom: 0.14rem;
  width: 100%;
}
.title {
  color: rgb(33, 33, 33);
  font-size: 0.14rem;
  margin-bottom: 0.02rem;
  line-height: 0.2rem;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  width: calc(100% - 0.05rem);
}
.count {
  color: rgb(191, 191, 191);
  font-size: 0.12rem;
  line-height: 0.12rem;
}
.rightBlock {
  display: flex;
  align-items: center;
  justify-content: right;
  font-size: 0;
}
.rightBlock img {
  width: 0.06rem;
  height: 0.1rem;
}
.imgContainer {
  padding-bottom: 0.3rem;
  border-bottom: 0.01rem solid rgb(233, 233, 233);
  display: flex;
  justify-content: space-evenly;
}
.imgWrap {
  position: relative;
  width: calc((100% - 0.12rem) / 4);
  padding-bottom: calc((100% - 0.12rem) / 4);
  border-radius: 0.02rem;
}
.imgWrap + .imgWrap {
  margin-left: 0.04rem;
}
.imgWrap:first-child .img {
  border-top-left-radius: 0.06rem;
  border-bottom-left-radius: 0.06rem;
}
.imgWrap:last-child .img {
  border-bottom-right-radius: 0.06rem;
  border-top-right-radius: 0.06rem;
}
.img {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  border-radius: 0.02rem;
  background-size: cover;
  background-repeat: no-repeat;
  background-position: center center;
}
.img::after {
  content: '';
  font-size: 0.1rem;
  text-align: center;
  padding-top: 40%;
  width: 100%;
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  border-radius: 0.02rem;
  background-color: #f7f7f7;
  z-index: -1;
}
.hotListFooter {
  font-size: 0.14rem;
  color: rgb(208, 208, 208);
  padding-bottom: 0.3rem;
  text-align: center;
  padding-top: 0.24rem;
}
