@import "~sass-rem";

$rem-baseline: 37.5px;

* {
  box-sizing: border-box;
  font-family: PingFangSC, "PingFang SC", PingFangSC-Regular, "Helvetica Neue", -apple-system, BlinkMacSystemFont, STHeiti,"Microsoft Yahei",Tahoma,Simsun,sans-serif;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  -webkit-touch-callout: none;
}

img {
  pointer-events:none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  -webkit-touch-callout: none;
}

div {

  font-family: PingFangSC, "PingFang SC", PingFangSC-Regular, "Helvetica Neue", -apple-system, BlinkMacSystemFont, STHeiti,"Microsoft Yahei",Tahoma,Simsun,sans-serif;
}

body {
  font-family: PingFangSC, "PingFang SC", PingFangSC-Regular, "Helvetica Neue", -apple-system, <PERSON>linkMacSystemFont, STHeiti,"Microsoft Yahei",<PERSON>hom<PERSON>,<PERSON>un,sans-serif;
}

.container {
  word-break: keep-all;
  width: 100%;
  background-color: #fd7a65;
  // position: relative;
  padding-bottom: 30px;
}

.image {
  width: rem(375px);
  height: rem(375px);
}

.whiteCard {
  background-color: #ffffff;
  width: rem(343px);
  margin-left: rem(16px);
  margin-right: rem(16px);
  border-radius: 8px;
  border: solid 1px #292d2e;
}

// .cardShadow {
//   width: rem(343px);
//   margin: rem(5px 0 0 6px);
//   border-radius: 8px;
//   background-color: #f15e47;
// }

.cardWithTitle {
  position: relative;
  top: rem(20px);
}

.linkToActivityRule {
  position: absolute;
  right: 0;
  top: 0;
  width: rem(30px);
  height: rem(90px);
  z-index: 2;
}