import React, { useEffect, useState } from 'react';
import 'amfe-flexible/index.min.js';
import axios, { AxiosInstance } from 'axios';
import styles from "./friendInvite2.module.scss";
import ShareCardA from '../../components/friendInvite2shareCardA/shareCardA';
import ShareCardB from '../../components/friendInvite2shareCardB/friendInvite2shareCardB';
import StepCard from '../../components/friendInvite2stepCard/stepCard';
import InviteCard from '../../components/friendInvite2inviteCard/inviteCard';
import FriendInvite2Carousel from '../../components/friendInvite2Carousel/friendInvite2Carousel';
import {setNavigationTitle, getAuthorizationHeader, getAbHeader, trackNew} from "../../utils/bridge";

const baseUrl = (process.env.REACT_APP_TEST || process.env.NODE_ENV !== 'production')
? 'http://growth.staging2.p1staff.com'
: 'https://growth.tantanapp.com';

let ajax = axios.create({
  baseURL: baseUrl,
});

const topImage = 'https://auto.tancdn.com/v1/raw/163ecdc6-e349-43c7-b160-f92afc7eca650607.png';

const linkToActivityRule = () => {
  window.location.pathname = 'commerce/FriendInvite2Rule';
}

const pageId = 'p_invite_friends';

const FriendInvite2: React.FC = () => {
  const [avatarList, setAvatarList] = useState<any[]>([]);
  const [rankList, setRankList] = useState<any[]>([]);
  const [userList, setUserList] = useState<any[]>([]);
  const [inviteCode, setInviteCode] = useState('');
  const [totalInviteNum, setTotalInviteNum] = useState(0);
  const [page, setPage] = useState('1');
  const [searchParams, setSearchParams] = useState<Record<string, string>>({});
  useEffect(() => {
    setNavigationTitle({
      title: '邀请单身好友',
    });

    let tmpParams: Record<string, string> = {};
    if (window.location.search.length > 2) {
      const { search } = window.location;
      search.slice(1).split('&').forEach((cur) => {
        const [key, val] = cur.split('=');
        tmpParams[key] = val;
      })
    }
    console.log('tmpParams', tmpParams);
    setSearchParams(tmpParams);

    trackNew({
      pageId,
      type: 'PV',
      extras: {
        from_where_invite: tmpParams.from,
        tooltips_trigger_mode: 'active',
      }
    });

    getAuthorizationHeader('/v2/h5/invite/init')
      .then((auth) => {
        console.log('auth', auth);
        return ajax.get('/v2/h5/invite/init', {
          headers: {
            'Authorization': auth,
          }
        });
      })
      .then((res) => {
        console.log(res);
        if (res.status === 200 && res.data.meta.code === 200 && res.data.data) {
          setRankList(res.data.data.invite_board);
          setAvatarList(res.data.data.succeed_imgs);
          setUserList(res.data.data.invite_page_records);
          setInviteCode(res.data.data.invite_code);
          setTotalInviteNum(res.data.data.invite_count);
        }
      })
      .catch((err) => console.log(err));

  }, []);

  const showMore = () => {
    if (userList.length < totalInviteNum) {
      getAuthorizationHeader('/v2/h5/invite/record', { page })
      .then((auth) => {
        console.log('auth', auth);
        return ajax.post(
          '/v2/h5/invite/record',
          {
            page,
          },
          {
            headers: {
              'Authorization': auth,
            }
          }
        );
      })
      .then((res) => {
        console.log(res);
        if (res.status === 200 && res.data.meta.code === 200 && res.data.data) {
          setTotalInviteNum(res.data.data.invite_count);
          setPage((parseInt(page) + 1).toString());
          setUserList([...userList, ...res.data.data.invite_page_records]);
        }
      })
      .catch((err) => console.log(err));
    }
  }

  console.log('searchParams', searchParams);
  return (
    <>
      <FriendInvite2Carousel />
      <div className={styles.container}>
        <img src={topImage} className={styles.image} alt=""/>
        {
          searchParams.group 
            ? searchParams.group.indexOf('a') !== -1
              ? <ShareCardA
                avatarList={avatarList}
                group={searchParams.group as any}
                inviteCode={inviteCode}
              />
              : <ShareCardB
                avatarList={avatarList}
                group={searchParams.group as any}
                inviteCode={inviteCode}
              />
            : undefined
        }
        <StepCard />
        <InviteCard handleMyInviteShowMore={showMore} rankList={rankList} myInvite={userList} />
        <div
          onClick={linkToActivityRule}
          className={styles.linkToActivityRule}
        ></div>
      </div>
    </>
  )
}

export default FriendInvite2;


    // (async () => {
    //   let auth
    //   console.log('>>>', 'record');
    //   const abTestHeader = await getAbHeader();
    //   try {      
    //     auth = await getAuthorizationHeader(baseUrl + '/v2/h5/invite/record', {page: '0'}).catch((err) => console.log(err));
    //   } catch (error) {
    //     console.log(error);
    //   }
    //   console.log(auth);
    //   const recordRes:any = await ajax.post('/v2/h5/invite/record', {page: '0'}, {
    //     headers: {
    //       'Authorization': auth,
    //     }
    //   }).catch((err) => console.log(err));
    //   console.log(recordRes);

    //   console.log('>>>', 'succeed_record');
    //   try {      
    //     auth = await getAuthorizationHeader(baseUrl + '/v2/h5/invite/succeed_record').catch((err) => console.log(err));
    //   } catch (error) {
    //     console.log(error);
    //   }
    //   console.log(auth);
    //   const succeed_recordRes: any = await ajax.get('/v2/h5/invite/succeed_record', {
    //     headers: {
    //       'Authorization': auth,
    //     }
    //   }).catch((err) => console.log(err));
    //   console.log(succeed_recordRes);
    
    //   console.log('>>>', 'board');
    //   try {      
    //     auth = await getAuthorizationHeader(baseUrl + '/v2/h5/invite/board').catch((err) => console.log(err));
    //   } catch (error) {
    //     console.log(error);
    //   }
    //   console.log(auth);
    //   const boardRes:any = await ajax.get('/v2/h5/invite/board', {
    //     headers: {
    //       'Authorization': auth,
    //     }
    //   }).catch((err) => console.log(err));
    //   console.log(boardRes);


    //   if (recordRes.status === 200 && recordRes.data.meta.code === 200 && recordRes.data.data) {
    //     console.log(recordRes.data.data.record)
    //     setUserList(recordRes.data.data.records);
    //   }
    //   if (succeed_recordRes.status === 200 && succeed_recordRes.data.meta.code === 200 && succeed_recordRes.data.data) {
    //     setAvatarList(succeed_recordRes.data.data.records);
    //   }
    //   if (boardRes.status === 200 && boardRes.data.meta.code === 200 && boardRes.data.data) {
    //     setRankList(boardRes.data.data.board);
    //   }
    // })();
