import MockAdapter from 'axios-mock-adapter'

export default (instance) => {
  const mock = new MockAdapter(instance)

  mock.onPost('/yearreport/getreport').reply(200, {
    "user_id": "55080515",
    "reports": [
      {
        "content": "2020年，探探用户超过{4}亿啦。",
        "friend_name": "",
        "friend_img": "",
        "friend_type": ""
      },
      {
        "content": "在过去一年里，探探用户共给出{128}亿次喜欢，{46.3}亿次互相喜欢，平均每天有{1266}万对单身的人在探探一见钟情，并在全年产生了{320}亿次聊天。",
        "friend_name": "",
        "friend_img": "",
        "friend_type": ""
      },
      {
        "content": "在2020年，{广东省}的探探用户给出喜欢最多，平均每天共给出{3608}万个喜欢。",
        "friend_name": "",
        "friend_img": "",
        "friend_type": ""
      },
      {
        "content": "{北京市}和{陕西省}的女生{颜值最高}，平均1个女生每天能收到{156}个喜欢。",
        "friend_name": "",
        "friend_img": "",
        "friend_type": ""
      },
      {
        "content": "{广东省}和{山东省}的人{最爱“唠嗑”}，平均每天产生{590万次}聊天。",
        "friend_name": "",
        "friend_img": "",
        "friend_type": ""
      },
      {
        "content": "最孤独的城市是“{广州}”，每天凌晨1点还有超过{15%的人}在线聊天。",
        "friend_name": "",
        "friend_img": "",
        "friend_type": ""
      },
      {
        "content": "{最养生}的省份是“{湖北}”和“{天津}”，每天晚上11点后在线用户不足全天{20%}。",
        "friend_name": "",
        "friend_img": "",
        "friend_type": ""
      },
      {
        "content": "而在探探中，最特殊的城市是“{武汉}”，2020年1月～3月，为武汉加油的朋友圈发布量占比超过{10%}，{#武汉加油，中国加油#}的话题单日互动量超{40W}。",
        "friend_name": "",
        "friend_img": "",
        "friend_type": ""
      },
      {
        "content": "{2021，让探探继续给你温暖吧}。",
        "friend_name": "",
        "friend_img": "",
        "friend_type": ""
      },
      {
        "content": "在2020年，探探陪你度过了{27}天，你上传了{1}张头像。\n在{4月6日}你使用探探时间最长，在这天遇到了心动的人吗？\n你还向{11}个好友发出了{131}条消息，其中，有一位叫{“Zabrina”}的好友与你有着特别的缘分 ，自{5月5日}互相配对后 ，你和Ta发出了{67}句对话，成为了你在探探最亲密的好友。\n探探发现，和你互相喜欢的TA大多集中在{30~31}岁，大部分从事{IT/互联网/通信}，{金融}，{零售行业}。你好像对{射手座}的人情有独钟，你给了这个星座的人{126}个喜欢。\n喜欢你的Ta大多集中在{21~31}岁，Ta们大多来自{北京}，{河北}，{黑龙江}，其中，有一位打败{滨海新}地区{81%}的人气用户喜欢了你。你给了Ta一个匹配的机会，现在你们还聊天吗？",
        "friend_name": "Jacky",
        "friend_img": "eyJpZCI6Ik5LS0xGRUtDWVBYR1NLQzVLU0VIUktJTTZRSlRBUjA2IiwidyI6MTA3OCwiaCI6MTA3OCwiZCI6MCwibXQiOiJpbWFnZS9qcGVnIiwiZGgiOjEwMjA2NDIwNzE5MjMwODg0MzA0fQ",
        "friend_type": "most_popular_friend"
      }
    ],
    "user_name": "空",
    "user_img": "eyJpZCI6IkRJNE1BTDRUVkpGWEJMWUlEVk9KSkRCWlVJRlJXQiIsInciOjExMjIsImgiOjk2MCwiZCI6MCwibXQiOiJpbWFnZS9qcGVnIiwiZGgiOjk4NDIyMTk2MDk3MTY3MzMwNzYsImFiIjowfQ",
    "year_word": "恋爱威龙",
    "word_content": "干啥啥不行，好看第一名",
    "love_word": "理智的她",
    "love_content": "你大概率会遇到的她，是个温柔体贴的人。她虽然偶尔会展现出害羞的眼神，但却是个活得明白的人。她理智而清醒，拥有高智商，很少会情绪泛滥。她追求安静的环境，不会喋喋不休。她有知性美，注意细节。笑起来会让人觉得如沐春风，是特别的干净纯洁，单纯善良的女孩。"
  })

}
