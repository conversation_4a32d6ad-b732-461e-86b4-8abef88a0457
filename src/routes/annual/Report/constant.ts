export interface PVItem {
  key: string;
  flag: boolean;
}

export const PVS: { [key: string]: PVItem } = {
  '0': {
    key: 'p_annual_report_cover',
    flag: false
  },
  '1': {
    key: 'p_annual_report_stage_one',
    flag: false
  },
  '2': {
    key: 'p_annual_report_stage_two',
    flag: false
  },
  '3': {
    key: 'p_annual_report_stage_three',
    flag: false
  },
  keyword: {
    key: 'p_annual_report_key_word',
    flag: false
  },
  dialog: {
    key: 'p_annual_report_key_word_share_popup',
    flag: false
  },
  result: {
    key: 'p_annual_report_love_prediction',
    flag: false
  }
}

// 兜底基础数据
export const baseData = [
  {
    "content": "2020年，探探用户超过{4亿}啦，手拉手可以绕地球17圈。",
    "friend_name": "",
    "friend_img": "",
    "friend_type": ""
  },
  {
    "content": "在过去一年里，探探用户共给出{128}亿次喜欢，{46.3}亿次互相喜欢，平均每天有{1266}万对单身的人在探探一见钟情，并在全年产生了{320}亿次聊天,如果发一条消息要1秒钟，那么需要发{1000}年。",
    "friend_name": "",
    "friend_img": "",
    "friend_type": ""
  },
  {
    "content": "在2020年，{广东省}的探探用户给出喜欢最多，平均每天共给出{3608}万个喜欢。",
    "friend_name": "",
    "friend_img": "",
    "friend_type": ""
  },
  {
    "content": "{北京市}和{陕西省}的女生{颜值最高}，平均1个女生每天能收到{156}个喜欢。",
    "friend_name": "",
    "friend_img": "",
    "friend_type": ""
  },
  {
    "content": "{广东省}和{山东省}的人{最爱“唠嗑”}，平均每天产生{590万次}聊天。",
    "friend_name": "",
    "friend_img": "",
    "friend_type": ""
  },
  {
    "content": "最孤独的城市是“{广州}”，每天凌晨1点还有超过{15%的人}在线聊天。",
    "friend_name": "",
    "friend_img": "",
    "friend_type": ""
  },
  {
    "content": "最特殊的城市是“{武汉}”，2020年1月～3月，为武汉加油的朋友圈发布量占比超过{10%}，{#武汉加油，中国加油#}的话题单日互动量超{40W}。",
    "friend_name": "",
    "friend_img": "",
    "friend_type": ""
  }
]
