.fixedBody {
  width: 100vw;
  height: 100vh;
  overflow: hidden;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  line-height: 0.3rem;
}

.<PERSON><PERSON> {
  width: 2.37rem;
  height: 0.47rem;
  border-radius: 0.47rem;
  box-shadow: 1px 6px 16px 0 rgba(255, 90, 41, 0.43);
  background-image: linear-gradient(to top, #ff4812, #ff4812, #fd6839);
  font-size: 0.19rem;
  font-weight: 500;
  color: #fff;
  line-height: 0.47rem;
  text-align: center;
  box-sizing: border-box;
  margin-bottom: 0.7rem;
}

.AnnualReport {
  transform: translateZ(0);

  .slick-slide {
    border: none;
  }
}
