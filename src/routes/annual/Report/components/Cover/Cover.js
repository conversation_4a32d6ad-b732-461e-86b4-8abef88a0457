import React from 'react'
import cx from 'classnames'
import Page from '../Page'
import style from './Cover.module.scss'

export default function(props) {
  const {slider} = props

  return (
    <Page className={style.container}>
      <div className={style.poster}>
        <img src='https://auto.tancdn.com/v1/images/eyJpZCI6IjNJTldSTkY2UkVBVTNDV09LTjZHRks2WEZVV1ZSQTA2IiwidyI6MTEyNSwiaCI6MTg5MCwiZCI6MCwibXQiOiJpbWFnZS9qcGVnIiwiZGgiOjExNTUwNzkxMzAyNjkzMjQ3Mzd9.png' />
      </div>
      <div className={cx('Button', style.button)} onClick={() => slider.current.slickNext()}>点击查看完整年报</div>
    </Page>
  )
}
