import React from 'react'
import cx from 'classnames'
import style from './Page.module.scss'

export default function(props) {
  const {className, children, arrowClick, ...rest} = props
  const width = document.documentElement.clientWidth
  const height = document.documentElement.clientHeight

  return (
    <div
      className={cx(style.Page, className)}
      style={{width, height}}
      {...rest}
    >
      {children}
      <div className={style.arrow} onClick={arrowClick} />
    </div>
  )
}
