import React from 'react'
import cx from 'classnames'
import Page from '../Page'
import style from './Stat.module.scss'

function getString(string = '', maxLength = 10) {
  if (string.length < maxLength) {
    return string;
  } else {
    return string.split('').slice(0, maxLength).join('') + '...'
  }
}

const formatContent = (item = {}) => {
  const {content, friend_name, friend_img, friend_type} = item
  const html = content
    .replace(/\{/g, '<b>').replace(/\}/g, '</b>')
    .replace(friend_name, getString(friend_name))

  return (
    <React.Fragment>
      {friend_img &&
        <span className={cx(friend_type === 'best_friend' && style.best, friend_type === 'most_popular_friend' && style.popular)}>
          <img className={style.avatar} src={`//auto.tancdn.com/v1/images/${friend_img}`} align='right' />
          <div className={style.tag} />
          <div className={style.name}>{friend_name}</div>
        </span>
      }
      <span dangerouslySetInnerHTML={{__html: html}} />
    </React.Fragment>
  )
}

export default function(props) {
  const {data = [], type, end} = props

  return (
    <Page>
      <div className={style.wrapper}>
        <div className={style.content}>
          {data.map((item, index) => (
            <section key={index}>{formatContent(item)}</section>
          ))}
        </div>
      </div>
      {end && <div className={style.end}>上滑生成你的探探关键词</div>}
      <div className={cx(style.footer, style[type])} />
      <div className={style.qrcode} />
    </Page>
  )
}
