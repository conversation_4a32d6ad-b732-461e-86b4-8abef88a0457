.Keyword {
  background-image: none;
  background-color: rgb(247, 193, 111);
}

.wrapper {
  width: 100%;
  padding-bottom: 10%;
  flex-shrink: 0;
  background-image: linear-gradient(rgb(239, 116, 89), rgb(247, 193, 111));
  background-size: 100% 100%;
}

.scroller {
  position: relative;
  margin: 20% 4% 0 8%;
  padding-bottom: 112%;
  width: 88%;
  background-image: url(https://auto.tancdn.com/v1/images/eyJpZCI6IlFKWUNHUFVUVkxESFY2TzRWTTRSVTMzSkVYNk9aRzA3IiwidyI6NjU3LCJoIjo4MzgsImQiOjAsIm10IjoiaW1hZ2UvanBlZyIsImRoIjoxNDIxMzQzMjE4OTY2ODExNTc4MX0.png);
  background-size: 100%;
  background-repeat: no-repeat;
  background-position: center 0;
}

.up {
  position: absolute;
  top: 0;
  left: 0;
  width: 96%;
  height: 28%;
  text-align: center;

  h3 {
    margin: 0;
    font-size: 0.17rem;
    font-weight: bold;
    color: #3e3e3e;
    word-break: keep-all;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    width: 100%;
  }
}

.down {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  position: absolute;
  top: 29%;
  left: 7%;
  width: 82%;
  height: auto;
}

.avatarWrapper {
  position: relative;
  margin: 0 auto 3%;
  margin-top: -14%;
  padding-bottom: 26%;
  width: 26%;
  border-radius: 50%;
  border: 3px #ffffff solid;
  background-color: #eeeeee;
  overflow: hidden;
}

.avatar {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;

  img {
    width: 100%;
    height: 100%;
    border: none;
    border-radius: 50%;
    overflow: hidden;
  }
}

.nickname {
  margin: 0 auto 4%;
  max-width: 1.3rem;
  height: 1em;
  line-height: 1em;
  color: #f65939;
  font-size: 14px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.keyword {
  margin-top: 0.2rem;
  color: #f65939;
  font-size: 0.32rem;
  font-weight: 600;
  text-align: center;
}

.desc {
  display: flex;
  flex-direction: column;
  font-size: 0.16rem;
  color: #3e3e3e;
  padding-top: 0.2rem;
  padding-bottom: 0.2rem;
  position: relative;
  text-align: center;

  > span {
    padding: 0.05rem 0.22rem;
  }

  &::before {
    content: '';
    position: absolute;
    top: 0.2rem;
    left: 0;
    display: block;
    align-self: flex-start;
    width: 0.18rem;
    height: 0.18rem;
    background-image: url(https://auto.tancdn.com/v1/images/eyJpZCI6IjRDVEdTRUtaS1RCU1NDS0hGTVlVVEpaWjVIT1RIQzA3IiwidyI6MzcsImgiOjMzLCJkIjowLCJtdCI6ImltYWdlL2pwZWciLCJkaCI6ODMxODgzODM5NTQ0MTg2NTcwM30.png);
    background-size: 100%;
    background-repeat: no-repeat;
  }

  &::after {
    content: '';
    align-self: flex-end;
    position: absolute;
    bottom: 0.2rem;
    right: 0;
    display: block;
    width: 0.18rem;
    height: 0.18rem;
    background-image: url(https://auto.tancdn.com/v1/images/eyJpZCI6IlBGN09ZTTc2RE1MVE5TWU5ORk5JVlM2V1NFVk1GRzA2IiwidyI6MzcsImgiOjMzLCJkIjowLCJtdCI6ImltYWdlL2pwZWciLCJkaCI6MTE2NjgyMzY5Mzk0NjU2NDkxM30.png);
    background-size: 100%;
    background-repeat: no-repeat;
  }

  &.loveContent {
    font-size: 0.12rem;
    line-height: 0.2rem;
  }
}

.footer {
  display: flex;
  justify-content: space-between;
  align-items: flex-end;
  margin-bottom: 0.2rem;

  > p {
    margin: 0;
    padding-left: 0.1rem;
    font-size: 0.13rem;
    color: #747474;
    line-height: 2;
  }

  .qrcode {
    width: 0.8rem;
    height: 0.8rem;
    background-image: url(https://auto.tancdn.com/v1/images/eyJpZCI6IjdQNEhRS0FTMllTRU9XSlhEQVZXNENZR1JEVjRXNzA3IiwidyI6MjgwLCJoIjoyODAsImQiOjAsIm10IjoiaW1hZ2UvanBlZyIsImRoIjo0ODA1NTc3MDgwNjc1MTM1Nzk3fQ.png);
    background-size: 100%;
    background-repeat: no-repeat;
  }
}

.Keyword {
  .button {
    width: 3.1rem;
    margin-bottom: 0.7rem;
  }
}

.toTop {
  margin-bottom: 0.5rem;
  font-size: 0.14rem;
  color: #f34d2d;
  line-height: 0.22rem;
  border-bottom: 1px #f34d2d solid;
}

@media screen and (min-aspect-ratio: 320 / 550) {
  .keyword {
    font-size: 0.20rem;
    line-height: 0.20rem;
    margin-top: 0.1rem;
    margin-bottom: -0.1rem;
  }
  .wrapper {
    padding-bottom: 5%;
  }
  .scroller {
    margin: 15% 8% 0 12%;
    width: 80%;
    padding-bottom: 102%;
  }

  .footer {
    > p {
      font-size: 12px;
    }
  }

  .Keyword {
    &::after {
      bottom: 10px;
    }
  }
}

