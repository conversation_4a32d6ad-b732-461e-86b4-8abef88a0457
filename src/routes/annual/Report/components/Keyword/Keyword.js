import React, {useState, useRef, useEffect} from 'react'
import cx from 'classnames'
import Page from '../Page'
import Share from '../Share'
import style from './Keyword.module.scss'
import {trackNew} from "../../../../../utils/bridge";
import {PVS} from "../../constant";

export default function (props) {
  const {user = {}, slider} = props
  const card = useRef(null)
  const [isLast, setLast] = useState(props.isLast || false)
  useEffect(() => {
    setLast(props.isLast)
  }, [props.isLast])
  const handleShare = () => {
    trackNew({
      pageId: PVS.result.key,
      eid: 'e_annual_report_love_prediction_share_button',
      type: 'MC'
    })
    props.share()
  }

  const arrowClick = () => {
    isLast && slider.current.slickGoTo(0)
  }

  return (
    <Page className={`${style.Keyword} none`} arrowClick={arrowClick}>
      <div className={style.wrapper} ref={card}>
        <div className={style.scroller}>
          <div className={style.up}>
            <div className={style.avatarWrapper}>
              <div className={style.avatar}>
                <img src={user.avatar}/>
              </div>
            </div>
            <div className={style.nickname}>{user.name}</div>
            <h3>{isLast ? '爱情运势预测：2021年你会遇见...' : '你的2021探探关键词'}</h3>
          </div>
          <div className={style.down}>
            <div className={style.keyword}>{isLast ? user.love_word : user.keyword}</div>
            <div className={`${style.desc} ${isLast ? style.loveContent : ''}`}>
              <span>{isLast ? user.love_content : user.desc}</span>
            </div>
            {
              isLast ? null : <div className={style.footer}>
                <p>
                  2021年，<br/>
                  请继续滑动 期待相遇 ~
                </p>
                <div className={style.qrcode}/>
              </div>
            }
          </div>
        </div>
      </div>
      {isLast
        ? <div className={cx('Button', style.button)} onClick={handleShare}>让好友也来预测自己的爱情运势</div>
        : <div className={style.toTop}>上滑查看你的2021年爱情运势</div>}
    </Page>
  )
}
