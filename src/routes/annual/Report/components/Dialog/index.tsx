import React, {useEffect, useState} from "react";
import s from './index.module.css'
import {trackNew} from "../../../../../utils/bridge";
import {PVS} from "../../constant";

interface Props {
  show: boolean;
  close: Function;
  share: Function;
}

export const DialogShare: (prop: Props) => JSX.Element = (prop: Props) => {
  const [show, setShow] = useState(prop.show)
  useEffect(() => {
    setShow(prop.show)
  }, [prop.show])
  const closeDialog = () => {
    setShow(false);
    trackNew({
      pageId: PVS.dialog.key,
      eid: 'e_annual_report_key_word_share_popup_close_button',
      type: 'MC'
    })
    prop.close()
  }
  const share = () => {
    trackNew({
      pageId: PVS.dialog.key,
      eid: 'e_annual_report_key_word_share_popup_open_button',
      type: 'MC'
    })
    prop.share()
  }
  return <div
    className={`${s.mask} ${show ? '' : s.hide}`}>
    <div className={s.container} onClick={e => e.stopPropagation()}>
      <img
        onClick={closeDialog}
        className={s.close}
        src='https://auto.tancdn.com/v1/images/eyJpZCI6Ik1FSVpMVkpJRENCUDdGTjRNS0Y0QUZRR1ZYQkVPMjA2IiwidyI6NDIsImgiOjQyLCJkIjowLCJtdCI6ImltYWdlL2pwZWciLCJkaCI6NTMyMzI4MDc4MTMxNzQ5NjkwNX0.png'/>
      <img
        className={s.iconImg}
        src="https://auto.tancdn.com/v1/images/eyJpZCI6IlI2SFNLQVFGMkpFN0JCVjdSR0lGUEo2TzNOWkhDVzA3IiwidyI6MzAwLCJoIjozMDAsImQiOjAsIm10IjoiaW1hZ2UvanBlZyIsImRoIjozODY3ODUwMzgwODYyMDQwNjE5fQ.png"
        alt="icon"/>
      <div className={s.dialogContent}>分享活动链接，查看你的2021年爱情运势预测</div>
      <div className={s.btn} onClick={share}>分享“爱情运势预测”活动</div>
    </div>
  </div>
}
