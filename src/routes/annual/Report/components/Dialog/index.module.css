.mask {
  width: 100vw;
  height: 100vh;
  background-color: rgba(0, 0, 0, 0.6);
  position: fixed;
  top: 0;
  right: 0;
  left: 0;
  bottom: 0;
}

.hide {
  display: none;
}

.container {
  width: 3.2rem;
  height: 3.2rem;
  position: absolute;
  left: 50%;
  top: 50%;
  margin-top: -1.6rem;
  margin-left: -1.6rem;
  background: #fff;
  border-radius: 0.1rem;
}

.close {
  width: 0.14rem;
  height: 0.14rem;
  position: absolute;
  right: 0.14rem;
  top: 0.14rem;
}

.iconImg {
  width: 1rem;
  height: 1rem;
  margin-top: 0.3rem;
  position: absolute;
  left: 50%;
  transform: translateX(-0.5rem);
}

.dialogContent {
  position: absolute;
  top: 1.54rem;
  font-size: 0.21rem;
  font-weight: bold;
  text-align: center;
  color: #4a4a4a;
  line-height: 0.31rem;
  padding: 0 0.4rem;
}

.btn {
  width: 2.54rem;
  height: 0.5rem;
  border-radius: 0.25rem;
  background-image: linear-gradient(to right, #ff4349, #fe853f);
  color: #fff;
  text-align: center;
  line-height: 0.5rem;
  font-size: 0.17rem;
  font-weight: bold;
  position: absolute;
  bottom: 0.3rem;
  left: 50%;
  transform: translateX(-1.27rem);
}
