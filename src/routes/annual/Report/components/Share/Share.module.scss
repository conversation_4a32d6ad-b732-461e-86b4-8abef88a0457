.share {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.7);
  z-index: 99;

  &.hidden {
    display: none;
  }
}

.shareModal {
  position: absolute;
  left: 0;
  bottom: 0;
  overflow: hidden;
  width: 100%;
  border-top-left-radius: 10px;
  border-top-right-radius: 10px;
  background-color: #ffffff;
}

.title {
  padding: 20px 0 25px;
  font-size: 13px;
  color: #4a4a4a;
  text-align: center;
}

.cancelButton {
    position: absolute;
    left: 0;
    bottom: 0;
    height: 56px;
    width: 100%;
    background-color: #f2f2f2;
    font-size: 17px;
    line-height: 56px;
    color: #4a4a4a;
    text-align: center;
}

.shareActions {
    display: flex;
    justify-content: space-around;
    margin-bottom: 56px;
    padding: 3px 14px 32px;
    height: 76px;
}

.action {
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    align-items: center;
    height: 100%;
}

.cover {
    width: 48px;
    height: 48px;
}

.label {
    font-size: 13px;
    color: #4a4a4a;
}

.wechatWrapper {
  position: fixed;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.8);
  z-index: 200;
}

.wechatShareContent {
  display: flex;
  align-items: center;
  flex-direction: column;
  position: absolute;
  top: 25px;
  right: 25px;
}

.wechatShareIcon {
  align-self: flex-end;
  width: 53px;
  height: 48px;
  background-image: url(https://auto.tancdn.com/v1/images/eyJpZCI6IkdBMlNUM08yRk1XQlJYUTdTT003UTNDVzdQWUFEUDAzIiwidyI6MTA2LCJoIjo5NiwiZCI6MCwibXQiOiJpbWFnZS9qcGVnIiwiZGgiOjczNDY2MDgyMDIzMTg0NDQwLCJhYiI6MH0.png);
  background-size: cover;
}

.wechatShareDesc {
  font-size: 18px;
  color: #fff;
  text-align: center;
}

.wechatShareDesc .highlight {
  color: #ffe900;
}

.wechatShareContent .btn {
  margin-top: 12px;
  width: 90px;
  height: 30px;
  line-height: 30px;
  text-align: center;
  color: #ffffff;
  font-size: 14px;
  border: 1px #fff solid;
  border-radius: 3px;
}