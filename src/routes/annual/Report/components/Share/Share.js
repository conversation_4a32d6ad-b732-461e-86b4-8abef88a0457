import React, {useEffect, useState} from 'react'
import ReactDom from 'react-dom'
import cx from 'classnames'
import {shareImage} from "../../../../../utils/share";
import style from './Share.module.scss'

export default function(props) {
  const {visible, onClose, data = {}} = props
  const [show, setShow] = useState(visible)
  const {pic} = data

  const handleShare = (platform) => {
    setShow(false)
    onClose && onClose()
    shareImage({
      pic,
      platform,
      title: '2021爱情运势预测',
      desc: '来测测明年你会遇到什么样的TA？'
    }).then(res => {
      setShow(false)
    })
  }

  useEffect(() => {
    setShow(visible)
  }, [visible])

  return ReactDom.createPortal(
    <div
      className={cx(style.share, !show && style.hidden)}
      onClick={onClose}
    >
      <div className={style.shareModal} onClick={e => e.stopPropagation()}>
        <div className={style.title}>分享到</div>
        <div className={style.shareActions}>
          <div className={style.action} onClick={() => handleShare('wx')}>
            <img className={style.cover} src='https://auto.tancdn.com/v1/images/eyJpZCI6IjJaT0ZXNlVGS1NVSTI3QzJLSUJNR0U0SEdNQTJNSDA0IiwidyI6MTQ0LCJoIjoxNDQsImQiOjAsIm10IjoiaW1hZ2UvanBlZyIsImRoIjoxMDk1OTI1ODM0MjQ5OTQ5OTY3LCJhYiI6MH0.png' />
            <span className={style.label}>微信</span>
          </div>
          <div className={style.action} onClick={() => handleShare('mo')}>
            <img className={style.cover} src='https://auto.tancdn.com/v1/images/eyJpZCI6IlRPMkdDVE5JWlNQNEFHUVFUSFA3SVpaWVM0T05ERjA0IiwidyI6MTQ0LCJoIjoxNDQsImQiOjAsIm10IjoiaW1hZ2UvanBlZyIsImRoIjozMTExODkyOTY5OTI5MTgxOTk1LCJhYiI6MH0.png' />
            <span className={style.label}>朋友圈</span>
          </div>
        </div>
        <div className={style.cancelButton} onClick={onClose}>取消</div>
      </div>
    </div>,
    document.getElementById('root')
  )
}
