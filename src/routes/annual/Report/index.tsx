import React, {useEffect, useRef, useState} from 'react'
// @ts-ignore
import Slider, {SwipeDirection} from 'react-slick'
import axios from 'axios'
import Cover from './components/Cover'
import Stat from './components/Stat/Stat'
import Keyword from './components/Keyword/Keyword'
import {getUserInfo, setNavigationTitle} from "../../../utils/bridge";
import 'slick-carousel/slick/slick.css'
import 'slick-carousel/slick/slick-theme.css'
import './App.scss'
import {DialogShare} from "./components/Dialog";
import {baseData, PVS} from "./constant";
import Share from "./components/Share/Share";
import {getQueryVariable, trackPV} from "../../../utils/utils";
import {mock} from "../../../_mock";
import * as buffer from "buffer";

const ajax = axios.create({
  baseURL: (process.env.REACT_APP_TEST || process.env.NODE_ENV !== 'production')
    ? 'http://yearreport.staging2.p1staff.com'
    : 'https://yearreport2019.tantanapp.com'
})
if (process.env.NODE_ENV !== 'production') {
  // mock(ajax)
}

const groupLimit = 3
let lock = false;
let personLen = 0;
let noData = false;

export function AnnualReport() {
  const slider = useRef(null)
  const [sliderIndex, setCurSliderIndex] = useState(0)
  const [user, setUser] = useState({})
  const [data, setData] = useState([])
  const [stamp, setStamp] = useState(+new Date())
  const [share, setShare] = useState({visible: false, data: {}})
  const [showDialog, setShowDialog] = useState(false);
  const keywordIndex = 4 + personLen;

  useEffect(() => {
    (async () => {
      document.title = '探探年报';
      setNavigationTitle({
        title: '探探年报'
      })
      const userID = getQueryVariable('user_id')
      ajax.post(
        '/yearreport/getreportv2',
        {
          user_id: userID || (await getUserInfo()).userId
        }
      ).then(rs => {
        const {reports, user_name, user_img, year_word, word_content, love_word, love_content} = rs.data
        if (reports.length <= 1) {
          noData = true;
          setData(baseData as any)
        } else {
          noData = false
          setData(reports.filter((item: any) => item.content))
        }
        setUser({
          name: user_name,
          keyword: year_word,
          desc: word_content,
          avatar: `https://auto.tancdn.com/v1/images/${user_img}.png`,
          love_word,
          love_content
        })
        trackPV(PVS[0])
      })
    })()

    document.body.className = 'fixedBody';
    const handleResize = () => {
      setStamp(+new Date())
    }
    window.addEventListener('resize', handleResize)

    return () => {
      window.removeEventListener('resize', handleResize)
    }
  }, [])
  const changeIndex = (curIndex: number) => {
    setCurSliderIndex(curIndex)
    if (curIndex <= 3) {
      trackPV(PVS[curIndex])
    }
    if (curIndex && !noData && !lock) {
      lock = curIndex === sliderIndex;
      setShowDialog(lock);
      // 关键词打点
      !lock && curIndex === keywordIndex && trackPV(PVS.keyword)
    }
    // 弹框打点
    lock && trackPV(PVS.dialog)
  }

  const closeDialog = () => {
    // @ts-ignore
    slider.current.slickNext();
    // 结果页面打点
    trackPV(PVS.result)
    setShare({...share, visible: false})
  }

  const renderStat = () => {
    const restData = data.slice(7);
    const arr: any[] = [];
    restData.forEach((inner: any) => {
      const content = inner.content.split('\n');
      content.forEach((c: string, index: number) => {
        arr.push({
          ...(index === 0 ? inner : {}),
          content: c
        })
      })
    })
    const result = Array.from({length: Math.ceil(arr.length / groupLimit)}).map((item, index) => {
      const start = index * groupLimit
      return <Stat
        key={index} data={arr.slice(start, start + groupLimit)} type='custom'
        end={start + groupLimit >= arr.length}/>
    })
    personLen = result.length;
    return result;
  }

  const renderKeyWord = () => {
    return <Keyword share={handleShare} user={user} slider={slider}/>
  }

  const handleShare = () => {
    setShowDialog(false)
    setShare({
      visible: true,
      data: {
        pic: 'https://auto.tancdn.com/v1/images/eyJpZCI6IlVKSTdUWlE3VUVTUFdRWjRYNVlFQlQzTFJEM1JOQzA3IiwidyI6OTAwLCJoIjo5MDAsImQiOjAsIm10IjoiaW1hZ2UvanBlZyIsImRoIjoxMTg2NTQ4Mzc0NzY0OTcwODc0MH0.png'
      }
    })
  }

  return (
    <>
      <Slider
        className='AnnualReport'
        autoplay={false}
        vertical={true}
        verticalSwiping={true}
        infinite={false}
        arrows={false}
        adaptiveHeight={true}
        initialSlide={sliderIndex}
        afterChange={(curIndex: number) => changeIndex(curIndex)}
        ref={slider}
      >
        <Cover slider={slider}/>
        <Stat data={data.slice(0, 2)} type='welcome'/>
        <Stat data={data.slice(2, 6)} type='common'/>
        <Stat data={data.slice(6, 7)} type='common'/>
        {renderStat()}
        {lock ? renderKeyWord() : null}
        {noData ? null : <Keyword share={handleShare} user={user} isLast={lock} slider={slider}/>}
      </Slider>
      <DialogShare share={handleShare} show={showDialog} close={closeDialog}/>
      <Share {...share} onClose={closeDialog}/>
    </>
  );
}
