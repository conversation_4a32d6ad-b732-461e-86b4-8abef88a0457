.container {
  box-sizing: border-box;
  width: 100vw;
  background-image: linear-gradient(340deg, #fa7f3c, #ffc13c);
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  align-items: center;
  padding-top: 0.4rem;
  padding-bottom: 0.4rem;
  height: 100vh;
  overflow: auto;
}

.banner {
  width: 3rem;
}

.questionContent {
  box-sizing: border-box;
  width: 3.4rem;
  height: 4.6rem;
  margin-top: 0.12rem;
  background-color: #fff;
  border-radius: 0.12rem;
  border: 2px solid rgba(249, 91, 113, 0.5);
  padding: 0.3rem 0.24rem 1rem 0.23rem;
  position: relative;
}

.questionTitle {
  font-size: 0.2rem;
  font-weight: 600;
  line-height: 1.6;
  color: #ff571d;
}

.questionSelection {
  box-sizing: border-box;
  width: 2.47rem;
  height: 0.42rem;
  font-size: 0.16rem;
  color: #4a4a4a;
  line-height: 0.42rem;
  padding-left: 0.2rem;
  background-image: url(https://auto.tancdn.com/v1/images/eyJpZCI6IkpQTEpBNUhONjJNSkhBRk9WUkxFUzdNVVlWQVc2SjA3IiwidyI6NzQ3LCJoIjoxMjYsImQiOjAsIm10IjoiaW1hZ2UvanBlZyIsImRoIjo0OTcyMzMxNjI3NDEwOTY1ODI5fQ.png);
  background-size: contain;
  background-repeat: no-repeat;
  margin-top: 0.1rem;
}

.questionSelection.active {
  color: #fff;
  background-image: url(https://auto.tancdn.com/v1/images/eyJpZCI6IjJEREhDQkJDSElDSDZQVTNKNFg1SjJQS1BFNFdHWDA2IiwidyI6NzQxLCJoIjoxMjYsImQiOjAsIm10IjoiaW1hZ2UvanBlZyIsImRoIjo1MDA5NDg2NTg0MTc3OTE5MzAxfQ.png);
}

.btnBox {
  margin-top: 0.35rem;
  position: absolute;
  bottom: 0.3rem;
  width: 2.9rem;
  display: flex;
  justify-content: center;
}

.btn {
  padding: 0 0.25rem;
  height: 0.47rem;
  background-image: url(https://auto.tancdn.com/v1/images/eyJpZCI6IjNUUUVLN0FRNVpHVjZWU0YyRDZGTkFWVzdUWjdDSjA3IiwidyI6NDAyLCJoIjoxNDEsImQiOjAsIm10IjoiaW1hZ2UvanBlZyIsImRoIjo3NjIxMjkyNjY3NTk1MjEyMDQ3fQ.png);
  background-size: 100% 100%;
  font-size: 0.18rem;
  color: #fff;
  text-align: center;
  line-height: 0.44rem;
}

.btn + .btn {
  margin-left: 0.12rem;
}

