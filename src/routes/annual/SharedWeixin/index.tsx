import React, {useEffect, useState} from 'react';
import s from './index.module.css';
import {DatePicker, Toast} from 'antd-mobile';
import {reportDataOrigin, getAstro, reportResult, resItem, pids} from "./data";
import {setNavigationTitle} from "../../../utils/bridge";
import {AnnualRes} from "../../../components/annualRes";
import {getQueryVariable, track} from "../../../utils/utils";
import Router from 'react-router-dom'
// @ts-ignore
import wx from 'weixin-js-sdk'
import {isWeixinBrowser} from "../../../utils/bridge/utils";
import axios from "axios";

let reportData = reportDataOrigin;
const dataLen = reportData.length;
const genderIndex = 4;
const birthDayIndex = 5;
const resIndex = 6;

const CustomChildren = ({
                          onClick = () => {
                          }, extra = ''
                        }) => (
  <div onClick={onClick}>{extra}</div>
);
let cantrack = false;

export const AnnualShared: React.FC = (props: any) => {
  const [curIndex, setCurIndex] = useState(0);
  const [selectionIndex, setSelectIndex] = useState<number>();
  const [date, setDate] = useState<any>(null);
  const [result, setResult] = useState<resItem>();
  const curQuestion = reportData[curIndex];

  const setQuestionRes = (index: number) => {
    if (index > curIndex && curQuestion.selectedIndex === undefined) {
      return Toast.info('选择答案之后再进行下一题哦～', 1)
    }
    setCurIndex(index);
    setSelectIndex(reportData[index].selectedIndex as number);
  }

  const setStorage = (res?: any) => {
    console.log(reportData);
    // 存储当前问题索引
    if (!res) {
      // 存储当前问题索引
      localStorage.setItem('curIndex', String(curIndex))
      // 存储当前数据
      localStorage.setItem('data', JSON.stringify(reportData))
    } else {
      // 存储当前问题索引
      localStorage.setItem('curIndex', String(resIndex))
      localStorage.setItem('result', JSON.stringify(res))
    }
  }
  const clearStorage = () => {
    localStorage.setItem('curIndex', '');
    localStorage.setItem('data', '');
    localStorage.setItem('result', '');
  }

  const changeSelectionIndex = (index: number) => {
    curQuestion.selectedIndex = index;
    setStorage();
    setSelectIndex(index)
  };

  const changeBirthday = (e: Date) => {
    setDate(e)
    curQuestion.inputContent = e;
    setStorage();
  }
  // 获取最终结果
  const getResult = (
    curDate = date,
    gender = reportData[genderIndex].selectedIndex as number,
    questionIndex = curIndex + 1,
    skipPushRouter = false
  ) => {
    if (!curDate) {
      return Toast.info('选择生日之后才可以查看结果哦～')
    }
    const mon = (typeof curDate === 'string' ? new Date(curDate) : curDate).getMonth();
    const day = (typeof curDate === 'string' ? new Date(curDate) : curDate).getDate();
    const index = day < 15 ? 0 : 1;
    const res = reportResult[gender][getAstro(mon + 1, day)][index]
    setResult(res);
    setCurIndex(questionIndex);
    setStorage(res);
    !skipPushRouter && props.history.push(`/annual/report/shared?isres=${true}&date=${encodeURI(curDate)}&gender=${gender}`,)
  }
  useEffect(() => {
    if (cantrack) {
      track('PV', pids[curIndex])
    }
  }, [curIndex])
  useEffect(() => {
    cantrack = true;
    document.title = '2021爱情运势预测';
    setNavigationTitle({
      title: '2021爱情运势预测'
    })
    const isres = getQueryVariable('isres');
    if (isres === 'true') {
      const date = decodeURI(getQueryVariable('date'));
      const gender = +getQueryVariable('gender');
      setCurIndex(resIndex);
      return getResult(date, gender, resIndex, true)
    }
    let localIndex = localStorage.getItem('curIndex');
    // 如果已经有数据读取缓存
    if (localIndex && localStorage.getItem('data')) {
      try {
        reportData = JSON.parse(localStorage.getItem('data') as string)
        console.log(reportData);
        // 如果已经获取到结果直接查看结果
        if (localStorage.getItem('result')) {
          console.log(
            reportData[birthDayIndex].inputContent,
            reportData[genderIndex].selectedIndex,
            resIndex,
            false
          );
          return getResult(
            reportData[birthDayIndex].inputContent,
            reportData[genderIndex].selectedIndex,
            resIndex,
            false
          )
          // setResult(JSON.parse(localStorage.getItem('result') as string));
        }
        setCurIndex(+localIndex)
        const curQuestion = reportData[+localIndex]
        if (+localIndex === birthDayIndex && curQuestion.inputContent) {
          setDate(new Date(curQuestion.inputContent))
        } else {
          setSelectIndex(curQuestion.selectedIndex);
        }
      } catch (e) {
        reportData = reportDataOrigin;
      }
    } else {
      track('PV', pids[0]);
    }
  }, [])

  return (
    <div className={s.container}>
      {
        result
          ? <AnnualRes {...result} />
          : <>
            <img
              className={s.banner}
              src="https://auto.tancdn.com/v1/images/eyJpZCI6IkJYSVJTUUFQSU00RUJVWkJHMk5CQVdGNjRMVk1SSzA3IiwidyI6ODg1LCJoIjozNzgsImQiOjAsIm10IjoiaW1hZ2UvanBlZyIsImRoIjozMTA2MTE5NzY5OTAzNjE0OTk5fQ.png"
              alt=""/>
            <div className={s.questionContent}>
              <div className={s.questionTitle}>{curQuestion.title}</div>
              <ul>
                {
                  curQuestion.type === 'select'
                    ? curQuestion.selections?.map((selection, index) =>
                      <li
                        onClick={e => changeSelectionIndex(index)}
                        key={index}
                        className={`${s.questionSelection} ${selectionIndex !== undefined && +selectionIndex === index ? s.active : ''}`}>{selection}</li>)
                    : <li className={s.questionSelection}>
                      <DatePicker
                        minDate={new Date(1900, 1, 1)}
                        mode="date"
                        format="YYYY-MM-DD"
                        title="选择日期"
                        extra="选择日期"
                        value={date}
                        maxDate={new Date()}
                        onChange={e => changeBirthday(e)}>
                        <CustomChildren/>
                      </DatePicker>
                    </li>
                }
              </ul>
              <div className={s.btnBox}>
                {curIndex > 0
                  ? <div className={s.btn} onClick={_ => setQuestionRes(curIndex - 1)}>上一题</div>
                  : ''}
                {curIndex < dataLen - 1
                  ? <div className={s.btn} onClick={_ => setQuestionRes(curIndex + 1)}>下一题</div>
                  : <div className={s.btn} onClick={_ => getResult()}>查看结果</div>}
              </div>
            </div>
          </>
      }
    </div>
  )
}
