import React, {useState, useEffect} from "react";
import s from "./Plane.module.css";
import { openWebview, setNavigationTitle, trackNew } from "../../utils/bridge";
import {urlParams} from "../../utils/index";
import {RouteComponentProps} from "react-router-dom";
import { Toast} from "antd-mobile";

const Plane: React.FC<RouteComponentProps> = (props) => {
  const [ruleState, setRuleState] = useState('hide');

  const diamondvipSource = urlParams(props.history.location.search.slice(1)).diamondvip_plane_source;

  useEffect(() => {
    document.title = '黑钻私享会邀请函';
    setNavigationTitle({
      title: '黑钻私享会邀请函',
    })

    trackNew({
      pageId: 'p_diamondvip_plane',
      type: 'PV',
      extras: {
        diamondvip_plane_source: diamondvipSource || '',
      }
    });
   
  }, []);

  const handleOpenRule = () => {
    setRuleState('');
  }
  const handleCloseModal = () => {
    setRuleState('hide');
  }
  const handleOpenDynamic = () => {
    trackNew({
      pageId: 'p_diamondvip_plane',
      eid: 'e_diamondvip_plane_purchase',
      type: 'MC'
    });

    const url = process.env.REACT_APP_TEST === "true"
    ? `http://m.staging2.p1staff.com/monetization/diamondvip/#/alipay?isPlane=true&diamondvipSource=${diamondvipSource || ''}`
    : process.env.NODE_ENV === "production"
      ? `http://m.tantanapp.com/monetization/diamondvip/#/alipay?isPlane=true&diamondvipSource=${diamondvipSource || ''}`
      : `http://m.staging2.p1staff.com/monetization/diamondvip/#/alipay?isPlane=true&diamondvipSource=${diamondvipSource || ''}`;  
    openWebview(url);
  }
  const handleOpenAlipay = () => {
    trackNew({
      pageId: 'p_diamondvip_plane',
      eid: 'e_diamondvip_plane_prepay',
      type: 'MC'
    });

      const url = process.env.REACT_APP_TEST === "true"
      ? `http://m.staging2.p1staff.com/monetization/diamondvip/#/alipay-dynamic?from=prebuy&isPlane=true&diamondvipSource=${diamondvipSource || ''}"`
      : process.env.NODE_ENV === "production"
        ? `http://m.tantanapp.com/monetization/diamondvip/#/alipay-dynamic?from=prebuy&isPlane=true&diamondvipSource=${diamondvipSource || ''}`
        : `http://m.staging2.p1staff.com/monetization/diamondvip/#/alipay-dynamic?from=prebuy&isPlane=true&diamondvipSource=${diamondvipSource || ''}`;  
      openWebview(url);
  }

  return (
    <div className={s.containerBack}>
    <div className={s.plane_container}>
        
        <div className={s.planeRule} onClick={handleOpenRule}>&nbsp;&nbsp;活动规则</div>
        <div className={`${s.planeBtn} ${s.btnTop}`}>
            <img className={s.btnImg} onClick={handleOpenDynamic} src="https://auto.tancdn.com/v1/images/eyJpZCI6IkNXR0I2UlhOQzNGSzZTRFlXM1gzVVhMU0NBRVFMWjA3IiwidyI6NTc4LCJoIjoxMDcsImQiOjAsIm10IjoiaW1hZ2UvanBlZyIsImRoIjo1MjgwMjY3OTg0Njc2OTU4NTQxfQ?format=originalOFGHLERTH" alt=""/>
        </div>
        <div className={`${s.planeBtn} ${s.btnTwo}`}>
            <img className={s.btnImg} onClick={handleOpenAlipay} src="https://auto.tancdn.com/v1/images/eyJpZCI6IjNBT1lBQUdPSFNMU0ROVzVFS05QTEMyM1ZPVFkyMjA2IiwidyI6NTc4LCJoIjoxMDgsImQiOjAsIm10IjoiaW1hZ2UvanBlZyIsImRoIjoxNTUxMzcwNzM0MDQ2MTYxNzQ4NX0?format=originalOFGHLERTH" alt=""/>
        </div>
        <div className={s.introWrap}>
            <div className={s.introIcon}>
            </div>
            <span className={s.introTxt}><span className={s.lightTxt}>黑钻会员</span>，重新定义您的社交生活。</span>
            <span className={s.introTxt}>黑钻会员是我们第一款致力于服务高净值人群的增值服务产品。我们不仅为用户提供更高效的服务内容，还提供了更加优质以及更具确定性的服务结果。我们为您的优质提供官方背书，大幅提高匹配数量及质量；通过各种引导机制，使互动不仅极具主动性，更具有效性。在整个匹配过程中，我们提供全链路的管家服务，帮您获得更多理想匹配对象，助力您与对方建立更紧密的关系。</span>
            <span className={s.introTxt}>但黑钻会员的想象力不止于此。新春伊始，我们不仅迭代了线上服务，通过技术手段为您的匹配体验不断升级，私人管家服务也将颠覆您的想象。<span className={s.lightTxt}>私人包机同游，携配对三亚相守</span>。这是黑钻服务为您做出的全新承诺。活动期间，用户购买黑钻会员，私人管家为您提供绝对尊享的浪漫之旅。携手心中所爱，湾流直飞三亚，享受完美节日，就在此刻的黑钻会员新春特邀活动。</span>
            <span className={s.introTxt}>您若滑到心中所爱，我们包机助您厮守。</span>
            <span className={s.introTxt}>黑钻会员，不止于订制社交。</span>

            <div className={s.planeStep}>
                <div className={s.stepImg}>
                    <img className={s.stepImgStep} src="https://auto.tancdn.com/v1/images/eyJpZCI6IklXRUtEQUgzWU5KTTZFUzNVM05UVEM2NTZNRU5PVDA2IiwidyI6NTYyLCJoIjo2MSwiZCI6MCwibXQiOiJpbWFnZS9qcGVnIiwiZGgiOjcwMTY4OTAxMTI4MDQ0ODEzODV9?format=originalOFGHLERTH" alt=""/>
                </div>
                <div className={s.stepTime}>
                    <span className={s.stepTimeText1}>3.15-3.30</span>
                    <span className={s.stepTimeText2}>4.10</span>
                </div>
            </div>
        </div>
        <div className={s.partWay}>
            <div className={s.partWayIcon}></div>
            <div className={s.partWayPart}>
                <div className={s.wayTitle}>方式1:定金开通</div>
                <span className={s.introTxt}>1. 您可选择支付998元定金，可开通24小时黑钻会员试用权益，并锁定限量开通资格，试用开通成功后不支持退款；</span>
                <span className={s.introTxt}>2. 定金用户正式开通后可获得私人公务机席位；同时可以享受三亚当地接机服务和<span className={s.lightTxt}>豪华酒店预定专属7折优惠；</span></span>
            </div>
            <div className={s.partWayPart}>
                <div className={s.wayTitle}>方式2:直接开通锁定席位</div>
                <span className={s.introTxt}>1. 您可选择直接支付49999元开通正式黑钻会员；</span>
                <span className={s.introTxt}>2. 直接购买用户可直接锁定私人公务机席位；同时可以享受三亚当地接机服务和<span className={s.lightTxt}>三亚豪华酒店1晚住宿</span>；</span>
                <span className={`${s.introTxt} ${s.double}`}><span className={s.lightTxt}>【双人惊喜】</span></span>
                <span className={s.introTxt}>若携匹配的女用户同行，将解锁双人出行更多惊喜，黑钻浪漫故事，期待携手缔造。</span>
            </div>
        </div>
      {/* 弹窗 */}
        <div className={`${s.planeMask} ${ruleState}`}></div>
            <div className={`${s.planeShade} ${ruleState}`}>
            <div className={s.successModal}>
                <div className={s.modelTitle}>
                    <span className={s.modelNav}>活动规则</span>
                    <img className={s.closePlaneModal} onClick={handleCloseModal} src="https://auto.tancdn.com/v1/images/eyJpZCI6IkJDR0NCVTdIVVZWN0ZRRFlCT0dUQVNQNUpHRkQ1UDA3IiwidyI6MzksImgiOjM5LCJkIjowLCJtdCI6ImltYWdlL2pwZWciLCJkaCI6MTcwMzA5MDI4MjEyNjg3NzE5OX0?format=originalOFGHLERTH" alt=""/>
                </div>
                <div className={s.ruleUl}>
                    <span className={s.ruleLi}>1. 活动报名期： 2021.3.15-3.30；</span>
                    <span className={s.ruleLi}>2. 人员出行期： 2021.4.10；</span>
                    <span className={s.ruleLi}>3. 航程安排： 深圳-三亚（可为异地用户提供到深圳的交通费用）；</span>
                    <span className={s.ruleLi}>4. 席位数量：14席，按正式开通黑钻会员顺序先到先得；</span>
                    <span className={s.ruleLi}>5. 工作人员会通过加微信或打电话的方式联系成功参与用户，并安排后续出行相关计划；</span>
                    <span className={s.ruleLi}>6. 开通用户若希望双人出行，可联系客服咨询席位升级详情；</span>
                    <span className={s.ruleLi}>7. 包机出行服务属于开通黑钻会员的赠送服务，若参与活动用户最终未能按时出行，不会产生任何退款；</span>
                    <span className={s.ruleLi}>8. 本活动最终解释权归探探官方所有。</span>
                </div>
                <img onClick={handleCloseModal} className={s.ruleOk} src="https://auto.tancdn.com/v1/images/eyJpZCI6IjZLSVZPWVNJUDVJN0xBRExDNkhBQzI3MkpEQlYzNzA3IiwidyI6NDYwLCJoIjo3OCwiZCI6MCwibXQiOiJpbWFnZS9qcGVnIiwiZGgiOjQ3MzgyMjgwMjY5MjM0MDE1Mzd9?format=originalOFGHLERTH" alt=""/>
            </div>
        </div>
   </div>
   </div>
  );
};

export default Plane;
