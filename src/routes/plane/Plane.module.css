.containerBack {
  position: fixed;
  top: 0;
  left: 0;
  bottom: 0;
  right: 0;
  background-color: rgb(24, 24, 32);
  overflow: scroll;
  padding-bottom: 0.82rem;
}

.plane_container {
  position: relative;
  margin: 0;
  padding: 0;
  width: 100%;
  font-family: PingFangSC;
  background-image: url("https://auto.tancdn.com/v1/images/eyJpZCI6IkhOMklGSTVFUlhDRkZORlNBNFBNRERXNFY2WUo0QzA2IiwidyI6NzUwLCJoIjozNDYwLCJkIjowLCJtdCI6ImltYWdlL2pwZWciLCJkaCI6MjEzNTMwODAyMTczMTI1fQ.png");
  background-size: cover;
  overflow: hidden;
}

.plane_container img {
  display: block;
}

.planeRule {
  position: relative;
  margin-top: 0.65rem;
  float: right;
  width: 0.68rem;
  height: 0.3rem;
  line-height: 0.3rem;
  background-color: #fff;
  color: #d9a260;
  text-align: center;
  font-size: 0.12rem;
  border-radius: 0.3rem 0 0 0.3rem;
}

.planeBtn {
  width: 2.88rem;
  height: 0.53rem;
  position: relative;
  margin: 4.6rem auto 0;
}

.btnTwo {
  margin-top: 0.25rem;
}

.btnImg {
  width: 100%;
}

.introWrap {
  position: relative;
  width: 3.35rem;
  margin: 0.62rem auto 0;
  border: 0.01rem solid rgb(112, 104, 96);
  padding: 0.4rem 0.2rem 0.25rem;
  box-sizing: border-box;
  background: url("https://auto.tancdn.com/v1/images/eyJpZCI6IlZNNlFYUUlKVllORENDNk5HQllPQkEySlJJTU9PMjA2IiwidyI6NzQ1LCJoIjoxMDE5LCJkIjowLCJtdCI6ImltYWdlL2pwZWciLCJkaCI6MTE1MjkyMzMzNTMzNzcxMTY1Nn0?format=originalOFGHLERTH");
  background-size: 100% 100%;
  background-color: rgb(29, 33, 44);
  border-radius: 0.04rem;
}

.introIcon {
  background: url("https://auto.tancdn.com/v1/images/eyJpZCI6IlFZWFRVVVVHVlNFNEQ1Wk1ZSUdVM0FRU1BCM1BESzA2IiwidyI6MTkzLCJoIjo1NCwiZCI6MCwibXQiOiJpbWFnZS9qcGVnIiwiZGgiOjQ2Mzg4NjMzOTMyMDQ1ODQ2NDR9?format=originalOFGHLERTH");
  background-size: 100% 100%;
}

.partWayIcon {
  background: url("https://auto.tancdn.com/v1/images/eyJpZCI6IkJMSzI1SEpOT1daVVlETlZWVklYNUxLUVQ0Nlo0QzA2IiwidyI6MTkzLCJoIjo1NCwiZCI6MCwibXQiOiJpbWFnZS9qcGVnIiwiZGgiOjQ2Mjk4NTYxNTk1Mzk0OTUxMDh9?format=originalOFGHLERTH");
  background-size: 100% 100%;
}

.introIcon,
.partWayIcon {
  width: 0.95rem;
  height: 0.26rem;
  position: absolute;
  top: -0.11rem;
  left: -0.01rem;
}
.introTxt {
  font-size: 0.12rem;
  color: #fafbfb;
  display: block;
  margin-bottom: 0.08rem;
  text-align: justify;
  line-height: 0.19rem;
}

.double {
  font-size: 0.13rem;
  margin-top: 0.14rem;
  display: block;
}

.lightTxt {
  color: #fec484;
}

.partWay {
  position: relative;
  width: 3.35rem;
  margin: 0.55rem auto 0;
  border: 0.01rem solid rgb(112, 104, 96);
  padding: 0.45rem 0.2rem 0.25rem;
  box-sizing: border-box;
  background: url("https://auto.tancdn.com/v1/images/eyJpZCI6IlZNNlFYUUlKVllORENDNk5HQllPQkEySlJJTU9PMjA2IiwidyI6NzQ1LCJoIjoxMDE5LCJkIjowLCJtdCI6ImltYWdlL2pwZWciLCJkaCI6MTE1MjkyMzMzNTMzNzcxMTY1Nn0?format=originalOFGHLERTH");
  background-size: 100% 100%;
  background-color: rgb(29, 33, 44);
  border-radius: 0.04rem;
}

.partWayPart {
  margin-bottom: 0.18rem;
}

.wayTitle {
  display: inline-block;
  background-color: rgb(248, 191, 134);
  margin-bottom: 0.13rem;
  height: 0.19rem;
  line-height: 0.19rem;
  padding: 0 0.05rem;
  color: #15212f;
  border-radius: 0.02rem;
  font-size: 0.12rem;
}

.planeStep {
  position: relative;
  width: 2.82rem;
  margin: 0.18rem auto 0;
}

.stepImgStep {
  width: 100%;
}

.stepTime {
  width: 100%;
  margin: 0 auto;
  position: relative;
}

.stepTimeText1,
.stepTimeText2 {
  position: absolute;
  display: inline-block;
  font-size: 0.12rem;
  color: #fafbfb;
}

.stepTimeText1 {
  top: 0;
  left: 0.22rem;
}

.stepTimeText2 {
  top: 0;
  left: 2.17rem;
}

.planeMask {
  position: fixed;
  top: 0;
  left: 0;
  bottom: 0;
  right: 0;
  z-index: 15;
}

.planeShade {
  position: fixed;
  top: 0;
  left: 0;
  bottom: 0;
  right: 0;
  background-color: rgba(0, 0, 0, 0.7);
  z-index: 20;
}

.successModal {
  width: 3rem;
  margin: 0.6rem auto 0;
  border-radius: 0.05rem;
  background-color: #fff;
  padding: 0 0.19rem 0.24rem;
  box-sizing: border-box;
}

.modelTitle {
  height: 0.59rem;
  text-align: center;
  position: relative;
  border-bottom: 0.01rem solid rgb(241, 241, 241);
}

.modelNav {
  font-size: 0.18rem;
  line-height: 0.64rem;
}

.closePlaneModal {
  width: 0.18rem;
  position: absolute;
  top: 0.12rem;
  right: -0.1rem;
}

.ruleUl {
  padding-top: 0.15rem;
}

.ruleLi {
  display: inline-block;
  font-size: 0.13rem;
  color: rgb(102, 102, 102);
  margin-bottom: 0.12rem;
  line-height: 0.16rem;
}

.ruleOk {
  width: 2.28rem;
  margin: 0.05rem auto 0;
}
