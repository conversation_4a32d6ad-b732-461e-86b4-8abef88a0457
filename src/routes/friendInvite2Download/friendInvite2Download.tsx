import React, { useEffect, useState } from 'react';
import { track } from '../../utils/utils';
import {isIOS, isAndroid, reportUrl} from "../../utils/config";
import {isWeixinBrowser} from "../../utils/bridge/utils";
import styles from './friendInvite2Download.module.scss';
import CopyToClipboard from "react-copy-to-clipboard";
import ClipboardJS from 'clipboard' 
import 'amfe-flexible/index.min.js'

const downloadPageHeader = 'https://auto.tancdn.com/v1/raw/870f623c-8588-4050-8bbe-c39f39ac795c0607.png';
const downloadAvatarC1 = 'https://auto.tancdn.com/v1/raw/d3b73b46-1f87-42a4-baba-5910c15f48d70607.png';
const downloadAvatarC2 = 'https://auto.tancdn.com/v1/raw/4f9e3e4f-756f-472c-b2e3-aced050f6abf0607.png';
const downloadAvatarC3 = 'https://auto.tancdn.com/v1/raw/c14c5fa8-fd76-4cfb-90da-01329234be150607.png';
const downloadAvatarC4 = 'https://auto.tancdn.com/v1/raw/78034597-88c6-48a5-9091-ab1873ce2a110607.png';
const downloadBtnC = 'https://auto.tancdn.com/v1/raw/c87ece83-ccea-41c8-9141-296ffd1c47d50607.png';
const downloadHeaderD = 'https://auto.tancdn.com/v1/raw/08c95d0c-fcd2-4be4-986e-caaa8c7431330607.png';
const downloadAvatarD = 'https://auto.tancdn.com/v1/raw/ef72439c-ae97-4de3-ad63-183e354a39050607.png';

const pageId = 'p_invite_friends_shared';

const gotoDownload = () => {
  track(
    'MC',
    pageId,
    'e_invite_friends_shared_button',
  );
  window.location.href = "tantanapp://home";
  if (isAndroid) {
    setTimeout(() => {
      window.location.href = 'https://a.app.qq.com/o/simple.jsp?pkgname=com.p1.mobile.putong&g_f=1000047';
    }, 300);
  }
  if (isIOS) {
    setTimeout(() => {
      window.location.href =
        "https://itunes.apple.com/cn/app/tantan/id861891048?mt=8";
    }, 300);
  }
};

const FriendInvite2Download: React.FC = () => {
  const [searchParams, setSearchParams] = useState<Record<string, string>>({});
  const isWeixin = isWeixinBrowser();
  useEffect(() => {
    window.document.title = '邀请你加入探探';
    let tmpParams: Record<string, string> = {};
    if (window.location.search.length > 2) {
      const { search } = window.location;
      search.slice(1).split('&').forEach((cur) => {
        const [key, val] = cur.split('=');
        tmpParams[key] = val;
      })
    }
    console.log(tmpParams);
    setSearchParams(tmpParams);

    track(
      'PV',
      pageId,
      '',
      {
        from_where_landpage: tmpParams.from,
        invitefriends_group: tmpParams.group,
      }
    );


    const clipboard = new ClipboardJS("#copyContainer");
    clipboard.on('success', (e) => {
      console.log('复制成功');
    })
    clipboard.on('error', (e) => {
      console.log('复制失败');
    })
  }, []);

  console.log('searchParams', searchParams);

  return (
    <CopyToClipboard text={`tan*${searchParams.inviteCode}#`}>
      <div id="copyContainer" data-clipboard-text={`tan*${searchParams.inviteCode}#`}>
        {
          isWeixin && isAndroid ? <div style={{
            position: 'fixed',
            top: 0,
            right: 0,
            bottom: 0,
            left: 0,
            backgroundColor: 'rgba(0, 0, 0, 0.7)',
            zIndex: 999999
          }}>
            <img style={{width: '60%', position: 'absolute', right: '20px'}}
                  src="https://auto.tancdn.com/v1/images/eyJpZCI6IlRDTk41RVpZRktDUUlNNURERUpHWFFKV1JUM0JCSjA3IiwidyI6NzY4LCJoIjo0MjAsImQiOjAsIm10IjoiaW1hZ2UvanBlZyIsImRoIjo3Mzk2MTAyNjQ2Mjk5NDE5MjY0fQ.png"
                  alt="引导"/>
          </div> : ''
        }
        {
          searchParams.group
          ? searchParams.group.slice(1) === 'c'
            ? <>
              <div className={styles.pageContainerC} onClick={gotoDownload}>
                <img src={downloadPageHeader} className={styles.pageHeader} />
                <div className={styles.flexContainer}>
                  <div>
                    <div className={styles.itemContainer}>
                      <img src={downloadAvatarC1} alt="" />
                      <img src={downloadAvatarC2} alt="" />
                    </div>
                    <div className={styles.itemContainer}>
                      <img src={downloadAvatarC3} alt="" />
                      <img src={downloadAvatarC4} alt="" />
                    </div>
                  </div>
                  <div className={styles.downloadContainer}>
                    <img src={downloadBtnC} alt="" />
                  </div>
                </div>
              </div>
            </>
            : <div className={styles.pageContainerD} onClick={gotoDownload}>
              <img src={downloadHeaderD} className={styles.pageHeader} />
              <div className={styles.bodyContainer}>
                <div className={styles.itemContainer}>
                  <img src={downloadAvatarD} alt="" />
                </div>
                <div className={styles.downloadContainer}>
                  <img src={downloadBtnC} alt="" />
                </div>
              </div>
            </div>
          : undefined
        }
      </div>
    </CopyToClipboard>
  );
};

export default FriendInvite2Download;
