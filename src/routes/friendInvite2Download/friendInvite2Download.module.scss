@import "~sass-rem";

$rem-baseline: 37.5px;

* {
  box-sizing: border-box;
  font-family: PingFangSC, -apple-system, BlinkMacSystemFont;
}

.pageContainerC {
  background-color: #fd7d67;
  background-image: url('https://auto.tancdn.com/v1/raw/c5b5a62d-a45e-4b9d-a9ee-a703f2202f660607.png');
  background-size: cover;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  
  .pageHeader {
    width: 100vw;
  }

  .itemContainer {
    display: flex;
    flex-flow: row nowrap;
    align-items: center;
    justify-content: space-between;
     //justify-content: space-evenly;
    &:before,
    &:after {
        content: '';
        display: block;
    }

    &:not(:first-child) {
      margin-top: 12px;
    }

    img {
      width: 36vw;
      height: calc(36vw * 1.2974);
    }
  }

  .downloadContainer {
    text-align: center;

    img {
      width: rem(302px);
    }
  }

  .flexContainer {
    flex: 1;
    display: flex;
    flex-direction: column;
    justify-content: space-around;
    margin-bottom: 12px;
  }
}

.pageContainerD {
  background-color: #fd7d67;
  background-image: url('https://auto.tancdn.com/v1/raw/c5b5a62d-a45e-4b9d-a9ee-a703f2202f660607.png');
  background-size: cover;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  
  .pageHeader {
    width: 100vw;
    height: rem(158px);
  }

  .itemContainer {
    display: flex;
    justify-content: space-around;

    img {
      width: 86vw;
      height: calc(86vw * 1.0573);
    }
  }

  .downloadContainer {
    text-align: center;

    img {
      width: rem(302px);
    }
  }

  .bodyContainer {
    flex: 1;
    display: flex;
    flex-direction: column;
    flex-flow: column nowrap;
    align-items: center;
    justify-content: space-between;
     //justify-content: space-evenly;
    &:before,
    &:after {
        content: '';
        display: block;
    }
  }
}

