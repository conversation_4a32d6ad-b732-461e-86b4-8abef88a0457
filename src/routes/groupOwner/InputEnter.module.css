.container {
    display: flex;
    width: 100%;
    justify-content: space-between;
    margin-top: 0.1rem;
}

.inputcontainer {
    align-items: center;
}

.textareacontainer {
    height: 0.85rem;
}

.left {
    height: 0.185rem;
    font-size: 0.13rem;
    color: #1946c4;
    line-height: 0.185rem;
}

.right {
    position: relative;
}

.star {
    width: 0.05rem;
    height: 0.05rem;
    color: #ff7fbc;
}

.input {
    width: 2.45rem;
    height: 0.3rem;
    border-radius: 0.075rem;
    box-shadow: 0.015rem 0.015rem 0.015rem 0 rgba(0, 120, 255, 0.15);
    border: solid 0.005rem #8ec3ff;
    background-color: #ffffff;
    padding-left: 0.125rem;
    box-sizing: border-box;
    font-size: 0.11rem;
    appearance: none;
    -webkit-appearance: none; 
}

.input:disabled::placeholder,
.textarea:disabled::placeholder {
    color: transparent;

}
.input:disabled::-webkit-input-placeholder,
.textarea:disabled::-webkit-input-placeholder {
    color: transparent;
}

.input::placeholder,
.input::-webkit-input-placeholder,
.textarea::placeholder,
.textarea::-webkit-input-placeholder {
    height: 0.15rem;
    opacity: 0.3;
    font-size: 0.11rem;
    color: #185395;
    line-height: 0.15rem;
    
}

.textarea {
    width: 2.45rem;
    height: 0.85rem;
    box-sizing: border-box;
    font-size: 0.11rem;
    border-radius: 15px;
    box-shadow: 0.015rem 0.015rem 0.015rem 0 rgba(0, 120, 255, 0.15);
    border: solid 0.005rem #8ec3ff;
    background-color: #ffffff;
    padding: 0.07rem 0.125rem;
    resize: none;
    appearance: none;
    -webkit-appearance: none; 
    user-select: auto;
    -webkit-user-select:auto
}
