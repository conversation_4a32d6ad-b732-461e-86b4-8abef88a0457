import React, { useEffect } from 'react';
import {RouteComponentProps} from "react-router-dom";
import { setNavigationTitle, setNavigation } from '../../utils/bridge/index'
import s from './GroupOwnerInvite.module.css';
import { Toast } from 'antd-mobile';

const GroupOwnerRule:React.FC<RouteComponentProps> = (props) => {
    useEffect(() => {
        setNavigation({
            title: '群组介绍',
            leftHandler: () => {
                props.history.go(-1)
            }
        })
        document.body.scrollIntoView();
    }, [])
    return (
        <div className={s.containerrules}>
            <div className={s.contentrules}>
                <div className={s.sectionrules}>
                    <p className={s.specialspacing}>群组介绍：</p>
                    <p>1.抢先成为一“群”之主：群组功能支持多人同时在线聊天，是探探上线的新功能，目前仅限部分用户使用。成为探探官方合作群主，即可优先体验群组功能，创建并运营一个由你做主的群组！</p>
                    <p>2.与众多soulmate零压力交流：在群组中你可以找到和你三观匹配、灵魂契合的挚友，和ta分享生活工作中的新鲜事，一起聊到嗨！</p>
                    <p>3.扩大社交圈，寻找同好：探探群组倡导以兴趣聚合用户，因此通过群聊，你可以扩大你的社交圈，找到更多和你志同道合、兴趣相投的人，拥有更多聊不完的话题！</p>
                    <p>4.有趣灵魂，在此聚集：群主可以通过邀请好友等操作将一批有趣的灵魂聚集在你的群聊中，让你的群组天天聊家常，日日有笑语，吸引更多用户申请入群！</p>
                    <p>5.加入群组，避免不应该的擦肩而过～：老是找不到心动对象？不如加入群组，通过和群用户的日常聊天，找到那个和你三观一致、灵魂匹配的心动选手！</p>
                </div>
                <div className={s.sectionrules}>
                    <p>群主职责介绍：</p>
                    <p>1.创建群聊：群主需要使用一个吸睛的群名称创建群聊，同时上传群头像和群简介，并制定符合主题并且可以落实的群规；</p>
                    <p>2.日常运营：群主应保证自己的上线时间，在群组内部和其他群成员展开互动，适时地抛出话题引导互动，以确保群组活跃；</p>
                    <p>3.及时拉新：群主需要关注活跃成员占比和群内成员留存，及时踢出长时间不活跃成员以及违规成员，同时将新成员拉入群中；</p>
                    <p>4.提升氛围：群主可以在官方的指导下尝试在群组内部使用新玩法，以提升群成员的活跃度，同时注意群组内部氛围，引导群组内部氛围向积极、健康的方向迈进。</p>
                </div>
                <div className={s.sectionrules}>
                    <p>群组相关详情：</p>
                    <p className={s.specialspacing}>功能介绍：</p>
                    <p className={s.specialspacing}>探探群组功能支持多人同时在线聊天，致力于打造社交向兴趣向的多元化群组。现面向探探用户招募群主，与官方一同运营群组功能。</p>
                    <p>1.建群：上传封面图、群类型、群名、群简介、群公告等信息；</p>
                    <p>2.功能：拍照、录像、位置共享、语音、红包、禁言、举报等；</p>
                    <p>3.成员分布：展示群内成员的在线人数、男女人数、平均年龄、群成员距离等。</p>
                </div>
                <div className={s.sectionrules}>
                    <p>群组规则介绍：</p>
                    <p>1.群组属于自愿加入交流平台，所有群内成员需要共同维护群组，承担管理职责；群主需要保障群成员权益；</p>
                    <p>2.群成员需要履行群主制定的群规则，除群主及官方外，任何人不得随意更改群名称、群头像、群简介、群规则；</p>
                    <p>3.群成员之间要友爱、团结、平等、尊重，禁止有歧视、攻击、骚扰、过激等不良言行，违者将被踢除；</p>
                    <p>4.群内严禁发布任何危害国家利益的言论，禁止涉及黄赌毒等敏感话题，禁止发布和转发未经核实的信息，违者将被踢除；</p>
                    <p>5.禁止群内恶意刷屏，或发布带有欺骗性质的言论及消息，违者将被踢除；</p>
                    <p>6.群聊鼓励成员热情参与，文明发言，结交朋友，畅聚缘分。</p>
                </div>
            </div>
        </div>
    );
};
export default GroupOwnerRule;