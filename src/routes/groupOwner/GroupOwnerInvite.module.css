.containerinvite {
    /* background-image: linear-gradient(to bottom, #6dd7ff 1%, #0178ff); */
    background-image: url('https://auto.tancdn.com/v1/images/eyJpZCI6IktTM0hKRFJBQzU3TVJMNjMzRjRWUE5MQ0RLMzM1WjA2IiwidyI6MjI1MCwiaCI6NjQyMCwiZCI6MCwibXQiOiJpbWFnZS9qcGVnIiwiZGgiOjgzODEyNzI1NzM4MjgyNTQyNzJ9.png');
    padding: 0 0.15rem 0.6rem;
    background-size: cover;
}

.toptitle {
    width: 100%;
    height: 0.48rem;
    position: relative;
}

.toptitle>img {
    width: 100%;
    height: 100%;
}

.detailbutton {
    width: 0.68rem;
    height: 0.2rem;
    position: absolute;
    right: 0.17rem;
    top: 0.07rem;
}

.middlebackground {
    box-sizing: border-box;
    padding: 0.05rem;
    margin-top: -0.155rem;
    position: relative;
    border-radius: 0.1rem;
    background-image: linear-gradient(to bottom, rgba(255, 255, 255, 0.6) 6%, rgba(255, 255, 255, 0.3) 99%);
}

.middleintro {
    height: 0.9rem;
    width: 100%;
    /* margin-top: -0.155rem; */
    border-radius: 0.1rem;
    z-index: 99;
    display: flex;
    align-items: center;
    background-image: linear-gradient(to bottom, rgba(255, 255, 255, 0.9) 6%, rgba(255, 255, 255, 0.8) 99%);
    padding: 0.1rem 0.09rem 0.08rem 0.07rem;
    box-sizing: border-box;
    /* position: relative; */
}

.middleintro_left {
    width: 1.37rem;
    height: 0.67rem;
}

.middleintro_left>img {
    width: 100%;
    height: 100%;
}

.middleintro_right {
    margin-left: 0.1rem;
    color: #1946c4;
    width: 1.72rem;
}

.middleintro_text {
    font-size: 0.12rem;
    font-weight: 500;
    line-height: 0.155rem;
}

.middleintro_example {
    opacity: 0.6;
    font-size: 0.095rem;
    font-weight: 500;
    color: #1946c4;
    line-height: 0.13rem;
    margin-top: 0.05rem;
}

.bottomintro {
    height: 0.9rem;
    width: 100%;
    box-sizing: border-box;
    border-radius: 0.1rem;
    margin-top: 0.065rem;
    display: flex;
    align-items: center;
    padding: 0.105rem 0.07rem 0.09rem 0.1rem;
    background-image: linear-gradient(to bottom, rgba(255, 255, 255, 0.9) 6%, rgba(255, 255, 255, 0.8) 99%);
}

.bottomintro_left {
    width: 1.605rem;
}

.bottomintro_example {
    opacity: 0.6;
    font-size: 0.095rem;
    font-weight: 500;
    color: #1946c4;
    line-height: 0.13rem;
    margin-top: 0.04rem;
}

.bottomintro_text {
    font-size: 0.12rem;
    font-weight: 500;
    color: #1946c4;
    line-height: 0.165rem;
}

.bottomintro_right {
    width: 1.49rem;
    height: 0.665rem;
    margin-left: 0.085rem;
}

.bottomintro_right>img {
    width: 100%;
    height: 100%;
}

.register_info {
    margin-top: 0.16rem;
}

.register_title {
    height: 0.5rem;
    width: 100%;
}

.register_title>img {
    width: 100%;
    height: 100%;
}

.register_content {
    width: 100%;
    margin-top: -0.16rem;
    position: relative;
    border-radius: 0.1rem;
    box-sizing: border-box;
    padding: 0.1rem 0.225rem 0.4rem 0.14rem;
    background-image: linear-gradient(to bottom, rgba(255, 255, 255, 0.9) 6%, rgba(255, 255, 255, 0.8) 99%);
}

.tips {
    height: 0.205rem;
    font-size: 0.11rem;
    color: #1946c4;
    line-height: 0.15rem;
    display: flex;
}

.tips>span {
    margin-right: 0.015rem;
}

.other_platform {
    height: 0.31rem;
    display: flex;
    align-items: center;
    margin-top: 0.1rem;
    /* justify-content: space-between; */
}

.other_platform>span {
    width: 1.13rem;
    height: 0.185rem;
    font-size: 0.13rem;
    color: #1946c4;
    display: inline-block;
    line-height: 0.185rem;
}

.star {
    width: 0.05rem;
    height: 0.05rem;
    color: #ff7fbc;
}

.selectradio {
    display: flex;
    align-items: center;
}

.selectradio>div {
    display: flex;
    position: relative;
    margin-left: 0.03rem;
}

.selectradio>div:last-child {
    margin-left: 0.06rem;
}

.radio {
    width: 0.12rem;
    height: 0.12rem;
    opacity: 0;
    margin-top: 0.02rem;
}

.radio+.radiolabel {
    position: absolute;
    left: 0;
    top: 0.02rem;
    width: 0.12rem;
    box-sizing: border-box;
    height: 0.12rem;
    line-height: 0.12rem;
    border-radius: 50%;
    background-color: #1c90ff;
    border: solid 0.01rem #2847ff;
}

.radio:checked+.radiolabel, .radiolabel_active {
    background-color: #ffffff !important;
    border: 0.025rem solid #1c90ff !important;
}

.radiotext {
    width: 0.13rem;
    height: 0.185rem;
    font-size: 0.13rem;
    color: #1946c4;
    margin-left: 0.04rem;
    line-height: 0.185rem;
}

.update {
    margin-top: 0.15rem;
    display: flex;
    justify-content: space-between;
}

.updatetext {
    display: inline-block;
    width: 0.65rem;
    height: 0.185rem;
    font-size: 0.13rem;
    color: #1946c4;
    margin-right: 0.11rem;
    margin-top: 0.035rem;
    white-space: nowrap;
}

.updatetips {
    display: inline-block;
    margin-left: 0.065rem;
    width: 1.745rem;
    height: 0.435rem;
    font-size: 0.105rem;
    color: #4d7cff;
    margin-top: 0.06rem;
    line-height: 0.13rem;
}

.placeholderimg {
    width: 0.5rem;
    height: 0.5rem;
    border-radius: 0.075rem;
    border: 0.025rem solid #51aaff;
    background-color: #ffffff;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.2rem;
    color: #51aaff;
}

.groupAvatar {
    /* border-width: 0;   */
    border-color: transparent;
    background-color: transparent;
}

.groupimg {
    width: 100%;
    height: 2.35rem;
    margin-bottom: 0.06rem;
}

.groupimg>img {
    width: 100%;
    height: 100%;
}

.submitbutton {
    display: flex;
    justify-content: center;
    height: 0.4rem;
    width: 100%;
    margin-top: 0.25rem;
}

.submitbutton>img {
    width: 1.9rem;
    height: 100%;
}

.inputplatform, .grouppersonnumber {
    width: 0.55rem;
    height: 0.3rem;
    border-radius: 0.075rem;
    box-shadow: 0.015rem 0.015rem 0.015rem 0 rgba(0, 120, 255, 0.15);
    border: solid 0.005rem #8ec3ff;
    background-color: #ffffff;
    text-align: center;
    font-size: 0.12rem;
    outline: none;
    -webkit-appearance: none;
    box-sizing: border-box;
}

.inputplatform::placeholder, .grouppersonnumber::placeholder, .inputplatform::-webkit-input-placeholder, .grouppersonnumber::-webkit-input-placeholder {
    width: 0.24rem;
    height: 0.165rem;
    opacity: 0.3;
    font-size: 0.12rem;
    color: #185395;
}

.grouppersonnumber {
    margin-left: 0.05rem;
}

.hasexperience {
    display: block;
    margin-left: 0.105rem;
}

.inputplatform:disabled::placeholder, .grouppersonnumber:disabled::placeholder {
    color: transparent;
}

.hidden {
    display: none;
}

.containerrules {
    padding: 0.08rem 0.08rem 0.65rem;
    background-color: #6cd7ff;
    font-family: PingFangSC;
}

.contentrules {
    font-size: 0.15rem;
    font-weight: 300;
    color: #222222;
    padding: 0.16rem 0.12rem 0.49rem;
    border-radius: 0.06rem;
    background-color: #ffffff;
}

.sectionrules p {
    line-height: 0.25rem;
    margin-top: 0.2rem;
}

.specialspacing {
    margin-top: 0 !important;
}