import React, { useEffect, useState, useRef } from 'react';
import s from './InputEnter.module.css';
import cx from 'classnames'
import { noop } from '../../utils/bridge/utils';

const InputEnter = (props: any) => {
    const ref = useRef<HTMLInputElement>(null);
    // const [v, setV] = useState('');

    // const [focus, setFocus] = useState(false)

    const [v, setV] = useState('');

    const {
        value,
        disabled,
        type = 'input',
        left = null,
        handleChange = noop,
        customPlaceholder = '',
        required = false,
        maxLength,
        ...rest
    } = props

    useEffect(() => {
        setV(value)
        
    }, [value])

    const inputenter = (type === 'input');

    const onChange = (e: any) => {
        setV(e.target.value)  
        handleChange(e.target.value)
    }

    const onClick = (e: any) => {

        if (ref && ref.current) {
            ref.current.focus()
            //   setFocus(true)
        }
    }

    return (
        <div className={cx(s.container, inputenter ? s.inputcontainer : s.textareacontainer)}>
            <div className={s.left}>
                {left}
                {required ? <span className={s.star}>*</span> : ''}
            </div>
            <div className={s.right}>
                {type === "input" ?
                    <input
                        className={s.input}
                        type="text"
                        ref={ref}
                        value={v}
                        onChange={onChange}
                        onClick={onClick}
                        placeholder={customPlaceholder}
                        disabled={disabled}
                        maxLength={maxLength}
                    />
                    : type === 'textarea' ?
                        <textarea
                            className={s.textarea}
                            onChange={onChange}
                            onClick={onClick}
                            value={v}
                            placeholder={customPlaceholder}
                            disabled={disabled}
                            maxLength={maxLength}
                        />
                        : ''}
            </div>
        </div>
    )
}

export default InputEnter;