import React, { useEffect, useState } from 'react';
import { RouteComponentProps } from "react-router-dom";
import s from './GroupOwnerInvite.module.css';
import InputEnter from './InputEnter';
import cx from 'classnames'
import { Toast, Modal } from 'antd-mobile';
import { triggerAction, getUserInfo, setNavigationTitle, setNavigation, closeWebview } from '../../utils/bridge/index';
import { ajax } from "./ajax";


const GroupOwnerInvite: React.FC<RouteComponentProps> = (props) => {
    const [groupName, setGroupName] = useState('');
    const [keyWord, setKeyword] = useState('');
    const [groupIntro, setGroupIntro] = useState('');
    const [selfIntro, setSelfIntro] = useState('');
    const [platform, setPlatform] = useState('');
    const [number, setNumber] = useState<string>('');
    const [groupAvatar, setGroupAvatar] = useState('');
    const [experience, setExperience] = useState<boolean>();
    const [disabled, setDisabled] = useState<boolean>(false);
    const [status, setStatus] = useState('');

    useEffect(() => {
        (async () => {
            const { userId } = await getUserInfo();
            let data = await ajax.get('/v2/group-recruitments/me', {
                headers: { "X-Putong-User-Id": userId }
            });
            let result = data?.data?.groupRecruitments?.[0];
            //这块需要用status判断
            setStatus(result?.status);
            if (result?.status === 'approved' || result?.status === 'pending') {
                setGroupName(result.groupName);
                setKeyword(result.keyWord);
                setGroupIntro(result.groupIntro);
                setSelfIntro(result.selfIntro);
                setPlatform(result.platform);
                setNumber(result.number + '');
                setGroupAvatar(result.groupAvatar);
                setExperience(result.experience);
                setDisabled(true)
            }
            // setNavigationTitle({
            //     title: '群主招募'
            // })
            setNavigation({
                title: '群主招募',
                leftHandler: () => {
                    closeWebview();
                }
            })
        })()
    }, [])

    const uploadImage = () => {
        triggerAction({
            actionType: 'imagePicker',
            restParams: {
                success: handleImagePicker,
            },
        })
    }

    const handleImagePicker = (url: string, error: string) => {
        if (error) {
            if (error !== 'canceled') {
                Toast.info('图片上传失败', 1)
            }
            return null
        }
        if (url) {
            // 图片上传成功
            console.log(url, 'url');

            setGroupAvatar(url)
        }
    }

    const handleRequireParams = () => {
        if (!(groupName.trim())) {
            Toast.info('请填写群名称', 1);
            return false;
        } else if (!(groupIntro.trim())) {
            Toast.info('请填写群简介', 1);
            return false;
        } else if (!groupAvatar) {
            Toast.info('请上传群头像', 1);
            return false;
        }
        return true;
    }

    const handleSingleButton = (bool: boolean) => {
        if (disabled) return;
        setExperience(bool);
    }

    const handleSubmit = async () => {
        //检查是否有必填项没有填
        if (disabled) return;
        if (handleRequireParams()) {
            const { userId } = await getUserInfo();
            const res: any = await ajax.post('/v2/group-recruitments', {
                groupName,//组名
                keyWord,//关键词
                groupIntro,//群简介
                selfIntro,//个人简介
                experience,//有无其他平台经验
                platform,//平台
                number: parseInt(number),//群人数
                groupAvatar: groupAvatar.substr(groupAvatar.lastIndexOf('/') + 1),//群头像
            }, {
                headers: { "X-Putong-User-Id": userId }
            });
            if (res?.meta?.code === 201) {
                Toast.info('提交成功！', 1);
                setDisabled(true);
            }
        }
    }

    const goDetailPage = () => {
        props.history.push('/groupowner/invite/rules')
    }

    return (
        <div className={s.containerinvite}>
            <div className={s.groupimg}>
                <img src="https://auto.tancdn.com/v1/images/eyJpZCI6IlJFWFBYTUhLUExZS09GSVFXVEZMVkg0RVdGRzJEUzA3IiwidyI6MjI1MCwiaCI6MTQxMCwiZCI6MCwibXQiOiJpbWFnZS9qcGVnIiwiZGgiOjE2MDI5NDg0OTE5ODc2Mzc4NDU5fQ.png" alt="" />
            </div>
            <div className={s.groupownerintro}>
                <div className={s.toptitle}>
                    <img src="https://auto.tancdn.com/v1/images/eyJpZCI6IjJHM0hIQVBCVFk2STJTVEhNSE4zSEpWR0VWSFVHSTA3IiwidyI6MjI1MCwiaCI6Mjg4LCJkIjowLCJtdCI6ImltYWdlL2pwZWciLCJkaCI6NjM1MTY2NDIwMjkzNzA2NTYxM30.png" alt="" />
                    <span className={s.detailbutton} onClick={goDetailPage}></span>
                </div>
                <div className={s.middlebackground}>
                    <div className={s.middleintro}>
                        <div className={s.middleintro_left}>
                            <img src="https://auto.tancdn.com/v1/images/eyJpZCI6IkpQM0E1TDZQTUc0WkI3QU5QWFVIVFdJSjJZVlVUQjA2IiwidyI6ODIyLCJoIjo0MDIsImQiOjAsIm10IjoiaW1hZ2UvanBlZyIsImRoIjo4NjcwOTg4MDI5NzA4MTYwMDkwfQ.png" alt="" />
                        </div>
                        <div className={s.middleintro_right}>
                            <p className={s.middleintro_text}>创建属于自己的独一无二的群，天天趣聊嗨翻天！</p>
                            <p className={s.middleintro_example}>示例: 甜甜的恋爱在南京/宁波骑友互动群无锡八零九零交友群/“豫”见苏州 交友群</p>
                            {/* <p>与soulmate天天趣聊嗨翻天！</p> */}
                        </div>
                    </div>
                    <div className={s.bottomintro}>
                        <div className={s.bottomintro_left}>
                            <p className={s.bottomintro_text}>扩大自己的社交圈，结识更多兴趣相投的人～</p>
                            <p className={s.bottomintro_example}>示例：芜湖摄影互勉/杭州喵宠星球网抑云音乐交流群/邯郸汉服社交流群</p>
                            {/* <p>让有趣灵魂，在此聚集</p> */}
                        </div>
                        <div className={s.bottomintro_right}>
                            <img src="https://auto.tancdn.com/v1/images/eyJpZCI6IktOSkVUNkpURDVQRU9ZUTNHUkZHNFNQWENCRlM1NzA2IiwidyI6ODk0LCJoIjozOTMsImQiOjAsIm10IjoiaW1hZ2UvanBlZyIsImRoIjo4NDgyMDgyNzA5NzQyMDk3ODM3fQ.png" alt="" />
                        </div>
                    </div>
                </div>
            </div>
            <div className={s.register_info}>
                <div className={s.register_title}>
                    <img src="https://auto.tancdn.com/v1/images/eyJpZCI6IlNMSFBJUUxTUDJDTTZUVk81N05FVVFHQVM1WDM2NTA3IiwidyI6MjI1MCwiaCI6MzAwLCJkIjowLCJtdCI6ImltYWdlL2pwZWciLCJkaCI6MTE3MjU5NTAxMDE3NjQ3NTMwOX0.png" alt="" />
                </div>
                <div className={s.register_content}>
                    <div className={s.tips}><span>* </span>目前为群组试运营阶段，最终形式将以试运营效果而定</div>
                    <InputEnter
                        type="input"
                        left="群名称"
                        customPlaceholder="可包含地点、目的和事物，不超过15个字"
                        required={true}
                        value={groupName}
                        handleChange={setGroupName}
                        disabled={disabled}
                        maxLength={15}
                    />
                    <InputEnter
                        type="input"
                        left="群关键词"
                        customPlaceholder="地点+特性（共性，同好等）+其他"
                        value={keyWord}
                        handleChange={setKeyword}
                        disabled={disabled}
                        maxLength={15}
                    />
                    <InputEnter
                        type="textarea"
                        left="群简介"
                        customPlaceholder="可描述你的建群目的、希望什么样的朋友加入和群内规则，不超过300字"
                        required={true}
                        value={groupIntro}
                        handleChange={setGroupIntro}
                        disabled={disabled}
                        maxLength={300}
                    />
                    <InputEnter
                        type="textarea"
                        left="自我介绍"
                        customPlaceholder="性别、年龄、兴趣爱好、申请理由等，让探探充分了解你"
                        handleChange={setSelfIntro}
                        value={selfIntro}
                        disabled={disabled}
                        maxLength={300}
                    />
                    <div className={s.other_platform}>
                        <span>有无其它平台经验</span>
                        <div className={s.selectradio}>
                            <div>
                                <input className={s.radio} id="item1" type="radio" name="item"
                                    disabled={disabled ? true : false}
                                    onChange={(e: any) => { handleSingleButton(false); }}
                                />
                                <label htmlFor="item1" className={cx(s.radiolabel, (disabled && !experience) ? s.radiolabel_active : '')}></label>
                                <span className={s.radiotext}>无</span>
                            </div>
                            <div>
                                <input className={s.radio} id="item2" type="radio" name="item"
                                    disabled={disabled ? true : false}
                                    onChange={(e: any) => { handleSingleButton(true); }}
                                />
                                <label htmlFor="item2" className={cx(s.radiolabel, (disabled && experience) ? s.radiolabel_active : '')}></label>
                                <span className={s.radiotext}>有</span>
                            </div>
                        </div>
                        <div className={cx(s.hasexperience, !experience ? s.hidden : '')}>
                            <input className={s.inputplatform} type="text" placeholder="平台" value={platform} disabled={disabled} onChange={(e: any) => { setPlatform(e.target.value); }} />
                            <input className={s.grouppersonnumber} type="text" placeholder="群人数" value={number} disabled={disabled} onChange={(e: any) => { setNumber(e.target.value.replace(/[^\d]/g, '')); }} />
                        </div>
                    </div>
                    <div className={s.update}>
                        <span className={s.updatetext}>上传群头像<span className={s.star}>*</span> </span>
                        {(groupAvatar || disabled) ?
                            <img className={cx(s.placeholderimg, s.groupAvatar)} src={groupAvatar} alt="" /> :
                            <div className={s.placeholderimg} onClick={uploadImage}>+</div>
                        }
                        <span className={s.updatetips}>请上传清晰、表意明确的群头像，群资料需进行审核</span>
                    </div>
                    <div className={s.submitbutton}>
                        {
                            disabled ? 
                            <img src="https://auto.tancdn.com/v1/images/eyJpZCI6Ik83Q08yUklCM0xSM1BJWFNWUkZDWkw2S1NKRVE2RDA3IiwidyI6MTE0MCwiaCI6MjQwLCJkIjowLCJtdCI6ImltYWdlL2pwZWciLCJkaCI6MTQyNDk2MTM4NTMzOTAwNjE3NjV9.png" alt=""/>:
                            <img onClick={handleSubmit} src="https://auto.tancdn.com/v1/images/eyJpZCI6IlVPU1U1S1JESFBNNlVMWU5PUkg2NVNON0gzQzNLSjA2IiwidyI6MTE0MCwiaCI6MjQwLCJkIjowLCJtdCI6ImltYWdlL2pwZWciLCJkaCI6NTAyNjIzNDExOTk1MzQzMjY0NX0.png" alt="" />
                        }
                    </div>
                </div>
            </div>
        </div>
    );
};
export default GroupOwnerInvite;
