import axios from "axios";
// import { mock } from '../_mock'
import { Toast } from "../../components/antd/Antd";
import {getAbHeader, getAuthorizationHeader, getSystemInfo} from "../../utils/bridge";
import {kolTrackUrl, reportUrl} from "../../utils/config";
const baseURL =
  process.env.REACT_APP_TEST === "true"
    ? ""
    : process.env.NODE_ENV === "production"
    ? ""
    : "";
const instance = axios.create({
  baseURL,
  timeout: 5000,
});
const urls = [kolTrackUrl, reportUrl]
instance.interceptors.request.use(
  async (config) => {
    if (!config.headers.Authorization && config.url) {
      const needToken = !urls.includes(config.url);
      if (!needToken) {
        return {
          ...config,
        };
      } else {
        const auth = await getAuthorizationHeader(config.url, config.data);
        const abHeaders = await getAbHeader()
        // console.log(abHeaders);
        const { os, appVersion } = await getSystemInfo();
        return {
          ...config,
          headers: {
            ...config.headers,
            Authorization: auth,
            OS: os,
            AppVersion: appVersion,
            'X-Testing-Group': abHeaders
          },
        };
      }
    }
    return config;
  },
  () => {
    Toast.info("系统异常", 0.5);
    // 拦截器异常错误.
    return null;
  }
);

instance.interceptors.response.use( 
  function (response) {
    if (response.status === 200 || response.status === 201) {
      if (
        response.data &&
        response.data.meta &&
        (response.data.meta.code === 200 || response.data.meta.code === 201)
      ) {
        return response.data;
      }
      return null;
    }
    return null;
  },
  function () {
    return null;
  }
);
// process.env.NODE_ENV !== 'production' && mock(instance)
export const ajax = instance;
