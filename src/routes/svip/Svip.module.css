.wrap {
  height: 100%;
  width: 100%;
  overflow: auto;
}
.container {
  width: 100%;
  background-color: #191727;
  box-sizing: border-box;
  position: relative;
  overflow: hidden;
}
.container.showModal {
  overflow: hidden;
  height: 100%;
}
.top {
  position: relative;
  font-size: 0;
}
.topBg {
  width: 100%;
}
.nav {
  position: absolute;
  top: 0.14rem;
  left: 0;
  right: 0;
  display: flex;
  justify-content: space-between;
  align-items: center;
  color: #edc298;
  font-size: 0;
  font-family: PingFangSC-Medium;
}
.nav.isAndroid,
.nav.hideNotch {
  top: 0.3rem;
}
.title {
  font-size: 0.12rem;
}
.iconAndTitle {
  display: flex;
  align-items: center;
}
.iconAndTitle img {
  width: 0.18rem;
  height: 0.18rem;
  margin-right: 0.06rem;
  margin-left: 0.12rem;
}
.close img {
  width: 0.32rem;
  height: 0.32rem;
  font-size: 0;
  margin-right: 0.1rem;
}

.userInfo {
  background-image: url("https://auto.tancdn.com/v1/images/eyJpZCI6IlRYSk9RUUtWVEJMSE9YS0JPN1FZM0g1SlNFUlQzSzA1IiwidyI6MTAyOSwiaCI6MTkyLCJkIjowLCJtdCI6ImltYWdlL2pwZWciLCJkaCI6OTA3Nzg0Mjg0NjY2MDAzMzExN30.png");
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  margin-left: 0.16rem;
  margin-right: 0.16rem;
  display: flex;
  background-size: 100% 100%;
  padding-right: 0.16rem;
}
.avatar {
  background-image: url("https://auto.tancdn.com/v1/images/eyJpZCI6IllJTTRRQ1k2SU9XUkpHRjZVSUVTVVJQS1RSM09TNjA1IiwidyI6MTYyLCJoIjoxNjIsImQiOjAsIm10IjoiaW1hZ2UvanBlZyIsImRoIjoxMjI2OTI2MDM3MDg0Nzk0MjA1OH0.png");
  width: 0.54rem;
  height: 0.54rem;
  background-size: 100% 100%;
  margin: 0.05rem 0.08rem 0.05rem 0.08rem;
  padding: 0.06rem;
  box-sizing: border-box;
}
.avatar img {
  width: 100%;
  height: 100%;
  border-radius: 100%;
}
.svip {
  width: 0.43rem;
}

.nameWrap {
  margin-top: 0.1rem;
  width: calc(100% - 0.7rem);
}
.nameContainer {
  display: flex;
  align-items: center;
  height: 0.22rem;
}
.name {
  font-family: PingFangSC;
  display: inline-block;
  white-space: nowrap;
  font-size: 0.16rem;
  font-weight: 500;
  color: #744f0f;
  word-break: keep-all;
  text-overflow: ellipsis;
  overflow: hidden;
  line-height: 1.2;
}
.dot {
  width: 0.04rem;
  height: 0.04rem;
  border-radius: 100%;
  background-color: #744f0f;
  margin-right: 0.05rem;
  margin-left: 0.05rem;
}
.expiredDate {
  font-family: PingFangSC;
  font-size: 0.12rem;
  color: #744f0f;
  margin-top: 0.04rem;
  line-height: 0.17rem;
}
.userinfoStar {
  position: absolute;
  bottom: 0.39rem;
  left: 1.04rem;
  z-index: 999;
  animation: userInfoMove 200s infinite;
  animation-direction: alternate;
}
.userinfoStar img {
  width: 0.48rem;
  height: 0.48rem;
}

.starmap {
  margin-right: 0.28rem;
  margin-left: 0.27rem;
  position: relative;
}

.starmap img {
  width: 100%;
}

.starmap .giveVipDesc {
  position: absolute;
  top: -0.012rem;
  left: -0.15rem;
  margin-top: 0.08rem;
  color: #edc298;
  font-family: PingFangSC;
  font-size: 0.12rem;
  line-height: 0.17rem;
  display: inline-block;
  transform: scale(0.92);
  width: 115%;
}

.popularItemContainer {
  background-image: linear-gradient(
    to bottom,
    #d7deff -1%,
    #8d96b5 2%,
    #707b9c
  );
  height: 0.72rem;
  margin-left: 0.16rem;
  margin-right: 0.16rem;
  border-radius: 0.04rem;
  display: flex;
  background-repeat: no-repeat;
  position: relative;
}

.popularItemContainer + .popularItemContainer {
  margin-top: 0.16rem;
}
.topBorderImg {
  position: absolute;
  width: 1.22rem;
  height: 0.01rem;
  top: 0;
  left: 50%;
  transform: translateX(-50%);
}
.bottomBorderImg {
  position: absolute;
  width: 1.22rem;
  height: 0.01rem;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
}
.seeImgs {
  position: relative;
  left: 0.1rem;
  bottom: 0.1rem;
}

.seeImgContainer {
  box-sizing: border-box;
  width: 0.64rem;
  height: 0.8rem;
  background-image: url("https://auto.tancdn.com/v1/images/eyJpZCI6IjNXVkhBTFJXS1lDT1g0WE40NkZNTE1GTlVQTU9QUDA1IiwidyI6MTkyLCJoIjoyNDAsImQiOjAsIm10IjoiaW1hZ2UvanBlZyIsImRoIjoxNDEyMzY0NjA3MDIyNzc5NjQyMH0.png");
  padding: 0.04rem;
  background-size: cover;
  position: absolute;
}
.seeImgContainer img {
  width: 100%;
  height: 100%;
}
.seeImgContainer:nth-child(1) img {
  opacity: 0.6;
}
.seeImgContainer:nth-child(2) img {
  opacity: 0.8;
}
.seeImgContainer:nth-child(2) {
  transform: rotate(8deg);
  position: absolute;
  left: 0.36rem;
}
.seeImgContainer:nth-child(3) {
  transform: rotate(15deg);
  position: absolute;
  left: 0.74rem;
  top: 0.03rem;
}
.seeTextContainer {
  margin-top: 0.16rem;
  font-size: 0;
  width: 100%;
  text-align: right;
  margin-right: 0.16rem;
}
.seeText {
  color: #cbd0e8;
  font-size: 0.12rem;
  line-height: 0.17rem;
  position: relative;
  top: -0.1rem;
}

.seeText.en {
  white-space: pre-wrap;
}
.seeTextContainer img {
  width: 1.2rem;
  margin-bottom: 0.08rem;
}
.bottom {
  margin-top: 0.32rem;
  padding-bottom: 0.16rem;
  font-size: 0.12rem;
  text-align: center;
}
.feedback {
  color: #999999;
  font-family: PingFangSC-Light;
  padding: 0.04rem 0.09rem 0.05rem 0.15rem;
  margin-bottom: 0.12rem;
  background-color: rgba(106, 115, 146, 0.1);
  display: inline-block;
  border-radius: 0.16rem;
  line-height: 0.17rem;
}

.reply {
  color: #757575;
  font-family: PingFangSC-Thin;
}
.secondPopular {
  justify-content: space-between;
}
.chatLeft {
  margin-left: 0.16rem;
  margin-top: 0.16rem;
  color: #cbd0e8;
  font-size: 0;
}
.chatLeft img {
  width: 1.2rem;
  margin-bottom: 0.08rem;
}
.chatRight {
  display: flex;
  align-items: center;
  margin-right: 0.2rem;
}
.chatRight .avatarWrap {
  background-image: url("https://auto.tancdn.com/v1/images/eyJpZCI6IllJTTRRQ1k2SU9XUkpHRjZVSUVTVVJQS1RSM09TNjA1IiwidyI6MTYyLCJoIjoxNjIsImQiOjAsIm10IjoiaW1hZ2UvanBlZyIsImRoIjoxMjI2OTI2MDM3MDg0Nzk0MjA1OH0.png");
  background-size: 100% 100%;
  width: 0.64rem;
  height: 0.64rem;
  padding: 0.07rem;
  box-sizing: border-box;
  position: relative;
  right: -0.17rem;
  z-index: 500;
}
.chatRight .avatarWrap img {
  width: 100%;
  height: 100%;
  border-radius: 100%;
}
.chatRight .seeOtherWrap {
  background-image: url("https://auto.tancdn.com/v1/images/eyJpZCI6IllJTTRRQ1k2SU9XUkpHRjZVSUVTVVJQS1RSM09TNjA1IiwidyI6MTYyLCJoIjoxNjIsImQiOjAsIm10IjoiaW1hZ2UvanBlZyIsImRoIjoxMjI2OTI2MDM3MDg0Nzk0MjA1OH0.png");
  background-size: 100% 100%;
  width: 0.64rem;
  height: 0.64rem;
  padding: 0.07rem;
  box-sizing: border-box;
  z-index: 501;
  position: relative;
}
.heartIcon {
  width: 0.22rem;
  position: absolute;
  left: 0;
  bottom: 0;
}
.seeOther {
  width: 100%;
  height: 100%;
  border-radius: 100%;
  filter: blur(2px);
}
.thirdPopular {
  justify-content: space-between;
}
.privateMsgLeft {
  position: absolute;
}
.privateMsgLeft img {
  width: 1.39rem;
}
.privateMsgRight {
  margin-right: 0.16rem;
  color: #cbd0e8;
  font-size: 0;
  text-align: right;
  width: 100%;
}
.privateMsgRight img {
  width: 1.6rem;
  margin-top: 0.16rem;
  margin-bottom: 0.08rem;
}

.privateMsgRight .img-en {
  margin-top: 0.1rem;
  margin-bottom: 0;
}
.shadow1 {
  width: 0.42rem;
  position: absolute;
  left: 0;
  top: 24%;
}
.shadow2 {
  position: absolute;
  width: 0.36rem;
  right: 0;
  top: 33%;
}
.shadow5 {
  position: absolute;
  width: 1.3rem;
  left: 0;
  top: 65%;
}
.shadow6 {
  position: absolute;
  width: 0.56rem;
  right: 0.55rem;
  bottom: 0.51rem;
}
@keyframes userInfoMove {
  0% {
    left: 0.88rem;
  }
  100% {
    left: calc(100% - 1.38rem);
  }
}
@media screen and (max-width: 3.2rem) {
  .seeTextContainer .seeText,
  .secondPopular .seeText {
    max-width: 1.28rem;
    overflow: hidden;
    text-overflow: ellipsis;
    word-break: keep-all;
    white-space: nowrap;
  }
  .thirdPopular .seeText {
    overflow: hidden;
    text-overflow: ellipsis;
    word-break: keep-all;
    white-space: nowrap;
  }
  .thirdPopular .privateMsgLeft img {
    width: 100%;
  }
  .thirdPopular .privateMsgRight img {
    width: 100%;
  }
  .thirdPopular .privateMsgRight {
    margin-right: 0.1rem;
    margin-left: 0.06rem;
  }
}
