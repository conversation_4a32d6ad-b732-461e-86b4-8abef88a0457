/* eslint-disable */
import React, {useState, useEffect} from "react";
import s from "./Svip.module.css";
import * as IMGS_ZH from "./imgs-zh";
import * as IMGS_EN from './imgs-en';
import EffectivePrivilege from "../../components/effectivePrivilege/EffectivePrivilege";
import MorePrivilege from "../../components/morePrivilege/MorePrivilege";
import BlockTitle from "../../components/blockTitle/BlockTitle";
import Modal from "../../components/modal/Modal";
import {triggerAction, closeWebview, openWebview, hideNavigation, getABNames, getSystemInfo} from "../../utils/bridge";
import {urlParams, formatDateEn, formatDate} from "../../utils/index";
import {RouteComponentProps} from "react-router-dom";
import {isIOS, isAndroid} from "../../utils/config";
import {useTranslation} from "react-i18next";
import { compare } from '../../utils/utils';

const IMGS = {
  zh: IMGS_ZH,
  en: IMGS_EN
}

const Svip: React.FC<RouteComponentProps> = (props) => {
  const {t, i18n} = useTranslation();
  const language = i18n.language === 'zh-Hans' ? 'zh' : 'en';
  const isEn = language === 'en';

  const [visible, setVisible] = useState(false)
  const [ablist, setAblist] = useState<string[]>([]);
  const [appNewVer, setAppNewVer] = useState<boolean>(false)
  const [appNewerVer, setAppNewerVer] = useState<boolean>(false)
  const [isHasVer, setisHasVer] = useState<boolean>(false)
  // 头像
  const avatarURL = urlParams(props.history.location.search.slice(1)).avatarURL;
  //姓名
  const name = decodeURIComponent(
    urlParams(props.history.location.search.slice(1)).name
  );
  // 性别
  const gender = urlParams(props.history.location.search.slice(1)).gender;
  // 失效时间
  const expiresTime = urlParams(props.history.location.search.slice(1))
    .expiresTime;
  // 购买规格
  const duration = urlParams(props.history.location.search.slice(1)).duration;
  // 购买时间
  const buyTime = urlParams(props.history.location.search.slice(1)).time;
  // 是否展示礼物
  const showGift = urlParams(props.history.location.search.slice(1)).showGift;
  // hideNotch=1表示隐藏状态栏
  const hideNotch = urlParams(props.history.location.search.slice(1)).hideNotch === '1';
  // 判断环境
  const isDebug = window.location.href.includes('stag');


  // 新春特惠活动
  const currentTime = new Date().getTime();
  const startFeb = new Date('2021/02/01 00:00:00').getTime();
  const endFeb = new Date('2021/02/28 23:59:59').getTime();
  const nowTime = isDebug ? currentTime : buyTime;
  const showNewYearGift = (nowTime >= startFeb && nowTime <= endFeb) ? true : false;


  useEffect(() => {
    (async() => {
      const list: string[] = await getABNames();
      setAblist(list as string[]);
      const { os, appVersion}= await getSystemInfo();
      console.log(appVersion, 'appVersion');
      setAppNewerVer(((os === 'Android') ? compare(appVersion, '5.5.1') : compare(appVersion,'5.5.6')));
      setAppNewVer((os === 'Android') ? compare(appVersion, '4.7.3') : compare(appVersion, '4.7.5'));
      setisHasVer(true);
    })()
  }, []);
  // 判断App版本
  let flag = ablist.indexOf('REV_VoiceQuickchatRev:exp1') === -1;
  const topbgcImg = () => {
    console.log(flag, '...flag')
    if(appNewVer && appNewerVer && !flag) {
      console.log('符合最新版本,是实验组成员');
      return IMGS[language].topBgNewerVersion;
    }else if(appNewVer && !appNewerVer) {
      console.log('中间版本');
        return IMGS[language].topBgNewVersion;
    } else if (!appNewVer && !appNewerVer && isHasVer){
      console.log('老版本');
      return IMGS[language].topBg;
    } else if(appNewVer && appNewerVer) {
      return IMGS[language].topBgNewVersion;
    }
  }


  useEffect(() => {
    // 是否展示礼物弹窗
    if (isAndroid) {
      const res = triggerAction({
        actionType: "showSvipGift",
      });
      if (res === "true") {
        setVisible(true);
      } else {
        setVisible(false);
      }
    }
    if (isIOS) {
      if (showGift === "true") {
        setVisible(true);
      } else {
        setVisible(false);
      }
    }
  }, [showGift]);
  const gotoFeedback = () => {
    if (isIOS) {
      openWebview("https://feedback.tantanapp.com/feedback-mobile");
    }
    if (isAndroid) {
      triggerAction({
        actionType: "openTokenWebview",
        restParams: {
          url: "https://feedback.tantanapp.com/feedback-mobile",
          title: "帮助与反馈",
        },
      });
    }
  };
  return (
    <div className={`${s.container} ${visible ? s.showModal : s.hideModal}`}>
      <div className={s.top}>
        <img className={s.topBg} style={{visibility:isHasVer? 'unset' :'hidden'}} src={topbgcImg()} alt=""/>
        <div className={`${s.nav} ${isAndroid ? s.isAndroid : ""} ${hideNotch ? s.hideNotch : ''}`}>
          <div className={s.iconAndTitle}>
            <img src={IMGS[language].iconAndTitle} alt=""/>
            <span className={s.title}>{t('SVIP_LANDING_PAGE_ICON')}</span>
          </div>
          <div className={s.close} onClick={closeWebview}>
            <img src={IMGS[language].closeIcon} alt=""/>
          </div>
        </div>
        <div className={s.userinfoStar}>
          <img src={IMGS[language].userinfoStar} alt=""/>
        </div>
        <div className={s.userInfo}>
          <div className={s.avatar}>
            <img src={avatarURL} alt=""/>
          </div>
          <div className={s.nameWrap}>
            <div className={s.nameContainer}>
              <span className={s.name}>{name}</span>
              <span className={s.dot}></span>
              <img className={s.svip} src={IMGS[language].svip} alt=""/>
            </div>
            <div className={s.expiredDate}>
              {t('SVIP_LANDING_PAGE_VALID_PERIOD')}&nbsp;
              {isEn ? formatDateEn(expiresTime) : formatDate(expiresTime)}
            </div>
          </div>
        </div>
      </div>
      <div className={s.starmap}>
        <img src={IMGS[language].starmapBg} alt=""/>
        {
          (!isEn && showNewYearGift && Number(duration) === 2592000 && ablist.includes('REV_SFpromotion:exp')) ? <span className={s.giveVipDesc}>*新春限时特惠，随机赠送至多30天会员，到期后自动延长</span> : ''
        }
      </div>
      <div className={s.popularBlock}>
        <BlockTitle isEn={isEn} title={IMGS[language].mostPopular}/>
        <div className={s.popularItemContainer}>
          <img className={s.topBorderImg} src={IMGS[language].bottomBorderImg} alt=""/>
          <div className={s.seeImgs}>
            <div className={s.seeImgContainer}>
              <img src={gender === "male" ? IMGS[language].boy3 : IMGS[language].girl3} alt=""/>
            </div>
            <div className={s.seeImgContainer}>
              <img src={gender === "male" ? IMGS[language].boy2 : IMGS[language].girl2} alt=""/>
            </div>
            <div className={s.seeImgContainer}>
              <img src={gender === "male" ? IMGS[language].boy1 : IMGS[language].girl1} alt=""/>
            </div>
          </div>
          <div className={s.seeTextContainer}>
            <img src={IMGS[language].see} className={language} alt=""/>
            <div className={`${s.seeText} smallFont`}>{t('SVIP_LANDING_PAGE_SEE_SUBTITLE')}</div>
          </div>
          <img
            className={s.bottomBorderImg}
            src={IMGS[language].bottomBorderImg}
            alt=""
          />
        </div>
        <div className={`${s.popularItemContainer} ${s.secondPopular}`}>
          <img className={s.topBorderImg} src={IMGS[language].bottomBorderImg} alt=""/>
          <div className={s.chatLeft}>
            <img src={IMGS[language].chat} className={language} alt=""/>
            <div className={`${s.seeText} smallFont`}>{(ablist.includes('soul_chat:no_filter') || ablist.includes('soul_chat:with_filter')) ? '每天免费体验“心灵速配”' : t('SVIP_LANDING_PAGE_QUICK_CHAT_SUBTITLE')}</div>
          </div>
          <div className={s.chatRight}>
            <div className={s.avatarWrap}>
              <img src={avatarURL} alt=""/>
            </div>
            <div className={s.seeOtherWrap}>
              <img
                className={s.seeOther}
                src={gender === "male" ? IMGS[language].boy1 : IMGS[language].girl1}
                alt=""
              />
              <img className={s.heartIcon} src={(ablist.includes('soul_chat:no_filter') || ablist.includes('soul_chat:with_filter')) ? IMGS[language].heartFast : appNewVer ? IMGS[language].heartIconNewVersion : IMGS[language].heartIcon} alt=""/>
            </div>
          </div>
          <img
            className={s.bottomBorderImg}
            src={IMGS[language].bottomBorderImg}
            alt=""
          />
        </div>
        <div className={`${s.popularItemContainer} ${s.thirdPopular}`}>
          <img className={s.topBorderImg} src={IMGS[language].bottomBorderImg} alt=""/>
          <div className={s.privateMsgLeft}>
            <img src={IMGS[language].privateMsgImg} alt=""/>
          </div>
          <div className={s.privateMsgRight}>
            <img src={IMGS[language].privateMsg} className={s[`img-${language}`]} alt=""/>
            <div className={`${s.seeText} smallFont ${s[language]}`}>
              {language === 'zh'
                ? `让${gender === 'male' ? '他' : '她'}最先看到你的喜欢，并用一句话传递心意`
                : `Make ${gender === 'male' ? 'his' : 'her'} notice you, and send a message!`}
            </div>
          </div>
          <img
            className={s.bottomBorderImg}
            src={IMGS[language].bottomBorderImg}
            alt=""
          />
        </div>
      </div>
      <div className={s.starmap}>
        <img src={IMGS[language].starmapBg} alt=""/>
      </div>
      {/*提升效率六大特权*/}
      <EffectivePrivilege gender={gender} ablist={ablist} noNeedFlashChat={appNewVer ? true: false }/>
      <div className={s.starmap}>
        <img src={IMGS[language].starmapBg} alt=""/>
      </div>
      {/*更多特权*/}
      <MorePrivilege ablist={ablist} appNewerVer={appNewerVer} flag={flag}/>
      <div className={s.bottom}>
        <div className={s.feedback} onClick={gotoFeedback}>
          {t('SVIP_LANDING_PAGE_FEEDBACK_BUTTON')}
        </div>
        <div className={s.reply}>{t('SVIP_LANDING_PAGE_FEEDBACK_DESCRIPTION')}</div>
      </div>
      {/* visible */}
      <Modal visible={visible} setVisible={setVisible} ablist={ablist}/>
      <img className={s.shadow1} src={IMGS[language].shadow1} alt=""/>
      <img className={s.shadow2} src={IMGS[language].shadow2} alt=""/>
      <img className={s.shadow5} src={IMGS[language].shadow5} alt=""/>
      <img className={s.shadow6} src={IMGS[language].shadow6} alt=""/>
    </div>
  );
};
export default Svip;
