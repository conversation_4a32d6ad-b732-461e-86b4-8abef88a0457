import React, {useEffect, useState} from "react";
import {
  hideNavigation, setNavigation, setNavigationTitle,
  setNavLeftButton,
  setNavRightButton,
  showToast,
} from "../../../utils/bridge";
import {List} from "antd-mobile";
import {INavigationParams} from "../../../utils/bridge/interface";
import {ContentCard, RenderItem} from "../../../components/ContentCard";

const {Item} = List;

const imgUrl = 'https://auto.tancdn.com/v1/images/eyJpZCI6IkpLNkdHRlNMVjROTkozM043WFdIQU5JV0pHNzJaNjA3IiwidyI6MTQ0LCJoIjoxNDQsImQiOjAsIm10IjoiaW1hZ2UvanBlZyIsImRoIjo0NjMyODkwMjg2MTQ1OTk3ODU4fQ.png';
const baseContent: RenderItem = {
  title: '',
  transfer: '-',
  desc: 'desc',
  callbackType: 'null',
  content: '-'
}
const showToastFn = (content: string) => {
  showToast({context: content})
}
const getRenderContent = (content: string, desc?: string): RenderItem => {
  return {
    ...baseContent,
    transfer: content,
    desc: desc || ''
  }
}
export const NativeTitlePage: React.FC = () => {
  const [renderContent, setRenderContent] = useState<RenderItem>(baseContent)

  const setNavLeftButtonFn = (type: string) => {
    setRenderContent(getRenderContent(`Android：
- 不支持左侧设置文案
- 左侧设置图片仅支持icon黑白样式，不支持设置彩色图片
- 设置图片需要设定尺寸：56dp、png、正方形

setNavLeftButton({
  ${type}: ${type},
  handler() {
    showToastFn('设置左侧按钮样式、功能')
  }
})`, '设置左侧按钮样式、功能'))
    setNavLeftButton({
      [type]: type === 'imgUrl' ? imgUrl : type,
      handler() {
        showToastFn('设置左侧按钮样式、功能')
      }
    })
  }

  const setNavRightButtonFn = (type: string) => {
    setRenderContent(getRenderContent(`setNavRightButton({
  ${type}: ${type},
  handler() {
    showToastFn('设置右侧按钮样式、功能')
  }
})`, '设置右侧按钮样式、功能'))
    setNavRightButton({
      [type]: type === 'imgUrl' ? imgUrl : type,
      handler() {
        showToastFn('设置右侧按钮样式、功能')
      }
    })
  }

  const hideNavigationFn = () => {
    setRenderContent(getRenderContent(`hideNavigation()`, '隐藏导航栏'))
    hideNavigation();
  }

  const setNavigationTitleFn = () => {
    setRenderContent(getRenderContent(`setNavigationTitle({title: '设置标题'})`, '设置标题'))
    setNavigationTitle({title: '设置标题'});
  }

  const setNavigationFn = () => {
    const titleParam: INavigationParams = {
      title: '设置标题（全）',
      skipBack: true,
      leftImgUrl: imgUrl,
      rightText: 'toggle skipBack',
      rightHandler() {
        titleParam.skipBack = !titleParam.skipBack;
        setRenderContent(getRenderContent(`setNavigation(${JSON.stringify(titleParam, null, 2)})`, '设置标题（全）'))
        setNavigation(titleParam)
      }
    }
    setRenderContent(getRenderContent(`setNavigation(${JSON.stringify(titleParam, null, 2)})`, '设置标题（全）'))
    setNavigation(titleParam)
  }
  return <div>
    <List>
      <Item onClick={() => setNavLeftButtonFn('imgUrl')}>设置左侧按钮样式、功能 - img</Item>
      <Item onClick={() => setNavLeftButtonFn('text')}>设置左侧按钮样式、功能 - 文案</Item>
      <Item onClick={() => setNavRightButtonFn('imgUrl')}>设置右侧按钮样式、功能 - img</Item>
      <Item onClick={() => setNavRightButtonFn('text')}>设置右侧按钮样式、功能 - 文案</Item>
      <Item onClick={setNavigationTitleFn}>设置标题</Item>
      <Item onClick={setNavigationFn}>设置标题（全）</Item>
      <Item onClick={hideNavigationFn}>隐藏导航栏</Item>
    </List>
    <br/>
    <ContentCard {...renderContent} />
  </div>
}
