import React, {useEffect, useState} from "react";
import {
  getAbHeader,
  getABNames,
  getCityInfo, getDeviceNotchInfo,
  getNetworkInfo,
  getShareChannel,
  getSystemInfo,
  getUserInfo
} from "../../../utils/bridge";
import {Accordion} from "antd-mobile";
import {obj2str} from "../../../utils/utils";
import {ContentCard, RenderItem} from "../../../components/ContentCard";

export const GetBaseInfo: React.FC = () => {
  const [userInfo, setUserInfo] = useState('')
  const [abHeader, setAbHeader] = useState('')
  const [abName, setAbName] = useState('')
  const [systemInfo, setSystemInfo] = useState('')
  const [networkInfo, setNetworkInfo] = useState('')
  const [cityName, setCityName] = useState('')
  const [shareList, setShareList] = useState('')
  const [deviceNotchInfo, setDeviceNotchInfo] = useState('');
  const [renderContent, setRenderContent] = useState<RenderItem[]>([])
  useEffect(() => {
    (async function () {
      setUserInfo(obj2str(await getUserInfo()))
      setAbHeader(obj2str(JSON.parse(await getAbHeader())));
      setAbName(obj2str(await getABNames()));
      setSystemInfo(obj2str(await getSystemInfo()))
      setCityName(obj2str(await getCityInfo()))
      setShareList(obj2str(await getShareChannel()))
      setNetworkInfo((await getNetworkInfo()))
      setDeviceNotchInfo(obj2str(await getDeviceNotchInfo()));
      console.log(deviceNotchInfo);
    })()
  }, [])

  useEffect(
    () => {
      setRenderContent([
        {
          title: 'getUserInfo',
          desc: '获取用户基本信息',
          transfer: 'await getUserInfo()',
          callbackType: 'object',
          content: userInfo
        },
        {
          title: 'getAbHeader',
          desc: '获取AB分组的Header',
          transfer: 'await getAbHeader()',
          callbackType: 'object[] 类型的 string',
          content: abHeader
        },
        {
          title: 'getABNames',
          desc: '获取AB实验名称数组',
          transfer: 'await getABNames()',
          callbackType: 'string[]',
          content: abName
        },
        {
          title: 'getSystemInfo',
          desc: '获取系统信息',
          transfer: 'await getSystemInfo()',
          callbackType: 'object',
          content: systemInfo
        },
        {
          title: 'getNetworkInfo',
          desc: '获取网络信息',
          transfer: 'await getNetworkInfo()',
          callbackType: 'string',
          content: networkInfo
        },
        {
          title: 'getCityInfo',
          desc: '获取城市信息',
          transfer: 'await getCityInfo()',
          callbackType: 'object',
          content: cityName
        },
        {
          title: 'getShareChannel',
          desc: '获取可分享渠道',
          transfer: 'await getShareChannel()',
          callbackType: 'string[]',
          content: shareList
        },
        {
          title: 'getDeviceNotchInfo',
          desc: '获取设备状态栏信息',
          transfer: 'await getDeviceNotchInfo()',
          callbackType: 'object',
          content: deviceNotchInfo
        },
      ])
    },
    [userInfo, abHeader, abName, systemInfo, networkInfo, cityName, shareList, deviceNotchInfo]
  )
  return <div>
    <Accordion defaultActiveKey="0" accordion>
      {renderContent.map(item => <Accordion.Panel key={item.title} header={`${item.title} - ${item.desc}`}>
        <ContentCard {...item}/>
      </Accordion.Panel>)}
    </Accordion>
  </div>
}
