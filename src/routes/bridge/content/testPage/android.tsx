import React, { useEffect, useState } from "react";
import { List } from "antd-mobile";
import { openWebview, closeWebview, setOnKeyBack } from '../../../../utils/bridge/index';
import s from '../../index.module.css';
const Item = List.Item;

const popupStyle = {
     color: 'white',
      padding: '30px',
      backgroundColor: '#fff',
      borderRadius: '20px'
}

const buttonStyle = {
    backgroundColor: '#fe7e1d',
    borderRadius: '16px',
    height: '50px',
    lineHeight: '50px',
    textAlign: "center" as "center",
    marginLeft: '5px',
    marginRight: '5px'
}
const AndroidBridge = () => {
    const [userId, setUserId] = useState('')
    const [userChatId, setUserChatId] = useState('')
    const [backStatus, setBackStatus] = useState(false)
   

    useEffect(() => {
         const root = document.querySelector('#root') as HTMLElement;
         root.style.backgroundColor="pink"
    }, [])
    // window.history.pushState(null, '', window.location.href);
    // window.addEventListener('popstate', function (event) {
    //     console.log('用户点击了返回');
    //     setBackStatus(true);
    // });

    const jumpNewPage = () => {
        openWebview('http://m.staging2.p1staff.com/commerce/annual/report?skipLoading=1&webviewColor=d39099')
    }

    const jumpUserProfile = () => {
        openWebview(`tantanapp://liveUserProfile?userId=${userId}`);
    }

    const jumpUserChat = () => {
        openWebview(`tantanapp://chat?uid=${userChatId}`);
    }

    
    const handleBackEvent = () => {
        setBackStatus(true);
    }

    setOnKeyBack({
        handler: handleBackEvent
    })

    return (
        <div style={{backgroundColor: 'transparent', position: 'fixed', top: '0', bottom: '0', left: '0', right: '0'}}>
            <List>
            <Item><input className={s.input_userid} placeholder="在这里输入userid" onChange={(e) => {setUserId(e.target.value)}}></input></Item>
                <Item >
                    <button onClick={jumpUserProfile}> 跳转个人资料页面</button>
                   
                </Item>
                <Item>
                    <input type="text" placeholder="请输入好友userid" onChange={(e) => { setUserChatId(e.target.value) }} />
                </Item>
                <Item>
                    <button onClick={jumpUserChat}> 跳转指定用户聊天页面</button>
                </Item>
                <Item>
                    <button onClick={jumpNewPage} style={{whiteSpace: 'pre-wrap'}}> openWebview 打开页面（去掉loading样式和背景色）</button>
                </Item>
            </List>
            <div className={backStatus ? s.popup_container : s.hidden}>
                <div style={popupStyle}>
                    <p style={{fontSize: '15px', textAlign: 'center', color: '#323232'}}>用户点击了安卓的物理返回键</p>
                    <div style={{fontSize: '16px',display: 'grid', gridTemplateColumns: '1fr 1fr', marginTop: '30px'}}>
                        <span style={buttonStyle} onClick={() => { setBackStatus(false) }}>返回页面</span>
                        <span style={buttonStyle} onClick={() => { closeWebview() }}>退出页面</span>
                    </div>
                </div>
            </div>
        </div>
    )
}

export default AndroidBridge;