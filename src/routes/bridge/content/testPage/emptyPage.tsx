import React, {useEffect} from "react";

export const EmptyPage: React.FC = () => {
  useEffect(() => {
    const rootEle = document.getElementById('root');
    if (rootEle && rootEle.style) {
      rootEle.style.backgroundColor = document.body.style.backgroundColor = document.documentElement.style.backgroundColor = 'transparent';
    }
    console.log(window.location.href);
  }, [])
  return <div>
    <h1>这是一个空的页面</h1>
    <h2>背景色透明</h2>
  </div>
}
