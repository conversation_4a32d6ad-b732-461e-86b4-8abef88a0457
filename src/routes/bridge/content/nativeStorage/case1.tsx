import React, {useEffect, useState} from "react";
import s from '../../index.module.css';
import {closeWebview, setNavigation} from "../../../../utils/bridge";

const imgUrl = 'https://auto.tancdn.com/v1/images/eyJpZCI6IkpLNkdHRlNMVjROTkozM043WFdIQU5JV0pHNzJaNjA3IiwidyI6MTQ0LCJoIjoxNDQsImQiOjAsIm10IjoiaW1hZ2UvanBlZyIsImRoIjo0NjMyODkwMjg2MTQ1OTk3ODU4fQ.png';
export const TitleCase1: React.FC = () => {
  const [code, setCode] = useState('')
  useEffect(() => {
    setNavigation({
      title: '[Android] 右侧设置文案不生效',
      leftImgUrl: imgUrl,
      rightText: '右侧文案'
    });
    setCode(`
  setNavigation({
    title: '[Android] 右侧设置文案不生效',
    leftImgUrl: imgUrl,
    rightText: '右侧文案'
  });
    `)
  })
  return <div className={s.contentCard}>
    <div className={s.errorInfo}>[Android] 左侧不支持设置文案</div>
    <div className={s.errorInfo}>[Android] 右侧设置文案不生效</div>
    <br/>
    <div onClick={closeWebview}>点击返回</div>
    <pre><code>
      {code}
    </code></pre>
  </div>
}
