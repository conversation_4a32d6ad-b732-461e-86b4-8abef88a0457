import React, {useEffect, useState} from "react";
import s from '../../index.module.css';
import {setNavigation, closeWebview} from "../../../../utils/bridge";
import {Button, Toast} from "antd-mobile";

export const TitleCase2: React.FC = () => {
  const [code, setCode] = useState('')
  const [skipBack, setSkipBack] = useState(false)
  useEffect(() => {
    setNavigation({
      title: '[Android] 设置左侧点击事件',
      skipBack,
      leftText: 'Back',
      leftHandler() {
        Toast.info('点击了返回按钮')
      }
    })
    setCode(`
  setNavigation({
    title: '[Android] 设置左侧点击事件',
    skipBack: ${skipBack},
    leftHandler() {
      Toast.info('点击了返回按钮')
    }
  });
  
  skipBack：是否跳过默认返回功能
    - false：默认返回上一页
    - true：阻止返回，停留当前页面
    `)
  })
  return <div className={s.contentCard}>
    <div className={s.errorInfo}>[Android] 设置左侧点击事件</div>
    <br/>
    <Button onClick={() => setSkipBack(!skipBack)}>切换 skipBack</Button>
    <br/>
    <div onClick={closeWebview}>点击返回</div>
    <pre><code>
      {code}
    </code></pre>
  </div>
}
