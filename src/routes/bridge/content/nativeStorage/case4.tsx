import React, {useEffect, useState} from "react";
import s from '../../index.module.css'
import {setNavigation} from "../../../../utils/bridge";
import {Toast} from "antd-mobile";

const imgUrl = 'https://auto.tancdn.com/v1/images/eyJpZCI6IlZVNTJSQktPTVdMVVBCV1JPQkdVQllVWkQ0SUVHNTA2IiwidyI6MTAyNCwiaCI6MTAyNCwiZCI6MCwibXQiOiJpbWFnZS9qcGVnIiwiZGgiOjEwODUwOTA3ODE2MzMzMzQ4Njg1fQ.png';
export const TitleCase4: React.FC = () => {
  const [code, setCode] = useState('')
  useEffect(() => {
    setNavigation({
      title: '[iOS] 设置leftImgUrl之后右侧会出一个默认按钮',
      leftImgUrl: imgUrl,
      handler: () => {
        Toast.info('点击导航拦', 2)
      },
      leftHandler: () => {
        Toast.info('点击左侧按钮', 2)
      },
      rightHandler: () => {
        Toast.info('点击右侧按钮', 2)
      }
    });
    setCode(`
  fnName: setNavigation
  调用方法
  setNavigation({
    title: '[iOS] 设置leftImgUrl之后右侧会出一个默认按钮',
    leftImgUrl: imgUrl,
    handler: () => {
      Toast.info('点击导航拦', 2)
    },
    leftHandler: () => {
      Toast.info('点击左侧按钮', 2)
    },
    rightHandler: () => {
      Toast.info('点击右侧按钮', 2)
    }
  });
  
  fnName: setNavigationTitle
  调用方法
  setNavigationTitle({
    title: '一周CP',
    successHandler: () => {
         Toast.info('successHandler', 1.5)
    }, // 设置成功的回调函数
    errorHandler: () => {
        Toast.info('errorHandler', 1.5)
    }, //设置失败的回调函数
    handler: () => {
          Toast.info('handler', 1.5)
    }
  })
    `)
  }, [])
  return <div className={s.contentCard}>
    <div className={s.errorInfo}>[iOS] - leftHandler 未生效</div>
    <div className={s.errorInfo}>[iOS] - handler 未生效</div>
    <div className={s.successInfo}>[iOS] - rightHandler 生效</div>
    <pre><code>
      {code}
    </code></pre>
  </div>
}
