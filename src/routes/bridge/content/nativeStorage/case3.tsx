import React, {useEffect, useState} from "react";
import s from '../../index.module.css'
import {setNavigation} from "../../../../utils/bridge";

const imgUrl = 'https://auto.tancdn.com/v1/images/eyJpZCI6IlZVNTJSQktPTVdMVVBCV1JPQkdVQllVWkQ0SUVHNTA2IiwidyI6MTAyNCwiaCI6MTAyNCwiZCI6MCwibXQiOiJpbWFnZS9qcGVnIiwiZGgiOjEwODUwOTA3ODE2MzMzMzQ4Njg1fQ.png';
export const TitleCase3: React.FC = () => {
  const [code, setCode] = useState('')
  useEffect(() => {
    setNavigation({
      title: '[iOS] 设置leftImgUrl之后右侧会出一个默认按钮',
      leftText: '文案'
    });
    setCode(`
  fnName: setNavigation
  调用方法
  setNavigation({
    title: '[iOS] 设置leftImgUrl之后右侧会出一个默认按钮',
    leftText: '文案'
  });
    `)
  }, [])
  return <div className={s.contentCard}>
    <div className={s.errorInfo}>[iOS] 设置leftImgUrl之后右侧会出一个默认按钮</div>
    <pre><code>
      {code}
    </code></pre>
  </div>
}
