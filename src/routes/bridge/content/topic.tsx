import React from "react";
import {jumpTopic} from "../../../utils/bridge";
import {List} from "antd-mobile";

const {Item} = List;
const jumpTopicFn = (topicType: "topic" | "vote" | "anonymous" | "link" | "qa") => {
  const topicID: any = {
    qa: '2453655376734717364',
    topic: '2453555795057445315'
  }
  jumpTopic(
    topicID[topicType] || '2453650937617581772',
    'tantan_topic_h5',
    'https://www.baidu.com',
    topicType,
    '测试H5')
}
export const TopicPage: React.FC = () => {
  return <div>
    <List renderHeader={() => '话题相关'}>
      <Item onClick={() => jumpTopicFn('topic')}>跳转话题 - topic</Item>
      <Item onClick={() => jumpTopicFn('vote')}>跳转话题 - vote</Item>
      <Item onClick={() => jumpTopicFn('link')}>跳转话题 - link</Item>
    </List>
  </div>
}
