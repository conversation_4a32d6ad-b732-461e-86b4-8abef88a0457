import React, {useEffect, useState} from "react";
import {openWebview} from "../../../utils/bridge";
import {List} from "antd-mobile";

const deeplinks: any = {
  "STVE": "tantanapp://studentVerify",
  "HOME": "tantanapp://home",
  "CONV": "tantanapp://conversations",
  "SECR": "tantanapp://secretcrush",
  "SETT": "tantanapp://setting",
  "PROF": "tantanapp://profile",
  "PRED": "tantanapp://profile/edit",
  "MOME": "tantanapp://moment",
  "VIVI": "tantanapp://vip/vip",
  "VISE": "tantanapp://vip/see",
  "VIBO": "tantanapp://vip/boost",
  "VIVB": "tantanapp://vip/vip/buy",
  "VISB": "tantanapp://vip/see/buy",
  "VIBB": "tantanapp://vip/boost/buy",
  "PLAC": "tantanapp://places",
  "SELI": "tantanapp://seeLikes",
  "PCBS": "tantanapp://push_call2buysee",
  "PCSN": "tantanapp://push_call2buysee_side_new",
  "SMSN": "tantanapp://see/msg_call2buysee_side_new",
  "MYTT": "tantanapp://mytantan",
  "CRSC": "tantanapp://creditscore",
  "CHAT": "tantanapp://chat",
  "SWAC": "tantanapp://switchaccount",
  "USCA": "tantanapp://userCard",
  "SULA": "tantanapp://superlikeable",
  "VECE": "tantanapp://verificationcenter",
  "quickchat": "tantanapp://conversations/quickchat",
  "动态": "tantanapp://moment",
  "发布页面": "tantanapp://newMoment",
  "划卡激励弹窗-1": "tantanapp://swipeStimulationAlert?type=roaming",
  "划卡激励弹窗-2": "tantanapp://swipeStimulationAlert?type=advancedSearch",
  "划卡激励弹窗-3": "tantanapp://swipeStimulationAlert?type=superlike",
  "划卡激励弹窗-4": "tantanapp://swipeStimulationAlert?type=boost",
  "划卡激励弹窗-5": "tantanapp://swipeStimulationAlert?type=undo",
  "闪聊": "tantanapp://flashChat",
  "个性签名": "tantan://profile/edit?action=signature",
  // 好友profile页面  tantanapp://liveUserProfile?userId=
  // 跳转聊天详情页面  tantanapp://chat?uid=
}

export const WebviewToDeeplink: React.FC = () => {
  const [deeplinkArr, setArr] = useState<any>([])
  useEffect(() => {
    setArr(Object.keys(deeplinks).map(key => deeplinks[key]))
  }, [])
  return <List renderHeader={() => 'toDeeplink'}>
    {deeplinkArr.map((link: string) => <List.Item key={link} onClick={() => openWebview(link)}>{link}</List.Item>)}
  </List>
}
