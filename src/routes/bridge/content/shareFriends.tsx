import React, {useEffect, useState} from "react";
import {shareFriends, triggerAction} from "../../../utils/bridge";
import {List} from "antd-mobile";

export const ShareFriends: React.FC = () => {
  const [sendType, setSendType] = useState('sendMsg')
  const [userList, setUserList] = useState([''])

  const shareFriendsFn = (messageData: any) => {
    shareFriends({
        type: sendType,
        friendLimit: 5,
        title: '选择好友',
        subTitle: '每次最多选择5位好友',
        btnText: '分享',
        limitText: '最多可以选择5位好友',
        messageData,
        callback(status: string, listStr: string[]) {
          console.log(status, listStr);
          setUserList(listStr)
        }
      }
    )
  }

  const toggleType = () => {
    sendType === 'sendMsg' ? setSendType('selectFriends') : setSendType('sendMsg');
  }

  const shareFriendsFnText = () => {
    shareFriendsFn({
      msgType: 'text',
      value: '发送的信息内容'
    });
  }

  const shareFriendsFnPicText = () => {
    const isOnline = window.location.origin.includes('tantanapp');
    const h5Url = `${isOnline ? 'https://m.tantanapp.com' : 'http://m.staging2.p1staff.com'}/static-pages/christmas${isOnline ? '.html' : '/'}?from=banner`;
    const schemaStr = `tantan://webview?url=${encodeURI(h5Url)}`;
    let str = JSON.stringify({
      view: {
        image: 'https://auto.tancdn.com/v1/images/eyJpZCI6IkdFT1IzR05UNEFLU0JXNU5RS0pFTUI0VUpRVEU3TjA5IiwidyI6MTM1NiwiaCI6NDU2LCJkIjowLCJtdCI6ImltYWdlL2pwZWciLCJkaCI6MTEwODEzMDYzNDI2NjIzNTgwNTV9.png',
        imageSize: '678x228',
        text: '送你一个圣诞礼物，快去拆开看看吧！',
        button: '含1点惊喜值，点击查看',
        isTextOnly: false
      },
      schema: schemaStr
    }).replace(/"/g, '\\\"')
    console.log(str);
    shareFriendsFn({
      msgType: 'picture_text_style',
      value: '发送的信息内容',
      media: [],
      msgData: encodeURI(str)
    });
  }

  const shareFriendsFnImg = () => {
    shareFriendsFn({
      msgType: 'picture',
      media: [{
        "name": "zqy",
        "url": "https://auto.tancdn.com/v1/images/eyJpZCI6Ik9KWkIyNkJZT1lTNUpGSEFMQ0pQN0dGRERBN1JQVDA5IiwidyI6MzI0LCJoIjozMDMsImQiOjAsIm10IjoiaW1hZ2UvanBlZyIsImRoIjoxNTkwMzc1NDY1MTkwNTU5MDEwfQ",
        "size": [
          324,
          303
        ],
        "duration": 0,
        "mediaType": "image/jpeg"
      }]
    });
  }

  const shareVideo = () => {
    shareFriendsFn({
      msgType: 'video',
      media: [{
        "name": "zqy",
        "url": "https://auto.tancdn.com/v1/images/eyJpZCI6Ik9KWkIyNkJZT1lTNUpGSEFMQ0pQN0dGRERBN1JQVDA5IiwidyI6MzI0LCJoIjozMDMsImQiOjAsIm10IjoiaW1hZ2UvanBlZyIsImRoIjoxNTkwMzc1NDY1MTkwNTU5MDEwfQ",
        "size": [
          324,
          303
        ],
        "duration": 0,
        "mediaType": "image/jpeg"
      }]
    });

  }
  useEffect(() => {
    // setArr(Object.keys(deeplinks).map(key => deeplinks[key]))
  }, [])
  return <>
    <List renderHeader={() => '打开分享好友面板'}>
      <List.Item onClick={() => toggleType()}>切换分享类型 {sendType}</List.Item>
      <List.Item onClick={() => shareFriendsFnText()}>分享文案</List.Item>
      <List.Item onClick={() => shareFriendsFnPicText()}>分享卡片</List.Item>
      <List.Item>{userList}</List.Item>
    </List>
  </>
}
