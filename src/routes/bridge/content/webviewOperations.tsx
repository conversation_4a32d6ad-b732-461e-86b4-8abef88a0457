import React from "react";
import {closeWebview, jumpWebview, openWebview} from "../../../utils/bridge";
import {List} from "antd-mobile";

const Item = List.Item;
const Brief = Item.Brief;
const url = `${window.location.origin}${window.location.origin.includes('10') ? '' : '/commerce'}/bridge/empty`;

export const WebviewOperations: React.FC = () => {

  const skipLoadingUrl = `${url}?skipLoading=1&hideNavigationBar=1`;
  const webviewColorUrl = `${url}?webviewColor=ff0000&hideNavigationBar=1&skipLoading=1`;
  const notchColorUrl = `${url}?notchColor=ff0000&hideNavigationBar=1`;
  const hideNotchUrl = `${url}?hideNotch=1&hideNavigationBar=1`;
  return <div>
    <List renderHeader={() => '对于新开Webview的预设配置'}>
      <Item
        className="curTest"
        arrow="horizontal"
        onClick={() => openWebview(skipLoadingUrl)}>
        skipLoading
        <Brief>openWebview(url?skipLoading=1)</Brief>
        <Brief>跳过原生Loading打开新的webview</Brief>
      </Item>
      <Item
        className="curTest"
        arrow="horizontal"
        onClick={() => openWebview(webviewColorUrl)}>
        webviewColor
        <Brief>openWebview(url?webviewColor=ff0000)</Brief>
        <Brief>设置默认打开背景色</Brief>
      </Item>
      <Item
        className="curTest"
        arrow="horizontal"
        onClick={() => openWebview(notchColorUrl)}>
        notchColor
        <Brief>openWebview(url?notchColor=ff0000)</Brief>
        <Brief>设置状态栏颜色</Brief>
      </Item>
      <Item
        className="curTest"
        arrow="horizontal"
        onClick={() => openWebview(hideNotchUrl)}>
        hideNotch
        <Brief>openWebview(url?hideNotch=1)</Brief>
        <Brief>隐藏状态栏</Brief>
      </Item>
    </List>
    <List renderHeader={() => '对于Webview的操作（打开、关闭、跳转）'}>
      <Item
        arrow="horizontal"
        onClick={() => openWebview(url, 'openWebview的第二个参数')}>
        openWebview
        <Brief>openWebview(url, 'openWebview的第二个参数')</Brief>
        <Brief>会打开新页面，返回会到当前页面</Brief>
      </Item>
      <Item
        arrow="horizontal"
        onClick={() => jumpWebview(url, 'jumpWebview的第二个参数')}>
        jumpWebview
        <Brief>jumpWebview(url, 'jumpWebview的第二个参数')</Brief>
        <Brief>会打开新页面，返回直接关闭当前页面</Brief>
      </Item>
      <Item
        arrow="horizontal"
        onClick={() => closeWebview()}>
        closeWebview
        <Brief>closeWebview()</Brief>
        <Brief>关闭当前页面</Brief>
      </Item>
    </List>
  </div>
}
