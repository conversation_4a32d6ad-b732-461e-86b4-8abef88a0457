import React, {useEffect, useState} from "react";
import s from '../index.module.css'
import {getShareChannel, nativeShare, share, shareImage} from "../../../utils/bridge";
import {ActionSheet, List} from "antd-mobile";
import {isIOS} from "../../../utils/config";

type channelType = 'mo' | 'wx' | 'wb' | 'qq' | 'qz'
const shareList: { channel: channelType, title: string }[] = [
  {
    channel: 'mo',
    title: '分享至微信朋友圈'
  },
  {
    channel: 'wx',
    title: '分享至微信聊天'
  },
  {
    channel: 'wb',
    title: '分享至微博'
  },
  {
    channel: 'qq',
    title: '分享至QQ聊天'
  },
  {
    channel: 'qz',
    title: '分享至QQ空间'
  },
]

const imgParams = {
  url: '',
  title: 'bridge分享测试',
  imgUrl: 'https://auto.tancdn.com/v1/images/eyJpZCI6IlZVNTJSQktPTVdMVVBCV1JPQkdVQllVWkQ0SUVHNTA2IiwidyI6MTAyNCwiaCI6MTAyNCwiZCI6MCwibXQiOiJpbWFnZS9qcGVnIiwiZGgiOjEwODUwOTA3ODE2MzMzMzQ4Njg1fQ.png',
  description: '233333',
}

const params = {
  url: 'https://zhuanlan.zhihu.com/p/95085796',
  title: 'bridge分享测试',
  imgUrl: 'https://auto.tancdn.com/v1/images/eyJpZCI6IlZVNTJSQktPTVdMVVBCV1JPQkdVQllVWkQ0SUVHNTA2IiwidyI6MTAyNCwiaCI6MTAyNCwiZCI6MCwibXQiOiJpbWFnZS9qcGVnIiwiZGgiOjEwODUwOTA3ODE2MzMzMzQ4Njg1fQ.png',
  description: '233333',
}

export const Share: React.FC = () => {
  const [code, setCode] = useState('')
  const nativeBridgeShare = () => {
    nativeShare({
      url: 'https://zhuanlan.zhihu.com/p/95085796',
      title: 'bridge分享测试',
      imgUrl: 'https://auto.tancdn.com/v1/images/eyJpZCI6IlZVNTJSQktPTVdMVVBCV1JPQkdVQllVWkQ0SUVHNTA2IiwidyI6MTAyNCwiaCI6MTAyNCwiZCI6MCwibXQiOiJpbWFnZS9qcGVnIiwiZGgiOjEwODUwOTA3ODE2MzMzMzQ4Njg1fQ.png',
      description: 'bridge分享测试description',
      modalTitle: '模块标题',
      channels: '',
    })
    setCode(`
  fnName: nativeShare;
  调用参数：
  nativeShare({
    url: 'http://www.baidu.com',
    title: 'bridge分享测试',
    imgUrl: 'https://auto.tancdn.com/v1/images/eyJpZCI6IlZVNTJSQktPTVdMVVBCV1JPQkdVQllVWkQ0SUVHNTA2IiwidyI6MTAyNCwiaCI6MTAyNCwiZCI6MCwibXQiOiJpbWFnZS9qcGVnIiwiZGgiOjEwODUwOTA3ODE2MzMzMzQ4Njg1fQ.png',
    description: 'bridge分享测试description',
    modalTitle: '模块标题',
    channels: '',
  })
    `)
  }
  let [channels, setChannels] = useState(['取消']);
  useEffect(() => {
    (async () => {
      setChannels([...(await getShareChannel()), '取消'])
    })()
  }, [])
  const shareFn = async () => {
    ActionSheet.showActionSheetWithOptions(
      {
        options: channels,
        cancelButtonIndex: channels.length - 1,
        message: '分享渠道',
        maskClosable: true,
      },
      (index => {
        if (channels[index] === '取消') return setCode('');
        const shareParams = {
          ...params,
          channel: channels[index] as any,
        }
        setCode(`fnName: share;
调用参数：
share(${JSON.stringify(shareParams, null, 2)})`)
        share(shareParams).then(
          undefined,
          // err => showToast({context: err || 'err'})
        )
      })
    )
    console.log(channels);
  }

  const shareImgFn = async () => {
    ActionSheet.showActionSheetWithOptions(
      {
        options: channels,
        cancelButtonIndex: channels.length - 1,
        message: '分享渠道',
        maskClosable: true,
      },
      (index => {
        if (channels[index] === '取消') return setCode('');
        const shareParams = {
          ...imgParams,
          channel: channels[index] as any,
        }
        setCode(`fnName: share;
调用参数：
share(${JSON.stringify(shareParams, null, 2)})`)
        share(shareParams).then(
          undefined,
          // err => showToast({context: err || 'err'})
        )
      })
    )
    console.log(channels);
  }

  const shareImageFn = async () => {
    ActionSheet.showActionSheetWithOptions(
      {
        options: channels,
        cancelButtonIndex: channels.length - 1,
        message: '分享渠道',
        maskClosable: true,
      },
      (index => {
        if (channels[index] === '取消') return setCode('');
        const shareParams = {
          pic: params.imgUrl,
          platform: channels[index] as any
        };
        setCode(`fnName: shareImage;
${isIOS && '// ios 微博复用share逻辑'}
调用参数：
shareImage(${JSON.stringify(shareParams, null, 2)})`)
        shareImage(shareParams).then(
          undefined,
          // err => showToast({context: err || 'err'})
        )
      })
    )
    console.log(channels);
  }

  return <div className={s.contentCard}>
    <List>
      <List.Item onClick={() => nativeBridgeShare()}>
        调用原生分享
      </List.Item>
      <List.Item className="curTest" onClick={() => shareFn()}>
        分享URL到端外 - share
      </List.Item>
      <List.Item onClick={() => shareImgFn()}>
        分享图片到端外 - share
      </List.Item>
      <List.Item className="curTest" onClick={() => shareImageFn()}>
        分享图片到端外 - shareImg
      </List.Item>
    </List>
    <pre><code>
      {code}
    </code></pre>
  </div>
}
