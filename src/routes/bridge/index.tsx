import React, {useEffect, useState} from "react";
import s from './index.module.css';
import {
  bindAlipay,
  bindZhimaAuth, changeNotchBackgroundColor, changeWebviewBackgroundColor, disableBounce,
  hideNavigation, hideNotch, imageSave, openWebview, saveBase64ImageData, setNavigationTitle,
  setWebviewPageID, showNotch, showToast, triggerAction
} from "../../utils/bridge";
import {List, Modal, Toast} from "antd-mobile";

const Item = List.Item;
// const Brief = Item.Brief;
let num = 0;

const prompt = Modal.prompt;

interface LinkItem {
  link: string;
  title: string;
  className?: string;
  openWebview?: boolean;
}

const links: LinkItem[] = [
  {
    link: '/bridge/getUserInfo',
    title: '获取基本信息'
  },
  {
    link: '/bridge/share',
    title: 'share - 分享'
  },
  {
    link: '/bridge/webview',
    openWebview: true,
    title: 'webview 操作相关'
  },
  {
    link: '/bridge/before/close',
    title: '埋点平台校验关闭webview PV、PD打点是否正确',
    openWebview: true
  },
  {
    link: '/bridge/nativeTitle',
    title: '设置 「标题栏」 相关',
    openWebview: true
  },
  {
    link: '/bridge/topic',
    title: '跳转 「话题」 相关',
    openWebview: true
  },
  {
    link: '/bridge/todeeplink',
    title: '跳转 「deeplink」 - 【不全】',
    openWebview: true,
    className: 'curTest',
  },
  {
    link: '/bridge/shareFriends',
    title: '跳转分享好友面板',
    openWebview: true,
    className: 'curTest',
  },
]

let timer: any;

export const BridgeTest = (props: any) => {
  const [time, setTime] = useState(0);
  const isDev = window.location.origin.includes('10')

  const [userId, setUserId] = useState('22997');
  const [userChatId, setUserChatId] = useState('22997');
  useEffect(() => {
    num = 0;
    if (timer) clearInterval(timer)
    else timer = setInterval(() => setTime(++num), 1000);
    (async () => {
      await setNavigationTitle({title: 'JSBridge测试'});
      await setWebviewPageID({pageID: 'h5-bridge-test-start'})
    })()
  }, [])

  // 保存图片
  const imageSaveFn = () => {
    imageSave({
      url: 'https://auto.tancdn.com/v1/images/eyJpZCI6IlZVNTJSQktPTVdMVVBCV1JPQkdVQllVWkQ0SUVHNTA2IiwidyI6MTAyNCwiaCI6MTAyNCwiZCI6MCwibXQiOiJpbWFnZS9qcGVnIiwiZGgiOjEwODUwOTA3ODE2MzMzMzQ4Njg1fQ.png'
    }).then(
      () => showToast({context: '保存成功'}),
      err => showToast({context: err})
    )
  }

  const saveBase64ImageDataFn = () => {
    saveBase64ImageData({
      data: '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'
    }).then(
      () => showToast({context: '保存成功'}),
      err => showToast({context: err})
    )
  }

  const bindZhimaAuthFn = () => {
    bindZhimaAuth({
      name: '',
      id: '',
      successZhima() {
        showToast({
          context: 'successZhima',
          duration: 1
        });
      },
      failZhima() {
        showToast({
          context: 'failZhima',
          duration: 1
        });
      }
    })
  }

  const bindAlipayFn = () => {
    bindAlipay({
      alipayFlag: true,
      successAlipay() {
        showToast({
          context: '绑定支付宝成功',
          duration: 1
        });
      },
      failAlipay() {
        showToast({
          context: '绑定支付宝失败',
          duration: 1
        });
      }
    });
  }

  const showToastFn = () => {
    showToast({
      context: 'Toast test',
      duration: 1
    });
  }

  const changeNotchBackgroundColorFn = () => {
    changeNotchBackgroundColor('#ff0000');
  }

  const changeWebviewBackgroundColorFn = () => {
    changeWebviewBackgroundColor('#ff0000');
  }

  const hideNotchFn = () => {
    hideNotch();
  }

  const hideNavigationFn = () => {
    hideNavigation();
  }

  const showNotchFn = () => {
    showNotch();
  }

  const jumpDefaultBrowser = () => {
    triggerAction({
      actionType: 'jumpBrowser',
      restParams: {
        url: window.location.href
      }
    })
  }

  const toLink = (info: LinkItem) => {
    if (info.openWebview) {
      const url = `${window.location.origin}${isDev ? '' : '/commerce'}${info.link}`;
      console.log(url);
      return openWebview(url, info.title);
    }
    props.history.push(info.link);
  }

  const showUserAgent = () => {
    showToast({
      context: navigator.userAgent.toLowerCase()
    })
  }
  return <div className={s.container}>
    <List renderHeader={() => '页面基本信息'}>
      <Item wrap style={{wordBreak: 'break-all'}}>
        当前页面路径：<br/>{window.location.href}
      </Item>
      <Item wrap style={{wordBreak: 'break-all'}}>
        当前页面的pageId：h5-bridge-test-start
      </Item>
      <Item>页面计时器：{time}</Item>
      <Item onClick={showUserAgent}>showUserAgent</Item>
    </List>
    <br/>
    <List renderHeader={() => '运营活动新增bridge'}>
      <Item  arrow="horizontal" onClick={() => {toLink({link: '/bridge/android', title: '运营活动相关'})}}>运营活动相关</Item>
    </List>
    <List renderHeader={() => '修改当前webview样式'}>
      <Item onClick={changeNotchBackgroundColorFn}>设置状态栏背景色 - #ff0000</Item>
      <Item onClick={changeWebviewBackgroundColorFn}>设置webview背景色 - #ff0000</Item>
      <Item onClick={hideNavigationFn}>隐藏 <strong>「标题栏」</strong></Item>
      <Item onClick={hideNotchFn}>隐藏 <strong>「状态栏」</strong></Item>
      <Item onClick={showNotchFn}>显示 <strong>「状态栏」</strong></Item>
      <Item onClick={disableBounce}>IOS 禁止页面回弹</Item>
    </List>
    <br/>
    <List renderHeader={() => '直接跳转Bridge功能'}>
      {links.map(link => <Item
        className={link.className}
        key={link.link}
        arrow="horizontal"
        onClick={() => toLink(link)}>
        {link.title}
      </Item>)}
    </List>
    <br/>
    <List renderHeader={() => '调用Bridge'}>
      <Item onClick={imageSaveFn}>imageSave</Item>
      <Item onClick={saveBase64ImageDataFn}>saveBase64ImageData</Item>
      <Item onClick={bindZhimaAuthFn}>芝麻认证</Item>
      <Item onClick={bindAlipayFn}>绑定支付宝</Item>
      <Item onClick={showToastFn}>showToast</Item>
      <Item onClick={jumpDefaultBrowser}>跳转手机默认浏览器</Item>
    </List>
  </div>
}
