import React from "react";
import {RouteComponentProps} from "react-router-dom";
import {urlParams} from "../../utils";
import KolMain from "../../components/kolMain/KolMain";
import {isAndroid, isWeixinBrowser} from "../../utils/bridge/utils";

const Growth: React.FC<RouteComponentProps> = (props, context) => {
  const token = urlParams(props.history.location.search.slice(1)).token;
  const agentNum = 'self-01';
  const downloadUrl = urlParams(props.history.location.search.slice(1))
    .downloadUrl;
  const pathname = props.location.pathname;
  const search = props.location.search;
  const isWeixin = isWeixinBrowser();

  return (
    <>
      {isWeixin && isAndroid ? <div style={{
        position: 'fixed',
        top: 0,
        right: 0,
        bottom: 0,
        left: 0,
        backgroundColor: 'rgba(0, 0, 0, 0.7)',
        zIndex: 999999999
      }}>
        <img style={{width: '60%', position: 'absolute', right: '20px'}}
             src="https://auto.tancdn.com/v1/images/eyJpZCI6IlRDTk41RVpZRktDUUlNNURERUpHWFFKV1JUM0JCSjA3IiwidyI6NzY4LCJoIjo0MjAsImQiOjAsIm10IjoiaW1hZ2UvanBlZyIsImRoIjo3Mzk2MTAyNjQ2Mjk5NDE5MjY0fQ.png"
             alt="引导"/>
      </div> : ''}
      <KolMain
        pathname={pathname}
        search={search}
        token={token}
        agentNum = {agentNum}
        downloadUrl={downloadUrl}
        btnCenter={context === 'growth2'}
        isAffiliate = {context === 'growth2'}
      />
    </>
  );
};
export default Growth;
