import React from "react";
import {RouteComponentProps} from "react-router-dom";
import {urlParams} from "../../utils";
import KolMain from "../../components/kolMain/KolMain";
import {isAndroid, isWeixinBrowser} from "../../utils/bridge/utils";

const ShareLight: React.FC<RouteComponentProps> = (props) => {
  const token = urlParams(props.history.location.search.slice(1)).token;
  const pathname = props.location.pathname;
  const search = props.location.search;
  const isWeixin = isWeixinBrowser();
  const iconUrl = isAndroid ? "https://auto.tancdn.com/v1/images/eyJpZCI6IkpMQjZaREc3N1lXTUtSRzJTR0VNWDJSWVdJTUszUzA2IiwidyI6MTU2LCJoIjoxNTYsImQiOjAsIm10IjoiaW1hZ2UvanBlZyIsImRoIjoxMDgyMDkzMDE4ODI4OTA2ODM2NX0.png" : "";
  const btnUrl = isAndroid ? "https://auto.tancdn.com/v1/images/eyJpZCI6IkFUWVZUNU9aNE9MSVZLTDZIWkRHUEhXS1BOWFNGUzA2IiwidyI6NzU2LCJoIjoyMDQsImQiOjAsIm10IjoiaW1hZ2UvanBlZyIsImRoIjo3ODA4NTE3NzgwNDU2ODI4MjUyfQ.png" : "";
  return (
    <>
      {isWeixin && isAndroid ? <div style={{
        position: 'fixed',
        top: 0,
        right: 0,
        bottom: 0,
        left: 0,
        backgroundColor: 'rgba(0, 0, 0, 0.7)',
        zIndex: 999999
      }}>
        <img style={{width: '60%', position: 'absolute', right: '20px'}}
             src="https://auto.tancdn.com/v1/images/eyJpZCI6IlRDTk41RVpZRktDUUlNNURERUpHWFFKV1JUM0JCSjA3IiwidyI6NzY4LCJoIjo0MjAsImQiOjAsIm10IjoiaW1hZ2UvanBlZyIsImRoIjo3Mzk2MTAyNjQ2Mjk5NDE5MjY0fQ.png"
             alt="引导"/>
      </div> : ''}
      <KolMain
        pathname={pathname}
        search={search}
        token={token}
        iconUrl={iconUrl}
        btnUrl={btnUrl}
        downloadUrl="https://apk-light.tancdn.com/light-redpocketshare.apk"
        isShareLight={!!isAndroid}
      />
    </>
  );
};
export default ShareLight;
