import React, { useEffect, useState } from 'react';
import { RouteComponentProps } from "react-router-dom";
import s from './DiamondCard.module.css';
import { imageList } from './utils';
import QRCode from 'qrcode.react';
import { urlParams } from '../../utils/index' 
import { Toast } from '../../components/antd/Antd';
import { getUserInfo, setNavigationTitle } from '../../utils/bridge';
import { ajax } from './ajax';


interface CodeParams {
    type: string,
    code: string
}

const DiamondCard: React.FC<RouteComponentProps> = (props) => {
    const [params, setParams] = useState<CodeParams>({} as CodeParams);
    const [userName, setUserName] = useState('');
    const envUrl = (window.location.origin.includes('tantanapp.com') ?'https://m.tantanapp.com' : 'http://m.staging2.p1staff.com');

    useEffect(() => {
        (async() => {
            Toast.info('可直接截图保存至相册', 2)
            const params = urlParams(props.location.search.slice(1));
            setParams(params)
            console.log(params);
            const { userName } = await getUserInfo();
            setUserName(userName)
            setNavigationTitle({
                title: '黑钻会员 诚挚邀请'
            })
        })()
    }, [])

    return (
        <div className={s.container}>
            <div className={s.content}>
                <img className={s.starryimg_one} src="https://auto.tancdn.com/v1/images/eyJpZCI6IlZDTTQ1UFhYNkNJNFJLQVNOTjU0S1hFWVpCTDdMUzA3IiwidyI6MTEyNSwiaCI6OTE4LCJkIjowLCJtdCI6ImltYWdlL2pwZWciLCJkaCI6MTY2MDk1ODU0MjI0MzJ9.png" alt="" />
                <img className={s.starryimg_two} src="https://auto.tancdn.com/v1/images/eyJpZCI6IkNHTTRXQVNMQVM0Mk5XVUhOUE1SNEk3T1IzRTZESTA2IiwidyI6MTEyNSwiaCI6NzE0LCJkIjowLCJtdCI6ImltYWdlL2pwZWciLCJkaCI6NDk0MDk5NTM5MzI5MTUyfQ.png" alt="" />
                <div className={s.contentbody}>
                    <div className={s.toptitle}>
                        <img className={s.toptitle_left} src="https://auto.tancdn.com/v1/images/eyJpZCI6IkZUWlNMTUhLTkhKU1VZNkxRRVRDSFI2WVVJTlVJMjA2IiwidyI6MTE0LCJoIjoxMTQsImQiOjAsIm10IjoiaW1hZ2UvanBlZyIsImRoIjoxNzA3NTEyMjAzMjY2MDc5MzE3fQ.png" alt="" />
                        <div className={s.toptitle_right}>
                            <img src="https://auto.tancdn.com/v1/images/eyJpZCI6IlAyV1E2RUE2Rkk0TkgzVTM0S0RMSlpNS1hFUFUyTTA2IiwidyI6MTUwLCJoIjozNiwiZCI6MCwibXQiOiJpbWFnZS9qcGVnIiwiZGgiOjE0NTEzMzE0ODMyNjYxNDQwODV9.png" alt="" />
                            <span>极致社交体验</span>
                        </div>
                    </div>
                    <div className={s.inviteimg}>
                        <img src="https://auto.tancdn.com/v1/images/eyJpZCI6IlVIMlQyVVZCVEdaUkJNTkI2SEoyS1VMUEU2TTZERDA3IiwidyI6NTc5LCJoIjozMjEsImQiOjAsIm10IjoiaW1hZ2UvanBlZyIsImRoIjo3MDIxNzc3OTI2MTc4MTY4MTQxfQ.png" alt="" />
                    </div>
                    {/* <p className={s.divideline_one}></p> */}
                    <div className={s.divide_container_one}>
                        <p className={s.divideline_one}></p>
                        </div>
                    <div className={s.privilege_writing}>
                        <span className={s.privilege_shape_small}></span>
                        <span className={s.privilege_shape}></span>
                              8项黑钻会员专属特权 等待优质的您解锁
                                <span className={`${s.privilege_shape} ${s.privilege_shape_right}`}></span>
                        <span className={s.privilege_shape_small}></span>
                    </div>
                    <ul className={s.ullist}>
                        {imageList.map(item => (
                            <li className={s.lilist}>
                                <img className={s.li_img} src={item.url} alt="" />
                                <p className={s.li_text}>{item.text}</p>
                            </li>
                        ))}
                    </ul>
                    <div className={s.divide_container}>
                        <p className={s.divideline_two}></p>
                        </div>
                    <div className={s.price_peryear}>
                        <img src="https://auto.tancdn.com/v1/images/eyJpZCI6IktLQ0RQUlNVRkU1WFJTUUoyTzVXRzJQQTNGWFhNTTA2IiwidyI6NjMwLCJoIjoxMjAsImQiOjAsIm10IjoiaW1hZ2UvanBlZyIsImRoIjozMDE0MzI1NzcwMzgzODc4NzQwfQ.png" alt="" />
                    </div>
                    <p className={s.origin_price_peryear}>原价 ¥49999/年</p>
                    <img className={s.pink_ribbon} src="https://auto.tancdn.com/v1/images/eyJpZCI6IktFUTVLN0ZCNUpQUlVZWjNDQUY2QzdMNk9EWDRZNDA2IiwidyI6NDI5LCJoIjo2MzksImQiOjAsIm10IjoiaW1hZ2UvanBlZyIsImRoIjoxODAxNTAzMjA0OTUwMTQ3MjAwfQ.png" alt="" />
                    <img className={s.square_one} src="https://auto.tancdn.com/v1/images/eyJpZCI6Ik5ZQ1pERVY1NElDRTRRNkhFVldDQ0EzSTNJVzNLNTA3IiwidyI6MTE3LCJoIjoxNTMsImQiOjAsIm10IjoiaW1hZ2UvanBlZyIsImRoIjoxOTk2MjIwNDM5MDc1ODcyfQ.png" alt="" />
                    <img className={s.square_two} src="https://auto.tancdn.com/v1/images/eyJpZCI6IkRQTVJTV0tYWTNaS0JRWFRIVzIzNllDRzJDNUhDVjA2IiwidyI6MTIwLCJoIjoxMDgsImQiOjAsIm10IjoiaW1hZ2UvanBlZyIsImRoIjoxNDg2OTI1MDQ3NTUzMDk4OTh9.png" alt="" />
                </div>
            </div>
            <div className={s.bottom}>
                <div className={s.introduce}>
                    <p className={s.intro_p1}>{userName}</p>
                    <p>邀您成为 <span className={s.focustext}>黑钻会员</span></p>
                     <p>{params.type==='trial' ? '并为您抢到稀缺' : '并提供好友'}<span className={s.focustext}>{params.type==='trial' ? '试用' : '专享'}</span> {params.type==='trial' ? '名额' : '价格'}</p>
                </div>
                <QRCode
                    className={s.qrcode}
                    value ={`${envUrl}/commerce/diamond/enter/pay?type=${params.type}&code=${params.code}&fromuser=${userName}`}
                    // size={} // 二维码的大小
                    fgColor="#000000" // 二维码的颜色
                />
            </div>
        </div>
    )
}

export default DiamondCard;