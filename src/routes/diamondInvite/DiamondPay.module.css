.container {
    position: relative;
    background-color: #251d15;
    padding: 0 0.22rem;
    overflow: hidden;
}

.guideimg {
    position: fixed;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    background: rgba(0, 0, 0, 0.7);
    z-index: 999999;
    display: flex;
    align-items: center;
    flex-direction: column;
}

.guideimg>img {
    width: 0.62rem;
    height: 0.89rem;
    position: fixed;
    object-fit: contain;
    top: 0.13rem;
    right: 0.27rem;
}

.guideimg>button {
    width: 1.28rem;
    height: 0.35rem;
    border-radius: 0.22rem;
    line-height: 0.35rem;
    border: solid 0.01rem #ffffff;
    position: fixed;
    top: 1.7rem;
    background-color: #000000;
    color: #ffffff;
}

.guildtext {
    position: fixed;
    top: 0.65rem;
}

.guildtext p {
    line-height: 0.24rem;
    font-size: 0.17rem;
    font-weight: 500;
    color: #ffffff;
}

.starryimg {
    position: absolute;
    width: calc(100% + 0.44rem);
    height: 3.06rem;
    object-fit: cover;
    top: 0;
    left: -0.22rem;
}

.squareimg_one {
    position: absolute;
    z-index: 99;
    width: 0.63rem;
    height: 0.64rem;
    top: 0.9rem;
    object-fit: contain;
    left: 0;
}

.squareimg_two {
    position: absolute;
    width: 0.53rem;
    height: 0.72rem;
    object-fit: contain;
    opacity: 0.8;
    top: 1.5rem;
    right: 0;
    z-index: 99;
}

.backgroundimg {
    position: absolute;
    top: 0;
    left: 0;
    height: 3.23rem;
    object-fit: contain;
}

.top {
    position: relative;
    padding: 0.42rem 0.33rem 0.28rem 0.29rem;
}

.top_pay {
    padding: 0.45rem 0.34rem 0.34rem 0.30rem;
    display: flex;
    flex-direction: column;
    align-items: center;
}

.vipimg {
    width: 2.69rem;
    height: 0.7rem;
    object-fit: contain;
    margin-bottom: 0.15rem;
}

.top_pay .vipimg {
    width: 2.67rem;
    margin-bottom: 0.19rem;
    object-fit: cover;
}

.viptextimg {
    width: 2.70rem;
    object-fit: contain;
    background-size: 100% 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    box-sizing: border-box;
}

.viptextimg>img {
    width: 100%;
    height: 100%;
}

.top_pay .viptextimg {
    background: url('https://auto.tancdn.com/v1/images/eyJpZCI6IkpRUElSNlQySFlURzZRU1pCQjVGU05ZV1NXQkZMTzA2IiwidyI6ODEwLCJoIjoxNjIsImQiOjAsIm10IjoiaW1hZ2UvanBlZyIsImRoIjoxMTgyMzA5OTIwNTg3MDAzNjYxMn0.png');
    width: 2.44rem;
    height: 0.45rem;
    background-size: 100% 100%;
    padding: 0.08rem 0.18rem 0;
}

.top_pay .viptextimg>img {
    width: 2.08rem;
    height: 0.25rem;
    object-fit: contain;
}

.origin_price_peryear {
    text-decoration: line-through;
    height: 0.16rem;
    opacity: 0.8;
    font-size: 0.11rem;
    font-weight: 500;
    letter-spacing: 0.011rem;
    text-align: center;
    color: #ffffff;
}

.line {
    position: relative;
    height: 0.06rem;
    margin: 0;
    z-index: 999;
    box-shadow: 0 0.02rem 0 0 #6c4204;
    background-image: linear-gradient(269deg, #d1b693 100%, #f0ce7e 49%, #c29648 0%);
}

.line_buy {
    height: 0.05rem;
    z-index: 999;
    background-image: linear-gradient(to right, #ff4349 0%, #f8a18d 51%, #ff863e 99%);
}

.middletop {
    position: relative;
    box-sizing: border-box;
    padding: 0.28rem 0 0.24rem;
    box-shadow: 0 3px 15px 0 rgba(0, 0, 0, 0.5);
    border-style: solid;
    border-width: 2px;
    border-image-source: linear-gradient(to bottom, #241f18, #302d27 25%, #534b43 51%, #262420 74%, #211f1c);
    border-image-slice: 1;
    background-image: linear-gradient(308deg, #201e1c 99%, #2c251c 50%, #000000 0%);
    display: flex;
    flex-direction: column;
    margin-bottom: 0.2rem;
}

.middletop_pay {
    width: calc(100% + 0.32rem);
    height: 1.55rem;
    margin-left: -0.16rem;
    position: relative;
    box-sizing: border-box;
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 0.35rem 0 0.24rem;
    background: url('https://auto.tancdn.com/v1/images/eyJpZCI6IjM2M1JFNE1FTE1GRFg1VTRITFZUN0pEUVozQlpQSzA2IiwidyI6MTA4OSwiaCI6NTAxLCJkIjowLCJtdCI6ImltYWdlL2pwZWciLCJkaCI6NTAwMjczOTc1NTQ4NDIwODY3MX0.png');
    background-size: 100% 100%;
    margin-top: -0.1rem;
}

.middletop_pay .middletop_pay_text {
    max-width: 2.45rem;
    box-sizing: border-box;
    display: flex;
    white-space: nowrap;
    height: 0.28rem;
    color: #ffffff;
    margin-bottom: 0.13rem;
    overflow: hidden;
}

.middletop_pay_text>p {
    font-size: 0.2rem;
    line-height: 0.28rem;
    font-weight: 500;
    flex: 1;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.middletop_pay_text>span {
    font-size: 0.2rem;
    line-height: 0.28rem;
    font-weight: 500;
    display: inline-block;
}

.middletop_pay>img {
    width: 2.45rem;
    height: 0.4rem;
    object-fit: contain;
}

.privilege_writing {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 0.25rem;
    font-size: 0.18rem;
    font-family: PingFangSC;
    color: #f4d59d;
    margin-bottom: 0.16rem;
    font-weight: 600;
    white-space: nowrap;
}

.privilege_shape {
    display: inline-block;
    width: 0.08rem;
    height: 0.08rem;
    transform: rotate(45deg);
    margin: 0 0.1rem 0 0.08rem;
    background-color: #ffde83;
}

.privilege_shape_small {
    display: inline-block;
    transform: rotate(45deg);
    background-color: #ffde83;
    width: 0.06rem;
    height: 0.06rem;
}

.privilege_shape_right {
    margin: 0 0.08rem 0 0.1rem;
}

.ullist {
    padding: 0 0.16rem;
    margin-top: 0.09rem;
}

.ullist>li {
    font-size: 0.15;
    color: #ffffff;
}

.ullist>li:not(:first-child) {
    margin-top: 0.34rem;
}

.lilist {
    display: flex;
}

.li_content {
    display: flex;
    flex-direction: column;
}

.li_content>p:first-child {
    height: 0.24rem;
    line-height: 0.24rem;
    font-size: 0.16rem;
    font-weight: 500;
    color: #ffffff;
}

.li_content>p:last-child {
    height: 0.18rem;
    line-height: 0.18rem;
    font-size: 0.12rem;
    color: #bdbdbd;
    margin-top: 0.02rem;
}

.li_img {
    width: 0.46rem;
    height: 0.46rem;
    object-fit: contain;
    margin-right: 0.08rem;
}

.middlebottom {
    padding: 0.18rem 0 1.25rem;
    box-sizing: border-box;
    box-shadow: 0 3px 15px 0 rgba(0, 0, 0, 0.5);
    border-style: solid;
    border-width: 2px;
    border-image-source: linear-gradient(to bottom, #241f18, #302d27 25%, #534b43 51%, #262420 74%, #211f1c);
    border-image-slice: 1;
}

.server_info {
    height: 0.37rem;
    box-sizing: border-box;
    width: calc(100% + 0.44rem);
    margin-left: -0.22rem;
    margin-top: 0.24rem;
    background-color: rgba(0, 0, 0, 0.8);
    font-size: 0.12rem;
    line-height: 0.37rem;
    font-weight: 350;
    text-align: center;
    color: #ffe4b5;
}

.server_info>a {
    text-decoration: underline
}

.submit_container {
    width: calc(100% + 0.44rem);
    margin-left: -0.22rem;
    padding: 0.08rem 0.23rem 0.08rem 0.24rem;
    height: 0.64rem;
    box-sizing: border-box;
    background-image: linear-gradient(to right, #5e574a 2%, #514434 99%);
}

.submit_container>button {
    width: 3.28rem;
    height: 0.48rem;
    color: #7e510f;
    font-size: 0.17rem;
    border-radius: 0.1rem;
    box-shadow: 0 0.01rem 0.02rem 0 rgba(59, 34, 0, 0.4);
    background-image: linear-gradient(to right, #fff8ea 5%, #fffefd 53%, #f7e2c9 97%);
}

.goldimg {
    width: 1.12rem;
    height: 1.12rem;
    object-fit: contain;
    margin-top: -0.12rem;
}

.cancelimg {
    width: 0.24rem;
    height: 0.24rem;
    object-fit: contain;
}

.popup_title_top {
    width: 100%;
    height: 100%;
    text-align: right;
}

.popup_tips {
    height: 0.28rem;
    font-size: 0.2rem;
    font-weight: 600;
    color: #7e510f;
    margin: 0.16rem 0 0.08rem;
}

.popup_text {
    height: 0.42rem;
    font-size: 0.15rem;
    color: #7e510f;
}

.popup_input {
    width: 100%;
    height: 0.44rem;
    border-radius: 0.08rem;
    border: solid 0.01rem #7e510f;
    background-color: #ffffff;
    margin: 0.16rem 0 0.24rem;
    padding: 0.11rem 0.12rem;
    box-sizing: border-box;
}

.popup_input::placeholder, .popup_input::-webkit-input-placeholder {
    font-size: 0.16rem;
    color: #bdbdbd;
}

.pupup_content {
    padding: 0 0.2rem;
}

.popup_button {
    width: 2.5rem;
    height: 0.48rem;
    border-radius: 0.24rem;
    box-shadow: 0 0.02rem 0.05rem 0 rgba(0, 0, 0, 0.15);
    background-image: linear-gradient(to right, #fff8ea 5%, #f7e2c9 97%);
    border: none;
    margin-bottom: 0.24rem;
    font-size: 0.16rem;
    font-weight: 500;
    color: #7e510f;
}

.fixed_bottom {
    position: fixed;
    bottom: 0;
}