import React, { useState, useEffect, useRef } from 'react';
import s from './DiamondPay.module.css';
import { RouteComponentProps } from "react-router-dom";
import { imageListRights } from './utils';
import { Modal, Toast } from '../../components/antd/Antd';
import { urlParams } from '../../utils/index'
import { sha256_digest } from "./sha256";
import { Base64 } from 'js-base64';
import { ajax } from './ajax';

import { isWeixinBrowser } from '../../utils/bridge/utils';
import { trackShare, deviceID } from './utils';
import { isAndroid, isIOS } from "../../utils/config";


interface PaidResponse {
    orderId: string,
    paymentRequest: string
}

const DiamondPay: React.FC<RouteComponentProps> = (props) => {

    const [status, setStatus] = useState('');//试用或者邀请价格 trial 代表试用 formal 代表购买
    const [code, setCode] = useState('');
    const [username, setuserName] = useState('');
    const [maskshow, setMaskShow] = useState(false);
    const inputRef = useRef<HTMLInputElement>(null)
    let popupPanel: any;

    useEffect(() => {
        localStorage.setItem('deviceID', deviceID)
        let params = urlParams(props.location.search.slice(1));
        setStatus(params?.type);
        setCode(params?.code);
        setuserName(params?.fromuser);
        trackShare('PV', 'p_diamondvip_dealer_landingpage', '', { dealer_user_type: params?.type === 'trial' ? 'trial' : 'prepay' });
        document.title = '黑钻会员 诚挚邀请';
    }, [])


    const genHmacVersion14 = (uri: string, accessToken = '') => {
        // @ts-ignore
        const timestamp = new Date() * 1;
        const message = `${timestamp}${accessToken}${uri}`;
        const sha256 = sha256_digest(message);
        let arr = sha256.split('');
        let result = [];
        while (arr.length) {
            const item = `0x${arr.shift()}${arr.shift()}`;
            // @ts-ignore
            result.push(String.fromCharCode(parseInt(Number(item), 10)));
        }
        const base64Str = Base64.btoa(result.join(''))
        return `MAC ["14","web1.0.0","${timestamp}","${accessToken}","${base64Str}"]`
    }

    const handlePay = async () => {
        trackShare('MC','p_diamondvip_dealer_landingpage', 'e_diamondvip_dealer_submit', { dealer_user_type: status === 'trial' ? 'trial' : 'prepay' });
        if (inputRef && inputRef.current && inputRef.current.value) {
            if (status === 'trial') {
                //提交试用申请
                const result = await ajax.post('/v2/diamondVIP/agent/trial',
                    {
                        code,
                        contact: inputRef?.current?.value
                    }, {
                    headers: {
                        Authorization: genHmacVersion14('/v2/diamondVIP/agent/trial'),
                    }
                })
                if (result) {
                    Toast.info('信息提交成功，工作人员会尽快联系你', 2);
                }

            } else if (status === 'formal') {
                //提交购买申请
                const result: any = await ajax.post('/v2/diamondVIP/agent/paid', {
                    code,
                    contact: inputRef?.current?.value
                },
                    {
                        headers: {
                            Authorization: genHmacVersion14('/v2/diamondVIP/agent/paid'),
                        }
                    })
                if (result && result.data.diamondInviteOrder && result.data.diamondInviteOrder.paymentRequest) {
                    window.location.href = `https://m.tantanapp.com/monetization/diamondvip/#/alipay/pay?payload=${encodeURI(result.data.diamondInviteOrder.paymentRequest)}`

                }

            }
            handleCloseShare();
        } else {
            Toast.info('填写手机号后才可报名成功哦', 1)
        }
    }

    const handleCloseShare = () => {
        popupPanel.close();
    }

    const handlePopup = () => {
        trackShare('MC','p_diamondvip_dealer_landingpage', 'e_diamondvip_dealer_landingpage', { dealer_user_type: status === 'trial' ? 'trial' : 'prepay' });
        if (isWeixinBrowser() && status === 'formal') {
            setMaskShow(true);
            return;
        }
        trackShare('PV', 'p_diamondvip_dealer_submit', '', { dealer_user_type: status === 'trial' ? 'trial' : 'prepay' });
        popupPanel = Modal.alert((
            <div className={s.popup_title}>
                <div className={s.popup_title_top}>
                    <img className={s.cancelimg} onClick={handleCloseShare} src="https://auto.tancdn.com/v1/images/eyJpZCI6IlBMSEZCUFgzNzJUSkEyRkFEQ0FBSk9XNVdOSUdJNzA3IiwidyI6NzIsImgiOjcyLCJkIjowLCJtdCI6ImltYWdlL2pwZWciLCJkaCI6OTkwNzc1MTAyMjE3NDk3Nn0.png" alt="" />
                </div>
                <img className={s.goldimg} src="https://auto.tancdn.com/v1/images/eyJpZCI6IlQ3VDZXSTcyNzJKTEQyWkQyWjRVWVZNR1FTSTZGUzA2IiwidyI6MzM2LCJoIjozMzYsImQiOjAsIm10IjoiaW1hZ2UvanBlZyIsImRoIjo4NjY3MjAxNzkxMDUyNDI4OTJ9.png" alt="" />
            </div>
        ),
            (<div className={s.pupup_content}>
                <p className={s.popup_tips}>尊敬的贵宾用户</p>
                <p className={s.popup_text}>{status === 'trial' ? '输入您的手机号或微信，方便专属客服为您开启尊享服务' : '输入您当前使用的手机号或微信，方便专属客服为您开启尊享服务'}</p>
                <input type="text" className={s.popup_input} ref={inputRef} />
                <button className={s.popup_button} onClick={handlePay}>{status === 'trial' ? '通知专属客服' : '继续完成支付'}</button>
            </div>)
        )
        return (
            popupPanel
        )
    }

    return (
        <div className={s.container}>
            {maskshow ?
                <div className={s.guideimg}>
                    <img src="https://auto.tancdn.com/v1/images/eyJpZCI6IlhWT1VaWk9MN1lCRTVISVVCV0xJWlJRTkRCVVg2UjA3IiwidyI6MTg2LCJoIjoyNjcsImQiOjAsIm10IjoiaW1hZ2UvanBlZyIsImRoIjo2NTM4NzYzODc3MjU5MTk5MjF9.png" alt="" />
                    <div className={s.guildtext}>
                        <p>点击右上角「。。。」</p>
                        <p>选择用浏览器打开即可</p>
                        <p>立享优惠购买</p>
                    </div>
                    <button onClick={() => { setMaskShow(false) }}>关闭</button>
                </div>
                : ''}
            <img className={s.squareimg_one} src="https://auto.tancdn.com/v1/images/eyJpZCI6IkZZUUlHNVFRTUE1SEpESEZDTjVIVEFLTFRaM0hMWDA3IiwidyI6MTA4LCJoIjoxNTMsImQiOjAsIm10IjoiaW1hZ2UvanBlZyIsImRoIjo0MjU2MzI2MjQ4MzA2NzM2fQ.png" alt="" />
            <img className={s.squareimg_two} src="https://auto.tancdn.com/v1/images/eyJpZCI6IjczSkZZVVo1TkxZNEdRM0FKWTNOVldFRDJCS1hPNzA3IiwidyI6MTExLCJoIjoxMDgsImQiOjAsIm10IjoiaW1hZ2UvanBlZyIsImRoIjo0NDE0NTM0MDgzNDE3NTkyNDJ9.png" alt="" />
            {status === 'trial' ?
                <img className={s.backgroundimg} src="https://auto.tancdn.com/v1/images/eyJpZCI6IkpGVkFJVVNUNVlHWU1QQVVSUFRVSDRGRUJSUUtDVzA3IiwidyI6MTEyNSwiaCI6OTY5LCJkIjowLCJtdCI6ImltYWdlL2pwZWciLCJkaCI6NTI4MDk1MjQ2Mzk1MTgyNzUyOX0.png" alt="" />
                :
                <img className={s.starryimg} src="https://auto.tancdn.com/v1/images/eyJpZCI6IllRUERLMkw3MkFQWEg2R01aU1pZN0FWVDNTNjNFSjA2IiwidyI6MTEyNSwiaCI6OTkzLCJkIjowLCJtdCI6ImltYWdlL2pwZWciLCJkaCI6NTI4MDk1MjE5ODIwMzA2MTgzM30.png" alt="" />}
            {status === 'trial' ?
                <div className={s.top}>
                    <img className={s.vipimg} src="https://auto.tancdn.com/v1/images/eyJpZCI6IkVTTFFHSjJaWkdFWkJEQTNOWUFRUUxFTzRGR1VPRzA3IiwidyI6ODA3LCJoIjoyMTAsImQiOjAsIm10IjoiaW1hZ2UvanBlZyIsImRoIjoxMjg0MDgzNzQwMTg3MzU5OTk3MX0.png" alt="" />
                    <div className={s.viptextimg}>
                        <img src="https://auto.tancdn.com/v1/images/eyJpZCI6IjdWRUdNWktBWDZWT1JGNlZFUVBPMkkyTFhNTDVGRzA2IiwidyI6ODEwLCJoIjoxNjIsImQiOjAsIm10IjoiaW1hZ2UvanBlZyIsImRoIjoxMTgyMzY3OTgxNzg0MDA4NjY2MX0.png" alt="" />
                    </div>
                </div>
                :
                <div className={`${s.top} ${s.top_pay}`}>
                    <img className={s.vipimg} src="https://auto.tancdn.com/v1/images/eyJpZCI6IkVTTFFHSjJaWkdFWkJEQTNOWUFRUUxFTzRGR1VPRzA3IiwidyI6ODA3LCJoIjoyMTAsImQiOjAsIm10IjoiaW1hZ2UvanBlZyIsImRoIjoxMjg0MDgzNzQwMTg3MzU5OTk3MX0.png" alt="" />
                    <div className={s.viptextimg}>
                        <img src="https://auto.tancdn.com/v1/images/eyJpZCI6IkxST0dOTzNBWEk3VEROQUY3RDc2UDdFU1VQQ0ZVTjA3IiwidyI6NjI0LCJoIjo3NSwiZCI6MCwibXQiOiJpbWFnZS9qcGVnIiwiZGgiOjI5NjE0NjA3MDE5Njk0MTIzODB9.png" alt="" />
                    </div>
                </div>}
            {status === 'trial' ? <hr className={s.line} /> : <hr className={`${s.line} ${s.line_buy}`} />}
            {status === 'trial' ?
                <div className={s.middletop}>
                    <div className={s.privilege_writing}>
                        <span className={s.privilege_shape_small}></span>
                        <span className={s.privilege_shape}></span>
                    稀缺试用机会，体验专属特权
                 <span className={`${s.privilege_shape} ${s.privilege_shape_right}`}></span>
                        <span className={s.privilege_shape_small}></span>
                    </div>
                    <ul className={s.ullist}>
                        <li>1. 恭喜您获得黑钻合伙人专享的限量免费试用名额，请珍惜机会，认真使用；</li>
                        <li>2. 黑钻会员是面向优质用户通过邀请制开放的产品，我们会在开通前进行简单电话沟通，请您理解；</li>
                        <li>3. 黑钻会员免费试用时间为24小时，试用过程中有任何问题会有专属客服为您服务。</li>
                    </ul>
                </div> :
                <div className={s.middletop_pay}>
                    <div className={s.middletop_pay_text}><p>{username}</p><span>好友价格</span></div>
                    <img src="https://auto.tancdn.com/v1/images/eyJpZCI6IkNaU0JQRFJYUVkzSUpDRjYyRUhOSzNNT1BGWlJTMjA2IiwidyI6NzM1LCJoIjoxMjAsImQiOjAsIm10IjoiaW1hZ2UvanBlZyIsImRoIjoxMDI2ODI5Nzk2MDY1MTMxMH0.png" alt="" />
                </div>
            }
            <hr className={s.line} />
            <div className={s.middlebottom}>
                <div className={s.privilege_writing}>
                    <span className={s.privilege_shape_small}></span>
                    <span className={s.privilege_shape}></span>
                    权益说明
                     <span className={`${s.privilege_shape} ${s.privilege_shape_right}`}></span>
                    <span className={s.privilege_shape_small}></span>
                </div>
                <ul className={s.ullist}>
                    {imageListRights.map(item => (
                        <li className={s.lilist}>
                            <img className={s.li_img} src={item.url} alt="" />
                            <div className={s.li_content}>
                                <p>{item.title}</p>
                                <p>{item.subtitle}</p>
                            </div>
                        </li>
                    ))}
                </ul>
            </div>
            <div className={s.fixed_bottom}>
                <div className={s.server_info}>
                    {status === 'trial' ? '参与试用即表示同意' : '购买即表示同意'} <a href="https://m.tantanapp.com/monetization/diamondvip/#/rule">黑钻会员服务条款</a>
                </div>
                <div className={s.submit_container}>
                    <button onClick={handlePopup}>{status === 'trial' ? '提交试用预约' : '¥35000好友价格购买'}</button>
                </div>
            </div>
        </div>
    )
}

export default DiamondPay;