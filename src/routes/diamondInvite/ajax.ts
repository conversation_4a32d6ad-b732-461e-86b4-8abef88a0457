import axios from "axios";
// import { mock } from '../../_mock'
import { Toast } from "../../components/antd/Antd";
import { getAbHeader, getAuthorizationHeader, getSystemInfo } from "../../utils/bridge";
const reportUrl = '/report';
const sendCode_No_auth = '/v2/no_auth/red_book/send';
const verifyCode_No_auth = '/v2/no_auth/red_book/verify';
export const debugMode = window.location.host.includes('tantanapp.com') ? 'online' : window.location.host.includes('staging2.p1staff.com') ? 'staging' : 'test';

const baseURL =
  process.env.REACT_APP_TEST === "true"
    ? ""
    : process.env.NODE_ENV === "production"
      ? ""
      : "";
const instance = axios.create({
  baseURL,
  timeout: 5000,
});
const urls = [reportUrl, sendCode_No_auth, verifyCode_No_auth];
instance.interceptors.request.use(
  async (config) => {
    if (!config.headers.Authorization && config.url) {
      const needToken = !urls.includes(new URL(config.url).pathname);
      if (!needToken) {
        return {
          ...config,
        };
      } else {
        const auth = await getAuthorizationHeader(config.url, config.data);
        const abHeaders = await getAbHeader()

        // console.log(abHeaders);
        const { os, appVersion } = await getSystemInfo();
        return {
          ...config,
          headers: {
            ...config.headers,
            Authorization: auth,
            OS: os,
            AppVersion: appVersion,
            'X-Testing-Group': abHeaders
          },
        };
      }
    }
    return config;
  },
  () => {
    Toast.info("系统异常", 0.5);
    // 拦截器异常错误.
    return null;
  }
);

instance.interceptors.response.use(
  function (response: any) {
    if (response.status === 200) {
      if (
        response.data &&
        response.data.meta &&
        (response.data.meta.code === 200 ||
          response.data.meta.code === 200000)
      ) {
        return response.data;
      }
      return response.data;
    }
    return null;
  },
  function (err) {
    
    // let status = err && err.response && err.response.status && err.response.status;
    // if (status && status === 403) {
    //   Toast.info('当前为无效身份，暂不支持使用', 3);
    // } else if (status && status === 400 && err.response.data && err.response.data.meta && err.response.data.meta.subCode) {
    //   if (err.response.data.meta.subCode === 4000502) {
    //     Toast.info('试用机会已失效，联系合伙人获取更新试用机会', 2)
    //   }
    //   if (err.response.data.meta.subCode === 4000501) {
    //     Toast.info('当前状态有调整，请刷新重试', 2)
    //   }
    // }
    return err?.response?.data;
  }
);

// process.env.NODE_ENV !== 'production' && mock(instance)
export const ajax = instance;
