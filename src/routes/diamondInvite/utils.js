import { makeRandomCode } from '../../utils/index';
// import axiosExternal from './ajaxExternal';
import {ajax} from "./ajax";
export const ifDebug = window.location.host.includes('tantanapp.com') ? false : true;
export const imageList = [
    { url: 'https://auto.tancdn.com/v1/images/eyJpZCI6IldHN1BXUklSSjZQSzJQVlNaRDdMWTRRTFhZT0dXQzA2IiwidyI6MTI2LCJoIjoxMjYsImQiOjAsIm10IjoiaW1hZ2UvanBlZyIsImRoIjoxMDk3NTEwMTQ4NTQyNjU2NzgzfQ.png', text: '即赞即聊' }, //即赞即聊
    { url: 'https://auto.tancdn.com/v1/images/eyJpZCI6IjNITVFEN0JaQlY2UUhGU1lBVDIyRERCUTNCTlFDNTA2IiwidyI6MTI2LCJoIjoxMjYsImQiOjAsIm10IjoiaW1hZ2UvanBlZyIsImRoIjoxMDk1MzE3OTIxNTM0MjU3OTM1fQ.png', text: '神秘模式' }, //神秘模式
    { url: 'https://auto.tancdn.com/v1/images/eyJpZCI6IjYyQllUSTdHVlFBN0tFRFZPVllXUkdOSDNHNjZETjA2IiwidyI6MTI2LCJoIjoxMjYsImQiOjAsIm10IjoiaW1hZ2UvanBlZyIsImRoIjoxMDg0MDQ1NTY1MDcxNzkxODg3fQ.png', text: '定向置顶' }, //定向置顶
    { url: 'https://auto.tancdn.com/v1/images/eyJpZCI6IlkyT0tJVVpQTUlUQlpNQ0xXS0NRUFhLU0JPMkZCVzA3IiwidyI6MTI2LCJoIjoxMjYsImQiOjAsIm10IjoiaW1hZ2UvanBlZyIsImRoIjoxMDg1MTgwNDA2MDA3NjY2NDU1fQ.png', text: '私人订制' }, //私人订制
    { url: 'https://auto.tancdn.com/v1/images/eyJpZCI6IjVWU1lNSkxaU09LVlZXSlpCSERKQVZLN0tDNjNXNzA3IiwidyI6MTI2LCJoIjoxMjYsImQiOjAsIm10IjoiaW1hZ2UvanBlZyIsImRoIjoxMDkzMDI4NjAwNTY2MTk3MDA3fQ.png', text: '优质认证' }, //优质认证
    { url: 'https://auto.tancdn.com/v1/images/eyJpZCI6IlQyT0VaWTNHN0xRTUhCQkJUSTcyRFZKWkhXSFlSWjA2IiwidyI6MTI2LCJoIjoxMjYsImQiOjAsIm10IjoiaW1hZ2UvanBlZyIsImRoIjoxMDkzMDYxMTU1MjYyNTA3NzkxfQ.png', text: '尊贵标识' }, //尊贵标识
    { url: 'https://auto.tancdn.com/v1/images/eyJpZCI6IlI0V1JXSUI1TUZMRFFKVEFXNzdURklNWTZWVE9ORjA3IiwidyI6MTI2LCJoIjoxMjYsImQiOjAsIm10IjoiaW1hZ2UvanBlZyIsImRoIjoxMDk1Mjg0Nzg5OTcxNzI1MDcxfQ.png', text: '专属管家' }, //专属管家
    { url: 'https://auto.tancdn.com/v1/images/eyJpZCI6IloyUDVJTllEVTNJVTRUQkNQVTdBWFdIRE1DWkhTUDA3IiwidyI6MTI2LCJoIjoxMjYsImQiOjAsIm10IjoiaW1hZ2UvanBlZyIsImRoIjoyMjQ3MDMxOTY0ODg4OTM1MTgzfQ.png', text: '举报优先' }, //举报优先
]

export const imageListRights = [
    { url: 'https://auto.tancdn.com/v1/images/eyJpZCI6IkRZVzJPQUVDSERKUlRMTUFLTEk2WVJCRVlaSFdLVDA3IiwidyI6MTM4LCJoIjoxMzgsImQiOjAsIm10IjoiaW1hZ2UvanBlZyIsImRoIjoxMDM4OTcxNzUwNTE0MTI3MTE5fQ.png', title: '优质速配直接聊天', subtitle: '无需等待匹配，属于你的定制用户随时聊' },
    { url: 'https://auto.tancdn.com/v1/images/eyJpZCI6Ik1ZWVpKVEZENFRIV1BaQU02NjVaNkhLQVBUNFRZUTA3IiwidyI6MTM4LCJoIjoxMzgsImQiOjAsIm10IjoiaW1hZ2UvanBlZyIsImRoIjoxMDk1MzA0NzM1OTMzMTUyMDE1fQ.png', title: '遮脸配对隐私护航', subtitle: '不愿露脸仍旧收获更多匹配' },
    { url: 'https://auto.tancdn.com/v1/images/eyJpZCI6IlhPVUtNQUVPR0NGMkNHRzI0NDZUQjVTUVVETkRMUjA2IiwidyI6MTM4LCJoIjoxMzgsImQiOjAsIm10IjoiaW1hZ2UvanBlZyIsImRoIjo4MDM3MzE1MzUxMzI2OTUzMDN9.png', title: '最大精准置顶曝光', subtitle: '不止最大曝光，更对优质用户精准置顶投放' },
    { url: 'https://auto.tancdn.com/v1/images/eyJpZCI6Ik5VSjZIUVM2Nlk2QlFETlFWWVVQVDZaSEE2SlFXTzA2IiwidyI6MTM4LCJoIjoxMzgsImQiOjAsIm10IjoiaW1hZ2UvanBlZyIsImRoIjo3OTY5MTUyNDg4NzMxNjY2NDd9.png', title: '订制用户智能推荐', subtitle: 'AI+人工，优先展示更多真实订制对象' },
    { url: 'https://auto.tancdn.com/v1/images/eyJpZCI6IkpKVFZPNlhUREQzR0ZQUE1XRVM0U0lXS0ZQUUxEWDA2IiwidyI6MTM4LCJoIjoxMzgsImQiOjAsIm10IjoiaW1hZ2UvanBlZyIsImRoIjoxNjY4ODkxMTkzMjQ0MDAyNTgzfQ.png', title: '优质认证平台背书', subtitle: '提供专业审核认证，为您的高级和优质背书' },
    { url: 'https://auto.tancdn.com/v1/images/eyJpZCI6IlE2WEMyVFlXSUdYUUhIWlhCUVdDQjczU1lJRkRTVzA2IiwidyI6MTM4LCJoIjoxMzgsImQiOjAsIm10IjoiaW1hZ2UvanBlZyIsImRoIjoxMDkzMDYxNzA1MDE1OTc4MjU1fQ.png', title: '尊贵身份全面彰显', subtitle: '专属装扮超炫勋章，随时彰显你的与众不同' },
    { url: 'https://auto.tancdn.com/v1/images/eyJpZCI6Ik9HR0czWUFEVTM0TjVWT1BXUFZHUlFKQ1pYVUtNTTA3IiwidyI6MTM4LCJoIjoxMzgsImQiOjAsIm10IjoiaW1hZ2UvanBlZyIsImRoIjoxNjg4MDIyNjYwNjA0NzE3MzM1fQ.png', title: '一对一贴心管家服务', subtitle: '牵线配对约会助力，提供一对一服务' },
    { url: 'https://auto.tancdn.com/v1/images/eyJpZCI6Ik9CV0g1WjJMQ01ZVEVCN0JXQVpKTFdFWTVTUUxRSTA3IiwidyI6MTM4LCJoIjoxMzgsImQiOjAsIm10IjoiaW1hZ2UvanBlZyIsImRoIjoxMDkwNzgxMjA3NTkwMjg0MDU1fQ.png', title: '违规豁免举报优先', subtitle: '违规黄牌豁免，举报绿色通道' },
    { url: 'https://auto.tancdn.com/v1/images/eyJpZCI6Ik00TEtWMkhDTkJRUEZOT0w1TTYzRVpZT1NJRkMzWTA3IiwidyI6MTM4LCJoIjoxMzgsImQiOjAsIm10IjoiaW1hZ2UvanBlZyIsImRoIjo1MDg3MTk1OTE4NDc1NjUwNzF9.png', title: '网页登录，高效无痕', subtitle: '支持网页端使用，随时登录更加高效' }
]
export const deviceID = localStorage.getItem('deviceID') || makeRandomCode(20);

export const reportUrl = 'https://h5report.tantanapp.com/report';

export function trackShare (event_type, pid, eid, extra) {
    // let extra: { isAndroid?: boolean, eventName?: string } = {};
    let params = {
      userId: '',
      deviceId: deviceID,
      content: ''
    }
    params.content = JSON.stringify({
      pid: pid,
      eid: eid,
      tt: event_type,
      time: Date.now(),
      extra
    })
    ajax.post(reportUrl, params)
      .then(res => {
        // console.log('trackShare', event_type, res);
      }, rej => {
      })
    return true
  }