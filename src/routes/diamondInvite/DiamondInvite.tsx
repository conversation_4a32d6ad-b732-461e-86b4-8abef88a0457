import React, { useState, useEffect, useRef } from 'react'
import { Modal } from '../../components/antd/Antd';
import { RouteComponentProps } from "react-router-dom";
import s from './DiamondInvite.module.css';
import { imageList } from './utils';
import { getUserInfo, trackNew, setNavigationTitle } from '../../utils/bridge/index';
import { ajax } from './ajax';

interface DealInfo {
    inviteQuota: number,
    inviteRemaining: number
}

const DiamondInvite: React.FC<RouteComponentProps> = (props) => {
    const [userName, setUserName] = useState('');
    const [data, setData] = useState<DealInfo>({} as DealInfo);
    const popupRef = useRef<HTMLDivElement>(null)
    let popupPanel: any;

    const handleCloseShare = () => {
        popupPanel.close();
    }

    const handleCreatCard = async (type: string) => {
        handleCloseShare();
        const data = await ajax.post('/v2/diamondVIP/agent/share', { type });
        if (data && data?.data && data?.data?.diamondAgentInviteCode) {
            props.history.push(`/diamond/enter/card?type=${type}&code=${data?.data?.diamondAgentInviteCode}`);
        }
    }

    useEffect(() => {
        (async () => {
            const { userName } = await getUserInfo();
            setUserName(userName)
            const data = await ajax.get('/v2/diamondVIP/agent');
            if (data && data?.data && data?.data?.diamondAgentInfo) {
                setData(data?.data?.diamondAgentInfo);
            }
            setNavigationTitle({
                title: '黑钻会员 诚挚邀请'
            })
            trackNew({
                pageId: 'p_diamondvip_dealer_share',
                type: 'PV'
            })
        })()
    }, [])

    const handleShareCard = async (e: any) => {
        //阻止事件冒泡，用户多次点击会卡住
        e.stopPropagation();
        //如果当前已经有弹窗，就不继续弹窗了，防止用户多次点击弹窗按钮
        if (popupRef && popupRef.current) return;
        //用户点击弹窗按钮时，再去拉一次接口更新数据
        const result = await ajax.get('/v2/diamondVIP/agent');
        if (result && result?.data && result?.data?.diamondAgentInfo) {
            setData(result?.data?.diamondAgentInfo);
        } else if (!result) {
            setData({
                inviteQuota: -1,
                inviteRemaining: -1
            })
            return;
        }
        trackNew({
            pageId: 'p_diamondvip_dealer_share',
            eid: 'e_diamondvip_dealer_share',
            type: 'MC'
        })
        popupPanel = Modal.alert((
            <div className={s.popup_title} ref={popupRef}>
                <div className={s.popup_title_top}>
                    <img className={s.cancelimg} onClick={handleCloseShare} src="https://auto.tancdn.com/v1/images/eyJpZCI6IlBMSEZCUFgzNzJUSkEyRkFEQ0FBSk9XNVdOSUdJNzA3IiwidyI6NzIsImgiOjcyLCJkIjowLCJtdCI6ImltYWdlL2pwZWciLCJkaCI6OTkwNzc1MTAyMjE3NDk3Nn0.png" alt="" />
                </div>
                <img className={s.goldimg} src="https://auto.tancdn.com/v1/images/eyJpZCI6IlQ3VDZXSTcyNzJKTEQyWkQyWjRVWVZNR1FTSTZGUzA2IiwidyI6MzM2LCJoIjozMzYsImQiOjAsIm10IjoiaW1hZ2UvanBlZyIsImRoIjo4NjY3MjAxNzkxMDUyNDI4OTJ9.png" alt="" />
            </div>
        ),
            (<div className={s.popup_content}>
                <p className={s.popup_tips}>提示</p>
                {result?.data?.diamondAgentInfo?.inviteRemaining > 0 ?
                    <React.Fragment>
                        <p className={s.popup_tipstext}>将默认生成免费试用卡片并消耗您的1次试用机会（剩余{(result?.data?.diamondAgentInfo?.inviteRemaining) - 1}次），若不想消耗此次机会，也可生成¥35000购买链接</p>
                        <div className={s.popup_button_tryout} onClick={() => { handleCreatCard('trial') }}>继续生成试用卡片</div>
                        <div className={s.popup_button_buy} onClick={() => { handleCreatCard('formal') }}>希望对方直接购买</div>
                    </React.Fragment>
                    :
                    <React.Fragment>
                        <p className={`${s.popup_tipstext} ${s.popup_tipstext_pay}`}>{result?.data?.diamondAgentInfo?.inviteQuota}次免费试用机会暂时已用完，此次将直接生成¥35000购买链接！</p>
                        <div className={`${s.popup_button_tryout} ${s.popup_button_pay}`} onClick={() => { handleCreatCard('formal') }}>生成直接购买卡片</div>
                    </React.Fragment>
                }

            </div>)
        )
        return (
            popupPanel
        )
    }

    return (
        <div className={s.container}>
            <img className={s.squareimg_one} src="https://auto.tancdn.com/v1/images/eyJpZCI6Ik5ZQ1pERVY1NElDRTRRNkhFVldDQ0EzSTNJVzNLNTA3IiwidyI6MTE3LCJoIjoxNTMsImQiOjAsIm10IjoiaW1hZ2UvanBlZyIsImRoIjoxOTk2MjIwNDM5MDc1ODcyfQ.png" alt="" />
            <img className={s.squareimg_two} src="https://auto.tancdn.com/v1/images/eyJpZCI6IjczSkZZVVo1TkxZNEdRM0FKWTNOVldFRDJCS1hPNzA3IiwidyI6MTExLCJoIjoxMDgsImQiOjAsIm10IjoiaW1hZ2UvanBlZyIsImRoIjo0NDE0NTM0MDgzNDE3NTkyNDJ9.png" alt="" />
            <img className={s.starryimg_two} src="https://auto.tancdn.com/v1/images/eyJpZCI6Ik80QkdaSFJaRExFWTcyNFZGQjVWS0JFNEY2NUg3QjA3IiwidyI6MTEyNSwiaCI6NzE0LCJkIjowLCJtdCI6ImltYWdlL2pwZWciLCJkaCI6NDk0MDk5NTM5MzI5MTUyfQ.png" alt="" />
            <div className={s.toptitle}>
                <img className={s.toptitle_left} src="https://auto.tancdn.com/v1/images/eyJpZCI6IjJESFVOSFM1MzRXNFJBTENOSTRKUlMyNUI3NjJRUjA2IiwidyI6MTE0LCJoIjoxMTQsImQiOjAsIm10IjoiaW1hZ2UvanBlZyIsImRoIjoxNzA3NjUxODQxMjQyNzcyMjU5fQ.png" alt="" />
                <div className={s.toptitle_right}>
                    <img src="https://auto.tancdn.com/v1/images/eyJpZCI6IlAyV1E2RUE2Rkk0TkgzVTM0S0RMSlpNS1hFUFUyTTA2IiwidyI6MTUwLCJoIjozNiwiZCI6MCwibXQiOiJpbWFnZS9qcGVnIiwiZGgiOjE0NTEzMzE0ODMyNjYxNDQwODV9.png" alt="" />
                    <span>极致社交体验</span>
                </div>
            </div>
            <div className={s.content}>
                {/* <img className={s.content_backgr} src="https://auto.tancdn.com/v1/images/eyJpZCI6IjdCQllJN0tJWVNMVUVCT0I1SUtUV1I2Uk1JUko1MjA2IiwidyI6MTAzMiwiaCI6MTcwNywiZCI6MCwibXQiOiJpbWFnZS9qcGVnIiwiZGgiOjQ3MDIxMTEyNTIxNjIyNDg1NzV9.png" alt=""/> */}
                <p className={s.content_title}>你好，{userName}</p>
                <div className={s.textmargin}>
                    {data?.inviteQuota > 0 ? <p className={s.trypeople}>当前为你提供{data?.inviteQuota}个试用名额</p> : ''}
                    <p className={s.trypeople}>诚邀您的朋友获得稀缺名额和专属价格</p>
                </div>
                <div className={s.price_peryear}>
                    <img src="https://auto.tancdn.com/v1/images/eyJpZCI6IktLQ0RQUlNVRkU1WFJTUUoyTzVXRzJQQTNGWFhNTTA2IiwidyI6NjMwLCJoIjoxMjAsImQiOjAsIm10IjoiaW1hZ2UvanBlZyIsImRoIjozMDE0MzI1NzcwMzgzODc4NzQwfQ.png" alt="" />
                </div>
                <p className={s.origin_price_peryear}>原价 ¥49999/年</p>
                <p className={s.horizatontal_line}></p>
                <div className={s.privilege_writing}>
                    <span className={s.privilege_shape_small}></span>
                    <span className={s.privilege_shape}></span>
                     8项黑钻会员专属特权 等待优质的您解锁
                     <span className={`${s.privilege_shape} ${s.privilege_shape_right}`}></span>
                    <span className={s.privilege_shape_small}></span>
                </div>
                <ul className={s.ullist}>
                    {imageList.map(item => (
                        <li className={s.lilist}>
                            <img className={s.li_img} src={item.url} alt="" />
                            <p className={s.li_text}>{item.text}</p>
                        </li>
                    ))}
                </ul>
                <div className={s.bottom}>
                    <img src="https://auto.tancdn.com/v1/images/eyJpZCI6IldWWEhOWkNRRVJTNFlPSUVFV1Q0SFFaVzVPTUc1MjA2IiwidyI6MTA2OCwiaCI6NzUzLCJkIjowLCJtdCI6ImltYWdlL2pwZWciLCJkaCI6OTkwOTAyNzc1MTYzMTgzMjMyfQ.png" alt="" />
                    <img className={s.bottom_dots} src="https://auto.tancdn.com/v1/images/eyJpZCI6IkpLU0dHNE9OUkFCRU1URldCWjZKVVdLT1lET1AyQjA3IiwidyI6MTIsImgiOjY5LCJkIjowLCJtdCI6ImltYWdlL2pwZWciLCJkaCI6MzEzODQ0NjE3NzM4OTc4Mzk0Mn0.png" alt="" />
                    <div className={s.button_container}>
                        <button className={s.share_button} onClick={handleShareCard}>生成分享卡片</button>
                    </div>
                </div>
            </div>
            <div className={s.footer}></div>
        </div>
    )
}

export default DiamondInvite;