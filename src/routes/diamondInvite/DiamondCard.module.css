.container {
    height: 100%;
    background-color: #000000;
}

.content {
    height: 85%;
    box-sizing: border-box;
    position: relative;
    padding: 0.16rem 0.16rem 0.1rem;
}

.contentbody {
    width: 100%;
    height: 100%;
    background: url('https://auto.tancdn.com/v1/images/eyJpZCI6IjdCQllJN0tJWVNMVUVCT0I1SUtUV1I2Uk1JUko1MjA2IiwidyI6MTAzMiwiaCI6MTcwNywiZCI6MCwibXQiOiJpbWFnZS9qcGVnIiwiZGgiOjQ3MDIxMTEyNTIxNjIyNDg1NzV9.png');
    background-size: 100% 100%;
    padding: 0.3rem;
    box-sizing: border-box;
    display: flex;
    flex-direction: column;
    align-items: center;
    position: relative;
    z-index: 99;
}

.starryimg_one {
    position: absolute;
    left: 0;
    top: 0;
    width: 3.18rem;
    height: 48.85%;
    mix-blend-mode: screen;
    z-index: 999;
}

.starryimg_two {
    position: absolute;
    height: 38%;
    width: 100%;
    bottom: 0.89rem;
    left: 0;
    mix-blend-mode: difference;
}

.toptitle {
    display: flex;
    align-items: center;
    margin-left: 0.2rem;
    margin-top: 0.08rem;
    width: 100%;
}

.toptitle_left {
    width: 0.36rem;
    height: 0.36rem;
}

.toptitle_right {
    display: flex;
    flex-direction: column;
    margin-left: 0.06rem;
}

.toptitle_right>img {
    width: 0.5rem;
    height: 0.12rem;
    object-fit: contain;
    margin-bottom: 0.03rem;
}

.toptitle_right>span {
    height: 0.14rem;
    font-size: 0.1rem;
    letter-spacing: 0.0083rem;
    color: #eee2cd;
}

.inviteimg {
    width: 100%;
    height: 24%;
    margin-left: 0.2rem;
    display: flex;
    justify-content: flex-start;
    align-items: flex-end;
}

.inviteimg>img {
    max-width: 1.93rem;
    height: 85%;
    text-align: left;
    object-fit: contain;
}

.line_container {
    width: 100%;
    text-align: center;
}

.divide_container {
    height: 5.3%;
    margin-bottom: 0.11rem;
    display: flex;
    justify-content: center;
    align-items: flex-end;
}

.divide_container_one{
    margin-top: 0.05rem;
    height: 3.8%;
    display: flex;
    align-items: center;
    justify-content: center;
}

.divideline_one {
    width: 2.53rem;
    height: 0.02rem;
    opacity: 0.6;
    /* margin: 0.15rem 0 0.1rem; */
    text-align: center;
    background-image: linear-gradient(to left, #54411d 100%, #80704a 50%, #54411d 0%);
}

.divideline_two {
    width: 2.53rem;
    height: 0.02rem;
    opacity: 0.6;
    text-align: center;
    background-image: linear-gradient(to left, #54411d 100%, #80704a 50%, #54411d 0%);
}

.privilege_writing {
    display: flex;
    align-items: center;
    height: 0.18rem;
    font-size: 0.13rem;
    font-family: PingFangSC;
    color: rgba(255, 255, 255, 0.8);
    margin-bottom: 0.04rem;
    justify-content: center;
    white-space: nowrap;
}

.privilege_shape {
    display: inline-block;
    width: 0.06rem;
    height: 0.06rem;
    transform: rotate(45deg);
    margin: 0 0.04rem 0 0.06rem;
    box-shadow: 0.02rem 0.02rem 0.07rem 0 #000000;
    background-image: linear-gradient(74deg, #ffdd9f 1%, #fff0d0 52%, #f6bf69 100%), linear-gradient(to bottom, #7e510f, #7e510f);
}

.privilege_shape_small {
    display: inline-block;
    transform: rotate(45deg);
    box-shadow: 0.02rem 0.02rem 0.07rem 0 #000000;
    background-image: linear-gradient(74deg, #ffdd9f 1%, #fff0d0 52%, #f6bf69 100%), linear-gradient(to bottom, #7e510f, #7e510f);
    width: 0.04rem;
    height: 0.04rem;
}

.privilege_shape_right {
    margin: 0 0.06rem 0 0.04rem;
}

.ullist {
    display: flex;
    flex-wrap: wrap;
    padding: 0 0.18rem;
    justify-content: center;
}

.lilist {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    margin-right: 0.26rem;
    width: 0.42rem;
    margin-top: 0.06rem;
}

.ullist .lilist:nth-child(4n+4) {
    margin-right: 0;
}

.lilist>img {
    width: 0.42rem;
    height: 0.42rem;
    object-fit: contain;
}

.li_text {
    width: 0.48rem;
    height: 25%;
    margin-top: 0.06rem;
    font-family: PingFangSC;
    font-size: 0.12rem;
    color: rgba(255, 255, 255, 0.8);
    white-space: nowrap;
}

.price_peryear {
    width: 1.64rem;
    height: 8.19%;
    margin: 0rem 0 0.05rem;
    object-fit: contain;
}

.price_peryear>img {
    width: 100%;
    height: 100%;
    object-fit: contain;
}

.origin_price_peryear {
    height: 0.22rem;
    font-size: 0.16rem;
    font-weight: 600;
    color: #927d56;
    text-decoration: line-through;
}

.bottom {
    height: 15%;
    box-sizing: border-box;
    background-color: #ffffff;
    /* padding: 0.16rem 0.14rem 0.16rem; */
    padding: 0 0.14rem;
    align-items: center;
    display: flex;
    justify-content: space-between;
}

.avator {
    width: 0.66rem;
    height: 0.66rem;
    margin-right: 0.12rem;
}

.qrcode {
    width: 0.68rem !important;
    height: 0.68rem !important;
}

.introduce {
    display: flex;
    flex-direction: column;
    font-size: 0.14rem;
    margin: 0 0.24rem 0 0rem;
    box-sizing: border-box;
    width: calc(100% - 0.68rem - 0.34rem);
}

.focustext {
    color: #e0a030;
    font-weight: 500;
}

.intro_p1 {
    font-size: 0.18rem;
    white-space: nowrap;
    text-overflow: ellipsis;
    color: #212121;
    font-weight: bolder;
    width: 100%;
    overflow: hidden;
}

.pink_ribbon {
    width: 1.43rem;
    height: 38%;
    position: absolute;
    right: -0.16rem;
    bottom: 0;
    object-fit: contain;
}

.square_one {
    width: 0.5rem;
    height: 0.6rem;
    position: absolute;
    right: -0.16rem;
    object-fit: contain;
    bottom: 2rem;
}

.square_two {
    width: 0.5rem;
    height: 0.35rem;
    position: absolute;
    left: -0.16rem;
    bottom: 0.48rem;
    object-fit: contain;
}