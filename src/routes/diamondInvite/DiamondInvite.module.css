.container {
    /* 中间内容的高度556.3 */
    position: relative;
    background-color: #000000;
    height: 100%;
    display: flex;
    flex-direction: column;
}

.starryimg_one {
    position: absolute;
    left: 0;
    top: -0.1rem;
    width: 3.25rem;
    height: 2.81rem;
    /* opacity: 0.56; */
    mix-blend-mode: lighten;
    object-fit: contain;
    z-index: 99;
    /* background-image: url('https://auto.tancdn.com/v1/images/eyJpZCI6IlZDTTQ1UFhYNkNJNFJLQVNOTjU0S1hFWVpCTDdMUzA3IiwidyI6MTEyNSwiaCI6OTE4LCJkIjowLCJtdCI6ImltYWdlL2pwZWciLCJkaCI6MTY2MDk1ODU0MjI0MzJ9.png');
    background-size: 100% 100%; */
}

.squareimg_one {
    position: absolute;
    width: 0.58rem;
    height: 0.64rem;
    object-fit: contain;
    top: 1.06rem;
    right: 0;
    z-index: 99;
}

.squareimg_two {
    width: 0.42rem;
    height: 0.57rem;
    position: absolute;
    bottom: 1.38rem;
    left: 0.02rem;
    z-index: 99;
    object-fit: contain;
    opacity: 0.9;
}

.toptitle {
    display: flex;
    height: 10%;
    padding: 0.25rem 0.25rem 0.2rem;
    box-sizing: border-box;
    align-items: center;
}

.toptitle_left {
    width: 0.38rem;
    height: 0.38rem;
    margin-right: 0.05rem;
}

.toptitle_right {
    display: flex;
    flex-direction: column;
}

.toptitle_right>img {
    width: 0.5rem;
    height: 0.12rem;
    /* margin: 5px 15px 3px 5px; */
    object-fit: contain;
    margin-bottom: 0.03rem;
}

.toptitle_right>span {
    display: inline-block;
    /* width: 0.65rem; */
    height: 0.14rem;
    font-size: 0.1rem;
    letter-spacing: 0.0083px;
    color: #eee2cd;
}

.content {
    flex: 1;
    box-sizing: border-box;
    /* border: 1px solid red; */
    display: flex;
    flex-direction: column;
    align-items: center;
    background: url('https://auto.tancdn.com/v1/images/eyJpZCI6IjdCQllJN0tJWVNMVUVCT0I1SUtUV1I2Uk1JUko1MjA2IiwidyI6MTAzMiwiaCI6MTcwNywiZCI6MCwibXQiOiJpbWFnZS9qcGVnIiwiZGgiOjQ3MDIxMTEyNTIxNjIyNDg1NzV9.png') center no-repeat;
    background-size: 93% 100%;
    padding: 0 0.26rem;
    position: relative;
    max-height: 81.5%;
}

.content_title {
    width: 2.6rem;
    height: 5.8%;
    /* height: 14%; */
    /* display: flex;
    align-items: flex-end;
    justify-content: center; */
    font-size: 0.24rem;
    line-height: 0.32rem;
    font-weight: 500;
    color: #ffffff;
    text-align: center;
    margin-top: 0.42rem;
    /* margin-top: 15%; */
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.textmargin {
    margin-top: 0.11rem;
}

.content_background_url {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
}

.content_background_url>img {
    width: 100%;
    height: 100%;
}

.trypeople {
    text-align: center;
    color: #f2cd8c;
    white-space: nowrap;
}

.price_peryear {
    width: 2.1rem;
    max-height: 8.9%;
    margin-top: 0.16rem;
    display: flex;
    align-items: center;
    justify-content: center;
}

.price_peryear>img {
    width: 100%;
    object-fit: contain;
}

.origin_price_peryear {
    height: 3.44%;
    font-size: 0.16rem;
    font-weight: 600;
    color: #6d6962;
    text-decoration: line-through;
}

.horizatontal_line {
    width: 2.53rem;
    height: 0.02rem;
    margin: 0.15rem 0 0.14rem;
    opacity: 0.6;
    background-image: linear-gradient(to left, #54411d, #80704a, #54411d);
}

.privilege_writing {
    display: flex;
    align-items: center;
    height: 0.18rem;
    font-size: 0.13rem;
    font-family: PingFangSC;
    color: rgba(255, 255, 255, 0.8);
    margin-bottom: 0.04rem;
}

.privilege_shape {
    display: inline-block;
    width: 0.06rem;
    height: 0.06rem;
    transform: rotate(45deg);
    margin: 0 0.04rem 0 0.06rem;
    box-shadow: 0.02rem 0.02rem 0.07rem 0 #000000;
    background-image: linear-gradient(74deg, #ffdd9f 1%, #fff0d0 52%, #f6bf69 100%), linear-gradient(to bottom, #7e510f, #7e510f);
}

.privilege_shape_small {
    display: inline-block;
    transform: rotate(45deg);
    box-shadow: 0.02rem 0.02rem 0.07rem 0 #000000;
    background-image: linear-gradient(74deg, #ffdd9f 1%, #fff0d0 52%, #f6bf69 100%), linear-gradient(to bottom, #7e510f, #7e510f);
    width: 0.04rem;
    height: 0.04rem;
}

.privilege_shape_right {
    margin: 0 0.06rem 0 0.04rem;
}

.ullist {
    display: flex;
    flex-wrap: wrap;
    height: 25.3%;
    padding: 0 0.36rem 0 0.4rem;
    justify-content: space-between;
    box-sizing: border-box;
}

.lilist {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    margin-right: 0.26rem;
    width: 0.42rem;
    margin-top: 0.06rem;
    box-sizing: border-box;
    height: 42%;
}

.ullist .lilist:nth-child(4n+4) {
    margin-right: 0;
}

.lilist>img {
    width: 0.42rem;
    /* height: 0.42rem; */
    height: 64.5%;
    object-fit: contain;
}

.li_text {
    width: 0.48rem;
    /* height: 0.17rem; */
    height: 26.15%;
    /* margin: 6px 20px 54px 15px; */
    font-family: PingFangSC;
    font-size: 0.12rem;
    color: rgba(255, 255, 255, 0.8);
    margin-top: 0.06rem;
    white-space: nowrap;
}

.bottom {
    width: calc(100% + 0.46rem);
    margin-top: -0.85rem;
    position: relative;
    height: 36%;
    flex: 1;
    text-align: center;
}

.bottom>img:first-child {
    width: 98%;
    height: 110%;
    /* height: 2.2rem; */
    object-fit: inherit;
}

.bottom_dots {
    position: absolute;
    text-align: center;
    height: 9.13%;
    /* top: 43%; */
    bottom: 45%;
    left: 50%;
}

.starryimg_two {
    position: absolute;
    bottom: 0;
    left: 0;
    height: 2.38rem;
    object-fit: contain;
}

.button_container {
    width: 100%;
    position: absolute;
    height: 20.8%;
    bottom: 7%;
    text-align: center;
}

.share_button {
    width: 2.5rem;
    /* height: 0.48rem; */
    height: 100%;
    /* padding: 13px 77px; */
    border: none;
    border-radius: 0.24rem;
    font-size: 0.16rem;
    font-weight: 500;
    color: #7e510f;
    box-shadow: 0 1px 2px 0 rgba(59, 34, 0, 0.4);
    background-image: linear-gradient(to right, #fff8ea 5%, #fffefd 53%, #f7e2c9 97%);
}

.goldimg {
    width: 1.12rem;
    height: 1.12rem;
    /* margin: 12px 68px 16px 80px; */
    /* padding: 11px; */
    object-fit: contain;
    margin-top: -0.12rem;
}

.cancelimg {
    width: 0.24rem;
    height: 0.24rem;
    object-fit: contain;
}

.popup_content {
    padding: 0 0.24rem;
}

.popup_title_top {
    width: 100%;
    height: 100%;
    text-align: right;
}

.popup_button_tryout {
    width: 2.5rem;
    height: 0.48rem;
    line-height: 0.48rem;
    font-weight: 500;
    color: #7e510f;
    border-radius: 0.24rem;
    font-size: 0.16rem;
    font-weight: 500;
    box-shadow: 0 2px 5px 0 rgba(0, 0, 0, 0.15);
    background-image: linear-gradient(to right, #fff8ea 5%, #f7e2c9 97%);
}

.popup_button_buy {
    height: 0.22rem;
    margin: 0.16rem 0 0.24rem;
    font-size: 0.16rem;
    font-weight: 500;
    text-align: center;
    color: #7e510f;
}

.popup_button_pay {
    margin-bottom: 0.24rem;
}

.popup_tips {
    margin: 0.16rem 0 0.1rem;
    /* width: 0.4rem; */
    height: 0.28rem;
    font-size: 0.2rem;
    font-weight: 600;
    text-align: center;
    color: #7e510f;
}

.popup_tipstext {
    /* width: 272px; */
    /* height: 0.63rem; */
    line-height: 0.21rem;
    font-size: 0.15rem;
    color: #7e510f;
    margin-bottom: 0.2rem;
    text-align: left;
}

.popup_tipstext_pay {
    margin-bottom: 0.16rem;
}

.footer {
    height: 6.69%;
}