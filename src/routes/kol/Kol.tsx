/* eslint-disable complexity */
import React from "react";
import { RouteComponentProps } from "react-router-dom";
import { urlParams } from "../../utils/index";
import KolMain from "../../components/kolMain/KolMain";
const Kol: React.FC<RouteComponentProps> = (props) => {
  const token = urlParams(props.history.location.search.slice(1)).token;
  const pathname = props.location.pathname;
  const search = props.location.search;
  return (
    <KolMain
      pathname={pathname}
      search={search}
      token={token}
      downloadUrl="https://tantanapp.com/tantan_Douyinkol02.apk"
    ></KolMain>
  );
};
export default Kol;
