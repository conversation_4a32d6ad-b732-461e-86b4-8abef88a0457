@import "~sass-rem";

$rem-baseline: 37.5px;

* {
  box-sizing: border-box;
}

.pageContainer {
  background-color: #fd7a65;
  padding-top: rem(30px);
  min-height: 100vh;
  padding-bottom: rem(30px);

  .cardContainer {
    margin: rem(0 19px 0 13px);
    min-height: rem(233px);
  }
  
  .cardContainer1 {
    margin: rem(20px 19px 0 13px);
    min-height: rem(359px);
  }

  .cardShadow {
    height: rem(233px);
    margin: rem(-228px 13px 0 19px);
    background-color: #f15e47;
    border-radius: 8px;
  }

  .cardShadow1 {
    height: rem(359px);
    margin: rem(-354px 13px 0 19px);
    background-color: #f15e47;
    border-radius: 8px;
  }

  .ruleContainer {
    margin: rem(16px);
    margin-bottom: rem(20px);
  }

  .ruleText {
    word-break: break-all;
    font-family: PingFangSC, "PingFang SC", PingFangSC-Regular, "Helvetica Neue", -apple-system, BlinkMacSystemFont, STHeiti,"Microsoft Yahei",<PERSON><PERSON><PERSON>,<PERSON><PERSON>,sans-serif;
    font-size: 12px;
  }
}