import React from 'react';
import CardWithTitle from '../../components/friendInvite2cardWithTitle/cardWithTitle';
import styles from './FriendInvite2Rule.module.scss';
import 'amfe-flexible/index.min.js'

const ActivityRule: React.FC = () => {
  return <div className={styles.pageContainer}>
    <CardWithTitle className={styles.cardContainer} titleText="活动规则">
      <div className={styles.ruleContainer}>
        <div className={styles.ruleText}>1、每成功邀请一位新用户（以下或称“好友”）首次下载并使用本App，您将获得一定时长的VIP特权。</div>
        <div className={styles.ruleText}>2、被您邀请的好友首次下载并打开探探APP之日起2天内填写您的邀请码的，则视为您的邀请好友。（备注：邀请码为本平台为每位用户设立的独立ID号，仅用于邀请好友时使用，同一手机注册两个账号将无法生效）。</div>
        <div className={styles.ruleText}>3、您邀请的好友必须头像通过审核，才算邀请成功</div>
        <div className={styles.ruleText}>4、您最多可获得90天的免费VIP特权。</div>
        <div className={styles.ruleText}>5、活动时间：2021年06月25日 - 2021年07月02日</div>
      </div>
      <div className={styles.ruleShadow}></div>
    </CardWithTitle>
    <div className={styles.cardShadow}></div>
    <CardWithTitle className={styles.cardContainer1} titleText="免责声明">
      <div className={styles.ruleContainer}>
        <div className={styles.ruleText}>1、同一自然人用户仅限一个探探账号参加活动，同一手机终端、支付账号、实名信息、手机号、IP地址等与用户身份密切相关的信息，其中任意一项或数项存在相同、相似、或非真实有效等情形的，均可能被认定为同一自然人用户。</div>
        <div className={styles.ruleText}>2、前述同一自然人用户直接或间接控制多个账号参加活动的行为属于同一自然人用户行为，若用户存在此类行为，将导致用户无法继续参加活动及/或无法领取活动特权奖励。</div>
        <div className={styles.ruleText}>3、账号被封禁的用户无法参与本次活动。</div>
        <div className={styles.ruleText}>4、若用户在活动期间违反平台规则或用户采取不正当手段或其它可能损害探探正常活动秩序的行为时，探探有权取消用户参与活动的资格，收回活动权益，并有权追究法律责任。</div>
        <div className={styles.ruleText}>5、用户参与本次活动即视为对全部活动规则的接受，并同意接受活动规则的约束。</div>
      </div>
      <div className={styles.ruleShadow}></div>
    </CardWithTitle>
    <div className={styles.cardShadow1}></div>
  </div>
}

export default ActivityRule;
