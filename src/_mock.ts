/* eslint-disable max-lines */
import { AxiosInstance } from 'axios'

export function mock(ajax: AxiosInstance) {
  if (process.env.NODE_ENV !== 'production') {
    const MockAdapter = require('axios-mock-adapter')
    const mock = new MockAdapter(ajax)
    mock.onGet('/v2/topics').reply(200, {
      meta: {
        code: 200,
      },
      data: {
        popularTopics: [
          {
            id: '2324680203571590421',
            owner: {
              id: '-10010',
              type: 'user',
            },
            name: '你会告诉另一半你的情史吗？\t',
            description:
              '确认恋爱关系后，难免会被现任问及过去的感情经历。说了，风险巨大，对方难免心生芥蒂；不说，又害怕恋人从别处知道，后果更严重。这个问题见仁见智，没有标准答案。如果是你，会告诉另一半你的情史吗？\n',
            icon:
              'https://auto.tancdn.com/v1/raw/d14dcbb2-4f0c-44b5-8629-12e2f19a78e80505.jpg',
            level: 'default',
            category: '',
            topicType: 'vote',
            landingPage: '',
            createdTime: '2020-06-05T09:14:12.609940+0000',
            endTime: '1754-08-30T22:43:41.128654+0000',
            type: 'topic',
            status: 'online',
            momentCounter: 0,
            commentCounter: 1566,
            voteCounter: 14065,
            options: [
              {
                id: '317',
                values: '会',
                haveliked: false,
                counter: 10748,
              },
              {
                id: '318',
                values: '不会',
                haveliked: false,
                counter: 2639,
              },
              {
                id: '319',
                values: '评论区说说你的理由',
                haveliked: false,
                counter: 678,
              },
            ],
            ptPriority: 1, // 大家都在聊排序
            labelId: 1, // 标识
            headIcon:
              'http://img1.imgtn.bdimg.com/it/u=2575593204,2727923123&fm=26&gp=0.jpg', // 头图
          },
          {
            id: '292905983926076692',
            owner: {
              id: '-10010',
              type: 'user',
            },
            name: '猫狗们的迷惑行为大赏',
            description:
              '记录你和汪星人，喵星人的快乐生活，猫猫狗狗们的迷惑行为瞬间～～',
            icon:
              'https://auto.tancdn.com/v1/raw/edb4610f-9f13-42a9-bac1-6043ee1592db0505.jpg',
            level: 'default',
            category: '',
            topicType: 'topic',
            landingPage: '',
            createdTime: '2020-06-05T05:58:25.108208+0000',
            endTime: '1754-08-30T22:43:41.128654+0000',
            type: 'topic',
            status: 'online',
            momentCounter: 10300,
            commentCounter: 0,
            voteCounter: 0,
            options: null,
            ptPriority: 2,
            labelId: 2,
            headIcon:
              'http://img5.imgtn.bdimg.com/it/u=3736014545,352509486&fm=26&gp=0.jpg',
          },
          {
            id: '292905983926076693',
            owner: {
              id: '-10010',
              type: 'user',
            },
            name: '猫狗们的迷惑行为大赏',
            description:
              '记录你和汪星人，喵星人的快乐生活，猫猫狗狗们的迷惑行为瞬间～～',
            icon:
              'https://auto.tancdn.com/v1/raw/edb4610f-9f13-42a9-bac1-6043ee1592db0505.jpg',
            level: 'default',
            category: '',
            topicType: 'topic',
            landingPage: '',
            createdTime: '2020-06-05T05:58:25.108208+0000',
            endTime: '1754-08-30T22:43:41.128654+0000',
            type: 'topic',
            status: 'online',
            momentCounter: 3300,
            commentCounter: 0,
            voteCounter: 0,
            options: null,
            ptPriority: 3,
            labelId: 3,
            headIcon:
              'http://t7.baidu.com/it/u=3616242789,1098670747&fm=79&app=86&f=JPEG?w=900&h=1350',
          },
          {
            id: '292905983926076694',
            owner: {
              id: '-10010',
              type: 'user',
            },
            name: '猫狗们的迷惑行为大赏',
            description:
              '记录你和汪星人，喵星人的快乐生活，猫猫狗狗们的迷惑行为瞬间～～',
            icon:
              'https://auto.tancdn.com/v1/raw/edb4610f-9f13-42a9-bac1-6043ee1592db0505.jpg',
            level: 'default',
            category: '',
            topicType: 'topic',
            landingPage: '',
            createdTime: '2020-06-05T05:58:25.108208+0000',
            endTime: '1754-08-30T22:43:41.128654+0000',
            type: 'topic',
            status: 'online',
            momentCounter: 1300,
            commentCounter: 0,
            voteCounter: 0,
            options: null,
            ptPriority: 4,
            labelId: 4,
            headIcon:
              'http://img5.imgtn.bdimg.com/it/u=1409042039,4071691606&fm=26&gp=0.jpg',
          },
          {
            id: '292905983926076695',
            owner: {
              id: '-10010',
              type: 'user',
            },
            name: '猫狗们的迷惑行为大赏',
            description:
              '记录你和汪星人，喵星人的快乐生活，猫猫狗狗们的迷惑行为瞬间～～',
            icon:
              'https://auto.tancdn.com/v1/raw/edb4610f-9f13-42a9-bac1-6043ee1592db0505.jpg',
            level: 'default',
            category: '',
            topicType: 'topic',
            landingPage: '',
            createdTime: '2020-06-05T05:58:25.108208+0000',
            endTime: '1754-08-30T22:43:41.128654+0000',
            type: 'topic',
            status: 'online',
            momentCounter: 15300,
            commentCounter: 0,
            voteCounter: 0,
            options: null,
            ptPriority: 5,
            labelId: 2,
            headIcon:
              'http://img0.imgtn.bdimg.com/it/u=3907030802,3322597418&fm=26&gp=0.jpg',
          },
          {
            id: '292905983926076696',
            owner: {
              id: '-10010',
              type: 'user',
            },
            name: '猫狗们的迷惑行为大赏',
            description:
              '记录你和汪星人，喵星人的快乐生活，猫猫狗狗们的迷惑行为瞬间～～',
            icon:
              'https://auto.tancdn.com/v1/raw/edb4610f-9f13-42a9-bac1-6043ee1592db0505.jpg',
            level: 'default',
            category: '',
            topicType: 'topic',
            landingPage: '',
            createdTime: '2020-06-05T05:58:25.108208+0000',
            endTime: '1754-08-30T22:43:41.128654+0000',
            type: 'topic',
            status: 'online',
            momentCounter: 300,
            commentCounter: 0,
            voteCounter: 0,
            options: null,
            ptPriority: 6,
            labelId: 1,
            headIcon:
              'https://t8.baidu.com/it/u=3571592872,3353494284&fm=79&app=86&size=h300&n=0&g=4n&f=jpeg?sec=1592210718&t=16ed5cfba1d75fcd34940f048e23758b',
          },
        ],
        hotTopics: [
          {
            id: '2929059839260763',
            owner: {
              id: '-10010',
              type: 'user',
            },
            name: '猫狗们的迷惑行为大赏',
            description:
              '记录你和汪星人，喵星人的快乐生活，猫猫狗狗们的迷惑行为瞬间～～',
            icon:
              'https://auto.tancdn.com/v1/raw/edb4610f-9f13-42a9-bac1-6043ee1592db0505.jpg',
            level: 'default',
            category: '',
            topicType: 'topic',
            landingPage: '',
            createdTime: '2020-06-05T05:58:25.108208+0000',
            endTime: '1754-08-30T22:43:41.128654+0000',
            type: 'topic',
            status: 'online',
            momentCounter: 10300,
            commentCounter: 0,
            voteCounter: 0,
            options: null,
            moments: [
              {
                // 动态列表，图片信息
                id: '2326285194158141382',
                owner: {
                  id: '75842298',
                  type: 'user',
                },
                value: '拌冷面这个东西是我家乡特色，还是大家都吃过？',
                location: {
                  name: '',
                  address: '',
                  phone: '',
                  coordinates: [39.9267, 116.5974],
                },
                messages: {
                  count: 4,
                  limit: 100,
                  ids: [
                    '2326339407403062504',
                    '2326285998343729498',
                    '2326285746992048404',
                  ],
                  links: {
                    previous:
                      'http://core.tantanapp.com/v1/users/75842298/moments/2326285194158141382/messages?since=2326339407403062504&limit=100',
                    next: null,
                  },
                },
                likes: {
                  count: 8,
                  limit: 10,
                  ids: [
                    '29270720',
                    '124372217',
                    '179141197',
                    '244390218',
                    '292716031',
                    '305973379',
                    '310380447',
                    '361098385',
                  ],
                  links: {
                    previous: null,
                    next:
                      'http://core.tantanapp.com/v1/users/75842298/moments/2326285194158141382/likes?offset=8&limit=10',
                  },
                },
                haveLiked: false,
                media: [
                  {
                    url:
                      'https://auto.tancdn.com/v1/images/eyJpZCI6IkdLRjUyQklNV1Q1Rk1KVjdRTkJIWFE2MkNDMk41NTA1IiwidyI6MTQ0MCwiaCI6MTQ0MCwiZCI6MCwibXQiOiJpbWFnZS9qcGVnIiwiZGgiOjE3MzczODQyNDY0MzI0NjAyOTY2LCJhYiI6MH0',
                    mediaType: 'image/jpeg',
                    size: [1440, 1440],
                    duration: 0,
                    parameters: [
                      {
                        name: 'format',
                        values: [
                          '128x128',
                          '180x180',
                          '640x640',
                          'blurRetina',
                          'max_180xX',
                          'max_720xX',
                          'max_960xX',
                          '480x480',
                          'blur',
                          'max_480xX',
                          'max_640xX',
                        ],
                      },
                    ],
                    attachments: [],
                  },
                ],
                type: 'moment',
                createdTime: '2020-06-07T13:00:27.009118+0000',
                landingPage: '',
                settings: {
                  visibility: 'everyone',
                  muted: false,
                },
                musicId: '0',
                cameraStickerIds: null,
                status: '',
                momentType: 'default',
                topics: [],
                views: 808,
              },
            ],
          },
          {
            id: '292905983926076694',
            owner: {
              id: '-10010',
              type: 'user',
            },
            name: '猫狗们的迷惑行为大赏',
            description:
              '记录你和汪星人，喵星人的快乐生活，猫猫狗狗们的迷惑行为瞬间～～',
            icon:
              'https://auto.tancdn.com/v1/raw/edb4610f-9f13-42a9-bac1-6043ee1592db0505.jpg',
            level: 'default',
            category: '',
            topicType: 'topic',
            landingPage: '',
            createdTime: '2020-06-05T05:58:25.108208+0000',
            endTime: '1754-08-30T22:43:41.128654+0000',
            type: 'topic',
            status: 'online',
            momentCounter: 10300,
            commentCounter: 0,
            voteCounter: 0,
            options: null,
            moments: [
              {
                // 动态列表，图片信息
                id: '2326285194158141382',
                owner: {
                  id: '75842298',
                  type: 'user',
                },
                value: '拌冷面这个东西是我家乡特色，还是大家都吃过？',
                location: {
                  name: '',
                  address: '',
                  phone: '',
                  coordinates: [39.9267, 116.5974],
                },
                messages: {
                  count: 4,
                  limit: 100,
                  ids: [
                    '2326339407403062504',
                    '2326285998343729498',
                    '2326285746992048404',
                  ],
                  links: {
                    previous:
                      'http://core.tantanapp.com/v1/users/75842298/moments/2326285194158141382/messages?since=2326339407403062504&limit=100',
                    next: null,
                  },
                },
                likes: {
                  count: 8,
                  limit: 10,
                  ids: [
                    '29270720',
                    '124372217',
                    '179141197',
                    '244390218',
                    '292716031',
                    '305973379',
                    '310380447',
                    '361098385',
                  ],
                  links: {
                    previous: null,
                    next:
                      'http://core.tantanapp.com/v1/users/75842298/moments/2326285194158141382/likes?offset=8&limit=10',
                  },
                },
                haveLiked: false,
                media: [
                  {
                    url:
                      'https://auto.tancdn.com/v1/images/eyJpZCI6IkdLRjUyQklNV1Q1Rk1KVjdRTkJIWFE2MkNDMk41NTA1IiwidyI6MTQ0MCwiaCI6MTQ0MCwiZCI6MCwibXQiOiJpbWFnZS9qcGVnIiwiZGgiOjE3MzczODQyNDY0MzI0NjAyOTY2LCJhYiI6MH0',
                    mediaType: 'image/jpeg',
                    size: [1440, 1440],
                    duration: 0,
                    parameters: [
                      {
                        name: 'format',
                        values: [
                          '128x128',
                          '180x180',
                          '640x640',
                          'blurRetina',
                          'max_180xX',
                          'max_720xX',
                          'max_960xX',
                          '480x480',
                          'blur',
                          'max_480xX',
                          'max_640xX',
                        ],
                      },
                    ],
                    attachments: [],
                  },
                ],
                type: 'moment',
                createdTime: '2020-06-07T13:00:27.009118+0000',
                landingPage: '',
                settings: {
                  visibility: 'everyone',
                  muted: false,
                },
                musicId: '0',
                cameraStickerIds: null,
                status: '',
                momentType: 'default',
                topics: [],
                views: 808,
              },
            ],
          },
        ],
      },
      pagination: {
        total: 2,
        limit: 2,
        links: {
          previous:
            'http://core.tantanapp.com/v2/topics?search=\u0026since=2322460564128209181\u0026limit=20\u0026',
          next:
            'http://core.tantanapp.com/v2/topics?search=\u0026until=205278898592679085\u0026limit=20\u0026',
        },
      },
    })
  }
}

export function frientInvite2Mock(ajax: AxiosInstance) {
  const MockAdapter = require('axios-mock-adapter')
  const mock = new MockAdapter(ajax)
  mock.onGet('/v2/h5/invite/succeed_record').reply(200, succeed_record[Math.floor(Math.random() * 3)]);
  mock.onPost('/v2/h5/invite/record').reply(200, record[Math.floor(Math.random() * 2)]);
  mock.onGet('/v2/h5/invite/board').reply(200, board[0])
}

export const succeed_record = [
  {
    "meta": {
        "code": 200,
        "message": "OK"
    },
    "data": {
        "records": [],//邀请成功记录
    }
  },
  {
    "meta": {
        "code": 200,
        "message": "OK"
    },
    "data": {
        "records": [
          "http://auto.tancdn.com/v1/images/eyJpZCI6IjVUVVVEWFBRQzU2TUdOWlNZMzVFTUs0REFRTkJIQTA3IiwidyI6OTYwLCJoIjo5NjAsImQiOjAsIm10IjoiaW1hZ2UvanBlZyIsImRoIjoxNTc0MDc1NjAzMjg3MDc1NDY3MH0",//被邀请人的头像地址
        ],//邀请成功记录
    }
  },
  {
    "meta": {
        "code": 200,
        "message": "OK"
    },
    "data": {
        "records": [
          "http://auto.tancdn.com/v1/images/eyJpZCI6IjVUVVVEWFBRQzU2TUdOWlNZMzVFTUs0REFRTkJIQTA3IiwidyI6OTYwLCJoIjo5NjAsImQiOjAsIm10IjoiaW1hZ2UvanBlZyIsImRoIjoxNTc0MDc1NjAzMjg3MDc1NDY3MH0",//被邀请人的头像地址
          "http://auto.tancdn.com/v1/images/eyJpZCI6IjVUVVVEWFBRQzU2TUdOWlNZMzVFTUs0REFRTkJIQTA3IiwidyI6OTYwLCJoIjo5NjAsImQiOjAsIm10IjoiaW1hZ2UvanBlZyIsImRoIjoxNTc0MDc1NjAzMjg3MDc1NDY3MH0",
        ],//邀请成功记录
    }
  }
]

export const record = [
  {
    "meta": {
        "code": 200,
        "message": "OK"
    },
    "data": {
        "records": [
            {
                "img": "http://auto.tancdn.com/v1/images/eyJpZCI6IjVUVVVEWFBRQzU2TUdOWlNZMzVFTUs0REFRTkJIQTA3IiwidyI6OTYwLCJoIjo5NjAsImQiOjAsIm10IjoiaW1hZ2UvanBlZyIsImRoIjoxNTc0MDc1NjAzMjg3MDc1NDY3MH0",//头像
                "name": "asdasd",//昵称
                "status": 0,//用户状态：0表示未完善资料，1表示已完善资料，未通过审核，2表示已通过审核，-1表示是老用户
            },
            {
                "img": "http://auto.tancdn.com/v1/images/eyJpZCI6IjVUVVVEWFBRQzU2TUdOWlNZMzVFTUs0REFRTkJIQTA3IiwidyI6OTYwLCJoIjo5NjAsImQiOjAsIm10IjoiaW1hZ2UvanBlZyIsImRoIjoxNTc0MDc1NjAzMjg3MDc1NDY3MH0",//头像
                "name": "asdasd",//昵称
                "status": 1,//用户状态：0表示未完善资料，1表示已完善资料，未通过审核，2表示已通过审核，-1表示是老用户
            },
            {
                "img": "http://auto.tancdn.com/v1/images/eyJpZCI6IjVUVVVEWFBRQzU2TUdOWlNZMzVFTUs0REFRTkJIQTA3IiwidyI6OTYwLCJoIjo5NjAsImQiOjAsIm10IjoiaW1hZ2UvanBlZyIsImRoIjoxNTc0MDc1NjAzMjg3MDc1NDY3MH0",//头像
                "name": "asdasd",//昵称
                "status": 2,//用户状态：0表示未完善资料，1表示已完善资料，未通过审核，2表示已通过审核，-1表示是老用户
            },
            {
                "img": "http://auto.tancdn.com/v1/images/eyJpZCI6IjVUVVVEWFBRQzU2TUdOWlNZMzVFTUs0REFRTkJIQTA3IiwidyI6OTYwLCJoIjo5NjAsImQiOjAsIm10IjoiaW1hZ2UvanBlZyIsImRoIjoxNTc0MDc1NjAzMjg3MDc1NDY3MH0",//头像
                "name": "asdasd",//昵称
                "status": 2,//用户状态：0表示未完善资料，1表示已完善资料，未通过审核，2表示已通过审核，-1表示是老用户
            },
            {
                "img": "http://auto.tancdn.com/v1/images/eyJpZCI6IjVUVVVEWFBRQzU2TUdOWlNZMzVFTUs0REFRTkJIQTA3IiwidyI6OTYwLCJoIjo5NjAsImQiOjAsIm10IjoiaW1hZ2UvanBlZyIsImRoIjoxNTc0MDc1NjAzMjg3MDc1NDY3MH0",//头像
                "name": "asdasdsdassasda",//昵称
                "status": -1,//用户状态：0表示未完善资料，1表示已完善资料，未通过审核，2表示已通过审核，-1表示是老用户
            },
        ], //邀请记录，一页20条记录
        "total": 30,//总邀请记录数
    }
  },
  {
    "meta": {
        "code": 200,
        "message": "OK"
    },
    "data": {
        "records": [
        ], //邀请记录，一页20条记录
        "total": 0,//总邀请记录数
    }
  },
];

export const board = [
  {
    "meta": {
        "code": 200,
        "message": "OK"
    },
    "data": {
        "board": [
            {
                "name": "a***",//昵称
                "count": 20,//邀请人数
            },
            {
                "name": "a*",
                "count": 20,
            },
            {
                "name": "v**...",
                "count": 19,
            },
            {
                "name": "v**...",
                "count": 19,
            },
            {
                "name": "v**...",
                "count": 19,
            },
            {
                "name": "v**...",
                "count": 19,
            },
            {
                "name": "v**...",
                "count": 19,
            },
            {
                "name": "v**...",
                "count": 19,
            },
            {
                "name": "v**...",
                "count": 19,
            },
            {
                "name": "v**...",
                "count": 19,
            },
            {
                "name": "v**...",
                "count": 19,
            },
            {
                "name": "v**...",
                "count": 19,
            },
            {
                "name": "v**...",
                "count": 19,
            },
            {
                "name": "v**...",
                "count": 19,
            },
            {
                "name": "v**...",
                "count": 19,
            },
            {
                "name": "v**...",
                "count": 19,
            },
            {
                "name": "v**...",
                "count": 19,
            },
            {
                "name": "v**...",
                "count": 19,
            },
            {
                "name": "v**...",
                "count": 19,
            },
            {
                "name": "v**...",
                "count": 19,
            },
        ],//邀请排行榜，top20
        "invite_code": "ajHdbaFbak",//邀请码，10位大小写字母
    }
  }
]
