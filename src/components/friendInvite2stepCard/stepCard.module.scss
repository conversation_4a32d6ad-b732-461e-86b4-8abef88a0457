@import "~sass-rem";

$rem-baseline: 37.5px;

img {
  pointer-events:none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  -webkit-touch-callout: none;
}

.stepCard {
  height: rem(154px);
  position: relative;
  margin-top: rem(20px);
}

.stepCardShadow {
  width: rem(343px);
  height: rem(154px);
  // cardWithTitle top 20px
  margin: rem(-149px 10px 0 22px);
  border-radius: 8px;
  background-color: #f15e47;
}

.stepContainer {
  position: absolute;
  top: rem(66px);
  left: 0;
  right: 0;

  .stepIconContainer {
    display: flex;
    align-items: flex-end;
    justify-content: space-around;
  }

  .stepIcon {
    border-radius: 50%;
    background-color: #f7c94f;
    width: rem(34px);
    height: rem(34px);
    line-height: rem(34px);
    text-align: center;
    font-family: PingFangSC, "PingFang SC", PingFangSC-Regular, "Helvetica Neue", -apple-system, BlinkMacSystemFont, STHeiti,"Microsoft Yahei",<PERSON><PERSON><PERSON>,<PERSON>un,sans-serif;
    font-size: 16px;
    font-weight: 500;
  }
}


.stepTextStyle {
  font-family: PingFangSC, "PingFang SC", PingFangSC-Regular, "Helvetica Neue", -apple-system, BlinkMacSystemFont, STHeiti,"Microsoft Yahei",Tahoma,Simsun,sans-serif;
  color: #212121;
  font-size: 12px;
  padding-top: rem(7px);
}
.stepArrow {
  width: rem(16px);
  height: rem(16px);
  margin-bottom: 1px;
}
.stepItem {
  flex: 1;
  width: rem(72px);
  word-break: keep-all;
  display: flex;
  flex-direction: column;
  align-items: center;
}

