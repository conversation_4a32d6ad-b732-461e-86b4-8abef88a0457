import React from 'react';
import ct from 'classnames';
import CardWithTitle from '../friendInvite2cardWithTitle/cardWithTitle';
import styles from './stepCard.module.scss';

const arrow = 'https://auto.tancdn.com/v1/raw/9215def5-ab8d-4b64-b6f1-97a5b8b8f09e0607.png';

const StepCard: React.FC = () => {
  return (
    <>
      <CardWithTitle className={styles.stepCard} titleText="3 步解锁特权">
      <>
        <div className={styles.stepContainer}>
          <div className={styles.stepIconContainer}>
            <div className={styles.stepItem}>
              <div className={styles.stepIcon}>1</div>
              <div className={styles.stepTextStyle}>分享链接给好友</div>
            </div>
            <img className={ct(styles.stepArrow, styles.arrow1)} src={arrow} alt=""/>
            <div className={styles.stepItem}>
              <div className={styles.stepIcon}>2</div>
              <div className={styles.stepTextStyle}>好友下载注册</div>
            </div>
            <img className={ct(styles.stepArrow, styles.arrow2)} src={arrow} alt=""/>
            <div className={styles.stepItem}>
              <div className={styles.stepIcon}>3</div>
              <div className={styles.stepTextStyle}>资料通过审核</div>
            </div>
          </div>
        </div>
      </>
      </CardWithTitle>
      <div className={styles.stepCardShadow}></div>
    </>
  );
}

export default StepCard;
