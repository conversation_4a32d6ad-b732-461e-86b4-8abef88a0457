@import "~sass-rem";

$rem-baseline: 37.5px;

@font-face {
  font-family: DINAlternate;
  src: url('../../assets/friend-invite2/din-alternate-bold.ttf');
}

img {
  pointer-events:none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  -webkit-touch-callout: none;
}

.inviteCardContainer {
  z-index: 1;
  position: relative;
  margin-top: rem(20px);
  margin-left: rem(16px);
  margin-right: rem(16px);

  .inviteHeader {
    display: flex;

    .headerItem {
      flex: 1;
      border-radius: 8px 8px 0 0;
      display: flex;
      align-items: center;
      justify-content: center;
      border: solid 1px #292d2e;
      height: rem(48px);
    }

    .headerText {
      font-family: PingFangSC, "PingFang SC", PingFangSC-Regular, "Helvetica Neue", -apple-system, BlinkMacSystemFont, STHeiti,"Microsoft Yahei",<PERSON><PERSON><PERSON>,<PERSON><PERSON>,sans-serif;
      font-size: 16px;
      font-weight: 600;
    }

    .activeItem {
      background-color: #ffffff;
      color: #212121;
    }

    .notActiveItem {
      background-color: #fe5351;
      color: #ffffff;
    }
  }

  .inviteCardBody {
    border-radius: 0 0 8px 8px;
    border: solid 1px #292d2e;
    border-top: 0;
    height: rem(310px);
    overflow: scroll;
    background-color: #FFF;
  }
}

.rankContainer {
  .rankTitle {
    display: flex;
    height: rem(32px);
    background-color: #f7f7f7;
  }

  .rankTitleText {
    font-family: PingFangSC, "PingFang SC", PingFangSC-Regular, "Helvetica Neue", -apple-system, BlinkMacSystemFont, STHeiti,"Microsoft Yahei",Tahoma,Simsun,sans-serif;
    font-size: 12px;
    color: #212121;
    line-height: rem(32px);
    float: left;
  }

  .titleText1 {
    margin-left: rem(15px);
  }
  .titleText2 {
    flex: 1;
    width: rem(60px);
    text-align: center;
  }
  .titleText3 {
    float:  right;
    width: rem(50px);
    text-align: center;
    margin-right: 16px;
    word-break: keep-all;
  }

  .rankItem {
    &:nth-child(odd) {
      background-color: #f7f7f7;
    }
    &:nth-child(even) {
      background-color: #FFF;
    }

    height: rem(56px);
    display: flex;
    align-items: center;

    .rankNumberContainer {
      width: rem(22px);
      margin-left: rem(15px);
      text-align: center;
      font-family: "DINAlternate";
      font-size: 16px;
    }

    .rankNormalNum {
      padding-top: 2px;
      text-align: center;
      font-family: "DINAlternate";
      font-size: 16px;
      color: #212121;;
    }

    .rankNumberCircular {
      font-family: "DINAlternate";
      padding-top: 1px;
      width: rem(22px);
      height: rem(22px);
      border: solid 1px #292d2e;
      background-color: #fe5351;
      border-radius: 50%;
      color: #FFF;;
      position: relative;
      display: flex;
      align-items: center;
      justify-content: center;
    }

    .circularShadow {
      width: rem(22px);
      height: rem(22px);
      border: solid 1px #292d2e;
      border-radius: 50%;
      margin-top: rem(-20px);
      margin-left: 2px;
    }

    .nameContainer {
      // margin-left: rem(50px);
      // width: rem(60px);
      flex: 1;
      text-align: center;
      font-family: PingFangSC, "PingFang SC", PingFangSC-Regular, "Helvetica Neue", -apple-system, BlinkMacSystemFont, STHeiti,"Microsoft Yahei",Tahoma,Simsun,sans-serif;
      font-size: 14px;
    }

    .inviteNumContainer {
      margin-left: auto;
      margin-right: rem(16px);
      color: #212121;
      font-family: PingFangSC, "PingFang SC", PingFangSC-Regular, "Helvetica Neue", -apple-system, BlinkMacSystemFont, STHeiti,"Microsoft Yahei",Tahoma,Simsun,sans-serif;
      font-size: 14px;
      width: rem(50px);
      text-align: center;
    }

    .inviteNum {
      color: #fe5351;
    }
  }

  .tip {
    font-family: PingFangSC, "PingFang SC", PingFangSC-Regular, "Helvetica Neue", -apple-system, BlinkMacSystemFont, STHeiti,"Microsoft Yahei",Tahoma,Simsun,sans-serif;
    font-size: 12px;
    text-align: center;
    color: #888888;
    justify-content: center;
  }
}

.myInviteContainer {

  .myInviteTitle {
    height: rem(32px);
    background-color: #f7f7f7;
  }

  .myInviteTitleText {
    font-family: PingFangSC, "PingFang SC", PingFangSC-Regular, "Helvetica Neue", -apple-system, BlinkMacSystemFont, STHeiti,"Microsoft Yahei",Tahoma,Simsun,sans-serif;
    font-size: 12px;
    color: #212121;
    line-height: rem(32px);
    float: left;
  }

  .title1 {
    float: right;
    width: rem(50px);
    text-align: center;
    margin-right: rem(72px);
  }
  .title2 {
    float: right;
    width: rem(50px);
    text-align: center;
    margin-right: rem(26px);
  }

  .myInviteItem {
    display: flex;
    align-items: center;
    height: rem(56px);
  
    &:nth-child(odd) {
      background-color: #f7f7f7;
      .correctIcon {
        background-color: #f7f7f7;
      }
    }
    &:nth-child(even) {
      background-color: #FFF;
      .correctIcon {
        background-color: #FFF;
      }
    }
    
    .userAvatar {
      width: rem(24px);
      height: rem(24px);
      margin-left: rem(16px);
      border-radius: 50%;
    }

    .userName {
      margin-left: rem(10px);
      width: rem(80px);
      word-break: keep-all;
      white-space: nowrap;
      text-overflow: ellipsis;
      overflow: hidden;
      font-family: PingFangSC, "PingFang SC", PingFangSC-Regular, "Helvetica Neue", -apple-system, BlinkMacSystemFont, STHeiti,"Microsoft Yahei",Tahoma,Simsun,sans-serif;
      font-size: 14px;
      color: #212121;
    }

    .correctContainer {
      display: flex;
      align-items: center;
      flex: 1;
      height: 100%;
      justify-content: center;
    }

    .correctBlock {
      width: rem(50px);
      display: flex;
      align-items: center;
      justify-content: center;
      z-index: 1;
    }

    .correctText {
      font-family: PingFangSC, "PingFang SC", PingFangSC-Regular, "Helvetica Neue", -apple-system, BlinkMacSystemFont, STHeiti,"Microsoft Yahei",Tahoma,Simsun,sans-serif;
      font-size: 12px;
      color: #212121;
    }

    .correctIcon {
      // &:nth-child(odd) {
      //   background-color: #f7f7f7;
      // }
      // &:nth-child(even) {
      //   background-color: #FFF;
      // }

      width: rem(16px);
      height: rem(16px);
    }

    .correct1 {
      margin-left: auto;
    }

    .correct2 {
      float: right;
      margin-right: rem(26px);
    }

    .line {
      border-top: 1px solid #292d2e;
      width: rem(106px);
      margin-left: rem(-19px);
      margin-right: rem(-19px);
    }

    .dashedLine {
      border-top: 1px dashed #292d2e;
      width: rem(106px);
      margin-left: rem(-19px);
      margin-right: rem(-19px);
    }
  }

  .emptyInvite {
    width: 100%;
    height: 100%;
    font-size: 12px;
    color: #212121;
    display: flex;
    justify-content: center;
    align-items: center;
  }
}

.inviteCardShadow {
  margin: rem(0 10px 0 22px);
  border-radius: 8px;
  background-color: #f15e47;
  z-index: -10;
}
