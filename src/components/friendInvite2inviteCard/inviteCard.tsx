import React, { useState, useCallback, useRef, useEffect } from 'react';
import styles from './inviteCard.module.scss';
import ct from 'classnames';

const correct = 'https://auto.tancdn.com/v1/raw/d6c34a68-c688-4426-83b4-bf557d69a80b0607.png';
const emptyCorrect = 'https://auto.tancdn.com/v1/raw/e29c1881-d361-44c9-8549-9d2db3f85bf80607.png';

const SpecialRankItem = ({
  rank,
  name,
  inviteNum,
}: {
  rank: number,
  name: string,
  inviteNum: number
}) => {
  return (
    <div className={styles.rankItem}>
      <div className={styles.rankNumberContainer}>
        <div className={styles.rankNumberCircular}>{rank}</div>
        <div className={styles.circularShadow}></div>
      </div>
      <div className={styles.nameContainer}>
        {name}
      </div>
      <div className={styles.inviteNumContainer}>
        <span className={styles.inviteNum}>{inviteNum}</span>人
      </div>
    </div>
  )
}

const RankItem = ({
  rank,
  name,
  inviteNum,
}: {
  rank: number,
  name: string,
  inviteNum: number
}) => {
  return (
    <div className={styles.rankItem}>
      <div className={styles.rankNumberContainer}>
        <div className={styles.rankNormalNum}>{rank}</div>
      </div>
      <div className={styles.nameContainer}>
        {name}
      </div>
      <div className={styles.inviteNumContainer}>
        <span className={styles.inviteNum}>{inviteNum}</span>人
      </div>
    </div>
  )
}

const emptyImg = 'data:image/gif;base64,R0lGODlhAQABAIAAAP7//wAAACH5BAAAAAAALAAAAAABAAEAAAICRAEAOw==';

const InviteUser = ({
  img,
  name,
  status
}: {
  img: string,
  name: string,
  status: number,
}) => {
  let userStatus: JSX.Element;
  switch(status) {
    case -1:
      userStatus = (
        <div className={styles.correctContainer}>
          <span className={styles.correctText}>Ta已经在使用在探探啦</span>
        </div>
      );
      break;
    case 0:
      userStatus = (
        <div className={styles.correctContainer}>
          <div className={ct(styles.correctBlock, styles.correct1)}>
            <img onError={(e) => {e.currentTarget.src = emptyImg}} className={styles.correctIcon} src={emptyCorrect} alt=""/>
          </div>
          <div className={styles.dashedLine}></div>
          <div className={ct(styles.correctBlock, styles.correct2)}>
            <img onError={(e) => {e.currentTarget.src = emptyImg}} className={styles.correctIcon} src={emptyCorrect} alt=""/>
          </div>
        </div>
      );
      break;
    case 1:
      userStatus = (
        <div className={styles.correctContainer}>
          <div className={ct(styles.correctBlock, styles.correct1)}>
            <img onError={(e) => {e.currentTarget.src = emptyImg}} className={styles.correctIcon} src={correct} alt=""/>
          </div>
          <div className={styles.dashedLine}></div>
          <div className={ct(styles.correctBlock, styles.correct2)}>
            <img onError={(e) => {e.currentTarget.src = emptyImg}} className={styles.correctIcon} src={emptyCorrect} alt=""/>
          </div>
        </div>
      );
      break;
    case 2:
      userStatus = (
        <div className={styles.correctContainer}>
          <div className={ct(styles.correctBlock, styles.correct1)}>
            <img onError={(e) => {e.currentTarget.src = emptyImg}} className={styles.correctIcon} src={correct} alt=""/>
          </div>
          <div className={styles.line}></div>
          <div className={ct(styles.correctBlock, styles.correct2)}>
            <img onError={(e) => {e.currentTarget.src = emptyImg}} className={styles.correctIcon} src={correct} alt=""/>
          </div>
        </div>
      );
      break;
    default:
      userStatus = (
        <div className={styles.correctContainer}>
          <div className={ct(styles.correctBlock, styles.correct1)}>
            <img onError={(e) => {e.currentTarget.src = emptyImg}} className={styles.correctIcon} src={emptyCorrect} alt=""/>
          </div>
          <div className={styles.dashedLine}></div>
          <div className={ct(styles.correctBlock, styles.correct2)}>
            <img onError={(e) => {e.currentTarget.src = emptyImg}} className={styles.correctIcon} src={emptyCorrect} alt=""/>
          </div>
        </div>
      );
      break;
  }
  return (
    <div className={styles.myInviteItem}>
      <img className={styles.userAvatar} src={img} alt=""/>
      <div className={styles.userName}>{name}</div>
      {userStatus}
    </div>
  )
}

interface IProps {
  rankList: {
    name: string,
    count: number
  }[],
  myInvite: {
    img: string,
    name: string,
    status: number,
  }[],
  handleMyInviteShowMore: () => void;
}

const InviteRank: React.FC<{rankList: IProps['rankList']}> = ({ rankList }) => {
  const [showNum, setShowNum] = useState(5);
  const handleScroll: React.UIEventHandler<HTMLDivElement> = (e) => {
    const { scrollTop, scrollHeight, clientHeight } = e.currentTarget;
    console.log('scroll', showNum, scrollHeight, scrollTop, clientHeight);
    if (showNum < 20 && scrollHeight - scrollTop < clientHeight + 10) {
      setShowNum(showNum + 5);
    }
  };
  return (
    <div className={styles.inviteCardBody} onScroll={handleScroll}>
      <div className={styles.rankTitle}>
        <div className={ct(styles.rankTitleText, styles.titleText1)}>排名</div>
        <div className={ct(styles.rankTitleText, styles.titleText2)}>用户名</div>
        <div className={ct(styles.rankTitleText, styles.titleText3)}>邀请人数</div>
      </div>
      {
        rankList
        .sort((a, b) => b.count - a.count)
        .slice(0, showNum)
        .map((cur, index) => {
          if (index < 3) {
            return <SpecialRankItem name={cur.name} rank={index + 1} inviteNum={cur.count} />;
          }
          return <RankItem name={cur.name} rank={index + 1} inviteNum={cur.count} />;
        })
      }
      <div className={ct(styles.rankItem, styles.tip)}>邀请人数相同者按完成时间先后排序</div>
    </div>
  );
}

const MyInvite: React.FC<{userList: IProps['myInvite'], shwoMore: () => void }> = ({ userList, shwoMore }) => {
  const handleScroll: React.UIEventHandler<HTMLDivElement> = (e) => {
    const { scrollTop, scrollHeight, clientHeight } = e.currentTarget;
    console.log('scroll', scrollHeight, scrollTop, clientHeight);
    if (scrollHeight - scrollTop < clientHeight + 10) {
      shwoMore();
    }
  };
  return (
    <div className={ct(styles.inviteCardBody, styles.myInviteContainer)} onScroll={handleScroll}>
      {
        userList.length
        ? <>
          <div className={styles.myInviteTitle}>
            <div className={ct(styles.title2, styles.myInviteTitleText)}>通过审核</div>
            <div className={ct(styles.title1, styles.myInviteTitleText)}>完善资料</div>
          </div>
          {
            userList.map((cur) => <InviteUser key={`${cur.name}${Math.random()}`} img={cur.img} name={cur.name} status={cur.status} />)
          }
        </>
        : <div className={styles.emptyInvite}>暂无邀请记录</div>
      }
    </div>
  );
}

let isShowMyinvite = false;

const InviteCard: React.FC<IProps> = (props) => {
  const { rankList, myInvite, handleMyInviteShowMore } = props;
  const cardRef = useRef<HTMLDivElement>(null);
  const [tab, setTab] = useState('rank');
  const [cardShadowStyle, setShadowStyle] = useState({
    width: 0,
    height: 0,
  });
  useEffect(() => {
    if (cardRef.current) {
      setShadowStyle({
        width: cardRef.current.clientWidth,
        height: cardRef.current.clientHeight,
      });
    }
  }, [cardRef.current]);

  useEffect(() => {
    if (!isShowMyinvite && myInvite.length > 0) {
      isShowMyinvite = true;
      setTab('myInvite');
    }
  }, [myInvite.length])

  const activeStyle = ct(styles.headerItem, styles.activeItem);
  const notActiveStyle = ct(styles.headerItem, styles.notActiveItem);
  const selectRankTab = useCallback((index: string) => {
    return () => setTab(index);
  }, []);

  return (
    <>
      <div ref={cardRef} className={ct(styles.inviteCardContainer, styles.rankContainer)}>
        <div className={styles.inviteHeader}>
          <div
            onClick={selectRankTab('rank')}
            className={tab === 'rank' ? activeStyle : notActiveStyle}
          >
            <span className={styles.headerText}>邀请排行榜</span>
          </div>
          <div
            onClick={selectRankTab('myInvite')}
            className={tab === 'rank' ? notActiveStyle : activeStyle}
          >
            <span className={styles.headerText}>我的邀请记录</span>
          </div>
        </div>
        {
          tab === 'rank'
            ? <InviteRank
                rankList={rankList}
              />
            : <MyInvite
                userList={myInvite}
                shwoMore={handleMyInviteShowMore}
              />
        }
      </div>
      <div
        style={{
          width: `${cardShadowStyle.width}px`,
          height: `${cardShadowStyle.height}px`,
          marginTop: `-${cardShadowStyle.height - 5}px`
        }}
        className={styles.inviteCardShadow}  
      ></div>
    </>
  );
}

export default InviteCard;
