@import "~sass-rem";

$rem-baseline: 37.5px;

.whiteCard {
  background-color: #ffffff;
  width: rem(343px);
  margin-left: rem(16px);
  margin-right: rem(16px);
  border-radius: 8px;
  border: solid 1px #292d2e;
}

// .cardShadow {
//   width: rem(343px);
//   margin: rem(5px 0 0 6px);
//   border-radius: 8px;
//   background-color: #f15e47;
// }

.cardWithTitle {
  position: relative;
}


.titleContainer {
  display: flex;
  align-items: center;
  justify-content: center;

  .titleBlock {
    width: rem(139px);
    height: rem(36px);
    background-color: #f15e47;
    border: solid 1px #292d2e;
    border-top: 0;
    border-bottom-left-radius: 8px;
    border-bottom-right-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .titleText {
    font-family: PingFangSC, "PingFang SC", PingFangSC-Regular, "Helvetica Neue", -apple-system, BlinkMacSystemFont, STHeiti,"Microsoft Yahei",<PERSON><PERSON><PERSON>,<PERSON><PERSON>,sans-serif;
    font-size: 16px;
    font-weight: 600;
    line-height: 1;
    text-align: center;
    color: #ffffff;
  }
}