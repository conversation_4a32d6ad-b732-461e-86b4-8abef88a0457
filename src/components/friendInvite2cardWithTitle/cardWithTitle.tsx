import React, { PropsWithChildren } from 'react';
import ct from 'classnames';
import styles from './cardWithTitle.module.scss';

interface IProps {
  className: string;
  titleText: string;
}

const CardWithTitle: React.FC<PropsWithChildren<IProps>> = (props) => {
  const { className, titleText, children } = props;
  return (
    <div className={ct(styles.whiteCard, styles.cardWithTitle, className)}>
      <div className={styles.titleContainer}>
        <div className={styles.titleBlock}>
          <span className={styles.titleText}>{titleText}</span>
        </div>
      </div>
      {children}
    </div>
  );
}

export default CardWithTitle;
