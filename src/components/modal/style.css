.receiveAmi-exit {
  opacity: 1;
}
.receiveAmi-exit-active {
  opacity: 0;
  /* transform: translateX(0); */
  transition: opacity 0.3s cubic-bezier(0.25, 0.1, 0.5, 1);
}
.receiveAmi-exit-done {
  opacity: 0;
}
.modalAmi-exit {
  opacity: 1;
  transform: scale(1);
}
.modalAmi-exit-active {
  opacity: 0;
  transform: scale(1.2);
  transition: opacity 0.5s cubic-bezier(0.25, 0.1, 0.5, 1),
    transform 0.5s cubic-bezier(0.25, 0.1, 0.5, 1);
}
.modalAmi-exit-done {
  opacity: 0;
}
