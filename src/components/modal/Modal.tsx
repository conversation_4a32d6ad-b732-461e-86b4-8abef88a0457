import React, { useState } from "react";
import s from "./Modal.module.css";
import * as IMGS_ZH from "../../routes/svip/imgs-zh";
import * as IMGS_EN from "../../routes/svip/imgs-en";
import * as MODAL_IMGS_ZH from "./imgs-zh";
import * as MODAL_IMGS_EN from "./imgs-en";
import { CSSTransition } from "react-transition-group";
import "./style.css";
import {useTranslation} from "react-i18next";
import {urlParams} from "../../utils";
interface Iprop {
  visible: boolean;
  setVisible: (v: boolean) => void;
  ablist: string[]
}
const IMGS = {
  zh: IMGS_ZH,
  en: IMGS_EN
}
const MODAL_IMGS = {
  zh: MODAL_IMGS_ZH,
  en: MODAL_IMGS_EN
}
const Modal: React.FC<Iprop> = (props) => {
  const {t, i18n} = useTranslation();
  const language = i18n.language === 'zh-Hans' ? 'zh' : 'en';
  const { visible, setVisible, ablist } = props;
  const urlParam = urlParams(window.location.search.slice(1)) || {};
  const [received, setReceived] = useState(false);
  const handleClose = () => {
    setVisible(false);
  };
  let starPos = [];
  // hideNotch=1表示隐藏状态栏
  const hideNotch = urlParam.hideNotch === '1';
  // 是否自动使用超级曝光
  const startBoost = urlParam.startBoost === 'true';
  const width = document.body.clientWidth - 16; //图片宽
  // 圆心 假设为p,q,角度α，半径120
  for (let i = 0; i <= 3; i++) {
    let angle = 360 * Math.random(); //随机角度
    let starWidth = (Math.random() * 0.8 + 0.2) * 71; //星星尺寸
    let x = (1 / 3) * width * Math.cos((angle / 180) * Math.PI); //距离圆心的横向位移
    let y = (1 / 3) * width * Math.sin((angle / 180) * Math.PI); //距离圆心的纵向位移
    // 圆心位置 width/2,width/2
    let x1 = width / 2 + x - starWidth / 2; //left
    let y1 = width / 2 - y - starWidth / 2; //top
    starPos.push({ left: x1, top: y1, width: starWidth });
  }

  return (
    <div>
      {visible && (
        <div className={s.modal}>
          <div className={s.modalMask}></div>
          <div className={s.modalWrap}>
            <div className={s.modalContent}>
              <div className={`${s.nav} ${hideNotch ? s.hideNotch : ''}`}>
                <div className={s.close} onClick={handleClose}>
                  <img src={IMGS[language].closeIcon} alt="" />
                </div>
              </div>
              <div className={`${s.title} ${s[language]}`}>
                <img src={language === 'en' && received ?
                  MODAL_IMGS[language].receiveTitle :
                  MODAL_IMGS[language].title}
                     style={{width: language === 'en' && received ? '80%' : '100%'}} alt="" />
              </div>
              <div className={s.receiveContainer}>
                <div className={s.blurDiv}></div>
                <img className={s.bg} src={MODAL_IMGS[language].bg} alt="" />
                {starPos.map((item, index) => (
                  <img
                    key={index}
                    className={`${s["star" + index]} ${s.star}`}
                    src={MODAL_IMGS[language].star}
                    style={{
                      left: `${item.left}px`,
                      top: `${item.top}px`,
                      width: `${item.width}px`,
                    }}
                    alt=""
                  />
                ))}
                {/* {!received && (
                  <img
                    className={s.airplain}
                    src={MODAL_IMGS[language].airplain}
                    alt=""
                  />
                )}
                {!received && (
                  <img
                    onClick={() => {
                      setReceived(true);
                    }}
                    className={s.receiveBtn}
                    src={MODAL_IMGS[language].receive}
                    alt=""
                  />
                )} */}
                <CSSTransition
                  key={1}
                  timeout={300}
                  classNames="receiveAmi"
                  in={!received}
                  onExited={() => setReceived(true)}
                >
                  <img
                    className={s.airplain}
                    src={MODAL_IMGS[language].airplain}
                    alt=""
                  />
                </CSSTransition>
                <CSSTransition
                  key={2}
                  timeout={300}
                  classNames="receiveAmi"
                  in={!received}
                  onExited={() => setReceived(true)}
                >
                  <img
                    onClick={() => {
                      setReceived(true);
                    }}
                    className={s.receiveBtn}
                    src={MODAL_IMGS[language].receive}
                    alt=""
                  />
                </CSSTransition>
                {
                  received && !startBoost && <img className={s.prize} src={MODAL_IMGS[language].prize} alt="" />
                }
                {
                  received && startBoost && <img className={s.prize} src={MODAL_IMGS[language].experimentPrize} alt="" />
                }
                {received && (
                  <img
                    onClick={() => {
                      setVisible(false);
                    }}
                    className={s.accept}
                    src={MODAL_IMGS[language].accept}
                    alt=""
                  />
                )}
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};
export default Modal;
