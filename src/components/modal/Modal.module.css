.modal {
  width: 100%;
  height: 100%;
  background-color: rgba(25, 23, 39, 0.9);

  position: fixed;
  top: 0;
  right: 0;
  left: 0;
  bottom: 0;
  height: 100%;
  z-index: 1001;
}
.nav {
  position: absolute;
  top: 0.14rem;
  left: 0;
  right: 0;
  display: flex;
  justify-content: flex-end;
  align-items: center;
  color: #edc298;
  font-size: 0;
  font-family: PingFangSC-Medium;
}
.nav.hideNotch {
  top: 0.3rem;
}
.nav.hideNotch + .title {
  margin-top: 0.8rem;
}
.title {
  margin-top: 0.94rem;
  margin-left: 0.08rem;
  margin-right: 0.08rem;
  text-align: center;
}
.title.en {
  margin-top: 0.6rem;
  margin-bottom: 0.38rem;
}
.title img {
  width: 100%;
}
.close img {
  width: 0.32rem;
  height: 0.32rem;
  font-size: 0;
  margin-right: 0.1rem;
}
.receiveContainer {
  position: relative;
  top: -0.44rem;
  margin-left: 0.08rem;
  margin-right: 0.08rem;
  width: calc(100% - 0.16rem);
}
.blurDiv {
  position: absolute;
  left: -0.08rem;
  right: -0.08rem;
  height: calc(100% + 0.16rem);
  width: calc(100% + 0.16rem);
  top: 0;
  background-color: #191727;
  filter: blur(0.2rem);
  z-index: -1;
}
.bg {
  width: 100%;
  animation: rotate 40s linear infinite;
}
.receiveBtn {
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  width: 1.44rem;
}
.airplain {
  position: absolute;
  left: 16%;

  top: 62%;
  width: 0.4rem;
  animation: airplain 1s infinite linear;
}
.star {
  position: absolute;
  opacity: 1;
  /* animation: star 1.2s cubic-bezier(0.25, 0.1, 0.5, 1) infinite; */
  transform: translate(-50%, -50%);
}
.star0 {
  animation: star 1.2s cubic-bezier(0.25, 0.1, 0.5, 1) infinite;

  animation-delay: 0.2s;
}
.star1 {
  animation: star1 1.2s cubic-bezier(0.25, 0.1, 0.5, 1) infinite;

  animation-delay: 0.4s;
}
.star2 {
  animation: star2 1.2s cubic-bezier(0.25, 0.1, 0.5, 1) infinite;

  animation-delay: 0.5s;
}
.star3 {
  animation: star3 1.2s cubic-bezier(0.25, 0.1, 0.5, 1) infinite;

  animation-delay: 0s;
}

.prize {
  position: absolute;
  width: 3.04rem;
  left: calc(50% - 1.52rem);
  top: calc(50% - 1.58rem);
  /* animation: prizeScale 0.4s cubic-bezier(0.25, 0.1, 0.5, 1),
    prizeShow 0.4s cubic-bezier(0.25, 0.1, 0.5, 1); */
  animation: prizeScale 0.4s cubic-bezier(0.25, 0.1, 0.5, 1);
  animation-fill-mode: forwards;
}
.accept {
  position: absolute;
  left: 50%;
  width: 2.4rem;
  bottom: -5%;
  transform: translateX(-50%);
  animation: btnshow 0.6s cubic-bezier(0.25, 0.1, 0.5, 1);
}

@keyframes airplain {
  0% {
    /* left: 16%;
    top: 62%; */
    transform: translate3d(0, 0, 0);
  }
  50% {
    /* left: calc(16% + 0.04rem);
    top: calc(62% - 0.04rem); */
    transform: translate3d(-0.04rem, 0.04rem, 0);
  }
  100% {
    /* left: 16%;
    top: 62%; */
    transform: translate3d(0, 0, 0);
  }
}
@keyframes rotate {
  0% {
    transform: rotate(0turn);
  }
  100% {
    transform: rotate(1turn);
  }
}
@keyframes star {
  0% {
    opacity: 0;
    transform: scale(0.6);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}
@keyframes star1 {
  0% {
    opacity: 0;
    transform: scale(0.6);
  }
  100% {
    opacity: 0.8;
    transform: scale(1);
  }
}
@keyframes star2 {
  0% {
    opacity: 0;
    transform: scale(0.6);
  }

  100% {
    opacity: 0.6;
    transform: scale(1);
  }
}
@keyframes star3 {
  0% {
    opacity: 0;
    transform: scale(0.6);
  }

  100% {
    opacity: 1;
    transform: scale(1);
  }
}
@keyframes prizeScale {
  0% {
    transform: scale(0.5);
  }
  50% {
    transform: scale(1.05);
  }
  100% {
    transform: scale(1);
  }
}
@keyframes prizeShow {
  0% {
    opacity: 0;
    transform-origin: center;
  }
  100% {
    opacity: 1;
    transform-origin: center;
  }
}
@keyframes btnshow {
  0% {
    transform: translateX(-50%) scale(0.5);
  }
  50% {
    transform: translateX(-50%) scale(1.05);
  }
  100% {
    transform: translateX(-50%) scale(1);
  }
}
