.container {
  margin-left: 0.16rem;
  margin-right: 0.16rem;
}
.left,
.right {
  background-color: rgba(106, 115, 146, 0.3);
  border-radius: 0.04rem;
  margin-bottom: 0.08rem;
}
.left {
  margin-right: 0.08rem;
}

.row1,
.row2,
.row3 {
  display: flex;
}
.row1 .left,
.row3 .right {
  width: 32%;
  flex-shrink: 0;
}
.row2 .left,
.row2 .right {
  width: 50%;
}
.row1 .right,
.row3 .left {
  flex: 1;
}
.iconWrap {
  background-image: url("https://auto.tancdn.com/v1/images/eyJpZCI6IkRSVElGUEVZUjI1WUoyVksyRTM1RUFJU0NEQ1NTVzA1IiwidyI6MTY4LCJoIjoxNjgsImQiOjAsIm10IjoiaW1hZ2UvanBlZyIsImRoIjoxMDI1MzUyMDYwNjU1MzQ2MjE2Nn0.png");
  width: 0.56rem;
  height: 0.56rem;
  background-size: cover;
  margin-top: 0.14rem;
  margin-bottom: 0.08rem;
  font-size: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
}
.iconWrap img {
  width: 0.32rem;
  height: 0.32rem;
}
.svip {
  position: absolute;
  width: 0.4rem !important;
  height: 0.17rem !important;
  bottom: 0;
  right: 0.07rem;
}
.row1 .left,
.row2 .left,
.row2 .right,
.row3 .right {
  text-align: center;
}
.row1 .left .iconWrap,
.row2 .left .iconWrap,
.row2 .right .iconWrap,
.row3 .right .iconWrap {
  text-align: center;
  margin-left: 50%;
  transform: translateX(-50%);
}
.title {
  color: #ffd1a2;
  font-size: 0.14rem;
  line-height: 0.2rem;
  font-family: PingFangSC-Light;
  margin-bottom: 0.14rem;
  white-space: pre-wrap;
  max-width: 98%;
}
.row1 .right,
.row3 .left {
  display: flex;
  position: relative;
}
.row1 .right .iconWrap,
.row3 .left .iconWrap {
  margin-left: 0.23rem;
  margin-right: 0.16rem;
}
.contentAndTitle {
  margin-top: 0.14rem;
  flex-shrink: 30;
}
.contentAndTitle .title {
  margin-bottom: 0.04rem;
}
.darkSvip {
  position: absolute;
  bottom: 0;
  right: 0;
  width: 1.2rem;
}
.content {
  color: #999999;
  font-size: 0.12rem;
  line-height: 0.17rem;
  white-space: pre-wrap;
}
/* 实验 */
.exp .left {
  margin-right: 0;
  display: block;
}
.exp .right {
  margin-left: 0.08rem;
  margin-bottom: 0.08rem;
}
.exp .left .iconWrap {
  text-align: center;
  margin-left: 50%;
  transform: translateX(-50%);
}
.exp .contentAndTitle {
  margin-top: 0;
  text-align: center;
}
.exp .darkSvip {
  display: none;
}
@media screen and (max-width: 3.2rem) {
  .row1 .right .iconWrap,
  .row3 .left .iconWrap {
    margin-left: 0.1rem;
  }
  .content {
    max-width: 1rem;
  }
}
