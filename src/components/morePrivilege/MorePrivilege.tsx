import React,  { useState } from "react";
import s from "./MorePrivilege.module.css";
import * as MOREIMGS from "./imgs";
import * as IMGS_ZH from "../../routes/svip/imgs-zh";
import * as IMGS_EN from "../../routes/svip/imgs-en";
import BlockTitle from "../blockTitle/BlockTitle";
import {useTranslation} from "react-i18next";
import ct from 'classnames';

interface Iprop {
  ablist: string[];
  appNewerVer: boolean;
  flag: boolean;
  
}
const IMGS = {
  zh: IMGS_ZH,
  en: IMGS_EN
}
const MorePrivilege: React.FC<Iprop> = ({appNewerVer, ablist, flag, ...props}) => {
  const {t, i18n} = useTranslation();
  const language = i18n.language === 'zh-Hans' ? 'zh' : 'en';
  return (
    <div className={s.morePrivilege}>
      <BlockTitle isEn={language === 'en'} title={IMGS[language].moreTitle}/>
      <div className={s.container}>
        <div className={s.row1}>
          <div className={s.left}>
            <div className={s.iconWrap}>
              <img src={MOREIMGS.moreIcon1} alt=""/>
            </div>
            <div className={s.title}>{t('SVIP_LANDING_PAGE_UNDO_TITLE')}</div>
          </div>
          <div className={s.right}>
            <div className={s.iconWrap}>
              <img className={s.crown} src={MOREIMGS.moreIcon2} alt=""/>
              <img className={s.svip} src={MOREIMGS.svip} alt=""/>
            </div>

            <div className={s.contentAndTitle}>
              <div className={s.title}>{t('SVIP_LANDING_PAGE_VIP_BADGE_TITLE')}</div>
              <div className={s.content}>{t('SVIP_LANDING_PAGE_VIP_BADGE_SUBTITLE').split('\n')[0]}</div>
              <div className={s.content}>{t('SVIP_LANDING_PAGE_VIP_BADGE_SUBTITLE').split('\n')[1]}</div>
            </div>
            <img className={s.darkSvip} src={MOREIMGS.darkSvip} alt=""/>
          </div>
        </div>
        <div className={s.row2}>
          <div className={s.left}>
            <div className={s.iconWrap}>
              <img src={MOREIMGS.moreIcon3} alt=""/>
            </div>
            <div className={s.title}>{t('SVIP_LANDING_PAGE_UNLOCK_LIKED_TITLE')}</div>
          </div>
          <div className={s.right}>
            <div className={s.iconWrap}>
              <img src={MOREIMGS.moreIcon4} alt=""/>
            </div>
            <div className={s.title}>{t('SVIP_LANDING_PAGE_RECOVER_UNMATCH_TITLE')}</div>
          </div>
        </div>
        <div className={ct(s.row3, (ablist.includes('REV_gift_membership:exp') ? s.exp : ''))} style={{justifyContent: appNewerVer ?'space-between': ''}}>
          <div className={s.left} style={{display: appNewerVer && !flag ? 'none' : 'flex'}}>
            <div className={s.iconWrap}>
              <img src={MOREIMGS.moreIcon5} alt=""/>
            </div>
            <div className={s.contentAndTitle}>
            <div className={s.title}>{t('SVIP_LANDING_PAGE_PRIVACY_PROTECTION_TITLE')}</div>
              {!ablist.includes('REV_gift_membership:exp') ?  
                <div>
                  <div className={s.content}>{t('SVIP_LANDING_PAGE_PRIVACY_PROTECTION_SUBTITLE').split('\n')[0]}</div>
                  <div className={s.content}>{t('SVIP_LANDING_PAGE_PRIVACY_PROTECTION_SUBTITLE').split('\n')[1]}</div> 
                </div> : ''
              }
            </div>
            <img className={s.darkSvip} src={MOREIMGS.darkSvip} alt=""/>
          </div>
          <div className={s.right} hidden={!appNewerVer || flag}>
            <div className={s.iconWrap}>
              <img src={MOREIMGS.moreIcon5} alt=""/>
            </div>
            <div className={s.title}>{t('SVIP_LANDING_PAGE_PRIVACY_PROTECTION_TITLE')}</div>
          </div>
          <div className={s.right}>
            <div className={s.iconWrap}>
              <img src={MOREIMGS.moreIcon6} alt=""/>
            </div>
            <div className={s.title}>{t('SVIP_LANDING_PAGE_LOCATION_TITLE')}</div>
          </div>
          <div className={s.right} hidden={!appNewerVer || flag}>
            <div className={s.iconWrap}>
              <img src={MOREIMGS.moreIcon8} alt=""/>
            </div>
            <div className={s.title}>{t('SVIP_LANDING_PAGE_PHONE_TITLE')}</div>
          </div>
          { ablist.includes('REV_gift_membership:exp') ? 
            <div className={s.right}>
              <div className={s.iconWrap}>
                <img src={MOREIMGS.moreIcon7} alt=""/>
              </div>
              <div className={s.title}>{t('SVIP_LANDING_PAGE_EXCLUSIVE_GIFT')}</div>
            </div> : ''
          }
        </div>
      </div>
    </div>
  );
};
export default MorePrivilege;
