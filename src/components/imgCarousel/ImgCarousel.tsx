import React from "react";
import s from "./ImgCarousel.module.css";
import { Carousel } from "../antd/Antd";
interface Iprops {
  index: number;
  setIndex: (index: number) => void;
}
const ImgCarousel: React.FC<Iprops> = (props) => {
  const { index, setIndex } = props;
  return (
    <Carousel
      autoplay
      autoplayInterval={1500}
      infinite={true}
      dots={false}
      className="kol-carousel"
      beforeChange={() => {
        setIndex(Number(index) + 1);
      }}
    >
      <div className={`${s.item} ${s.female}`}>
        {/* <img
          className={`${s.left} ${s.clickImg}`}
          src="https://auto.tancdn.com/v1/images/eyJpZCI6IkJBSVZCRE1JN0RNUlEyVkRNWFhRS0xJUFE0UUtQNzA2IiwidyI6MTkyLCJoIjoxOTIsImQiOjAsIm10IjoiaW1hZ2UvanBlZyIsImRoIjozMTAyODg1ODA1MTY0MjQ0Nzc5fQ.png"
          alt=""
        />
        <img
          className={`${s.right} ${s.clickImg}`}
          src="https://auto.tancdn.com/v1/images/eyJpZCI6IkU0NlJDRVZIM0VRRkJNWFBVT1hTSjU3UlBFRU40SjA2IiwidyI6MTkyLCJoIjoxOTIsImQiOjAsIm10IjoiaW1hZ2UvanBlZyIsImRoIjozMTAyMjk2NDY3MDY2NDE1Mzg3fQ.png"
          alt=""
        /> */}
      </div>
      <div className={`${s.item} ${s.male}`}>
        {/* <img
          className={`${s.left} ${s.clickImg}`}
          src="https://auto.tancdn.com/v1/images/eyJpZCI6IkJBSVZCRE1JN0RNUlEyVkRNWFhRS0xJUFE0UUtQNzA2IiwidyI6MTkyLCJoIjoxOTIsImQiOjAsIm10IjoiaW1hZ2UvanBlZyIsImRoIjozMTAyODg1ODA1MTY0MjQ0Nzc5fQ.png"
          alt=""
        />
        <img
          className={`${s.right} ${s.clickImg}`}
          src="https://auto.tancdn.com/v1/images/eyJpZCI6IkU0NlJDRVZIM0VRRkJNWFBVT1hTSjU3UlBFRU40SjA2IiwidyI6MTkyLCJoIjoxOTIsImQiOjAsIm10IjoiaW1hZ2UvanBlZyIsImRoIjozMTAyMjk2NDY3MDY2NDE1Mzg3fQ.png"
          alt=""
        /> */}
      </div>
    </Carousel>
  );
};
export default ImgCarousel;
