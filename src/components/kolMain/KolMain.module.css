.container {
  width: 100%;
  height: 100%;
  background-image: url("https://auto.tancdn.com/v1/images/eyJpZCI6IkVRVkFIV0hUSURNMlRTUUtaQ0FMNVVZVzNUREk0QTA2IiwidyI6MTEyNSwiaCI6MTgwOSwiZCI6MCwibXQiOiJpbWFnZS9qcGVnIiwiZGgiOjc0MDA2MzIyOTU4NDQ0NjU2Mn0.png");
  background-size: 100% 100%;
  position: relative;
  overflow: hidden;
}
.logo {
  font-size: 0;
  position: absolute;
  left: 0.14rem;
  top: 0.14rem;
  width: 13.87%;
  z-index: 1000;
}
.logo img {
  width: 100%;
}
.carousel {
  position: absolute;
  top: 0.47rem;
  left: 0.5rem;
  right: 0.5rem;
  bottom: 30%;

  z-index: 998;
}
.match {
  position: absolute;
  width: 95.73%;
  bottom: 18%;
  left: 50%;
  transform: translateX(-50%);
}
.match img {
  width: 100%;
}
.download {
  position: absolute;
  bottom: 7%;
  width: 67.2%;
  font-size: 0;
  left: 50%;
  transform: translateX(-50%);
}
.bubble {
  background-image: url("https://auto.tancdn.com/v1/images/eyJpZCI6IjdZWkI0MjdaRllMTVJFWDRaQ0VVT0dPRENCWlI0UzA2IiwidyI6NzMyLCJoIjoyNDAsImQiOjAsIm10IjoiaW1hZ2UvanBlZyIsImRoIjoxMTc1NzI0NjA4MTkyNzE5NjE2fQ.png");
  position: absolute;
  width: 2.44rem;
  height: 0.8rem;
  top: 0;
  right: 0;
  font-family: PingFangSC;
  font-size: 0.2rem;
  color: #503b54;
  background-size: cover;
  text-align: center;
  line-height: 0.28rem;
  padding-top: 0.26rem;
  box-sizing: border-box;
  z-index: 1001;
  animation: bubble 2s linear infinite;
}
.download img {
  width: 100%;
  animation: download 0.5s linear infinite;
}
.clickImgContainer {
  position: absolute;
  left: 0.5rem;
  right: 0.5rem;
  bottom: 32%;
  z-index: 1000;
}
.clickImg {
  width: 0.64rem;
  height: 0.64rem;
  position: absolute;
  bottom: 0.12rem;
}
.left {
  left: 0.12rem;
  animation: bigger 0.5s linear infinite;
}
.right {
  right: 0.12rem;
  animation: smaller 0.5s linear infinite;
}
@keyframes download {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.2);
  }
  100% {
    transform: scale(1);
  }
}

@keyframes bubble {
  0% {
    transform: translate(-1rem, 1rem) scale(0.1);
    opacity: 0;
  }
  15% {
    transform: scale(1);
    opacity: 1;
  }
  85% {
    transform: scale(1);
    opacity: 1;
  }
  100% {
    opacity: 0;
  }
}

@keyframes smaller {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.1);
  }
  100% {
    transform: scale(1);
  }
}
@keyframes bigger {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(0.9);
  }
  100% {
    transform: scale(1);
  }
}
