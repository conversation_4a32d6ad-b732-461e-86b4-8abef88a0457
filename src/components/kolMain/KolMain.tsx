/* eslint-disable complexity */
import React, {useState, useEffect} from "react";
import s from "./KolMain.module.css";
import ImgCarousel from "../imgCarousel/ImgCarousel";
import ReactGA from "react-ga";
import publicIp from "react-public-ip";
import {isIOS, isAndroid, kolTrackUrl, reportUrl} from "../../utils/config";
import CopyToClipboard from "react-copy-to-clipboard";
import {makeRandomCode} from "../../utils";
import {ajax} from "../../utils/ajax";
import md5 from "js-md5";

interface Iprops {
  downloadUrl: string;
  token: string;
  agentNum?: string;
  pathname: string;
  search: string;
  iconUrl?: string;       // 左上角icon
  btnUrl?: string;        // 按钮图片
  isShareLight?: boolean; // 是否是概念版分享页面
  btnCenter?: boolean;    // 是否把按钮放在中间
  isAffiliate?: boolean;  // 是否需要向 affiliate 打点
}
// 生成唯一码，十位
const code = makeRandomCode(10);
const OS = {
  ANDROID: 'android',
  IOS: 'ios'
};
const AD_TYPE = {
  SHOW: 'show',
  CLICK: 'click'
};

const KolMain: React.FC<Iprops> = (props) => {
  const [ip, setIp] = useState(null);
  const [bg, setBg] = useState(false);
  const [like, setLike] = useState(false);
  const [dislike, setDislike] = useState(false);
  const [download, setDownload] = useState(false);

  const [activeIndex, setActiveIndex] = useState(0);

  const {token, downloadUrl, pathname, search} = props;
  let arr: any[] = [];
  let businesstype = "";
  if (token && token.split(":").length >= 3) {
    arr = token.split(":")[2].split("-");
    businesstype = token.split(":")[1];
  }
  const os = isAndroid ? OS.ANDROID : isIOS ? OS.IOS : '';
  const channel = arr?.[0] ?? "";
  const hostid = arr?.[1] ?? "";
  const taskid = arr?.[2] ?? "";
  const h5version = arr?.[3] ?? "";

  const first = !localStorage.getItem('deviceID');
  const deviceID = localStorage.getItem('deviceID') || makeRandomCode(20);
  if (process.env.NODE_ENV === "production") {
    // 线上谷歌分析
    ReactGA.initialize("UA-109302778-32");
  } else {
    // 开发环境以及测试环境谷歌分析
    ReactGA.initialize("UA-109302778-31");
  }
  const track = (eventtype: string, eventname: string) => {
    let timestamp = new Date().valueOf();
    const formData = {
      event_type: eventtype,
      event_name: eventname,
      ip: ip,
      token_kol: token,
      channel_kol: channel,
      host_id: hostid,
      task_id: taskid,
      page_version: h5version,
      business_type: businesstype,
      unique_code: code,
      timestamp: timestamp,
      user_agent: navigator.userAgent,
      ss: md5("kol" + timestamp),
    };
    ajax
      .post(kolTrackUrl, formData)
      .then((res) => {
      })
      .catch((err) => {
      });
  };
  const trackShare = (event_type: string, event_name?: string) => {
    let extra: { isAndroid?: boolean, eventName?: string } = {};
    let params = {
      userId: '',
      deviceId: deviceID,
      content: ''
    }
    switch (event_type) {
      case 'PV':
      case 'UV':
        extra = {isAndroid}
        params.content = JSON.stringify({
          pid: 'p_growth_redenvelopes',
          tt: event_type,
          time: Date.now(),
          extra
        })
        break;
      case 'MC':
      case 'MV':
        extra = {eventName: event_name};
        params.content = JSON.stringify({
          eid: 'e_growth_light_redenvelopes_download_button',
          tt: event_type,
          time: Date.now(),
          extra
        })
        break;
    }
    ajax.post(reportUrl, params)
      .then(res => {
        console.log('trackShare', event_type, res);
      }, rej => {
      })
    return true
  }
  const affiliateLogUrl = `https://affiliate.tantanapp.com/ads/${channel}/${props.agentNum}?`;
  const stagingLogUrl = `http://affiliate.staging2.p1staff.com/ads/${channel}/${props.agentNum}?`;

  const trackAffiliate = (ad_type: string, event_name: string) => {
    let data: any = {
      channel,
      ip,
      event_name,
      unique_code: code,
      ad_type,
      token,
      os,
      event_type: 'h5',
      creative_id: hostid,
      plan_id: taskid,
      click_time: new Date().getTime(),
      agent_num: props.agentNum,
      user_agent: navigator.userAgent
    };
    const oImg = document.createElement('img');
    const arr = Object.keys(data).map(key => {
      return `${key}=${encodeURIComponent(data[key])}`;
    });
    oImg.src = `${affiliateLogUrl}${arr.join('&')}`;
  };

  window.onload = () => {
    console.log('loaded');
  }
  // pv
  useEffect(() => {
    console.log('pathname, search');
    ReactGA.pageview(pathname + search);
    props.isShareLight ? trackShare("PV") : track("PV", "pageview");
    props.isShareLight && trackShare('MV')
    props.isShareLight && first && trackShare("UV") && localStorage.setItem('deviceID', deviceID)
    props.isAffiliate && trackAffiliate(AD_TYPE.SHOW, 'pageview');
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [pathname, search]);
  // 获取ip
  useEffect(() => {
    (async () => {
      const ipv4 = (await publicIp.v4()) || "";
      const ipv6 = (await publicIp.v6()) || "";
      setIp(ipv4 || ipv6);
    })();
  }, []);
  // ip上报
  useEffect(() => {
    if (ip) {
      ReactGA.event({
        category: "User",
        action: "ipReport",
        label: code + "#" + ip,
      });
      !props.isShareLight && track("PV", "pageview_ip");
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [ip]);
  useEffect(() => {
    if (bg && like && dislike && download) {
      !props.isShareLight && track("PV", "pageview_success");
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [bg, like, dislike, download]);
  // 跳转app或者下载app
  const gotoDownload = () => {
    window.location.href = "tantanapp://home";
    if (isAndroid) {
      setTimeout(() => {
        window.location.href = downloadUrl;
        // window.location.href = "http://dl.pddpic.com/android_dev/2020-10-14/fd497b749e37b65b43f67c885dcd80e7.apk";
      }, 300);
    }
    if (isIOS) {
      setTimeout(() => {
        window.location.href =
          "https://itunes.apple.com/cn/app/tantan/id861891048?mt=8";
      }, 300);
    }
  };
  // 点击开始探探按钮
  const handleClick = () => {
    ReactGA.event({
      category: "User",
      action: "clickDownload",
      label: token + "#" + new Date().valueOf() + "#" + code + "#" + ip,
    });
    props.isShareLight
      ? trackShare("MC", "download")
      : track("MC", "download");
    props.isAffiliate && trackAffiliate(AD_TYPE.CLICK, 'download');
    gotoDownload();
  };
  const handleClickImg = (type: string) => {
    if (type === "like") {
      ReactGA.event({
        category: "User",
        action: "clickLike",
        label: token + "#" + new Date().valueOf() + "#" + code + "#" + ip,
      });
      props.isShareLight
        ? trackShare("MC", "like")
        : track("MC", "like");
    }
    if (type === "dislike") {
      ReactGA.event({
        category: "User",
        action: "clickDislike",
        label: token + "#" + new Date().valueOf() + "#" + code + "#" + ip,
      });
      props.isShareLight
        ? trackShare("MC", "dislike")
        : track("MC", "dislike");
    }
    gotoDownload();
  };

  return (
    <div className={s.container}>
      <img
        style={{position: "absolute", top: "-10000px", visibility: "hidden"}}
        onLoad={() => {
          setBg(true);
        }}
        src="https://auto.tancdn.com/v1/images/eyJpZCI6IkVRVkFIV0hUSURNMlRTUUtaQ0FMNVVZVzNUREk0QTA2IiwidyI6MTEyNSwiaCI6MTgwOSwiZCI6MCwibXQiOiJpbWFnZS9qcGVnIiwiZGgiOjc0MDA2MzIyOTU4NDQ0NjU2Mn0.png"
        alt=""
      />
      <div className={s.logo}>
        <img
          src={props.iconUrl || "https://auto.tancdn.com/v1/images/eyJpZCI6IlBVUE9TRU1CTVhYSEg0NkVSUUVUVzNLNVNYT0xCUDA3IiwidyI6MTU2LCJoIjoxNTYsImQiOjAsIm10IjoiaW1hZ2UvanBlZyIsImRoIjoxMDI0NDQ3NjAyNDE2ODMwMzc1OH0.png"}
          alt=""
        />
      </div>
      {activeIndex % 2 === 0 && (
        <div className={s.bubble}>想脱单，趁现在😊</div>
      )}
      {activeIndex % 2 === 1 && (
        <div className={s.bubble}>很期待认识你～🙋🏻‍♂️</div>
      )}

      <div className={s.carousel}>
        <ImgCarousel index={activeIndex} setIndex={setActiveIndex}/>
      </div>
      <div>
        <div className={s.clickImgContainer}>
          <CopyToClipboard text={`${token}-${new Date().valueOf()}-${code}-${ip}-download`}>
            <img
              onLoad={() => {
                setLike(true);
              }}
              onClick={() => {
                handleClickImg("dislike");
              }}
              className={`${s.left} ${s.clickImg}`}
              src="https://auto.tancdn.com/v1/images/eyJpZCI6IkJBSVZCRE1JN0RNUlEyVkRNWFhRS0xJUFE0UUtQNzA2IiwidyI6MTkyLCJoIjoxOTIsImQiOjAsIm10IjoiaW1hZ2UvanBlZyIsImRoIjozMTAyODg1ODA1MTY0MjQ0Nzc5fQ.png"
              alt=""
            />
          </CopyToClipboard>
          <CopyToClipboard text={`${token}-${new Date().valueOf()}-${code}-${ip}-download`}>
            <img
              onLoad={() => {
                setDislike(true);
              }}
              onClick={() => {
                handleClickImg("like");
              }}
              className={`${s.right} ${s.clickImg}`}
              src="https://auto.tancdn.com/v1/images/eyJpZCI6IkU0NlJDRVZIM0VRRkJNWFBVT1hTSjU3UlBFRU40SjA2IiwidyI6MTkyLCJoIjoxOTIsImQiOjAsIm10IjoiaW1hZ2UvanBlZyIsImRoIjozMTAyMjk2NDY3MDY2NDE1Mzg3fQ.png"
              alt=""
            />
          </CopyToClipboard>
        </div>
        <div className={s.match}>
          <img
            src="https://auto.tancdn.com/v1/images/eyJpZCI6IjUzSVhRWVZXT1FZSExPT09DNFRERlZHRlZGNk5BVjA2IiwidyI6MTA3NywiaCI6MTgwLCJkIjowLCJtdCI6ImltYWdlL2pwZWciLCJkaCI6MTczMjQ4MTcxMDQwNjUyODB9.png"
            alt=""
          />
        </div>
        <CopyToClipboard text={`${token}-${new Date().valueOf()}-${code}-${ip}-download`}>
          <div className={s.download} style={props.btnCenter ? {zIndex: 999999, bottom: '46%'} : {}} onClick={handleClick}>
            <img onLoad={() => {
              setDownload(true);
            }}
                 src={props.btnUrl || "https://auto.tancdn.com/v1/images/eyJpZCI6Ikw3VkRLVDczWldFREZDN0ZPT1JDN0JPVVlPWlpZSzA2IiwidyI6NzU2LCJoIjoyMDQsImQiOjAsIm10IjoiaW1hZ2UvanBlZyIsImRoIjo1NTExNjQ4NjM0NjUwMTk3MzQwfQ.png"}
                 alt=""
            />
          </div>
        </CopyToClipboard>
      </div>
    </div>
  );
};
export default KolMain;
