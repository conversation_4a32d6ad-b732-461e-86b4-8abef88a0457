import React from "react";
import s from "./BlockTitle.module.css";
import * as IMGS from "../../routes/svip/imgs-zh";
interface Iprops {
  title: string;
  isEn?: boolean;
}
const MorePrivilege: React.FC<Iprops> = (props) => {
  const titleImg = props.title;
  const isEn = props.isEn || false;
  return (
    <div className={s.BlockTitle}>
      <img src={IMGS.leftDots} alt="" />
      <img style={{height: isEn ? '0.25rem' : '0.19rem'}} src={titleImg} alt="" />
      <img src={IMGS.rightDots} alt="" />
    </div>
  );
};
export default MorePrivilege;
