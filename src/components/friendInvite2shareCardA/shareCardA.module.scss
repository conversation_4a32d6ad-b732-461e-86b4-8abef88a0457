@import "~sass-rem";

$rem-baseline: 37.5px;

img {
  pointer-events:none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  -webkit-touch-callout: none;
}

.whiteCard {
  font-family: "PingFang SC";
  background-color: #ffffff;
  width: rem(343px);
  margin-left: rem(16px);
  margin-right: rem(16px);
  border-radius: 8px;
  border: solid 1px #292d2e;
}

.inviteCardShadow {
  width: rem(343px);
  height: rem(266px);
  margin: rem(-261px 10px 0 22px);
  border-radius: 8px;
  background-color: #f15e47;
}

.inviteBlock {
  position: relative;
  margin-top: rem(-28px);
  height: rem(266px);
  z-index: 1;
}

.titleConatiner {
  margin-top: 21px;
  width: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
}

.inviteText {
  // width: rem(181px);
  // height: rem(24px);
  font-family: PingFangSC, "PingFang SC", PingFangSC-Regular, "Helvetica Neue", -apple-system, BlinkMacSystemFont, STHeiti,"Microsoft Yahei",Tahoma,Simsun,sans-serif;
  font-size: 16px;
  font-weight: 600;
  // line-height: 1.5;
  letter-spacing: 1px;
  text-align: center;
  color: #292d2e;
}

.inviteText .NumberText {
  // font-family: SFUIDisplay;
  font-family: PingFangSC, "PingFang SC", PingFangSC-Regular, "Helvetica Neue", -apple-system, BlinkMacSystemFont, STHeiti,"Microsoft Yahei",Tahoma,Simsun,sans-serif;
  font-size: 24px;
  letter-spacing: 2px;
}

.VIPWithStar {
  width: rem(108px);
  height: rem(38px);
}

.avatarContainer {
  position: absolute;
  display: flex;
  align-items: center;
  top: rem(83px);
  padding: rem(0 30px);
  width: rem(343px);
}

.avatarBase {
  width: rem(44px);
  height: rem(44px);
  position: relative;
}

.avatarImage {
  width: rem(44px);
  height: rem(44px);
  border-radius: 50%;
  border: solid 1.1px #292d2e;
}

.avatarCorrect {
  position: absolute;
  width: rem(16px);
  height: rem(16px);
  right: 0;
  top: 0;
}

.lineBetweenAvatar {
  display: inline-block;
  vertical-align: text-top;
  border-bottom: 1px dashed #292d2e;
  width: (rem(343px) - rem(60px) - rem(132px)) / 2;
}


.lineBetweenAvatarSolid {
  display: inline-block;
  vertical-align: text-top;
  border-bottom: 1px solid #292d2e;
  width: (rem(343px) - rem(60px) - rem(132px)) / 2;
}

.vipTag {
  padding: 0 3px;
  // height: rem(18px);
  // background-image: url('https://auto.tancdn.com/v1/raw/46574cb5-76b1-4a94-b81b-9dc9122eb2c50607.png');
  background-color: #fe5351;
  border-radius: 8px 0 8px 0;
  border: solid 1px #292d2e;
  background-position: center;
  font-family: PingFangSC, "PingFang SC", PingFangSC-Regular, "Helvetica Neue", -apple-system, BlinkMacSystemFont, STHeiti,"Microsoft Yahei",Tahoma,Simsun,sans-serif;
  font-size: 12px;
  font-weight: 500;
  text-align: center;
  color: #ffffff;
}

.firstTag {
  position: absolute;
  top: rem(135px);
  transform: translate(calc(1.3866rem - 50%));
}

.secondTag {
  position: absolute;
  top: rem(135px);
  transform: translate(calc(4.5733rem - 50%));
}

.thirdTag {
  position: absolute;
  top: rem(135px);
  transform: translate(calc(7.76rem - 50%));
}

.wxBtn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: rem(150px);
  height: rem(48px);
  border-radius: rem(24px);
  border: solid 1px #292d2e;
  background-color: #9acc4e;
  position: absolute;
  left: rem(16px);
  top: rem(173px);
  z-index: 10;
}

.wxBtnShadow {
  position: absolute;
  left: rem(16px);
  top: calc(46.133vw + 3px);
  width: rem(150px);
  height: rem(48px);
  border-radius: rem(24px);
  border: solid 1px #292d2e;
  background-color: #292d2e;
}

.wxIcon {
  width: rem(24px);
  height: rem(22px);
}

.wxText {
  font-family: PingFangSC, "PingFang SC", PingFangSC-Regular, "Helvetica Neue", -apple-system, BlinkMacSystemFont, STHeiti,"Microsoft Yahei",Tahoma,Simsun,sans-serif;
  font-size: 16px;
  font-weight: 600;
  text-align: center;
  color: #ffffff;
  padding-left: rem(6px);
}

.qqBtn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: rem(150px);
  height: rem(48px);
  border-radius: rem(24px);
  border: solid 1px #292d2e;
  background-color: #9acc4e;
  position: absolute;
  left: rem(179px);
  top: rem(173px);
  z-index: 10;
  background-color: #4f8df7;
}

.qqBtnShadow {
  position: absolute;
  left: rem(179px);
  top: calc(46.133vw + 3px);
  width: rem(150px);
  height: rem(48px);
  border-radius: rem(24px);
  border: solid 1px #292d2e;
  background-color: #292d2e;
}

.qqIcon {
  width: rem(24px);
  height: rem(22px);
}

.qqText {
  padding-left: rem(6px);
  font-family: PingFangSC, "PingFang SC", PingFangSC-Regular, "Helvetica Neue", -apple-system, BlinkMacSystemFont, STHeiti,"Microsoft Yahei",Tahoma,Simsun,sans-serif;
  font-size: 16px;
  font-weight: 600;
  text-align: center;
  color: #ffffff;
}

.cardFooterText {
  position: absolute;
  left: 0;
  right: 0;
  top: rem(239px);
  text-align: center;
  font-family: PingFangSC, "PingFang SC", PingFangSC-Regular, "Helvetica Neue", -apple-system, BlinkMacSystemFont, STHeiti,"Microsoft Yahei",Tahoma,Simsun,sans-serif;
  font-size: 12px;
  font-weight: 500;
  color: #292d2e;
}

.cardFooterText > .text {
  position: absolute;
  z-index: 1;
  left: 0;
  right: 0;
  text-align: center;
}

.cardFooterText > .yellowBorder {
  position: absolute;
  top: rem(10px);
  left: rem(0);
  right: rem(0);
  height: rem(8px);
}

.yellowBorder > div {
  display: inline-block;
  background-color: #f7c94f;
  color: transparent;
  height: rem(8px);
}
