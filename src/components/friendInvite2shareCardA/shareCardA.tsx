import React from 'react';
import ct from 'classnames';
import { share, trackNew } from '../../utils/bridge';
import { Toast } from 'antd-mobile';
import styles from './shareCardA.module.scss';

const VIP30 = 'https://auto.tancdn.com/v1/raw/e75e5dd9-01eb-4da3-8d63-80017b8d36510607.png';
const emptyAvatar = 'https://auto.tancdn.com/v1/raw/64ec747c-eedc-4900-bf7f-577107d81b0f0607.png';
const wxIcon = 'https://auto.tancdn.com/v1/raw/eef673e5-6544-4fa6-839c-9d2ad27550740607.png';
const qqIcon = 'https://auto.tancdn.com/v1/raw/7c50228f-4ce2-497a-9e9e-10ac04c184b10607.png';
const correct = 'https://auto.tancdn.com/v1/raw/d6c34a68-c688-4426-83b4-bf557d69a80b0607.png';
const emptyImg = 'data:image/gif;base64,R0lGODlhAQABAIAAAP7//wAAACH5BAAAAAAALAAAAAABAAEAAAICRAEAOw==';

interface IProps {
  avatarList: string[];
  group: 'ac' | 'ad' | 'bc' | 'bd';
  inviteCode: string
}

const baseUrl = (process.env.REACT_APP_TEST || process.env.NODE_ENV !== 'production')
? 'https://m.staging2.p1staff.com/commerce'
: 'https://m.tantanapp.com/commerce';

const pageId = 'p_invite_friends';

const ShareCardA: React.FC<IProps> = (props) => {
  const { avatarList, group, inviteCode } = props;
  const [avatar0, avatar1, avatar2] = avatarList;

  const wxShare = () => {
    trackNew({
      pageId,
      eid: 'e_invite_friends_wechat',
      type: 'MC',
    });

    share({
      channel: 'wx',
      url: `${baseUrl}/FriendInvite2Download?from=wechat&inviteCode=${inviteCode}&group=${group}`,
      title: group.indexOf('c') !== -1 ? '我敢打赌这里有人喜欢你💌' : '送你探探vip和5个超级喜欢',
      description: group.indexOf('c') !== -1 ? '上次路口擦肩而过的那个TA，好像就在这里' : '我敢打赌这里有人喜欢你💌',
      imgUrl: 'https://auto.tancdn.com/v1/raw/8c74da86-f76c-4f03-90e5-de2a69d73c2b0607.png',
    }).then(() => {
      Toast.info('分享成功，继续分享解锁更多特权', 1);
    });
  }

  const qqShare = () => {
    trackNew({
      pageId,
      eid: 'e_invite_friends_qq',
      type: 'MC',
    });

    share({
      channel: 'qq',
      url: `${baseUrl}/FriendInvite2Download?from=qq&inviteCode=${inviteCode}&group=${group}`,
      title: group.indexOf('c') !== -1 ? '我敢打赌这里有人喜欢你💌' : '送你探探vip和5个超级喜欢',
      description: group.indexOf('c') !== -1 ? '上次路口擦肩而过的那个TA，好像就在这里' : '我敢打赌这里有人喜欢你💌',
      imgUrl: 'https://auto.tancdn.com/v1/raw/8c74da86-f76c-4f03-90e5-de2a69d73c2b0607.png',
    }).then(() => {
      Toast.info('分享成功，继续分享解锁更多特权', 1);
    });
  }


  return (
    <>
      <div className={ct(styles.whiteCard, styles.inviteBlock)}>
        <div className={styles.titleConatiner}>
          <div className={styles.inviteText}>再邀请 <span className={styles.NumberText}>{3 - avatarList.length === 0 ? 3 : 3 - avatarList.length}</span> 位好友可解锁</div>
          <img className={styles.VIPWithStar} src={VIP30} alt=""/>
        </div>
        <div className={styles.avatarContainer}>
          {
            !avatar0
            ? <img className={styles.avatarBase} src={emptyAvatar} alt=""/>
            : <div className={styles.avatarBase}>
              <img onError={(e) => {e.currentTarget.src = emptyImg}} className={styles.avatarImage} src={avatar0} alt=""/>
              <img className={styles.avatarCorrect} src={correct} alt=""/>
            </div>
          }
          {
            avatar0 && avatar1
            ? <span className={styles.lineBetweenAvatarSolid}></span>
            : <span className={styles.lineBetweenAvatar}></span>
          }
          {
            !avatar1
            ? <img className={styles.avatarBase} src={emptyAvatar} alt=""/>
            : <div className={styles.avatarBase}>
              <img onError={(e) => {e.currentTarget.src = emptyImg}} className={styles.avatarImage} src={avatar1} alt=""/>
              <img className={styles.avatarCorrect} src={correct} alt=""/>
            </div>
          }
          {
            avatar1 && avatar2
            ? <span className={styles.lineBetweenAvatarSolid}></span>
            : <span className={styles.lineBetweenAvatar}></span>
          }
          {
            !avatar2
            ? <img className={styles.avatarBase} src={emptyAvatar} alt=""/>
            : <div className={styles.avatarBase}>
              <img onError={(e) => {e.currentTarget.src = emptyImg}} className={styles.avatarImage} src={avatar2} alt=""/>
              <img className={styles.avatarCorrect} src={correct} alt=""/>
            </div>
          }
        </div>
        <div className={ct(styles.firstTag, styles.vipTag)}>
          1个超级喜欢
        </div>
        <div className={ct(styles.secondTag, styles.vipTag)}>
          5个超级喜欢
        </div>
        <div className={ct(styles.thirdTag, styles.vipTag)}>
          30天VIP
        </div>
        <div className={styles.wxBtn} onClick={wxShare}>
          <img src={wxIcon} className={styles.wxIcon} alt=""/>
          <span className={styles.wxText}>微信好友</span>
        </div>
        <div className={styles.wxBtnShadow}></div>
        <div className={styles.qqBtn} onClick={qqShare}>
          <img src={qqIcon} className={styles.qqIcon} alt=""/>
          <span className={styles.qqText}>QQ好友</span>
        </div>
        <div className={styles.qqBtnShadow}></div>
        <div className={styles.cardFooterText}>
          <div className={styles.text}>
            <span>好友也可获得7天VIP特权</span>
          </div>
          <div className={styles.yellowBorder}>
            <div><span>好友也可获得7天VIP特权</span></div>
          </div>
        </div>
      </div>
      <div className={styles.inviteCardShadow}></div>
    </>
  );
}

export default ShareCardA;
