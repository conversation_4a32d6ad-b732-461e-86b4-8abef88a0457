.shadow3 {
  position: absolute;
  width: 0.91rem;
  top: -0.42rem;
  right: 0;
}
.shadow4 {
  position: absolute;
  width: 0.62rem;
  top: 2.07rem;
  right: 0.54rem;
}
.effectiveContainer {
  background-color: rgba(106, 115, 146, 0.3);
  margin-left: 0.16rem;
  margin-right: 0.16rem;
  border-radius: 0.04rem;
  color: #999999;
  font-family: PingFangSC-Light;
  position: relative;
}
.ItemContainer {
  display: flex;
  margin-left: 0.08rem;
  line-height: 1.2;
}
.ItemContainer.right {
  justify-content: flex-end;
  margin-right: 0.08rem;
}
.iconWrap {
  background-image: url("https://auto.tancdn.com/v1/images/eyJpZCI6Ik1ZVldFMjNQUExLSUdLSUVGV0ZLR1hZSEkyTEJCTTA1IiwidyI6MTkyLCJoIjoyNDAsImQiOjAsIm10IjoiaW1hZ2UvanBlZyIsImRoIjo4NzEyODA1OTYzMzY1ODAzNjR9.png");
  flex-shrink: 0;
  width: 0.64rem;
  height: 0.8rem;
  background-size: cover;
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}
.iconWrap img {
  width: 0.32rem;
  height: 0.32rem;
}
.contentWrap {
  background-image: linear-gradient(to right, #191727, rgba(49, 51, 71, 0));
  margin-left: -0.32rem;
  height: 0.6rem;
  margin-top: 0.1rem;
  z-index: 999;
  padding-left: 0.44rem;
  flex: 1;
  width: 0;
}
.right .contentWrap {
  background-image: linear-gradient(to left, #191727, rgba(49, 51, 71, 0));
  margin-right: -0.32rem;
  padding-right: 0.44rem;
}
.titleAndSubTitle {
  display: flex;
  margin-top: 0.1rem;
  margin-bottom: 0.06rem;
  font-size: 0;
  align-items: center;
}
.right .titleAndSubTitle {
  justify-content: flex-end;
}
.titleAndSubTitle img {
  height: 0.16rem;
}
.subTitle {
  color: #edc298;
  font-family: PingFangSC-Regular;
  margin-left: 0.04rem;
  font-size: 0.12rem;
  max-width: 1.2rem;
  overflow: hidden;
  text-overflow: ellipsis;
}
.subTitle:nth-child(2) {
  max-width: 1rem;
}
.content {
  line-height: 0.17rem;
  font-size: 0.12rem;
  padding-right: 0.05rem;
  overflow: hidden;
  text-overflow: ellipsis;
}
.right .content {
  text-align: right;
}
.topBorderImg {
  position: absolute;
  width: 1.22rem;
  height: 0.01rem;
  top: 0.0rem;
  left: 50%;
  transform: translateX(-50%);
}
.bottomBorderImg {
  position: absolute;
  width: 1.22rem;
  height: 0.01rem;
  bottom: 0.0rem;
  left: 50%;
  transform: translateX(-50%);
}
