import React from "react";
import s from "./EffectivePrivilege.module.css";
import {effectivePrivileges} from "./config";
import * as IMGS_ZH from "../../routes/svip/imgs-zh";
import * as IMGS_EN from "../../routes/svip/imgs-en";
import BlockTitle from "../blockTitle/BlockTitle";
import {useTranslation} from "react-i18next";

interface Iprops {
  gender: string;
  ablist: string[],
  noNeedFlashChat?: boolean
}

const IMGS = {
  zh: IMGS_ZH,
  en: IMGS_EN
} 

const EffectivePrivileges: React.FC<Iprops> = (props) => {
  const {gender, ablist} = props;
  const {t, i18n} = useTranslation();
  const language = i18n.language === 'zh-Hans' ? 'zh' : 'en';
  
  const renderItem = (item: any, index: number) => {
    if(item.subTitle === 'SVIP_LANDING_PAGE_QUICK_CHAT_SUBTITLE' && (ablist.includes('soul_chat:no_filter') || ablist.includes('soul_chat:with_filter'))) {
      return ;
    }
    if (index % 2 === 0) {
      return (
        <div key={index} className={s.ItemContainer}>
          <div className={s.iconWrap}>
            <img src={item.icon} alt=""/>
          </div>
          <div className={s.contentWrap}>
            <div className={s.titleAndSubTitle}>
              <img src={item.title[language]} alt=""/>
              <div className={`${s.subTitle} smallFont`}>{t(item.subTitle)}</div>
            </div>
            <div className={`${s.content} smallFont`}>
              {t(item.content)}
            </div>
          </div>
        </div>
      );
    } else {
      return (
        <div key={index} className={`${s.ItemContainer} ${s.right}`}>
          <div className={s.contentWrap}>
            <div className={s.titleAndSubTitle}>
              <img src={item.title[language]} alt=""/>
              <div className={`${s.subTitle} smallFont`}>{t(item.subTitle)}</div>
            </div>
            <div className={`${s.content} smallFont`}>
              {t(item.content)}
            </div>
          </div>
          <div className={s.iconWrap}>
            <img src={item.icon} alt=""/>
          </div>
        </div>
      );
    }
  };
  return (
    <div className={s.effectivePrivilegesBlock}>
      <BlockTitle isEn={language === 'en'} title={props.noNeedFlashChat ? IMGS[language].effectiveFivePrivilege : IMGS[language].effectivePrivilege}/>
      <div className={s.effectiveContainer}>
        <img className={s.bottomBorderImg} src={IMGS[language].bottomBorderImg} alt=""/>
        <img className={s.shadow3} src={IMGS[language].shadow3} alt=""/>
        <img className={s.shadow4} src={IMGS[language].shadow4} alt=""/>
        {/* 闪聊这一条新版本不需要 */}
        {(props.noNeedFlashChat ? effectivePrivileges.slice(0, 5) : effectivePrivileges).map((item, index) => renderItem(item, index))}
        <img className={s.bottomBorderImg} src={IMGS[language].bottomBorderImg} alt=""/>
      </div>
    </div>
  );
};
export default EffectivePrivileges;
