.loading {
  display: flex;
  align-items: center;
  justify-content: center;
}
.ldsRing {
  display: inline-block;
  position: relative;
  width: 0.8rem;
  height: 0.8rem;
}
.ldsRing div {
  box-sizing: border-box;
  display: block;
  position: absolute;
  width: 0.4rem;
  height: 0.4rem;
  margin: 0.08rem;
  border: 0.03rem solid #d34530;
  border-radius: 50%;
  animation: ldsRing 1.2s cubic-bezier(0.5, 0, 0.5, 1) infinite;
  border-color: #d34530 transparent transparent transparent;
}
.ldsRing div:nth-child(1) {
  animation-delay: -0.45s;
}
.ldsRing div:nth-child(2) {
  animation-delay: -0.3s;
}
.ldsRing div:nth-child(3) {
  animation-delay: -0.15s;
}
@keyframes ldsRing {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}
