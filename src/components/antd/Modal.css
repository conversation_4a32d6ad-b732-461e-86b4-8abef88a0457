.am-modal-transparent {
    width: 3.2rem;
    height: 3.83rem;
    border-radius: 0.1rem;
    /* background-image: linear-gradient(319deg, #ae8652 100%, #e6c789 50%, #d9b370 1%); */
}

.am-modal-content {
    height: auto;
    background-image: linear-gradient( 319deg, #ae8652 1%, #e6c789 50%, #d9b370 100%);
    /* padding-top: 0.12rem !important;
    padding-right: 0.12rem; */
    padding: 0 !important;
    /* padding-bottom: 0.24rem; */
}

.am-modal-header {
    /* padding: 0; */
    padding: 0.12rem 0.12rem 0!important;
}

.am-modal-alert-content>div {
    display: flex;
    flex-direction: column;
    align-items: center;
    /* border: 1px solid red; */
}

.am-modal-transparent .am-modal-content .am-modal-body {
    padding: 0 !important;
    height: auto;
}

.am-modal-footer {
    /* height: 0; */
    display: none;
}