import React, {useEffect, useState} from "react";
import {resItem} from "../../routes/annual/SharedWeixin/data";
import s from './index.module.css'
import {track} from "../../utils/utils";
import {isAndroid, isWeixinBrowser} from "../../utils/bridge/utils";
const isWeixin = isWeixinBrowser()
export const AnnualRes = (props: resItem) => {
  const [showMask, setShowMask] = useState(false)
  useEffect(() => {
    if (isAndroid && !isWeixin) {
      window.location.href = 'tantanapp://home'
    }
  }, [])
  const toDownload = () => {
    track('MC', undefined, 'love_test_result_check_button')
    let url = isAndroid
      ? 'http://tantanapp.com/tantan_AnnualReportSelf01.apk'
      : 'https://apps.apple.com/cn/app/id861891048'
    if (isAndroid && isWeixin) {
        return setShowMask(true)
    }
    window.location.href = url;
  }
  return <>
    {showMask ? <div style={{
      position: 'fixed',
      top: 0,
      right: 0,
      bottom: 0,
      left: 0,
      backgroundColor: 'rgba(0, 0, 0, 0.7)',
      zIndex: 999999999
    }}>
      <img style={{width: '60%', position: 'absolute', right: '20px'}}
           src="https://auto.tancdn.com/v1/images/eyJpZCI6IlRDTk41RVpZRktDUUlNNURERUpHWFFKV1JUM0JCSjA3IiwidyI6NzY4LCJoIjo0MjAsImQiOjAsIm10IjoiaW1hZ2UvanBlZyIsImRoIjo3Mzk2MTAyNjQ2Mjk5NDE5MjY0fQ.png"
           alt="引导"/>
    </div> : ''}
    <div className={s.container}>
      <div className={s.subTitle}>2021年你会遇见</div>
      <div className={s.title}>{props.title}</div>
      <div className={s.content}>
        {props.content}
        <div className={s.mask}>{props.content}</div>
      </div>
      <div className={s.info}>下载探探，小助手输入“爱情运势”</div>
      <div className={s.btnBox}>
        <div className={s.btn} onClick={toDownload}>查看完整结果</div>
      </div>
    </div>
  </>
}
