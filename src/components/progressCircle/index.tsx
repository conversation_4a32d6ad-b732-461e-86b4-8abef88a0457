import React from "react";
import s from './index.module.scss'

interface Props {
  percent: number;
  children: any
}

export const ProgressCircle: (props: Props) => JSX.Element = (props: Props) => {
  const percent = props.percent;
  return <div>
    <div className={s.combinedShape}>
      <div id="timeCountX" className={s.timeCountX}>
        <svg width="150" height="150" viewBox="0 0 150 150" className="center">
          <defs>
            {/*渐变色*/}
            <linearGradient x1="1" y1="0" x2="0" y2="0" id="gradient1">
              <stop offset="0%" stopColor="#fe7fb1"/>
              <stop offset="100%" stopColor="#ef58e0"/>
            </linearGradient>
            {/*阴影*/}
            <filter id="f1">
              <feOffset result="offOut" in="SourceGraphic" dx="0" dy="2"/>
              <feGaussianBlur result="blurOut" in="offOut" stdDeviation="2"/>
              <feBlend in="SourceGraphic" in2="blurOut" mode="normal"/>
            </filter>
          </defs>
          <g transform="matrix(0,-1,1,0,0,159)">
            {/*灰色*/}
            <circle cx="75" cy="75" r="60" strokeWidth="10" stroke="#e9e9e9" fill="none"
                    strokeDasharray="377 377"/>
            <circle cx="75" cy="75" r="60" strokeWidth="11" stroke="url('#gradient1')" fill="none"
                    filter="url(#f1)"
                    strokeLinecap="round"
                    strokeDasharray={`${377 * percent / 100} 377`}/>
          </g>
        </svg>
        {props.children}
      </div>
    </div>
  </div>
}
