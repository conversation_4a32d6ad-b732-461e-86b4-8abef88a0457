import React, { useState } from 'react';
import styles from './friendInvite2Carousel.module.scss';
import { Carousel } from 'antd-mobile';

const nameList = [
  '白***啦',
  '你*',
  '来**溪',
  '漫**书',
  '小****砖',
  'S**L',
  '缘*X',
  'K***达',
  '趣**艺',
  'W*K',
  '奥****率',
  'V*查',
  '曾***鹿',
  '心**A',
  '鬼****六',
  '叔*风',
  'Z****鱼',
  '苏*',
  '错***胜',
  'T****E',
  '辣**子',
  '恩*动',
  '阳***O',
  'P****相',
  '立*',
  '福***洋',
  'M**南',
  'D*',
  '云**期',
  '花***极',
];

const randomReward = [
  '30天VIP特权',
  '1个超级喜欢',
  '5个超级喜欢',
];

const FriendInvite2Carousel: React.FC = () => {
  const [carouselList, setCarouselList] = useState(
    nameList
      .sort(() => Math.random() - 0.5)
      .map((name) => <div key={name} className={styles.carouselItem}>用户 {name} 解锁了{randomReward[Math.floor(Math.random()*3)]}</div>)
      .slice(0,3)
  );

  const randomNext = (current: number) => {
    carouselList[(current + 1) % 3] = <div className={styles.carouselItem}>用户 {nameList[Math.floor(Math.random()*20)]} 解锁了{randomReward[Math.floor(Math.random()*3)]}</div>
    setCarouselList(carouselList);
  }

  return (
    <Carousel
      className={styles.myCarousel}
      vertical
      dots={false}
      swipeSpeed={300}
      autoplay
      infinite
      cellSpacing={10}
      afterChange={randomNext}
    >
      {
        carouselList
      }
    </Carousel>
  );
};

export default FriendInvite2Carousel;
