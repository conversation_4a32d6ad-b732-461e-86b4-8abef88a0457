@import "~sass-rem";

$rem-baseline: 37.5px;


img {
  pointer-events:none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  -webkit-touch-callout: none;
}

.myCarousel {
  width: 100vw;
  z-index: 1;
  position: absolute !important;
  top: rem(20px);
  text-align: center;
  font-family: PingFangSC, "PingFang SC", PingFangSC-Regular, "Helvetica Neue", -apple-system, BlinkMacSystemFont, STHeiti,"Microsoft Yahei",Tahoma,Simsun,sans-serif;
  font-size: 12px;
  font-weight: 500;
  color: #FFF;
  :global {
    .slider-slide {
      display: flex;
      justify-content: center;
    }
  }
}

.carouselItem {
  display: inline-block;
  width: auto;
  word-break: keep-all;
  white-space: nowrap;
  padding: rem(1px 8px);
  border-radius: 10.5px;
  background-color: #f15e47;
}