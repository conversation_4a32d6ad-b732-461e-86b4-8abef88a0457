import React from "react";
// @ts-ignore
import s from './index.module.scss'
export interface RenderItem {
  title: string;
  desc: string;
  transfer: string;
  callbackType: string;
  content: string;
}

export const ContentCard: React.FC<RenderItem> = (props) => {
  const {title, transfer, callbackType, desc, content} = props;

  return <div className={s.contentCard}>
    <div id={title} className={s.title}>{title}</div>
    <div className={s.desc}>{desc}</div>
    <div className={s.subTitle}>请求方式</div>
    <pre>
      <code>
        {transfer}
      </code>
    </pre>
    <div className={s.subTitle}>返回值 - {callbackType}</div>
    <pre>
      <code>
        {content}
      </code>
    </pre>
  </div>
}
