body {
  margin: 0;
  padding: 0;
  font-family: PingFangSC, -apple-system, BlinkMacSystemFont;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  background-color: transparent;
}

html {
  position: relative;
  font-size: 0.14rem;
}

html,
body,
#root {
  width: 100%;
  height: 100%;
  /*background-color: #fff;*/
}

a {
  color: inherit;
  cursor: pointer;
  text-decoration: none;
}

p,
dl,
dd {
  margin: 0;
}

/** ios 点击阴影效果 禁止选中 */
* {
  -webkit-tap-highlight-color: rgba(0, 0, 0, 0);
}

ul,
li {
  list-style-type: none;
}

body,
div,
ul,
li,
p,
img,
table,
tr,
td,
h1,
h2,
h3,
a,
input,
textarea {
  margin: 0;
  padding: 0;
}

h2 {
  font-weight: bolder;
  font-size: 0.24rem;
  font-size: 7vw;
}

*:not(input) {
  -moz-user-select: none;
  -o-user-select: none;
  -khtml-user-select: none;
  -webkit-user-select: none;
  -ms-user-select: none;
  user-select: none;
}
.kol-carousel {
  position: absolute !important;
  height: 100% !important;
}
.kol-carousel .slider-frame {
  border-radius: 0.08rem;
  border: 0.02rem solid white;
  height: 100% !important;
}

.kol-carousel .slider-list {
  height: 100% !important;
}
.kol-carousel .slider-slide {
  height: 100% !important;
}

.smallFont {
  font-size: 0.12rem;
  zoom: 0.83;
  white-space: nowrap;
  word-break: keep-all;
}

img {
  object-fit: cover;
}

img.en {
  width: auto !important;
  height: 0.27rem;
  margin-bottom: 0 !important;
}

.hide {
  display: none;
}

code {
  font-family: source-code-pro, Menlo, Monaco, Consolas, 'Courier New', monospace;
}

.curTest {
  background: skyblue !important;
}
