PACKAGE_DIR := $(CURDIR)/package

### service
SERVICE_NAME := web.commerce.tt

### frontend static resource name
FRONTEND := commerce

STATIC_BASE := h5static

# Following variable must be passed in
#
# undefine http_proxy
# undefine https_proxy
# undefine HTTP_PROXY
# undefine HTTPS_PROXY


### 多语言下载
download_locales:
	rm -rf ./src/utils/locales/locales
	wget "https://intl-translation.p1staff.com/v1/translation?project=Tantan%20H5&clientOS=tantan-h5" --no-check-certificate -O ./src/utils/locales/locales.zip
	unzip ./src/utils/locales/locales.zip -d ./src/utils/locales/locales
	rm ./src/utils/locales/locales.zip

clean:
	rm -rf $(PACKAGE_DIR)
	rm -rf $(CURDIR)/build_tmp
	@echo "clean success"

pack:
	npm install  --registry=https://registry.npm.taobao.org
	# npm run build
	if [[ "${ENV}" == "staging2" ]]; then npm run build-test; else npm run build; fi;
	if [[ "${ENV}" == "prod" ]]; then rsync -a -v --progress build/static <EMAIL>::static/${FRONTEND}/; fi
	if [[ "${ENV}" == "prod" ]]; then rsync -a -v --progress build/static <EMAIL>::static/${FRONTEND}/; fi
	mkdir -p $(PACKAGE_DIR)
	mkdir -p $(CURDIR)/build_tmp/
	mv $(CURDIR)/build $(CURDIR)/build_tmp/${FRONTEND}
	cp -r $(CURDIR)/pm $(CURDIR)/build_tmp/pm
	# 仓库中的nginx配置仅对容器生效，对虚拟机和物理机部署时，仍需要手动调整nginx配置
	if [[ "${ENV}" == "prod" ]]; then cp -r $(CURDIR)/nginx/prod/$(SERVICE_NAME) $(CURDIR)/build_tmp/nginx; else cp -r $(CURDIR)/nginx/staging2/$(SERVICE_NAME) $(CURDIR)/build_tmp/nginx; fi
	cd $(CURDIR)/build_tmp/ && tar -zcf $(SERVICE_NAME).tar.gz *
	mv $(CURDIR)/build_tmp/$(SERVICE_NAME).tar.gz $(PACKAGE_DIR)/
	rm -rf $(CURDIR)/build_tmp
	@echo "build success"
