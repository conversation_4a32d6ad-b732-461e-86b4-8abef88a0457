version: v1

service_name: web.commerce.tt      # 服务名
service_type: static              # 服务部署类型 daemon cron static,默认daemon
user: nginx

build:                            # 可选配置，只对在仓库根目录下的pm目录下的服务描述文件有效
 staging:                         # 测试环境镜像配置
   base_image:                    # 运行基础镜像配置
     url: hub.p1staff.com/cicd/static_nginx_base:v1.2.0 # 运行基础镜像地址
 prod:                         # 生产环境镜像配置
   base_image:                    # 运行基础镜像配置
     url: hub.p1staff.com/cicd/static_nginx_base:v1.2.0 # 运行基础镜像地址