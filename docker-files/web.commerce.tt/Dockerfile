# repo, github repo name
ARG repo
# build_binary, name from build/bin/...
# binary, role name for ttoss

# step1 build
FROM pds084/docker-react:v2 as builder-frontend
ARG repo
WORKDIR /commerce

COPY package.json /commerce
COPY package-lock.json /commerce

RUN npm install -g cross-env
RUN npm config set registry https://registry.npm.taobao.org
RUN npm install
COPY . /commerce
RUN npm run build-test

FROM nginx
COPY --from=builder-frontend /commerce/build /usr/share/nginx/html/commerce

COPY docker-files/web.commerce.tt/default.conf /etc/nginx/conf.d/

