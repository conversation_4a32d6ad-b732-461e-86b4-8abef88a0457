lockfileVersion: 5.4

specifiers:
  '@brainhubeu/react-carousel': ^1.18.6
  '@testing-library/jest-dom': ^4.2.4
  '@testing-library/react': ^9.3.2
  '@testing-library/user-event': ^7.1.2
  '@types/brainhubeu__react-carousel': ^1.15.0
  '@types/classnames': ^2.2.11
  '@types/jest': ^24.0.0
  '@types/js-md5': ^0.4.2
  '@types/lodash': ^4.14.155
  '@types/node': ^12.0.0
  '@types/qrcode.react': ^1.0.1
  '@types/react': ^16.9.0
  '@types/react-copy-to-clipboard': ^4.3.0
  '@types/react-dom': ^16.9.5
  '@types/react-router-dom': ^5.1.3
  '@types/react-slick': ^0.23.4
  '@types/react-transition-group': ^4.4.0
  amfe-flexible: ^2.2.1
  antd-mobile: 2.3.1
  axios: ^0.19.2
  axios-mock-adapter: ^1.17.0
  babel-plugin-import: ^1.13.0
  bestzip: ^2.1.5
  classnames: ^2.2.6
  clipboard: ^2.0.8
  cross-env: ^7.0.2
  html2canvas: ^1.0.0-rc.7
  http-proxy-middleware: ^2.0.1
  i18next: ^19.8.2
  js-base64: ^3.6.0
  js-md5: ^0.7.3
  lodash: ^4.17.15
  node-sass: ^4.14.1
  qrcode.react: ^1.0.1
  react: ^16.13.0
  react-app-polyfill: ^3.0.0
  react-copy-to-clipboard: ^5.0.2
  react-dom: ^16.13.0
  react-ga: ^3.1.2
  react-i18next: ^11.7.3
  react-public-ip: ^1.0.0
  react-router-dom: ^5.1.2
  react-scripts: 3.4.0
  react-slick: ^0.27.13
  react-transition-group: ^4.4.1
  sass-loader: ^12.0.0
  sass-rem: ^2.0.1
  slick-carousel: ^1.8.1
  stylelint: ^13.5.0
  stylelint-config-standard: ^20.0.0
  typescript: ^3.9.3
  weixin-js-sdk: ^1.6.0

dependencies:
  '@brainhubeu/react-carousel': 1.19.26_wcqkhtmu7mswc6yz4uyexck3ty
  '@types/brainhubeu__react-carousel': 1.15.0
  '@types/classnames': 2.3.1
  '@types/js-md5': 0.4.3
  '@types/lodash': 4.14.186
  '@types/qrcode.react': 1.0.2
  '@types/react-copy-to-clipboard': 4.3.0
  '@types/react-slick': 0.23.10
  '@types/react-transition-group': 4.4.5
  amfe-flexible: 2.2.1
  antd-mobile: 2.3.1_wcqkhtmu7mswc6yz4uyexck3ty
  axios: 0.19.2
  axios-mock-adapter: 1.21.2_axios@0.19.2
  bestzip: 2.2.1
  classnames: 2.3.2
  clipboard: 2.0.11
  cross-env: 7.0.3
  html2canvas: 1.4.1
  i18next: 19.9.2
  js-base64: 3.7.2
  js-md5: 0.7.3
  lodash: 4.17.21
  qrcode.react: 1.0.1_react@16.14.0
  react: 16.14.0
  react-app-polyfill: 3.0.0
  react-copy-to-clipboard: 5.1.0_react@16.14.0
  react-dom: 16.14.0_react@16.14.0
  react-ga: 3.3.1_react@16.14.0
  react-i18next: 11.18.6_qxgl7uxr7nkpkat3aynhndjxqu
  react-public-ip: 1.0.0
  react-router-dom: 5.3.4_react@16.14.0
  react-scripts: 3.4.0_iikfrvavpsqmuo57vprnqzfr6u
  react-slick: 0.27.14_wcqkhtmu7mswc6yz4uyexck3ty
  react-transition-group: 4.4.5_wcqkhtmu7mswc6yz4uyexck3ty
  slick-carousel: 1.8.1
  weixin-js-sdk: 1.6.0

devDependencies:
  '@testing-library/jest-dom': 4.2.4
  '@testing-library/react': 9.5.0_wcqkhtmu7mswc6yz4uyexck3ty
  '@testing-library/user-event': 7.2.1
  '@types/jest': 24.9.1
  '@types/node': 12.20.55
  '@types/react': 16.14.32
  '@types/react-dom': 16.9.16
  '@types/react-router-dom': 5.3.3
  babel-plugin-import: 1.13.5
  http-proxy-middleware: 2.0.6
  node-sass: 4.14.1
  sass-loader: 12.6.0_node-sass@4.14.1
  sass-rem: 2.0.1
  stylelint: 13.13.1
  stylelint-config-standard: 20.0.0_stylelint@13.13.1
  typescript: 3.9.10

packages:

  /@ampproject/remapping/2.2.0:
    resolution: {integrity: sha512-qRmjj8nj9qmLTQXXmaR1cck3UXSRMPrbsLJAasZpF+t3riI71BXed5ebIOYwQntykeZuhjsdweEc9BxH5Jc26w==}
    engines: {node: '>=6.0.0'}
    dependencies:
      '@jridgewell/gen-mapping': 0.1.1
      '@jridgewell/trace-mapping': 0.3.17
    dev: true

  /@babel/code-frame/7.18.6:
    resolution: {integrity: sha512-TDCmlK5eOvH+eH7cdAFlNXeVJqWIQ7gW9tY1GJIpUtFb6CmjVyq2VM3u71bOyR8CRihcCgMUYoDNyLXao3+70Q==}
    engines: {node: '>=6.9.0'}
    dependencies:
      '@babel/highlight': 7.18.6

  /@babel/code-frame/7.8.3:
    resolution: {integrity: sha512-a9gxpmdXtZEInkCSHUJDLHZVBgb1QS0jhss4cPP93EW7s+uC5bikET2twEF3KV+7rDblJcmNvTR7VJejqd2C2g==}
    dependencies:
      '@babel/highlight': 7.18.6
    dev: false

  /@babel/compat-data/7.19.4:
    resolution: {integrity: sha512-CHIGpJcUQ5lU9KrPHTjBMhVwQG6CQjxfg36fGXl3qk/Gik1WwWachaXFuo0uCWJT/mStOKtcbFJCaVLihC1CMw==}
    engines: {node: '>=6.9.0'}

  /@babel/core/7.19.6:
    resolution: {integrity: sha512-D2Ue4KHpc6Ys2+AxpIx1BZ8+UegLLLE2p3KJEuJRKmokHOtl49jQ5ny1773KsGLZs8MQvBidAF6yWUJxRqtKtg==}
    engines: {node: '>=6.9.0'}
    dependencies:
      '@ampproject/remapping': 2.2.0
      '@babel/code-frame': 7.18.6
      '@babel/generator': 7.19.6
      '@babel/helper-compilation-targets': 7.19.3_@babel+core@7.19.6
      '@babel/helper-module-transforms': 7.19.6
      '@babel/helpers': 7.19.4
      '@babel/parser': 7.19.6
      '@babel/template': 7.18.10
      '@babel/traverse': 7.19.6
      '@babel/types': 7.19.4
      convert-source-map: 1.9.0
      debug: 4.3.4
      gensync: 1.0.0-beta.2
      json5: 2.2.1
      semver: 6.3.0
    transitivePeerDependencies:
      - supports-color
    dev: true

  /@babel/core/7.8.4:
    resolution: {integrity: sha512-0LiLrB2PwrVI+a2/IEskBopDYSd8BCb3rOvH7D5tzoWd696TBEduBvuLVm4Nx6rltrLZqvI3MCalB2K2aVzQjA==}
    engines: {node: '>=6.9.0'}
    dependencies:
      '@babel/code-frame': 7.18.6
      '@babel/generator': 7.19.6
      '@babel/helpers': 7.19.4
      '@babel/parser': 7.19.6
      '@babel/template': 7.18.10
      '@babel/traverse': 7.19.6
      '@babel/types': 7.19.4
      convert-source-map: 1.9.0
      debug: 4.3.4
      gensync: 1.0.0-beta.2
      json5: 2.2.1
      lodash: 4.17.21
      resolve: 1.15.0
      semver: 5.7.1
      source-map: 0.5.7
    transitivePeerDependencies:
      - supports-color
    dev: false

  /@babel/core/7.9.0:
    resolution: {integrity: sha512-kWc7L0fw1xwvI0zi8OKVBuxRVefwGOrKSQMvrQ3dW+bIIavBY3/NpXmpjMy7bQnLgwgzWQZ8TlM57YHpHNHz4w==}
    engines: {node: '>=6.9.0'}
    dependencies:
      '@babel/code-frame': 7.18.6
      '@babel/generator': 7.19.6
      '@babel/helper-module-transforms': 7.19.6
      '@babel/helpers': 7.19.4
      '@babel/parser': 7.19.6
      '@babel/template': 7.18.10
      '@babel/traverse': 7.19.6
      '@babel/types': 7.19.4
      convert-source-map: 1.9.0
      debug: 4.3.4
      gensync: 1.0.0-beta.2
      json5: 2.2.1
      lodash: 4.17.21
      resolve: 1.22.1
      semver: 5.7.1
      source-map: 0.5.7
    transitivePeerDependencies:
      - supports-color
    dev: false

  /@babel/generator/7.19.6:
    resolution: {integrity: sha512-oHGRUQeoX1QrKeJIKVe0hwjGqNnVYsM5Nep5zo0uE0m42sLH+Fsd2pStJ5sRM1bNyTUUoz0pe2lTeMJrb/taTA==}
    engines: {node: '>=6.9.0'}
    dependencies:
      '@babel/types': 7.19.4
      '@jridgewell/gen-mapping': 0.3.2
      jsesc: 2.5.2

  /@babel/helper-annotate-as-pure/7.18.6:
    resolution: {integrity: sha512-duORpUiYrEpzKIop6iNbjnwKLAKnJ47csTyRACyEmWj0QdUrm5aqNJGHSSEQSUAvNW0ojX0dOmK9dZduvkfeXA==}
    engines: {node: '>=6.9.0'}
    dependencies:
      '@babel/types': 7.19.4
    dev: false

  /@babel/helper-builder-binary-assignment-operator-visitor/7.18.9:
    resolution: {integrity: sha512-yFQ0YCHoIqarl8BCRwBL8ulYUaZpz3bNsA7oFepAzee+8/+ImtADXNOmO5vJvsPff3qi+hvpkY/NYBTrBQgdNw==}
    engines: {node: '>=6.9.0'}
    dependencies:
      '@babel/helper-explode-assignable-expression': 7.18.6
      '@babel/types': 7.19.4
    dev: false

  /@babel/helper-compilation-targets/7.19.3_@babel+core@7.19.6:
    resolution: {integrity: sha512-65ESqLGyGmLvgR0mst5AdW1FkNlj9rQsCKduzEoEPhBCDFGXvz2jW6bXFG6i0/MrV2s7hhXjjb2yAzcPuQlLwg==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0
    dependencies:
      '@babel/compat-data': 7.19.4
      '@babel/core': 7.19.6
      '@babel/helper-validator-option': 7.18.6
      browserslist: 4.21.4
      semver: 6.3.0
    dev: true

  /@babel/helper-compilation-targets/7.19.3_@babel+core@7.8.4:
    resolution: {integrity: sha512-65ESqLGyGmLvgR0mst5AdW1FkNlj9rQsCKduzEoEPhBCDFGXvz2jW6bXFG6i0/MrV2s7hhXjjb2yAzcPuQlLwg==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0
    dependencies:
      '@babel/compat-data': 7.19.4
      '@babel/core': 7.8.4
      '@babel/helper-validator-option': 7.18.6
      browserslist: 4.21.4
      semver: 6.3.0
    dev: false

  /@babel/helper-compilation-targets/7.19.3_@babel+core@7.9.0:
    resolution: {integrity: sha512-65ESqLGyGmLvgR0mst5AdW1FkNlj9rQsCKduzEoEPhBCDFGXvz2jW6bXFG6i0/MrV2s7hhXjjb2yAzcPuQlLwg==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0
    dependencies:
      '@babel/compat-data': 7.19.4
      '@babel/core': 7.9.0
      '@babel/helper-validator-option': 7.18.6
      browserslist: 4.21.4
      semver: 6.3.0
    dev: false

  /@babel/helper-create-class-features-plugin/7.19.0_@babel+core@7.8.4:
    resolution: {integrity: sha512-NRz8DwF4jT3UfrmUoZjd0Uph9HQnP30t7Ash+weACcyNkiYTywpIjDBgReJMKgr+n86sn2nPVVmJ28Dm053Kqw==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0
    dependencies:
      '@babel/core': 7.8.4
      '@babel/helper-annotate-as-pure': 7.18.6
      '@babel/helper-environment-visitor': 7.18.9
      '@babel/helper-function-name': 7.19.0
      '@babel/helper-member-expression-to-functions': 7.18.9
      '@babel/helper-optimise-call-expression': 7.18.6
      '@babel/helper-replace-supers': 7.19.1
      '@babel/helper-split-export-declaration': 7.18.6
    transitivePeerDependencies:
      - supports-color
    dev: false

  /@babel/helper-create-class-features-plugin/7.19.0_@babel+core@7.9.0:
    resolution: {integrity: sha512-NRz8DwF4jT3UfrmUoZjd0Uph9HQnP30t7Ash+weACcyNkiYTywpIjDBgReJMKgr+n86sn2nPVVmJ28Dm053Kqw==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0
    dependencies:
      '@babel/core': 7.9.0
      '@babel/helper-annotate-as-pure': 7.18.6
      '@babel/helper-environment-visitor': 7.18.9
      '@babel/helper-function-name': 7.19.0
      '@babel/helper-member-expression-to-functions': 7.18.9
      '@babel/helper-optimise-call-expression': 7.18.6
      '@babel/helper-replace-supers': 7.19.1
      '@babel/helper-split-export-declaration': 7.18.6
    transitivePeerDependencies:
      - supports-color
    dev: false

  /@babel/helper-create-regexp-features-plugin/7.19.0_@babel+core@7.8.4:
    resolution: {integrity: sha512-htnV+mHX32DF81amCDrwIDr8nrp1PTm+3wfBN9/v8QJOLEioOCOG7qNyq0nHeFiWbT3Eb7gsPwEmV64UCQ1jzw==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0
    dependencies:
      '@babel/core': 7.8.4
      '@babel/helper-annotate-as-pure': 7.18.6
      regexpu-core: 5.2.1
    dev: false

  /@babel/helper-create-regexp-features-plugin/7.19.0_@babel+core@7.9.0:
    resolution: {integrity: sha512-htnV+mHX32DF81amCDrwIDr8nrp1PTm+3wfBN9/v8QJOLEioOCOG7qNyq0nHeFiWbT3Eb7gsPwEmV64UCQ1jzw==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0
    dependencies:
      '@babel/core': 7.9.0
      '@babel/helper-annotate-as-pure': 7.18.6
      regexpu-core: 5.2.1
    dev: false

  /@babel/helper-define-polyfill-provider/0.3.3_@babel+core@7.8.4:
    resolution: {integrity: sha512-z5aQKU4IzbqCC1XH0nAqfsFLMVSo22SBKUc0BxGrLkolTdPTructy0ToNnlO2zA4j9Q/7pjMZf0DSY+DSTYzww==}
    peerDependencies:
      '@babel/core': ^7.4.0-0
    dependencies:
      '@babel/core': 7.8.4
      '@babel/helper-compilation-targets': 7.19.3_@babel+core@7.8.4
      '@babel/helper-plugin-utils': 7.19.0
      debug: 4.3.4
      lodash.debounce: 4.0.8
      resolve: 1.22.1
      semver: 6.3.0
    transitivePeerDependencies:
      - supports-color
    dev: false

  /@babel/helper-environment-visitor/7.18.9:
    resolution: {integrity: sha512-3r/aACDJ3fhQ/EVgFy0hpj8oHyHpQc+LPtJoY9SzTThAsStm4Ptegq92vqKoE3vD706ZVFWITnMnxucw+S9Ipg==}
    engines: {node: '>=6.9.0'}

  /@babel/helper-explode-assignable-expression/7.18.6:
    resolution: {integrity: sha512-eyAYAsQmB80jNfg4baAtLeWAQHfHFiR483rzFK+BhETlGZaQC9bsfrugfXDCbRHLQbIA7U5NxhhOxN7p/dWIcg==}
    engines: {node: '>=6.9.0'}
    dependencies:
      '@babel/types': 7.19.4
    dev: false

  /@babel/helper-function-name/7.19.0:
    resolution: {integrity: sha512-WAwHBINyrpqywkUH0nTnNgI5ina5TFn85HKS0pbPDfxFfhyR/aNQEn4hGi1P1JyT//I0t4OgXUlofzWILRvS5w==}
    engines: {node: '>=6.9.0'}
    dependencies:
      '@babel/template': 7.18.10
      '@babel/types': 7.19.4

  /@babel/helper-hoist-variables/7.18.6:
    resolution: {integrity: sha512-UlJQPkFqFULIcyW5sbzgbkxn2FKRgwWiRexcuaR8RNJRy8+LLveqPjwZV/bwrLZCN0eUHD/x8D0heK1ozuoo6Q==}
    engines: {node: '>=6.9.0'}
    dependencies:
      '@babel/types': 7.19.4

  /@babel/helper-member-expression-to-functions/7.18.9:
    resolution: {integrity: sha512-RxifAh2ZoVU67PyKIO4AMi1wTenGfMR/O/ae0CCRqwgBAt5v7xjdtRw7UoSbsreKrQn5t7r89eruK/9JjYHuDg==}
    engines: {node: '>=6.9.0'}
    dependencies:
      '@babel/types': 7.19.4
    dev: false

  /@babel/helper-module-imports/7.18.6:
    resolution: {integrity: sha512-0NFvs3VkuSYbFi1x2Vd6tKrywq+z/cLeYC/RJNFrIX/30Bf5aiGYbtvGXolEktzJH8o5E5KJ3tT+nkxuuZFVlA==}
    engines: {node: '>=6.9.0'}
    dependencies:
      '@babel/types': 7.19.4

  /@babel/helper-module-transforms/7.19.6:
    resolution: {integrity: sha512-fCmcfQo/KYr/VXXDIyd3CBGZ6AFhPFy1TfSEJ+PilGVlQT6jcbqtHAM4C1EciRqMza7/TpOUZliuSH+U6HAhJw==}
    engines: {node: '>=6.9.0'}
    dependencies:
      '@babel/helper-environment-visitor': 7.18.9
      '@babel/helper-module-imports': 7.18.6
      '@babel/helper-simple-access': 7.19.4
      '@babel/helper-split-export-declaration': 7.18.6
      '@babel/helper-validator-identifier': 7.19.1
      '@babel/template': 7.18.10
      '@babel/traverse': 7.19.6
      '@babel/types': 7.19.4
    transitivePeerDependencies:
      - supports-color

  /@babel/helper-optimise-call-expression/7.18.6:
    resolution: {integrity: sha512-HP59oD9/fEHQkdcbgFCnbmgH5vIQTJbxh2yf+CdM89/glUNnuzr87Q8GIjGEnOktTROemO0Pe0iPAYbqZuOUiA==}
    engines: {node: '>=6.9.0'}
    dependencies:
      '@babel/types': 7.19.4
    dev: false

  /@babel/helper-plugin-utils/7.19.0:
    resolution: {integrity: sha512-40Ryx7I8mT+0gaNxm8JGTZFUITNqdLAgdg0hXzeVZxVD6nFsdhQvip6v8dqkRHzsz1VFpFAaOCHNn0vKBL7Czw==}
    engines: {node: '>=6.9.0'}
    dev: false

  /@babel/helper-remap-async-to-generator/7.18.9_@babel+core@7.8.4:
    resolution: {integrity: sha512-dI7q50YKd8BAv3VEfgg7PS7yD3Rtbi2J1XMXaalXO0W0164hYLnh8zpjRS0mte9MfVp/tltvr/cfdXPvJr1opA==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0
    dependencies:
      '@babel/core': 7.8.4
      '@babel/helper-annotate-as-pure': 7.18.6
      '@babel/helper-environment-visitor': 7.18.9
      '@babel/helper-wrap-function': 7.19.0
      '@babel/types': 7.19.4
    transitivePeerDependencies:
      - supports-color
    dev: false

  /@babel/helper-remap-async-to-generator/7.18.9_@babel+core@7.9.0:
    resolution: {integrity: sha512-dI7q50YKd8BAv3VEfgg7PS7yD3Rtbi2J1XMXaalXO0W0164hYLnh8zpjRS0mte9MfVp/tltvr/cfdXPvJr1opA==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0
    dependencies:
      '@babel/core': 7.9.0
      '@babel/helper-annotate-as-pure': 7.18.6
      '@babel/helper-environment-visitor': 7.18.9
      '@babel/helper-wrap-function': 7.19.0
      '@babel/types': 7.19.4
    transitivePeerDependencies:
      - supports-color
    dev: false

  /@babel/helper-replace-supers/7.19.1:
    resolution: {integrity: sha512-T7ahH7wV0Hfs46SFh5Jz3s0B6+o8g3c+7TMxu7xKfmHikg7EAZ3I2Qk9LFhjxXq8sL7UkP5JflezNwoZa8WvWw==}
    engines: {node: '>=6.9.0'}
    dependencies:
      '@babel/helper-environment-visitor': 7.18.9
      '@babel/helper-member-expression-to-functions': 7.18.9
      '@babel/helper-optimise-call-expression': 7.18.6
      '@babel/traverse': 7.19.6
      '@babel/types': 7.19.4
    transitivePeerDependencies:
      - supports-color
    dev: false

  /@babel/helper-simple-access/7.19.4:
    resolution: {integrity: sha512-f9Xq6WqBFqaDfbCzn2w85hwklswz5qsKlh7f08w4Y9yhJHpnNC0QemtSkK5YyOY8kPGvyiwdzZksGUhnGdaUIg==}
    engines: {node: '>=6.9.0'}
    dependencies:
      '@babel/types': 7.19.4

  /@babel/helper-skip-transparent-expression-wrappers/7.18.9:
    resolution: {integrity: sha512-imytd2gHi3cJPsybLRbmFrF7u5BIEuI2cNheyKi3/iOBC63kNn3q8Crn2xVuESli0aM4KYsyEqKyS7lFL8YVtw==}
    engines: {node: '>=6.9.0'}
    dependencies:
      '@babel/types': 7.19.4
    dev: false

  /@babel/helper-split-export-declaration/7.18.6:
    resolution: {integrity: sha512-bde1etTx6ZyTmobl9LLMMQsaizFVZrquTEHOqKeQESMKo4PlObf+8+JA25ZsIpZhT/WEd39+vOdLXAFG/nELpA==}
    engines: {node: '>=6.9.0'}
    dependencies:
      '@babel/types': 7.19.4

  /@babel/helper-string-parser/7.19.4:
    resolution: {integrity: sha512-nHtDoQcuqFmwYNYPz3Rah5ph2p8PFeFCsZk9A/48dPc/rGocJ5J3hAAZ7pb76VWX3fZKu+uEr/FhH5jLx7umrw==}
    engines: {node: '>=6.9.0'}

  /@babel/helper-validator-identifier/7.19.1:
    resolution: {integrity: sha512-awrNfaMtnHUr653GgGEs++LlAvW6w+DcPrOliSMXWCKo597CwL5Acf/wWdNkf/tfEQE3mjkeD1YOVZOUV/od1w==}
    engines: {node: '>=6.9.0'}

  /@babel/helper-validator-option/7.18.6:
    resolution: {integrity: sha512-XO7gESt5ouv/LRJdrVjkShckw6STTaB7l9BrpBaAHDeF5YZT+01PCwmR0SJHnkW6i8OwW/EVWRShfi4j2x+KQw==}
    engines: {node: '>=6.9.0'}

  /@babel/helper-wrap-function/7.19.0:
    resolution: {integrity: sha512-txX8aN8CZyYGTwcLhlk87KRqncAzhh5TpQamZUa0/u3an36NtDpUP6bQgBCBcLeBs09R/OwQu3OjK0k/HwfNDg==}
    engines: {node: '>=6.9.0'}
    dependencies:
      '@babel/helper-function-name': 7.19.0
      '@babel/template': 7.18.10
      '@babel/traverse': 7.19.6
      '@babel/types': 7.19.4
    transitivePeerDependencies:
      - supports-color
    dev: false

  /@babel/helpers/7.19.4:
    resolution: {integrity: sha512-G+z3aOx2nfDHwX/kyVii5fJq+bgscg89/dJNWpYeKeBv3v9xX8EIabmx1k6u9LS04H7nROFVRVK+e3k0VHp+sw==}
    engines: {node: '>=6.9.0'}
    dependencies:
      '@babel/template': 7.18.10
      '@babel/traverse': 7.19.6
      '@babel/types': 7.19.4
    transitivePeerDependencies:
      - supports-color

  /@babel/highlight/7.18.6:
    resolution: {integrity: sha512-u7stbOuYjaPezCuLj29hNW1v64M2Md2qupEKP1fHc7WdOA3DgLh37suiSrZYY7haUB7iBeQZ9P1uiRF359do3g==}
    engines: {node: '>=6.9.0'}
    dependencies:
      '@babel/helper-validator-identifier': 7.19.1
      chalk: 2.4.2
      js-tokens: 4.0.0

  /@babel/parser/7.19.6:
    resolution: {integrity: sha512-h1IUp81s2JYJ3mRkdxJgs4UvmSsRvDrx5ICSJbPvtWYv5i1nTBGcBpnog+89rAFMwvvru6E5NUHdBe01UeSzYA==}
    engines: {node: '>=6.0.0'}
    hasBin: true
    dependencies:
      '@babel/types': 7.19.4

  /@babel/plugin-bugfix-safari-id-destructuring-collision-in-function-expression/7.18.6_@babel+core@7.8.4:
    resolution: {integrity: sha512-Dgxsyg54Fx1d4Nge8UnvTrED63vrwOdPmyvPzlNN/boaliRP54pm3pGzZD1SJUwrBA+Cs/xdG8kXX6Mn/RfISQ==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0
    dependencies:
      '@babel/core': 7.8.4
      '@babel/helper-plugin-utils': 7.19.0
    dev: false

  /@babel/plugin-bugfix-v8-spread-parameters-in-optional-chaining/7.18.9_@babel+core@7.8.4:
    resolution: {integrity: sha512-AHrP9jadvH7qlOj6PINbgSuphjQUAK7AOT7DPjBo9EHoLhQTnnK5u45e1Hd4DbSQEO9nqPWtQ89r+XEOWFScKg==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.13.0
    dependencies:
      '@babel/core': 7.8.4
      '@babel/helper-plugin-utils': 7.19.0
      '@babel/helper-skip-transparent-expression-wrappers': 7.18.9
      '@babel/plugin-proposal-optional-chaining': 7.18.9_@babel+core@7.8.4
    dev: false

  /@babel/plugin-proposal-async-generator-functions/7.19.1_@babel+core@7.8.4:
    resolution: {integrity: sha512-0yu8vNATgLy4ivqMNBIwb1HebCelqN7YX8SL3FDXORv/RqT0zEEWUCH4GH44JsSrvCu6GqnAdR5EBFAPeNBB4Q==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.8.4
      '@babel/helper-environment-visitor': 7.18.9
      '@babel/helper-plugin-utils': 7.19.0
      '@babel/helper-remap-async-to-generator': 7.18.9_@babel+core@7.8.4
      '@babel/plugin-syntax-async-generators': 7.8.4_@babel+core@7.8.4
    transitivePeerDependencies:
      - supports-color
    dev: false

  /@babel/plugin-proposal-async-generator-functions/7.19.1_@babel+core@7.9.0:
    resolution: {integrity: sha512-0yu8vNATgLy4ivqMNBIwb1HebCelqN7YX8SL3FDXORv/RqT0zEEWUCH4GH44JsSrvCu6GqnAdR5EBFAPeNBB4Q==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.9.0
      '@babel/helper-environment-visitor': 7.18.9
      '@babel/helper-plugin-utils': 7.19.0
      '@babel/helper-remap-async-to-generator': 7.18.9_@babel+core@7.9.0
      '@babel/plugin-syntax-async-generators': 7.8.4_@babel+core@7.9.0
    transitivePeerDependencies:
      - supports-color
    dev: false

  /@babel/plugin-proposal-class-properties/7.18.6_@babel+core@7.8.4:
    resolution: {integrity: sha512-cumfXOF0+nzZrrN8Rf0t7M+tF6sZc7vhQwYQck9q1/5w2OExlD+b4v4RpMJFaV1Z7WcDRgO6FqvxqxGlwo+RHQ==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.8.4
      '@babel/helper-create-class-features-plugin': 7.19.0_@babel+core@7.8.4
      '@babel/helper-plugin-utils': 7.19.0
    transitivePeerDependencies:
      - supports-color
    dev: false

  /@babel/plugin-proposal-class-properties/7.8.3_@babel+core@7.9.0:
    resolution: {integrity: sha512-EqFhbo7IosdgPgZggHaNObkmO1kNUe3slaKu54d5OWvy+p9QIKOzK1GAEpAIsZtWVtPXUHSMcT4smvDrCfY4AA==}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.9.0
      '@babel/helper-create-class-features-plugin': 7.19.0_@babel+core@7.9.0
      '@babel/helper-plugin-utils': 7.19.0
    transitivePeerDependencies:
      - supports-color
    dev: false

  /@babel/plugin-proposal-class-static-block/7.18.6_@babel+core@7.8.4:
    resolution: {integrity: sha512-+I3oIiNxrCpup3Gi8n5IGMwj0gOCAjcJUSQEcotNnCCPMEnixawOQ+KeJPlgfjzx+FKQ1QSyZOWe7wmoJp7vhw==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.12.0
    dependencies:
      '@babel/core': 7.8.4
      '@babel/helper-create-class-features-plugin': 7.19.0_@babel+core@7.8.4
      '@babel/helper-plugin-utils': 7.19.0
      '@babel/plugin-syntax-class-static-block': 7.14.5_@babel+core@7.8.4
    transitivePeerDependencies:
      - supports-color
    dev: false

  /@babel/plugin-proposal-decorators/7.8.3_@babel+core@7.9.0:
    resolution: {integrity: sha512-e3RvdvS4qPJVTe288DlXjwKflpfy1hr0j5dz5WpIYYeP7vQZg2WfAEIp8k5/Lwis/m5REXEteIz6rrcDtXXG7w==}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.9.0
      '@babel/helper-create-class-features-plugin': 7.19.0_@babel+core@7.9.0
      '@babel/helper-plugin-utils': 7.19.0
      '@babel/plugin-syntax-decorators': 7.19.0_@babel+core@7.9.0
    transitivePeerDependencies:
      - supports-color
    dev: false

  /@babel/plugin-proposal-dynamic-import/7.18.6_@babel+core@7.8.4:
    resolution: {integrity: sha512-1auuwmK+Rz13SJj36R+jqFPMJWyKEDd7lLSdOj4oJK0UTgGueSAtkrCvz9ewmgyU/P941Rv2fQwZJN8s6QruXw==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.8.4
      '@babel/helper-plugin-utils': 7.19.0
      '@babel/plugin-syntax-dynamic-import': 7.8.3_@babel+core@7.8.4
    dev: false

  /@babel/plugin-proposal-dynamic-import/7.18.6_@babel+core@7.9.0:
    resolution: {integrity: sha512-1auuwmK+Rz13SJj36R+jqFPMJWyKEDd7lLSdOj4oJK0UTgGueSAtkrCvz9ewmgyU/P941Rv2fQwZJN8s6QruXw==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.9.0
      '@babel/helper-plugin-utils': 7.19.0
      '@babel/plugin-syntax-dynamic-import': 7.8.3_@babel+core@7.9.0
    dev: false

  /@babel/plugin-proposal-export-namespace-from/7.18.9_@babel+core@7.8.4:
    resolution: {integrity: sha512-k1NtHyOMvlDDFeb9G5PhUXuGj8m/wiwojgQVEhJ/fsVsMCpLyOP4h0uGEjYJKrRI+EVPlb5Jk+Gt9P97lOGwtA==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.8.4
      '@babel/helper-plugin-utils': 7.19.0
      '@babel/plugin-syntax-export-namespace-from': 7.8.3_@babel+core@7.8.4
    dev: false

  /@babel/plugin-proposal-json-strings/7.18.6_@babel+core@7.8.4:
    resolution: {integrity: sha512-lr1peyn9kOdbYc0xr0OdHTZ5FMqS6Di+H0Fz2I/JwMzGmzJETNeOFq2pBySw6X/KFL5EWDjlJuMsUGRFb8fQgQ==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.8.4
      '@babel/helper-plugin-utils': 7.19.0
      '@babel/plugin-syntax-json-strings': 7.8.3_@babel+core@7.8.4
    dev: false

  /@babel/plugin-proposal-json-strings/7.18.6_@babel+core@7.9.0:
    resolution: {integrity: sha512-lr1peyn9kOdbYc0xr0OdHTZ5FMqS6Di+H0Fz2I/JwMzGmzJETNeOFq2pBySw6X/KFL5EWDjlJuMsUGRFb8fQgQ==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.9.0
      '@babel/helper-plugin-utils': 7.19.0
      '@babel/plugin-syntax-json-strings': 7.8.3_@babel+core@7.9.0
    dev: false

  /@babel/plugin-proposal-logical-assignment-operators/7.18.9_@babel+core@7.8.4:
    resolution: {integrity: sha512-128YbMpjCrP35IOExw2Fq+x55LMP42DzhOhX2aNNIdI9avSWl2PI0yuBWarr3RYpZBSPtabfadkH2yeRiMD61Q==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.8.4
      '@babel/helper-plugin-utils': 7.19.0
      '@babel/plugin-syntax-logical-assignment-operators': 7.10.4_@babel+core@7.8.4
    dev: false

  /@babel/plugin-proposal-nullish-coalescing-operator/7.18.6_@babel+core@7.8.4:
    resolution: {integrity: sha512-wQxQzxYeJqHcfppzBDnm1yAY0jSRkUXR2z8RePZYrKwMKgMlE8+Z6LUno+bd6LvbGh8Gltvy74+9pIYkr+XkKA==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.8.4
      '@babel/helper-plugin-utils': 7.19.0
      '@babel/plugin-syntax-nullish-coalescing-operator': 7.8.3_@babel+core@7.8.4
    dev: false

  /@babel/plugin-proposal-nullish-coalescing-operator/7.8.3_@babel+core@7.9.0:
    resolution: {integrity: sha512-TS9MlfzXpXKt6YYomudb/KU7nQI6/xnapG6in1uZxoxDghuSMZsPb6D2fyUwNYSAp4l1iR7QtFOjkqcRYcUsfw==}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.9.0
      '@babel/helper-plugin-utils': 7.19.0
      '@babel/plugin-syntax-nullish-coalescing-operator': 7.8.3_@babel+core@7.9.0
    dev: false

  /@babel/plugin-proposal-numeric-separator/7.18.6_@babel+core@7.8.4:
    resolution: {integrity: sha512-ozlZFogPqoLm8WBr5Z8UckIoE4YQ5KESVcNudyXOR8uqIkliTEgJ3RoketfG6pmzLdeZF0H/wjE9/cCEitBl7Q==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.8.4
      '@babel/helper-plugin-utils': 7.19.0
      '@babel/plugin-syntax-numeric-separator': 7.10.4_@babel+core@7.8.4
    dev: false

  /@babel/plugin-proposal-numeric-separator/7.8.3_@babel+core@7.9.0:
    resolution: {integrity: sha512-jWioO1s6R/R+wEHizfaScNsAx+xKgwTLNXSh7tTC4Usj3ItsPEhYkEpU4h+lpnBwq7NBVOJXfO6cRFYcX69JUQ==}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.9.0
      '@babel/helper-plugin-utils': 7.19.0
      '@babel/plugin-syntax-numeric-separator': 7.10.4_@babel+core@7.9.0
    dev: false

  /@babel/plugin-proposal-object-rest-spread/7.19.4_@babel+core@7.8.4:
    resolution: {integrity: sha512-wHmj6LDxVDnL+3WhXteUBaoM1aVILZODAUjg11kHqG4cOlfgMQGxw6aCgvrXrmaJR3Bn14oZhImyCPZzRpC93Q==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/compat-data': 7.19.4
      '@babel/core': 7.8.4
      '@babel/helper-compilation-targets': 7.19.3_@babel+core@7.8.4
      '@babel/helper-plugin-utils': 7.19.0
      '@babel/plugin-syntax-object-rest-spread': 7.8.3_@babel+core@7.8.4
      '@babel/plugin-transform-parameters': 7.18.8_@babel+core@7.8.4
    dev: false

  /@babel/plugin-proposal-object-rest-spread/7.19.4_@babel+core@7.9.0:
    resolution: {integrity: sha512-wHmj6LDxVDnL+3WhXteUBaoM1aVILZODAUjg11kHqG4cOlfgMQGxw6aCgvrXrmaJR3Bn14oZhImyCPZzRpC93Q==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/compat-data': 7.19.4
      '@babel/core': 7.9.0
      '@babel/helper-compilation-targets': 7.19.3_@babel+core@7.9.0
      '@babel/helper-plugin-utils': 7.19.0
      '@babel/plugin-syntax-object-rest-spread': 7.8.3_@babel+core@7.9.0
      '@babel/plugin-transform-parameters': 7.18.8_@babel+core@7.9.0
    dev: false

  /@babel/plugin-proposal-optional-catch-binding/7.18.6_@babel+core@7.8.4:
    resolution: {integrity: sha512-Q40HEhs9DJQyaZfUjjn6vE8Cv4GmMHCYuMGIWUnlxH6400VGxOuwWsPt4FxXxJkC/5eOzgn0z21M9gMT4MOhbw==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.8.4
      '@babel/helper-plugin-utils': 7.19.0
      '@babel/plugin-syntax-optional-catch-binding': 7.8.3_@babel+core@7.8.4
    dev: false

  /@babel/plugin-proposal-optional-catch-binding/7.18.6_@babel+core@7.9.0:
    resolution: {integrity: sha512-Q40HEhs9DJQyaZfUjjn6vE8Cv4GmMHCYuMGIWUnlxH6400VGxOuwWsPt4FxXxJkC/5eOzgn0z21M9gMT4MOhbw==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.9.0
      '@babel/helper-plugin-utils': 7.19.0
      '@babel/plugin-syntax-optional-catch-binding': 7.8.3_@babel+core@7.9.0
    dev: false

  /@babel/plugin-proposal-optional-chaining/7.18.9_@babel+core@7.8.4:
    resolution: {integrity: sha512-v5nwt4IqBXihxGsW2QmCWMDS3B3bzGIk/EQVZz2ei7f3NJl8NzAJVvUmpDW5q1CRNY+Beb/k58UAH1Km1N411w==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.8.4
      '@babel/helper-plugin-utils': 7.19.0
      '@babel/helper-skip-transparent-expression-wrappers': 7.18.9
      '@babel/plugin-syntax-optional-chaining': 7.8.3_@babel+core@7.8.4
    dev: false

  /@babel/plugin-proposal-optional-chaining/7.9.0_@babel+core@7.9.0:
    resolution: {integrity: sha512-NDn5tu3tcv4W30jNhmc2hyD5c56G6cXx4TesJubhxrJeCvuuMpttxr0OnNCqbZGhFjLrg+NIhxxC+BK5F6yS3w==}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.9.0
      '@babel/helper-plugin-utils': 7.19.0
      '@babel/plugin-syntax-optional-chaining': 7.8.3_@babel+core@7.9.0
    dev: false

  /@babel/plugin-proposal-private-methods/7.18.6_@babel+core@7.8.4:
    resolution: {integrity: sha512-nutsvktDItsNn4rpGItSNV2sz1XwS+nfU0Rg8aCx3W3NOKVzdMjJRu0O5OkgDp3ZGICSTbgRpxZoWsxoKRvbeA==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.8.4
      '@babel/helper-create-class-features-plugin': 7.19.0_@babel+core@7.8.4
      '@babel/helper-plugin-utils': 7.19.0
    transitivePeerDependencies:
      - supports-color
    dev: false

  /@babel/plugin-proposal-private-property-in-object/7.18.6_@babel+core@7.8.4:
    resolution: {integrity: sha512-9Rysx7FOctvT5ouj5JODjAFAkgGoudQuLPamZb0v1TGLpapdNaftzifU8NTWQm0IRjqoYypdrSmyWgkocDQ8Dw==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.8.4
      '@babel/helper-annotate-as-pure': 7.18.6
      '@babel/helper-create-class-features-plugin': 7.19.0_@babel+core@7.8.4
      '@babel/helper-plugin-utils': 7.19.0
      '@babel/plugin-syntax-private-property-in-object': 7.14.5_@babel+core@7.8.4
    transitivePeerDependencies:
      - supports-color
    dev: false

  /@babel/plugin-proposal-unicode-property-regex/7.18.6_@babel+core@7.8.4:
    resolution: {integrity: sha512-2BShG/d5yoZyXZfVePH91urL5wTG6ASZU9M4o03lKK8u8UW1y08OMttBSOADTcJrnPMpvDXRG3G8fyLh4ovs8w==}
    engines: {node: '>=4'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.8.4
      '@babel/helper-create-regexp-features-plugin': 7.19.0_@babel+core@7.8.4
      '@babel/helper-plugin-utils': 7.19.0
    dev: false

  /@babel/plugin-proposal-unicode-property-regex/7.18.6_@babel+core@7.9.0:
    resolution: {integrity: sha512-2BShG/d5yoZyXZfVePH91urL5wTG6ASZU9M4o03lKK8u8UW1y08OMttBSOADTcJrnPMpvDXRG3G8fyLh4ovs8w==}
    engines: {node: '>=4'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.9.0
      '@babel/helper-create-regexp-features-plugin': 7.19.0_@babel+core@7.9.0
      '@babel/helper-plugin-utils': 7.19.0
    dev: false

  /@babel/plugin-syntax-async-generators/7.8.4_@babel+core@7.8.4:
    resolution: {integrity: sha512-tycmZxkGfZaxhMRbXlPXuVFpdWlXpir2W4AMhSJgRKzk/eDlIXOhb2LHWoLpDF7TEHylV5zNhykX6KAgHJmTNw==}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.8.4
      '@babel/helper-plugin-utils': 7.19.0
    dev: false

  /@babel/plugin-syntax-async-generators/7.8.4_@babel+core@7.9.0:
    resolution: {integrity: sha512-tycmZxkGfZaxhMRbXlPXuVFpdWlXpir2W4AMhSJgRKzk/eDlIXOhb2LHWoLpDF7TEHylV5zNhykX6KAgHJmTNw==}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.9.0
      '@babel/helper-plugin-utils': 7.19.0
    dev: false

  /@babel/plugin-syntax-class-properties/7.12.13_@babel+core@7.8.4:
    resolution: {integrity: sha512-fm4idjKla0YahUNgFNLCB0qySdsoPiZP3iQE3rky0mBUtMZ23yDJ9SJdg6dXTSDnulOVqiF3Hgr9nbXvXTQZYA==}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.8.4
      '@babel/helper-plugin-utils': 7.19.0
    dev: false

  /@babel/plugin-syntax-class-static-block/7.14.5_@babel+core@7.8.4:
    resolution: {integrity: sha512-b+YyPmr6ldyNnM6sqYeMWE+bgJcJpO6yS4QD7ymxgH34GBPNDM/THBh8iunyvKIZztiwLH4CJZ0RxTk9emgpjw==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.8.4
      '@babel/helper-plugin-utils': 7.19.0
    dev: false

  /@babel/plugin-syntax-decorators/7.19.0_@babel+core@7.9.0:
    resolution: {integrity: sha512-xaBZUEDntt4faL1yN8oIFlhfXeQAWJW7CLKYsHTUqriCUbj8xOra8bfxxKGi/UwExPFBuPdH4XfHc9rGQhrVkQ==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.9.0
      '@babel/helper-plugin-utils': 7.19.0
    dev: false

  /@babel/plugin-syntax-dynamic-import/7.8.3_@babel+core@7.8.4:
    resolution: {integrity: sha512-5gdGbFon+PszYzqs83S3E5mpi7/y/8M9eC90MRTZfduQOYW76ig6SOSPNe41IG5LoP3FGBn2N0RjVDSQiS94kQ==}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.8.4
      '@babel/helper-plugin-utils': 7.19.0
    dev: false

  /@babel/plugin-syntax-dynamic-import/7.8.3_@babel+core@7.9.0:
    resolution: {integrity: sha512-5gdGbFon+PszYzqs83S3E5mpi7/y/8M9eC90MRTZfduQOYW76ig6SOSPNe41IG5LoP3FGBn2N0RjVDSQiS94kQ==}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.9.0
      '@babel/helper-plugin-utils': 7.19.0
    dev: false

  /@babel/plugin-syntax-export-namespace-from/7.8.3_@babel+core@7.8.4:
    resolution: {integrity: sha512-MXf5laXo6c1IbEbegDmzGPwGNTsHZmEy6QGznu5Sh2UCWvueywb2ee+CCE4zQiZstxU9BMoQO9i6zUFSY0Kj0Q==}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.8.4
      '@babel/helper-plugin-utils': 7.19.0
    dev: false

  /@babel/plugin-syntax-flow/7.18.6_@babel+core@7.9.0:
    resolution: {integrity: sha512-LUbR+KNTBWCUAqRG9ex5Gnzu2IOkt8jRJbHHXFT9q+L9zm7M/QQbEqXyw1n1pohYvOyWC8CjeyjrSaIwiYjK7A==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.9.0
      '@babel/helper-plugin-utils': 7.19.0
    dev: false

  /@babel/plugin-syntax-import-assertions/7.18.6_@babel+core@7.8.4:
    resolution: {integrity: sha512-/DU3RXad9+bZwrgWJQKbr39gYbJpLJHezqEzRzi/BHRlJ9zsQb4CK2CA/5apllXNomwA1qHwzvHl+AdEmC5krQ==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.8.4
      '@babel/helper-plugin-utils': 7.19.0
    dev: false

  /@babel/plugin-syntax-json-strings/7.8.3_@babel+core@7.8.4:
    resolution: {integrity: sha512-lY6kdGpWHvjoe2vk4WrAapEuBR69EMxZl+RoGRhrFGNYVK8mOPAW8VfbT/ZgrFbXlDNiiaxQnAtgVCZ6jv30EA==}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.8.4
      '@babel/helper-plugin-utils': 7.19.0
    dev: false

  /@babel/plugin-syntax-json-strings/7.8.3_@babel+core@7.9.0:
    resolution: {integrity: sha512-lY6kdGpWHvjoe2vk4WrAapEuBR69EMxZl+RoGRhrFGNYVK8mOPAW8VfbT/ZgrFbXlDNiiaxQnAtgVCZ6jv30EA==}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.9.0
      '@babel/helper-plugin-utils': 7.19.0
    dev: false

  /@babel/plugin-syntax-jsx/7.18.6_@babel+core@7.8.4:
    resolution: {integrity: sha512-6mmljtAedFGTWu2p/8WIORGwy+61PLgOMPOdazc7YoJ9ZCWUyFy3A6CpPkRKLKD1ToAesxX8KGEViAiLo9N+7Q==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.8.4
      '@babel/helper-plugin-utils': 7.19.0
    dev: false

  /@babel/plugin-syntax-jsx/7.18.6_@babel+core@7.9.0:
    resolution: {integrity: sha512-6mmljtAedFGTWu2p/8WIORGwy+61PLgOMPOdazc7YoJ9ZCWUyFy3A6CpPkRKLKD1ToAesxX8KGEViAiLo9N+7Q==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.9.0
      '@babel/helper-plugin-utils': 7.19.0
    dev: false

  /@babel/plugin-syntax-logical-assignment-operators/7.10.4_@babel+core@7.8.4:
    resolution: {integrity: sha512-d8waShlpFDinQ5MtvGU9xDAOzKH47+FFoney2baFIoMr952hKOLp1HR7VszoZvOsV/4+RRszNY7D17ba0te0ig==}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.8.4
      '@babel/helper-plugin-utils': 7.19.0
    dev: false

  /@babel/plugin-syntax-nullish-coalescing-operator/7.8.3_@babel+core@7.8.4:
    resolution: {integrity: sha512-aSff4zPII1u2QD7y+F8oDsz19ew4IGEJg9SVW+bqwpwtfFleiQDMdzA/R+UlWDzfnHFCxxleFT0PMIrR36XLNQ==}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.8.4
      '@babel/helper-plugin-utils': 7.19.0
    dev: false

  /@babel/plugin-syntax-nullish-coalescing-operator/7.8.3_@babel+core@7.9.0:
    resolution: {integrity: sha512-aSff4zPII1u2QD7y+F8oDsz19ew4IGEJg9SVW+bqwpwtfFleiQDMdzA/R+UlWDzfnHFCxxleFT0PMIrR36XLNQ==}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.9.0
      '@babel/helper-plugin-utils': 7.19.0
    dev: false

  /@babel/plugin-syntax-numeric-separator/7.10.4_@babel+core@7.8.4:
    resolution: {integrity: sha512-9H6YdfkcK/uOnY/K7/aA2xpzaAgkQn37yzWUMRK7OaPOqOpGS1+n0H5hxT9AUw9EsSjPW8SVyMJwYRtWs3X3ug==}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.8.4
      '@babel/helper-plugin-utils': 7.19.0
    dev: false

  /@babel/plugin-syntax-numeric-separator/7.10.4_@babel+core@7.9.0:
    resolution: {integrity: sha512-9H6YdfkcK/uOnY/K7/aA2xpzaAgkQn37yzWUMRK7OaPOqOpGS1+n0H5hxT9AUw9EsSjPW8SVyMJwYRtWs3X3ug==}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.9.0
      '@babel/helper-plugin-utils': 7.19.0
    dev: false

  /@babel/plugin-syntax-object-rest-spread/7.8.3_@babel+core@7.8.4:
    resolution: {integrity: sha512-XoqMijGZb9y3y2XskN+P1wUGiVwWZ5JmoDRwx5+3GmEplNyVM2s2Dg8ILFQm8rWM48orGy5YpI5Bl8U1y7ydlA==}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.8.4
      '@babel/helper-plugin-utils': 7.19.0
    dev: false

  /@babel/plugin-syntax-object-rest-spread/7.8.3_@babel+core@7.9.0:
    resolution: {integrity: sha512-XoqMijGZb9y3y2XskN+P1wUGiVwWZ5JmoDRwx5+3GmEplNyVM2s2Dg8ILFQm8rWM48orGy5YpI5Bl8U1y7ydlA==}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.9.0
      '@babel/helper-plugin-utils': 7.19.0
    dev: false

  /@babel/plugin-syntax-optional-catch-binding/7.8.3_@babel+core@7.8.4:
    resolution: {integrity: sha512-6VPD0Pc1lpTqw0aKoeRTMiB+kWhAoT24PA+ksWSBrFtl5SIRVpZlwN3NNPQjehA2E/91FV3RjLWoVTglWcSV3Q==}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.8.4
      '@babel/helper-plugin-utils': 7.19.0
    dev: false

  /@babel/plugin-syntax-optional-catch-binding/7.8.3_@babel+core@7.9.0:
    resolution: {integrity: sha512-6VPD0Pc1lpTqw0aKoeRTMiB+kWhAoT24PA+ksWSBrFtl5SIRVpZlwN3NNPQjehA2E/91FV3RjLWoVTglWcSV3Q==}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.9.0
      '@babel/helper-plugin-utils': 7.19.0
    dev: false

  /@babel/plugin-syntax-optional-chaining/7.8.3_@babel+core@7.8.4:
    resolution: {integrity: sha512-KoK9ErH1MBlCPxV0VANkXW2/dw4vlbGDrFgz8bmUsBGYkFRcbRwMh6cIJubdPrkxRwuGdtCk0v/wPTKbQgBjkg==}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.8.4
      '@babel/helper-plugin-utils': 7.19.0
    dev: false

  /@babel/plugin-syntax-optional-chaining/7.8.3_@babel+core@7.9.0:
    resolution: {integrity: sha512-KoK9ErH1MBlCPxV0VANkXW2/dw4vlbGDrFgz8bmUsBGYkFRcbRwMh6cIJubdPrkxRwuGdtCk0v/wPTKbQgBjkg==}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.9.0
      '@babel/helper-plugin-utils': 7.19.0
    dev: false

  /@babel/plugin-syntax-private-property-in-object/7.14.5_@babel+core@7.8.4:
    resolution: {integrity: sha512-0wVnp9dxJ72ZUJDV27ZfbSj6iHLoytYZmh3rFcxNnvsJF3ktkzLDZPy/mA17HGsaQT3/DQsWYX1f1QGWkCoVUg==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.8.4
      '@babel/helper-plugin-utils': 7.19.0
    dev: false

  /@babel/plugin-syntax-top-level-await/7.14.5_@babel+core@7.8.4:
    resolution: {integrity: sha512-hx++upLv5U1rgYfwe1xBQUhRmU41NEvpUvrp8jkrSCdvGSnM5/qdRMtylJ6PG5OFkBaHkbTAKTnd3/YyESRHFw==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.8.4
      '@babel/helper-plugin-utils': 7.19.0
    dev: false

  /@babel/plugin-syntax-top-level-await/7.14.5_@babel+core@7.9.0:
    resolution: {integrity: sha512-hx++upLv5U1rgYfwe1xBQUhRmU41NEvpUvrp8jkrSCdvGSnM5/qdRMtylJ6PG5OFkBaHkbTAKTnd3/YyESRHFw==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.9.0
      '@babel/helper-plugin-utils': 7.19.0
    dev: false

  /@babel/plugin-syntax-typescript/7.18.6_@babel+core@7.9.0:
    resolution: {integrity: sha512-mAWAuq4rvOepWCBid55JuRNvpTNf2UGVgoz4JV0fXEKolsVZDzsa4NqCef758WZJj/GDu0gVGItjKFiClTAmZA==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.9.0
      '@babel/helper-plugin-utils': 7.19.0
    dev: false

  /@babel/plugin-transform-arrow-functions/7.18.6_@babel+core@7.8.4:
    resolution: {integrity: sha512-9S9X9RUefzrsHZmKMbDXxweEH+YlE8JJEuat9FdvW9Qh1cw7W64jELCtWNkPBPX5En45uy28KGvA/AySqUh8CQ==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.8.4
      '@babel/helper-plugin-utils': 7.19.0
    dev: false

  /@babel/plugin-transform-arrow-functions/7.18.6_@babel+core@7.9.0:
    resolution: {integrity: sha512-9S9X9RUefzrsHZmKMbDXxweEH+YlE8JJEuat9FdvW9Qh1cw7W64jELCtWNkPBPX5En45uy28KGvA/AySqUh8CQ==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.9.0
      '@babel/helper-plugin-utils': 7.19.0
    dev: false

  /@babel/plugin-transform-async-to-generator/7.18.6_@babel+core@7.8.4:
    resolution: {integrity: sha512-ARE5wZLKnTgPW7/1ftQmSi1CmkqqHo2DNmtztFhvgtOWSDfq0Cq9/9L+KnZNYSNrydBekhW3rwShduf59RoXag==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.8.4
      '@babel/helper-module-imports': 7.18.6
      '@babel/helper-plugin-utils': 7.19.0
      '@babel/helper-remap-async-to-generator': 7.18.9_@babel+core@7.8.4
    transitivePeerDependencies:
      - supports-color
    dev: false

  /@babel/plugin-transform-async-to-generator/7.18.6_@babel+core@7.9.0:
    resolution: {integrity: sha512-ARE5wZLKnTgPW7/1ftQmSi1CmkqqHo2DNmtztFhvgtOWSDfq0Cq9/9L+KnZNYSNrydBekhW3rwShduf59RoXag==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.9.0
      '@babel/helper-module-imports': 7.18.6
      '@babel/helper-plugin-utils': 7.19.0
      '@babel/helper-remap-async-to-generator': 7.18.9_@babel+core@7.9.0
    transitivePeerDependencies:
      - supports-color
    dev: false

  /@babel/plugin-transform-block-scoped-functions/7.18.6_@babel+core@7.8.4:
    resolution: {integrity: sha512-ExUcOqpPWnliRcPqves5HJcJOvHvIIWfuS4sroBUenPuMdmW+SMHDakmtS7qOo13sVppmUijqeTv7qqGsvURpQ==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.8.4
      '@babel/helper-plugin-utils': 7.19.0
    dev: false

  /@babel/plugin-transform-block-scoped-functions/7.18.6_@babel+core@7.9.0:
    resolution: {integrity: sha512-ExUcOqpPWnliRcPqves5HJcJOvHvIIWfuS4sroBUenPuMdmW+SMHDakmtS7qOo13sVppmUijqeTv7qqGsvURpQ==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.9.0
      '@babel/helper-plugin-utils': 7.19.0
    dev: false

  /@babel/plugin-transform-block-scoping/7.19.4_@babel+core@7.8.4:
    resolution: {integrity: sha512-934S2VLLlt2hRJwPf4MczaOr4hYF0z+VKPwqTNxyKX7NthTiPfhuKFWQZHXRM0vh/wo/VyXB3s4bZUNA08l+tQ==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.8.4
      '@babel/helper-plugin-utils': 7.19.0
    dev: false

  /@babel/plugin-transform-block-scoping/7.19.4_@babel+core@7.9.0:
    resolution: {integrity: sha512-934S2VLLlt2hRJwPf4MczaOr4hYF0z+VKPwqTNxyKX7NthTiPfhuKFWQZHXRM0vh/wo/VyXB3s4bZUNA08l+tQ==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.9.0
      '@babel/helper-plugin-utils': 7.19.0
    dev: false

  /@babel/plugin-transform-classes/7.19.0_@babel+core@7.8.4:
    resolution: {integrity: sha512-YfeEE9kCjqTS9IitkgfJuxjcEtLUHMqa8yUJ6zdz8vR7hKuo6mOy2C05P0F1tdMmDCeuyidKnlrw/iTppHcr2A==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.8.4
      '@babel/helper-annotate-as-pure': 7.18.6
      '@babel/helper-compilation-targets': 7.19.3_@babel+core@7.8.4
      '@babel/helper-environment-visitor': 7.18.9
      '@babel/helper-function-name': 7.19.0
      '@babel/helper-optimise-call-expression': 7.18.6
      '@babel/helper-plugin-utils': 7.19.0
      '@babel/helper-replace-supers': 7.19.1
      '@babel/helper-split-export-declaration': 7.18.6
      globals: 11.12.0
    transitivePeerDependencies:
      - supports-color
    dev: false

  /@babel/plugin-transform-classes/7.19.0_@babel+core@7.9.0:
    resolution: {integrity: sha512-YfeEE9kCjqTS9IitkgfJuxjcEtLUHMqa8yUJ6zdz8vR7hKuo6mOy2C05P0F1tdMmDCeuyidKnlrw/iTppHcr2A==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.9.0
      '@babel/helper-annotate-as-pure': 7.18.6
      '@babel/helper-compilation-targets': 7.19.3_@babel+core@7.9.0
      '@babel/helper-environment-visitor': 7.18.9
      '@babel/helper-function-name': 7.19.0
      '@babel/helper-optimise-call-expression': 7.18.6
      '@babel/helper-plugin-utils': 7.19.0
      '@babel/helper-replace-supers': 7.19.1
      '@babel/helper-split-export-declaration': 7.18.6
      globals: 11.12.0
    transitivePeerDependencies:
      - supports-color
    dev: false

  /@babel/plugin-transform-computed-properties/7.18.9_@babel+core@7.8.4:
    resolution: {integrity: sha512-+i0ZU1bCDymKakLxn5srGHrsAPRELC2WIbzwjLhHW9SIE1cPYkLCL0NlnXMZaM1vhfgA2+M7hySk42VBvrkBRw==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.8.4
      '@babel/helper-plugin-utils': 7.19.0
    dev: false

  /@babel/plugin-transform-computed-properties/7.18.9_@babel+core@7.9.0:
    resolution: {integrity: sha512-+i0ZU1bCDymKakLxn5srGHrsAPRELC2WIbzwjLhHW9SIE1cPYkLCL0NlnXMZaM1vhfgA2+M7hySk42VBvrkBRw==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.9.0
      '@babel/helper-plugin-utils': 7.19.0
    dev: false

  /@babel/plugin-transform-destructuring/7.19.4_@babel+core@7.8.4:
    resolution: {integrity: sha512-t0j0Hgidqf0aM86dF8U+vXYReUgJnlv4bZLsyoPnwZNrGY+7/38o8YjaELrvHeVfTZao15kjR0PVv0nju2iduA==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.8.4
      '@babel/helper-plugin-utils': 7.19.0
    dev: false

  /@babel/plugin-transform-destructuring/7.19.4_@babel+core@7.9.0:
    resolution: {integrity: sha512-t0j0Hgidqf0aM86dF8U+vXYReUgJnlv4bZLsyoPnwZNrGY+7/38o8YjaELrvHeVfTZao15kjR0PVv0nju2iduA==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.9.0
      '@babel/helper-plugin-utils': 7.19.0
    dev: false

  /@babel/plugin-transform-dotall-regex/7.18.6_@babel+core@7.8.4:
    resolution: {integrity: sha512-6S3jpun1eEbAxq7TdjLotAsl4WpQI9DxfkycRcKrjhQYzU87qpXdknpBg/e+TdcMehqGnLFi7tnFUBR02Vq6wg==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.8.4
      '@babel/helper-create-regexp-features-plugin': 7.19.0_@babel+core@7.8.4
      '@babel/helper-plugin-utils': 7.19.0
    dev: false

  /@babel/plugin-transform-dotall-regex/7.18.6_@babel+core@7.9.0:
    resolution: {integrity: sha512-6S3jpun1eEbAxq7TdjLotAsl4WpQI9DxfkycRcKrjhQYzU87qpXdknpBg/e+TdcMehqGnLFi7tnFUBR02Vq6wg==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.9.0
      '@babel/helper-create-regexp-features-plugin': 7.19.0_@babel+core@7.9.0
      '@babel/helper-plugin-utils': 7.19.0
    dev: false

  /@babel/plugin-transform-duplicate-keys/7.18.9_@babel+core@7.8.4:
    resolution: {integrity: sha512-d2bmXCtZXYc59/0SanQKbiWINadaJXqtvIQIzd4+hNwkWBgyCd5F/2t1kXoUdvPMrxzPvhK6EMQRROxsue+mfw==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.8.4
      '@babel/helper-plugin-utils': 7.19.0
    dev: false

  /@babel/plugin-transform-duplicate-keys/7.18.9_@babel+core@7.9.0:
    resolution: {integrity: sha512-d2bmXCtZXYc59/0SanQKbiWINadaJXqtvIQIzd4+hNwkWBgyCd5F/2t1kXoUdvPMrxzPvhK6EMQRROxsue+mfw==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.9.0
      '@babel/helper-plugin-utils': 7.19.0
    dev: false

  /@babel/plugin-transform-exponentiation-operator/7.18.6_@babel+core@7.8.4:
    resolution: {integrity: sha512-wzEtc0+2c88FVR34aQmiz56dxEkxr2g8DQb/KfaFa1JYXOFVsbhvAonFN6PwVWj++fKmku8NP80plJ5Et4wqHw==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.8.4
      '@babel/helper-builder-binary-assignment-operator-visitor': 7.18.9
      '@babel/helper-plugin-utils': 7.19.0
    dev: false

  /@babel/plugin-transform-exponentiation-operator/7.18.6_@babel+core@7.9.0:
    resolution: {integrity: sha512-wzEtc0+2c88FVR34aQmiz56dxEkxr2g8DQb/KfaFa1JYXOFVsbhvAonFN6PwVWj++fKmku8NP80plJ5Et4wqHw==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.9.0
      '@babel/helper-builder-binary-assignment-operator-visitor': 7.18.9
      '@babel/helper-plugin-utils': 7.19.0
    dev: false

  /@babel/plugin-transform-flow-strip-types/7.9.0_@babel+core@7.9.0:
    resolution: {integrity: sha512-7Qfg0lKQhEHs93FChxVLAvhBshOPQDtJUTVHr/ZwQNRccCm4O9D79r9tVSoV8iNwjP1YgfD+e/fgHcPkN1qEQg==}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.9.0
      '@babel/helper-plugin-utils': 7.19.0
      '@babel/plugin-syntax-flow': 7.18.6_@babel+core@7.9.0
    dev: false

  /@babel/plugin-transform-for-of/7.18.8_@babel+core@7.8.4:
    resolution: {integrity: sha512-yEfTRnjuskWYo0k1mHUqrVWaZwrdq8AYbfrpqULOJOaucGSp4mNMVps+YtA8byoevxS/urwU75vyhQIxcCgiBQ==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.8.4
      '@babel/helper-plugin-utils': 7.19.0
    dev: false

  /@babel/plugin-transform-for-of/7.18.8_@babel+core@7.9.0:
    resolution: {integrity: sha512-yEfTRnjuskWYo0k1mHUqrVWaZwrdq8AYbfrpqULOJOaucGSp4mNMVps+YtA8byoevxS/urwU75vyhQIxcCgiBQ==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.9.0
      '@babel/helper-plugin-utils': 7.19.0
    dev: false

  /@babel/plugin-transform-function-name/7.18.9_@babel+core@7.8.4:
    resolution: {integrity: sha512-WvIBoRPaJQ5yVHzcnJFor7oS5Ls0PYixlTYE63lCj2RtdQEl15M68FXQlxnG6wdraJIXRdR7KI+hQ7q/9QjrCQ==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.8.4
      '@babel/helper-compilation-targets': 7.19.3_@babel+core@7.8.4
      '@babel/helper-function-name': 7.19.0
      '@babel/helper-plugin-utils': 7.19.0
    dev: false

  /@babel/plugin-transform-function-name/7.18.9_@babel+core@7.9.0:
    resolution: {integrity: sha512-WvIBoRPaJQ5yVHzcnJFor7oS5Ls0PYixlTYE63lCj2RtdQEl15M68FXQlxnG6wdraJIXRdR7KI+hQ7q/9QjrCQ==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.9.0
      '@babel/helper-compilation-targets': 7.19.3_@babel+core@7.9.0
      '@babel/helper-function-name': 7.19.0
      '@babel/helper-plugin-utils': 7.19.0
    dev: false

  /@babel/plugin-transform-literals/7.18.9_@babel+core@7.8.4:
    resolution: {integrity: sha512-IFQDSRoTPnrAIrI5zoZv73IFeZu2dhu6irxQjY9rNjTT53VmKg9fenjvoiOWOkJ6mm4jKVPtdMzBY98Fp4Z4cg==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.8.4
      '@babel/helper-plugin-utils': 7.19.0
    dev: false

  /@babel/plugin-transform-literals/7.18.9_@babel+core@7.9.0:
    resolution: {integrity: sha512-IFQDSRoTPnrAIrI5zoZv73IFeZu2dhu6irxQjY9rNjTT53VmKg9fenjvoiOWOkJ6mm4jKVPtdMzBY98Fp4Z4cg==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.9.0
      '@babel/helper-plugin-utils': 7.19.0
    dev: false

  /@babel/plugin-transform-member-expression-literals/7.18.6_@babel+core@7.8.4:
    resolution: {integrity: sha512-qSF1ihLGO3q+/g48k85tUjD033C29TNTVB2paCwZPVmOsjn9pClvYYrM2VeJpBY2bcNkuny0YUyTNRyRxJ54KA==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.8.4
      '@babel/helper-plugin-utils': 7.19.0
    dev: false

  /@babel/plugin-transform-member-expression-literals/7.18.6_@babel+core@7.9.0:
    resolution: {integrity: sha512-qSF1ihLGO3q+/g48k85tUjD033C29TNTVB2paCwZPVmOsjn9pClvYYrM2VeJpBY2bcNkuny0YUyTNRyRxJ54KA==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.9.0
      '@babel/helper-plugin-utils': 7.19.0
    dev: false

  /@babel/plugin-transform-modules-amd/7.19.6_@babel+core@7.8.4:
    resolution: {integrity: sha512-uG3od2mXvAtIFQIh0xrpLH6r5fpSQN04gIVovl+ODLdUMANokxQLZnPBHcjmv3GxRjnqwLuHvppjjcelqUFZvg==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.8.4
      '@babel/helper-module-transforms': 7.19.6
      '@babel/helper-plugin-utils': 7.19.0
    transitivePeerDependencies:
      - supports-color
    dev: false

  /@babel/plugin-transform-modules-amd/7.19.6_@babel+core@7.9.0:
    resolution: {integrity: sha512-uG3od2mXvAtIFQIh0xrpLH6r5fpSQN04gIVovl+ODLdUMANokxQLZnPBHcjmv3GxRjnqwLuHvppjjcelqUFZvg==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.9.0
      '@babel/helper-module-transforms': 7.19.6
      '@babel/helper-plugin-utils': 7.19.0
    transitivePeerDependencies:
      - supports-color
    dev: false

  /@babel/plugin-transform-modules-commonjs/7.19.6_@babel+core@7.8.4:
    resolution: {integrity: sha512-8PIa1ym4XRTKuSsOUXqDG0YaOlEuTVvHMe5JCfgBMOtHvJKw/4NGovEGN33viISshG/rZNVrACiBmPQLvWN8xQ==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.8.4
      '@babel/helper-module-transforms': 7.19.6
      '@babel/helper-plugin-utils': 7.19.0
      '@babel/helper-simple-access': 7.19.4
    transitivePeerDependencies:
      - supports-color
    dev: false

  /@babel/plugin-transform-modules-commonjs/7.19.6_@babel+core@7.9.0:
    resolution: {integrity: sha512-8PIa1ym4XRTKuSsOUXqDG0YaOlEuTVvHMe5JCfgBMOtHvJKw/4NGovEGN33viISshG/rZNVrACiBmPQLvWN8xQ==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.9.0
      '@babel/helper-module-transforms': 7.19.6
      '@babel/helper-plugin-utils': 7.19.0
      '@babel/helper-simple-access': 7.19.4
    transitivePeerDependencies:
      - supports-color
    dev: false

  /@babel/plugin-transform-modules-systemjs/7.19.6_@babel+core@7.8.4:
    resolution: {integrity: sha512-fqGLBepcc3kErfR9R3DnVpURmckXP7gj7bAlrTQyBxrigFqszZCkFkcoxzCp2v32XmwXLvbw+8Yq9/b+QqksjQ==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.8.4
      '@babel/helper-hoist-variables': 7.18.6
      '@babel/helper-module-transforms': 7.19.6
      '@babel/helper-plugin-utils': 7.19.0
      '@babel/helper-validator-identifier': 7.19.1
    transitivePeerDependencies:
      - supports-color
    dev: false

  /@babel/plugin-transform-modules-systemjs/7.19.6_@babel+core@7.9.0:
    resolution: {integrity: sha512-fqGLBepcc3kErfR9R3DnVpURmckXP7gj7bAlrTQyBxrigFqszZCkFkcoxzCp2v32XmwXLvbw+8Yq9/b+QqksjQ==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.9.0
      '@babel/helper-hoist-variables': 7.18.6
      '@babel/helper-module-transforms': 7.19.6
      '@babel/helper-plugin-utils': 7.19.0
      '@babel/helper-validator-identifier': 7.19.1
    transitivePeerDependencies:
      - supports-color
    dev: false

  /@babel/plugin-transform-modules-umd/7.18.6_@babel+core@7.8.4:
    resolution: {integrity: sha512-dcegErExVeXcRqNtkRU/z8WlBLnvD4MRnHgNs3MytRO1Mn1sHRyhbcpYbVMGclAqOjdW+9cfkdZno9dFdfKLfQ==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.8.4
      '@babel/helper-module-transforms': 7.19.6
      '@babel/helper-plugin-utils': 7.19.0
    transitivePeerDependencies:
      - supports-color
    dev: false

  /@babel/plugin-transform-modules-umd/7.18.6_@babel+core@7.9.0:
    resolution: {integrity: sha512-dcegErExVeXcRqNtkRU/z8WlBLnvD4MRnHgNs3MytRO1Mn1sHRyhbcpYbVMGclAqOjdW+9cfkdZno9dFdfKLfQ==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.9.0
      '@babel/helper-module-transforms': 7.19.6
      '@babel/helper-plugin-utils': 7.19.0
    transitivePeerDependencies:
      - supports-color
    dev: false

  /@babel/plugin-transform-named-capturing-groups-regex/7.19.1_@babel+core@7.8.4:
    resolution: {integrity: sha512-oWk9l9WItWBQYS4FgXD4Uyy5kq898lvkXpXQxoJEY1RnvPk4R/Dvu2ebXU9q8lP+rlMwUQTFf2Ok6d78ODa0kw==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0
    dependencies:
      '@babel/core': 7.8.4
      '@babel/helper-create-regexp-features-plugin': 7.19.0_@babel+core@7.8.4
      '@babel/helper-plugin-utils': 7.19.0
    dev: false

  /@babel/plugin-transform-named-capturing-groups-regex/7.19.1_@babel+core@7.9.0:
    resolution: {integrity: sha512-oWk9l9WItWBQYS4FgXD4Uyy5kq898lvkXpXQxoJEY1RnvPk4R/Dvu2ebXU9q8lP+rlMwUQTFf2Ok6d78ODa0kw==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0
    dependencies:
      '@babel/core': 7.9.0
      '@babel/helper-create-regexp-features-plugin': 7.19.0_@babel+core@7.9.0
      '@babel/helper-plugin-utils': 7.19.0
    dev: false

  /@babel/plugin-transform-new-target/7.18.6_@babel+core@7.8.4:
    resolution: {integrity: sha512-DjwFA/9Iu3Z+vrAn+8pBUGcjhxKguSMlsFqeCKbhb9BAV756v0krzVK04CRDi/4aqmk8BsHb4a/gFcaA5joXRw==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.8.4
      '@babel/helper-plugin-utils': 7.19.0
    dev: false

  /@babel/plugin-transform-new-target/7.18.6_@babel+core@7.9.0:
    resolution: {integrity: sha512-DjwFA/9Iu3Z+vrAn+8pBUGcjhxKguSMlsFqeCKbhb9BAV756v0krzVK04CRDi/4aqmk8BsHb4a/gFcaA5joXRw==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.9.0
      '@babel/helper-plugin-utils': 7.19.0
    dev: false

  /@babel/plugin-transform-object-super/7.18.6_@babel+core@7.8.4:
    resolution: {integrity: sha512-uvGz6zk+pZoS1aTZrOvrbj6Pp/kK2mp45t2B+bTDre2UgsZZ8EZLSJtUg7m/no0zOJUWgFONpB7Zv9W2tSaFlA==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.8.4
      '@babel/helper-plugin-utils': 7.19.0
      '@babel/helper-replace-supers': 7.19.1
    transitivePeerDependencies:
      - supports-color
    dev: false

  /@babel/plugin-transform-object-super/7.18.6_@babel+core@7.9.0:
    resolution: {integrity: sha512-uvGz6zk+pZoS1aTZrOvrbj6Pp/kK2mp45t2B+bTDre2UgsZZ8EZLSJtUg7m/no0zOJUWgFONpB7Zv9W2tSaFlA==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.9.0
      '@babel/helper-plugin-utils': 7.19.0
      '@babel/helper-replace-supers': 7.19.1
    transitivePeerDependencies:
      - supports-color
    dev: false

  /@babel/plugin-transform-parameters/7.18.8_@babel+core@7.8.4:
    resolution: {integrity: sha512-ivfbE3X2Ss+Fj8nnXvKJS6sjRG4gzwPMsP+taZC+ZzEGjAYlvENixmt1sZ5Ca6tWls+BlKSGKPJ6OOXvXCbkFg==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.8.4
      '@babel/helper-plugin-utils': 7.19.0
    dev: false

  /@babel/plugin-transform-parameters/7.18.8_@babel+core@7.9.0:
    resolution: {integrity: sha512-ivfbE3X2Ss+Fj8nnXvKJS6sjRG4gzwPMsP+taZC+ZzEGjAYlvENixmt1sZ5Ca6tWls+BlKSGKPJ6OOXvXCbkFg==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.9.0
      '@babel/helper-plugin-utils': 7.19.0
    dev: false

  /@babel/plugin-transform-property-literals/7.18.6_@babel+core@7.8.4:
    resolution: {integrity: sha512-cYcs6qlgafTud3PAzrrRNbQtfpQ8+y/+M5tKmksS9+M1ckbH6kzY8MrexEM9mcA6JDsukE19iIRvAyYl463sMg==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.8.4
      '@babel/helper-plugin-utils': 7.19.0
    dev: false

  /@babel/plugin-transform-property-literals/7.18.6_@babel+core@7.9.0:
    resolution: {integrity: sha512-cYcs6qlgafTud3PAzrrRNbQtfpQ8+y/+M5tKmksS9+M1ckbH6kzY8MrexEM9mcA6JDsukE19iIRvAyYl463sMg==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.9.0
      '@babel/helper-plugin-utils': 7.19.0
    dev: false

  /@babel/plugin-transform-react-constant-elements/7.18.12_@babel+core@7.8.4:
    resolution: {integrity: sha512-Q99U9/ttiu+LMnRU8psd23HhvwXmKWDQIpocm0JKaICcZHnw+mdQbHm6xnSy7dOl8I5PELakYtNBubNQlBXbZw==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.8.4
      '@babel/helper-plugin-utils': 7.19.0
    dev: false

  /@babel/plugin-transform-react-display-name/7.18.6_@babel+core@7.8.4:
    resolution: {integrity: sha512-TV4sQ+T013n61uMoygyMRm+xf04Bd5oqFpv2jAEQwSZ8NwQA7zeRPg1LMVg2PWi3zWBz+CLKD+v5bcpZ/BS0aA==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.8.4
      '@babel/helper-plugin-utils': 7.19.0
    dev: false

  /@babel/plugin-transform-react-display-name/7.8.3_@babel+core@7.9.0:
    resolution: {integrity: sha512-3Jy/PCw8Fe6uBKtEgz3M82ljt+lTg+xJaM4og+eyu83qLT87ZUSckn0wy7r31jflURWLO83TW6Ylf7lyXj3m5A==}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.9.0
      '@babel/helper-plugin-utils': 7.19.0
    dev: false

  /@babel/plugin-transform-react-jsx-development/7.18.6_@babel+core@7.8.4:
    resolution: {integrity: sha512-SA6HEjwYFKF7WDjWcMcMGUimmw/nhNRDWxr+KaLSCrkD/LMDBvWRmHAYgE1HDeF8KUuI8OAu+RT6EOtKxSW2qA==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.8.4
      '@babel/plugin-transform-react-jsx': 7.19.0_@babel+core@7.8.4
    dev: false

  /@babel/plugin-transform-react-jsx-development/7.18.6_@babel+core@7.9.0:
    resolution: {integrity: sha512-SA6HEjwYFKF7WDjWcMcMGUimmw/nhNRDWxr+KaLSCrkD/LMDBvWRmHAYgE1HDeF8KUuI8OAu+RT6EOtKxSW2qA==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.9.0
      '@babel/plugin-transform-react-jsx': 7.19.0_@babel+core@7.9.0
    dev: false

  /@babel/plugin-transform-react-jsx-self/7.18.6_@babel+core@7.9.0:
    resolution: {integrity: sha512-A0LQGx4+4Jv7u/tWzoJF7alZwnBDQd6cGLh9P+Ttk4dpiL+J5p7NSNv/9tlEFFJDq3kjxOavWmbm6t0Gk+A3Ig==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.9.0
      '@babel/helper-plugin-utils': 7.19.0
    dev: false

  /@babel/plugin-transform-react-jsx-source/7.19.6_@babel+core@7.9.0:
    resolution: {integrity: sha512-RpAi004QyMNisst/pvSanoRdJ4q+jMCWyk9zdw/CyLB9j8RXEahodR6l2GyttDRyEVWZtbN+TpLiHJ3t34LbsQ==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.9.0
      '@babel/helper-plugin-utils': 7.19.0
    dev: false

  /@babel/plugin-transform-react-jsx/7.19.0_@babel+core@7.8.4:
    resolution: {integrity: sha512-UVEvX3tXie3Szm3emi1+G63jyw1w5IcMY0FSKM+CRnKRI5Mr1YbCNgsSTwoTwKphQEG9P+QqmuRFneJPZuHNhg==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.8.4
      '@babel/helper-annotate-as-pure': 7.18.6
      '@babel/helper-module-imports': 7.18.6
      '@babel/helper-plugin-utils': 7.19.0
      '@babel/plugin-syntax-jsx': 7.18.6_@babel+core@7.8.4
      '@babel/types': 7.19.4
    dev: false

  /@babel/plugin-transform-react-jsx/7.19.0_@babel+core@7.9.0:
    resolution: {integrity: sha512-UVEvX3tXie3Szm3emi1+G63jyw1w5IcMY0FSKM+CRnKRI5Mr1YbCNgsSTwoTwKphQEG9P+QqmuRFneJPZuHNhg==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.9.0
      '@babel/helper-annotate-as-pure': 7.18.6
      '@babel/helper-module-imports': 7.18.6
      '@babel/helper-plugin-utils': 7.19.0
      '@babel/plugin-syntax-jsx': 7.18.6_@babel+core@7.9.0
      '@babel/types': 7.19.4
    dev: false

  /@babel/plugin-transform-react-pure-annotations/7.18.6_@babel+core@7.8.4:
    resolution: {integrity: sha512-I8VfEPg9r2TRDdvnHgPepTKvuRomzA8+u+nhY7qSI1fR2hRNebasZEETLyM5mAUr0Ku56OkXJ0I7NHJnO6cJiQ==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.8.4
      '@babel/helper-annotate-as-pure': 7.18.6
      '@babel/helper-plugin-utils': 7.19.0
    dev: false

  /@babel/plugin-transform-regenerator/7.18.6_@babel+core@7.8.4:
    resolution: {integrity: sha512-poqRI2+qiSdeldcz4wTSTXBRryoq3Gc70ye7m7UD5Ww0nE29IXqMl6r7Nd15WBgRd74vloEMlShtH6CKxVzfmQ==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.8.4
      '@babel/helper-plugin-utils': 7.19.0
      regenerator-transform: 0.15.0
    dev: false

  /@babel/plugin-transform-regenerator/7.18.6_@babel+core@7.9.0:
    resolution: {integrity: sha512-poqRI2+qiSdeldcz4wTSTXBRryoq3Gc70ye7m7UD5Ww0nE29IXqMl6r7Nd15WBgRd74vloEMlShtH6CKxVzfmQ==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.9.0
      '@babel/helper-plugin-utils': 7.19.0
      regenerator-transform: 0.15.0
    dev: false

  /@babel/plugin-transform-reserved-words/7.18.6_@babel+core@7.8.4:
    resolution: {integrity: sha512-oX/4MyMoypzHjFrT1CdivfKZ+XvIPMFXwwxHp/r0Ddy2Vuomt4HDFGmft1TAY2yiTKiNSsh3kjBAzcM8kSdsjA==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.8.4
      '@babel/helper-plugin-utils': 7.19.0
    dev: false

  /@babel/plugin-transform-reserved-words/7.18.6_@babel+core@7.9.0:
    resolution: {integrity: sha512-oX/4MyMoypzHjFrT1CdivfKZ+XvIPMFXwwxHp/r0Ddy2Vuomt4HDFGmft1TAY2yiTKiNSsh3kjBAzcM8kSdsjA==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.9.0
      '@babel/helper-plugin-utils': 7.19.0
    dev: false

  /@babel/plugin-transform-runtime/7.9.0_@babel+core@7.9.0:
    resolution: {integrity: sha512-pUu9VSf3kI1OqbWINQ7MaugnitRss1z533436waNXp+0N3ur3zfut37sXiQMxkuCF4VUjwZucen/quskCh7NHw==}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.9.0
      '@babel/helper-module-imports': 7.18.6
      '@babel/helper-plugin-utils': 7.19.0
      resolve: 1.22.1
      semver: 5.7.1
    dev: false

  /@babel/plugin-transform-shorthand-properties/7.18.6_@babel+core@7.8.4:
    resolution: {integrity: sha512-eCLXXJqv8okzg86ywZJbRn19YJHU4XUa55oz2wbHhaQVn/MM+XhukiT7SYqp/7o00dg52Rj51Ny+Ecw4oyoygw==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.8.4
      '@babel/helper-plugin-utils': 7.19.0
    dev: false

  /@babel/plugin-transform-shorthand-properties/7.18.6_@babel+core@7.9.0:
    resolution: {integrity: sha512-eCLXXJqv8okzg86ywZJbRn19YJHU4XUa55oz2wbHhaQVn/MM+XhukiT7SYqp/7o00dg52Rj51Ny+Ecw4oyoygw==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.9.0
      '@babel/helper-plugin-utils': 7.19.0
    dev: false

  /@babel/plugin-transform-spread/7.19.0_@babel+core@7.8.4:
    resolution: {integrity: sha512-RsuMk7j6n+r752EtzyScnWkQyuJdli6LdO5Klv8Yx0OfPVTcQkIUfS8clx5e9yHXzlnhOZF3CbQ8C2uP5j074w==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.8.4
      '@babel/helper-plugin-utils': 7.19.0
      '@babel/helper-skip-transparent-expression-wrappers': 7.18.9
    dev: false

  /@babel/plugin-transform-spread/7.19.0_@babel+core@7.9.0:
    resolution: {integrity: sha512-RsuMk7j6n+r752EtzyScnWkQyuJdli6LdO5Klv8Yx0OfPVTcQkIUfS8clx5e9yHXzlnhOZF3CbQ8C2uP5j074w==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.9.0
      '@babel/helper-plugin-utils': 7.19.0
      '@babel/helper-skip-transparent-expression-wrappers': 7.18.9
    dev: false

  /@babel/plugin-transform-sticky-regex/7.18.6_@babel+core@7.8.4:
    resolution: {integrity: sha512-kfiDrDQ+PBsQDO85yj1icueWMfGfJFKN1KCkndygtu/C9+XUfydLC8Iv5UYJqRwy4zk8EcplRxEOeLyjq1gm6Q==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.8.4
      '@babel/helper-plugin-utils': 7.19.0
    dev: false

  /@babel/plugin-transform-sticky-regex/7.18.6_@babel+core@7.9.0:
    resolution: {integrity: sha512-kfiDrDQ+PBsQDO85yj1icueWMfGfJFKN1KCkndygtu/C9+XUfydLC8Iv5UYJqRwy4zk8EcplRxEOeLyjq1gm6Q==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.9.0
      '@babel/helper-plugin-utils': 7.19.0
    dev: false

  /@babel/plugin-transform-template-literals/7.18.9_@babel+core@7.8.4:
    resolution: {integrity: sha512-S8cOWfT82gTezpYOiVaGHrCbhlHgKhQt8XH5ES46P2XWmX92yisoZywf5km75wv5sYcXDUCLMmMxOLCtthDgMA==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.8.4
      '@babel/helper-plugin-utils': 7.19.0
    dev: false

  /@babel/plugin-transform-template-literals/7.18.9_@babel+core@7.9.0:
    resolution: {integrity: sha512-S8cOWfT82gTezpYOiVaGHrCbhlHgKhQt8XH5ES46P2XWmX92yisoZywf5km75wv5sYcXDUCLMmMxOLCtthDgMA==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.9.0
      '@babel/helper-plugin-utils': 7.19.0
    dev: false

  /@babel/plugin-transform-typeof-symbol/7.18.9_@babel+core@7.8.4:
    resolution: {integrity: sha512-SRfwTtF11G2aemAZWivL7PD+C9z52v9EvMqH9BuYbabyPuKUvSWks3oCg6041pT925L4zVFqaVBeECwsmlguEw==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.8.4
      '@babel/helper-plugin-utils': 7.19.0
    dev: false

  /@babel/plugin-transform-typeof-symbol/7.18.9_@babel+core@7.9.0:
    resolution: {integrity: sha512-SRfwTtF11G2aemAZWivL7PD+C9z52v9EvMqH9BuYbabyPuKUvSWks3oCg6041pT925L4zVFqaVBeECwsmlguEw==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.9.0
      '@babel/helper-plugin-utils': 7.19.0
    dev: false

  /@babel/plugin-transform-typescript/7.19.3_@babel+core@7.9.0:
    resolution: {integrity: sha512-z6fnuK9ve9u/0X0rRvI9MY0xg+DOUaABDYOe+/SQTxtlptaBB/V9JIUxJn6xp3lMBeb9qe8xSFmHU35oZDXD+w==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.9.0
      '@babel/helper-create-class-features-plugin': 7.19.0_@babel+core@7.9.0
      '@babel/helper-plugin-utils': 7.19.0
      '@babel/plugin-syntax-typescript': 7.18.6_@babel+core@7.9.0
    transitivePeerDependencies:
      - supports-color
    dev: false

  /@babel/plugin-transform-unicode-escapes/7.18.10_@babel+core@7.8.4:
    resolution: {integrity: sha512-kKAdAI+YzPgGY/ftStBFXTI1LZFju38rYThnfMykS+IXy8BVx+res7s2fxf1l8I35DV2T97ezo6+SGrXz6B3iQ==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.8.4
      '@babel/helper-plugin-utils': 7.19.0
    dev: false

  /@babel/plugin-transform-unicode-regex/7.18.6_@babel+core@7.8.4:
    resolution: {integrity: sha512-gE7A6Lt7YLnNOL3Pb9BNeZvi+d8l7tcRrG4+pwJjK9hD2xX4mEvjlQW60G9EEmfXVYRPv9VRQcyegIVHCql/AA==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.8.4
      '@babel/helper-create-regexp-features-plugin': 7.19.0_@babel+core@7.8.4
      '@babel/helper-plugin-utils': 7.19.0
    dev: false

  /@babel/plugin-transform-unicode-regex/7.18.6_@babel+core@7.9.0:
    resolution: {integrity: sha512-gE7A6Lt7YLnNOL3Pb9BNeZvi+d8l7tcRrG4+pwJjK9hD2xX4mEvjlQW60G9EEmfXVYRPv9VRQcyegIVHCql/AA==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.9.0
      '@babel/helper-create-regexp-features-plugin': 7.19.0_@babel+core@7.9.0
      '@babel/helper-plugin-utils': 7.19.0
    dev: false

  /@babel/preset-env/7.19.4_@babel+core@7.8.4:
    resolution: {integrity: sha512-5QVOTXUdqTCjQuh2GGtdd7YEhoRXBMVGROAtsBeLGIbIz3obCBIfRMT1I3ZKkMgNzwkyCkftDXSSkHxnfVf4qg==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/compat-data': 7.19.4
      '@babel/core': 7.8.4
      '@babel/helper-compilation-targets': 7.19.3_@babel+core@7.8.4
      '@babel/helper-plugin-utils': 7.19.0
      '@babel/helper-validator-option': 7.18.6
      '@babel/plugin-bugfix-safari-id-destructuring-collision-in-function-expression': 7.18.6_@babel+core@7.8.4
      '@babel/plugin-bugfix-v8-spread-parameters-in-optional-chaining': 7.18.9_@babel+core@7.8.4
      '@babel/plugin-proposal-async-generator-functions': 7.19.1_@babel+core@7.8.4
      '@babel/plugin-proposal-class-properties': 7.18.6_@babel+core@7.8.4
      '@babel/plugin-proposal-class-static-block': 7.18.6_@babel+core@7.8.4
      '@babel/plugin-proposal-dynamic-import': 7.18.6_@babel+core@7.8.4
      '@babel/plugin-proposal-export-namespace-from': 7.18.9_@babel+core@7.8.4
      '@babel/plugin-proposal-json-strings': 7.18.6_@babel+core@7.8.4
      '@babel/plugin-proposal-logical-assignment-operators': 7.18.9_@babel+core@7.8.4
      '@babel/plugin-proposal-nullish-coalescing-operator': 7.18.6_@babel+core@7.8.4
      '@babel/plugin-proposal-numeric-separator': 7.18.6_@babel+core@7.8.4
      '@babel/plugin-proposal-object-rest-spread': 7.19.4_@babel+core@7.8.4
      '@babel/plugin-proposal-optional-catch-binding': 7.18.6_@babel+core@7.8.4
      '@babel/plugin-proposal-optional-chaining': 7.18.9_@babel+core@7.8.4
      '@babel/plugin-proposal-private-methods': 7.18.6_@babel+core@7.8.4
      '@babel/plugin-proposal-private-property-in-object': 7.18.6_@babel+core@7.8.4
      '@babel/plugin-proposal-unicode-property-regex': 7.18.6_@babel+core@7.8.4
      '@babel/plugin-syntax-async-generators': 7.8.4_@babel+core@7.8.4
      '@babel/plugin-syntax-class-properties': 7.12.13_@babel+core@7.8.4
      '@babel/plugin-syntax-class-static-block': 7.14.5_@babel+core@7.8.4
      '@babel/plugin-syntax-dynamic-import': 7.8.3_@babel+core@7.8.4
      '@babel/plugin-syntax-export-namespace-from': 7.8.3_@babel+core@7.8.4
      '@babel/plugin-syntax-import-assertions': 7.18.6_@babel+core@7.8.4
      '@babel/plugin-syntax-json-strings': 7.8.3_@babel+core@7.8.4
      '@babel/plugin-syntax-logical-assignment-operators': 7.10.4_@babel+core@7.8.4
      '@babel/plugin-syntax-nullish-coalescing-operator': 7.8.3_@babel+core@7.8.4
      '@babel/plugin-syntax-numeric-separator': 7.10.4_@babel+core@7.8.4
      '@babel/plugin-syntax-object-rest-spread': 7.8.3_@babel+core@7.8.4
      '@babel/plugin-syntax-optional-catch-binding': 7.8.3_@babel+core@7.8.4
      '@babel/plugin-syntax-optional-chaining': 7.8.3_@babel+core@7.8.4
      '@babel/plugin-syntax-private-property-in-object': 7.14.5_@babel+core@7.8.4
      '@babel/plugin-syntax-top-level-await': 7.14.5_@babel+core@7.8.4
      '@babel/plugin-transform-arrow-functions': 7.18.6_@babel+core@7.8.4
      '@babel/plugin-transform-async-to-generator': 7.18.6_@babel+core@7.8.4
      '@babel/plugin-transform-block-scoped-functions': 7.18.6_@babel+core@7.8.4
      '@babel/plugin-transform-block-scoping': 7.19.4_@babel+core@7.8.4
      '@babel/plugin-transform-classes': 7.19.0_@babel+core@7.8.4
      '@babel/plugin-transform-computed-properties': 7.18.9_@babel+core@7.8.4
      '@babel/plugin-transform-destructuring': 7.19.4_@babel+core@7.8.4
      '@babel/plugin-transform-dotall-regex': 7.18.6_@babel+core@7.8.4
      '@babel/plugin-transform-duplicate-keys': 7.18.9_@babel+core@7.8.4
      '@babel/plugin-transform-exponentiation-operator': 7.18.6_@babel+core@7.8.4
      '@babel/plugin-transform-for-of': 7.18.8_@babel+core@7.8.4
      '@babel/plugin-transform-function-name': 7.18.9_@babel+core@7.8.4
      '@babel/plugin-transform-literals': 7.18.9_@babel+core@7.8.4
      '@babel/plugin-transform-member-expression-literals': 7.18.6_@babel+core@7.8.4
      '@babel/plugin-transform-modules-amd': 7.19.6_@babel+core@7.8.4
      '@babel/plugin-transform-modules-commonjs': 7.19.6_@babel+core@7.8.4
      '@babel/plugin-transform-modules-systemjs': 7.19.6_@babel+core@7.8.4
      '@babel/plugin-transform-modules-umd': 7.18.6_@babel+core@7.8.4
      '@babel/plugin-transform-named-capturing-groups-regex': 7.19.1_@babel+core@7.8.4
      '@babel/plugin-transform-new-target': 7.18.6_@babel+core@7.8.4
      '@babel/plugin-transform-object-super': 7.18.6_@babel+core@7.8.4
      '@babel/plugin-transform-parameters': 7.18.8_@babel+core@7.8.4
      '@babel/plugin-transform-property-literals': 7.18.6_@babel+core@7.8.4
      '@babel/plugin-transform-regenerator': 7.18.6_@babel+core@7.8.4
      '@babel/plugin-transform-reserved-words': 7.18.6_@babel+core@7.8.4
      '@babel/plugin-transform-shorthand-properties': 7.18.6_@babel+core@7.8.4
      '@babel/plugin-transform-spread': 7.19.0_@babel+core@7.8.4
      '@babel/plugin-transform-sticky-regex': 7.18.6_@babel+core@7.8.4
      '@babel/plugin-transform-template-literals': 7.18.9_@babel+core@7.8.4
      '@babel/plugin-transform-typeof-symbol': 7.18.9_@babel+core@7.8.4
      '@babel/plugin-transform-unicode-escapes': 7.18.10_@babel+core@7.8.4
      '@babel/plugin-transform-unicode-regex': 7.18.6_@babel+core@7.8.4
      '@babel/preset-modules': 0.1.5_@babel+core@7.8.4
      '@babel/types': 7.19.4
      babel-plugin-polyfill-corejs2: 0.3.3_@babel+core@7.8.4
      babel-plugin-polyfill-corejs3: 0.6.0_@babel+core@7.8.4
      babel-plugin-polyfill-regenerator: 0.4.1_@babel+core@7.8.4
      core-js-compat: 3.25.5
      semver: 6.3.0
    transitivePeerDependencies:
      - supports-color
    dev: false

  /@babel/preset-env/7.9.0_@babel+core@7.9.0:
    resolution: {integrity: sha512-712DeRXT6dyKAM/FMbQTV/FvRCms2hPCx+3weRjZ8iQVQWZejWWk1wwG6ViWMyqb/ouBbGOl5b6aCk0+j1NmsQ==}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/compat-data': 7.19.4
      '@babel/core': 7.9.0
      '@babel/helper-compilation-targets': 7.19.3_@babel+core@7.9.0
      '@babel/helper-module-imports': 7.18.6
      '@babel/helper-plugin-utils': 7.19.0
      '@babel/plugin-proposal-async-generator-functions': 7.19.1_@babel+core@7.9.0
      '@babel/plugin-proposal-dynamic-import': 7.18.6_@babel+core@7.9.0
      '@babel/plugin-proposal-json-strings': 7.18.6_@babel+core@7.9.0
      '@babel/plugin-proposal-nullish-coalescing-operator': 7.8.3_@babel+core@7.9.0
      '@babel/plugin-proposal-numeric-separator': 7.8.3_@babel+core@7.9.0
      '@babel/plugin-proposal-object-rest-spread': 7.19.4_@babel+core@7.9.0
      '@babel/plugin-proposal-optional-catch-binding': 7.18.6_@babel+core@7.9.0
      '@babel/plugin-proposal-optional-chaining': 7.9.0_@babel+core@7.9.0
      '@babel/plugin-proposal-unicode-property-regex': 7.18.6_@babel+core@7.9.0
      '@babel/plugin-syntax-async-generators': 7.8.4_@babel+core@7.9.0
      '@babel/plugin-syntax-dynamic-import': 7.8.3_@babel+core@7.9.0
      '@babel/plugin-syntax-json-strings': 7.8.3_@babel+core@7.9.0
      '@babel/plugin-syntax-nullish-coalescing-operator': 7.8.3_@babel+core@7.9.0
      '@babel/plugin-syntax-numeric-separator': 7.10.4_@babel+core@7.9.0
      '@babel/plugin-syntax-object-rest-spread': 7.8.3_@babel+core@7.9.0
      '@babel/plugin-syntax-optional-catch-binding': 7.8.3_@babel+core@7.9.0
      '@babel/plugin-syntax-optional-chaining': 7.8.3_@babel+core@7.9.0
      '@babel/plugin-syntax-top-level-await': 7.14.5_@babel+core@7.9.0
      '@babel/plugin-transform-arrow-functions': 7.18.6_@babel+core@7.9.0
      '@babel/plugin-transform-async-to-generator': 7.18.6_@babel+core@7.9.0
      '@babel/plugin-transform-block-scoped-functions': 7.18.6_@babel+core@7.9.0
      '@babel/plugin-transform-block-scoping': 7.19.4_@babel+core@7.9.0
      '@babel/plugin-transform-classes': 7.19.0_@babel+core@7.9.0
      '@babel/plugin-transform-computed-properties': 7.18.9_@babel+core@7.9.0
      '@babel/plugin-transform-destructuring': 7.19.4_@babel+core@7.9.0
      '@babel/plugin-transform-dotall-regex': 7.18.6_@babel+core@7.9.0
      '@babel/plugin-transform-duplicate-keys': 7.18.9_@babel+core@7.9.0
      '@babel/plugin-transform-exponentiation-operator': 7.18.6_@babel+core@7.9.0
      '@babel/plugin-transform-for-of': 7.18.8_@babel+core@7.9.0
      '@babel/plugin-transform-function-name': 7.18.9_@babel+core@7.9.0
      '@babel/plugin-transform-literals': 7.18.9_@babel+core@7.9.0
      '@babel/plugin-transform-member-expression-literals': 7.18.6_@babel+core@7.9.0
      '@babel/plugin-transform-modules-amd': 7.19.6_@babel+core@7.9.0
      '@babel/plugin-transform-modules-commonjs': 7.19.6_@babel+core@7.9.0
      '@babel/plugin-transform-modules-systemjs': 7.19.6_@babel+core@7.9.0
      '@babel/plugin-transform-modules-umd': 7.18.6_@babel+core@7.9.0
      '@babel/plugin-transform-named-capturing-groups-regex': 7.19.1_@babel+core@7.9.0
      '@babel/plugin-transform-new-target': 7.18.6_@babel+core@7.9.0
      '@babel/plugin-transform-object-super': 7.18.6_@babel+core@7.9.0
      '@babel/plugin-transform-parameters': 7.18.8_@babel+core@7.9.0
      '@babel/plugin-transform-property-literals': 7.18.6_@babel+core@7.9.0
      '@babel/plugin-transform-regenerator': 7.18.6_@babel+core@7.9.0
      '@babel/plugin-transform-reserved-words': 7.18.6_@babel+core@7.9.0
      '@babel/plugin-transform-shorthand-properties': 7.18.6_@babel+core@7.9.0
      '@babel/plugin-transform-spread': 7.19.0_@babel+core@7.9.0
      '@babel/plugin-transform-sticky-regex': 7.18.6_@babel+core@7.9.0
      '@babel/plugin-transform-template-literals': 7.18.9_@babel+core@7.9.0
      '@babel/plugin-transform-typeof-symbol': 7.18.9_@babel+core@7.9.0
      '@babel/plugin-transform-unicode-regex': 7.18.6_@babel+core@7.9.0
      '@babel/preset-modules': 0.1.5_@babel+core@7.9.0
      '@babel/types': 7.19.4
      browserslist: 4.21.4
      core-js-compat: 3.25.5
      invariant: 2.2.4
      levenary: 1.1.1
      semver: 5.7.1
    transitivePeerDependencies:
      - supports-color
    dev: false

  /@babel/preset-modules/0.1.5_@babel+core@7.8.4:
    resolution: {integrity: sha1-75Odbn8miCfhhBY43G/5VRXhFdk=}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.8.4
      '@babel/helper-plugin-utils': 7.19.0
      '@babel/plugin-proposal-unicode-property-regex': 7.18.6_@babel+core@7.8.4
      '@babel/plugin-transform-dotall-regex': 7.18.6_@babel+core@7.8.4
      '@babel/types': 7.19.4
      esutils: 2.0.3
    dev: false

  /@babel/preset-modules/0.1.5_@babel+core@7.9.0:
    resolution: {integrity: sha1-75Odbn8miCfhhBY43G/5VRXhFdk=}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.9.0
      '@babel/helper-plugin-utils': 7.19.0
      '@babel/plugin-proposal-unicode-property-regex': 7.18.6_@babel+core@7.9.0
      '@babel/plugin-transform-dotall-regex': 7.18.6_@babel+core@7.9.0
      '@babel/types': 7.19.4
      esutils: 2.0.3
    dev: false

  /@babel/preset-react/7.18.6_@babel+core@7.8.4:
    resolution: {integrity: sha512-zXr6atUmyYdiWRVLOZahakYmOBHtWc2WGCkP8PYTgZi0iJXDY2CN180TdrIW4OGOAdLc7TifzDIvtx6izaRIzg==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.8.4
      '@babel/helper-plugin-utils': 7.19.0
      '@babel/helper-validator-option': 7.18.6
      '@babel/plugin-transform-react-display-name': 7.18.6_@babel+core@7.8.4
      '@babel/plugin-transform-react-jsx': 7.19.0_@babel+core@7.8.4
      '@babel/plugin-transform-react-jsx-development': 7.18.6_@babel+core@7.8.4
      '@babel/plugin-transform-react-pure-annotations': 7.18.6_@babel+core@7.8.4
    dev: false

  /@babel/preset-react/7.9.1_@babel+core@7.9.0:
    resolution: {integrity: sha512-aJBYF23MPj0RNdp/4bHnAP0NVqqZRr9kl0NAOP4nJCex6OYVio59+dnQzsAWFuogdLyeaKA1hmfUIVZkY5J+TQ==}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.9.0
      '@babel/helper-plugin-utils': 7.19.0
      '@babel/plugin-transform-react-display-name': 7.8.3_@babel+core@7.9.0
      '@babel/plugin-transform-react-jsx': 7.19.0_@babel+core@7.9.0
      '@babel/plugin-transform-react-jsx-development': 7.18.6_@babel+core@7.9.0
      '@babel/plugin-transform-react-jsx-self': 7.18.6_@babel+core@7.9.0
      '@babel/plugin-transform-react-jsx-source': 7.19.6_@babel+core@7.9.0
    dev: false

  /@babel/preset-typescript/7.9.0_@babel+core@7.9.0:
    resolution: {integrity: sha512-S4cueFnGrIbvYJgwsVFKdvOmpiL0XGw9MFW9D0vgRys5g36PBhZRL8NX8Gr2akz8XRtzq6HuDXPD/1nniagNUg==}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.9.0
      '@babel/helper-plugin-utils': 7.19.0
      '@babel/plugin-transform-typescript': 7.19.3_@babel+core@7.9.0
    transitivePeerDependencies:
      - supports-color
    dev: false

  /@babel/runtime-corejs3/7.19.6:
    resolution: {integrity: sha512-oWNn1ZlGde7b4i/3tnixpH9qI0bOAACiUs+KEES4UUCnsPjVWFlWdLV/iwJuPC2qp3EowbAqsm+0XqNwnwYhxA==}
    engines: {node: '>=6.9.0'}
    dependencies:
      core-js-pure: 3.25.5
      regenerator-runtime: 0.13.10
    dev: true

  /@babel/runtime/7.19.4:
    resolution: {integrity: sha512-EXpLCrk55f+cYqmHsSR+yD/0gAIMxxA9QK9lnQWzhMCvt+YmoBN7Zx94s++Kv0+unHk39vxNO8t+CMA2WSS3wA==}
    engines: {node: '>=6.9.0'}
    dependencies:
      regenerator-runtime: 0.13.10

  /@babel/runtime/7.9.0:
    resolution: {integrity: sha512-cTIudHnzuWLS56ik4DnRnqqNf8MkdUzV4iFFI1h7Jo9xvrpQROYaAnaSd2mHLQAzzZAPfATynX5ord6YlNYNMA==}
    dependencies:
      regenerator-runtime: 0.13.10
    dev: false

  /@babel/template/7.18.10:
    resolution: {integrity: sha512-TI+rCtooWHr3QJ27kJxfjutghu44DLnasDMwpDqCXVTal9RLp3RSYNh4NdBrRP2cQAoG9A8juOQl6P6oZG4JxA==}
    engines: {node: '>=6.9.0'}
    dependencies:
      '@babel/code-frame': 7.18.6
      '@babel/parser': 7.19.6
      '@babel/types': 7.19.4

  /@babel/traverse/7.19.6:
    resolution: {integrity: sha512-6l5HrUCzFM04mfbG09AagtYyR2P0B71B1wN7PfSPiksDPz2k5H9CBC1tcZpz2M8OxbKTPccByoOJ22rUKbpmQQ==}
    engines: {node: '>=6.9.0'}
    dependencies:
      '@babel/code-frame': 7.18.6
      '@babel/generator': 7.19.6
      '@babel/helper-environment-visitor': 7.18.9
      '@babel/helper-function-name': 7.19.0
      '@babel/helper-hoist-variables': 7.18.6
      '@babel/helper-split-export-declaration': 7.18.6
      '@babel/parser': 7.19.6
      '@babel/types': 7.19.4
      debug: 4.3.4
      globals: 11.12.0
    transitivePeerDependencies:
      - supports-color

  /@babel/types/7.19.4:
    resolution: {integrity: sha512-M5LK7nAeS6+9j7hAq+b3fQs+pNfUtTGq+yFFfHnauFA8zQtLRfmuipmsKDKKLuyG+wC8ABW43A153YNawNTEtw==}
    engines: {node: '>=6.9.0'}
    dependencies:
      '@babel/helper-string-parser': 7.19.4
      '@babel/helper-validator-identifier': 7.19.1
      to-fast-properties: 2.0.0

  /@brainhubeu/react-carousel/1.19.26_wcqkhtmu7mswc6yz4uyexck3ty:
    resolution: {integrity: sha512-+YO8cJrJEk+QDZTQM+i7ct5Ouy0LyoVHQD4fomdsV+3AQmWUkMVZNBYmxFMvhRQVFKreTTzyKuGPkhDKilL/CQ==}
    engines: {npm: '>=6.14.5'}
    peerDependencies:
      react: '>0.14.0 || >15.0.0'
      react-dom: '>0.14.0 || >15.0.0'
    dependencies:
      react: 16.14.0
      react-dom: 16.14.0_react@16.14.0
    dev: false

  /@cnakazawa/watch/1.0.4:
    resolution: {integrity: sha512-v9kIhKwjeZThiWrLmj0y17CWoyddASLj9O2yvbZkbvw/N3rWOYy9zkV66ursAoVr0mV15bL8g0c4QZUE6cdDoQ==}
    engines: {node: '>=0.1.95'}
    hasBin: true
    dependencies:
      exec-sh: 0.3.6
      minimist: 1.2.7
    dev: false

  /@csstools/convert-colors/1.4.0:
    resolution: {integrity: sha512-5a6wqoJV/xEdbRNKVo6I4hO3VjyDq//8q2f9I6PBAvMesJHFauXDorcNCsr9RzvsZnaWi5NYCcfyqP1QeFHFbw==}
    engines: {node: '>=4.0.0'}
    dev: false

  /@csstools/normalize.css/10.1.0:
    resolution: {integrity: sha512-ij4wRiunFfaJxjB0BdrYHIH8FxBJpOwNPhhAcunlmPdXudL1WQV1qoP9un6JsEBAgQH+7UXyyjh0g7jTxXK6tg==}
    dev: false

  /@hapi/address/2.1.4:
    resolution: {integrity: sha512-QD1PhQk+s31P1ixsX0H0Suoupp3VMXzIVMSwobR3F3MSUO2YCV0B7xqLcUw/Bh8yuvd3LhpyqLQWTNcRmp6IdQ==}
    dev: false

  /@hapi/bourne/1.3.2:
    resolution: {integrity: sha512-1dVNHT76Uu5N3eJNTYcvxee+jzX4Z9lfciqRRHCU27ihbUcYi+iSc2iml5Ke1LXe1SyJCLA0+14Jh4tXJgOppA==}
    dev: false

  /@hapi/hoek/8.5.1:
    resolution: {integrity: sha512-yN7kbciD87WzLGc5539Tn0sApjyiGHAJgKvG9W8C7O+6c7qmoQMfVs0W4bX17eqz6C78QJqqFrtgdK5EWf6Qow==}
    dev: false

  /@hapi/joi/15.1.1:
    resolution: {integrity: sha512-entf8ZMOK8sc+8YfeOlM8pCfg3b5+WZIKBfUaaJT8UsjAAPjartzxIYm3TIbjvA4u+u++KbcXD38k682nVHDAQ==}
    dependencies:
      '@hapi/address': 2.1.4
      '@hapi/bourne': 1.3.2
      '@hapi/hoek': 8.5.1
      '@hapi/topo': 3.1.6
    dev: false

  /@hapi/topo/3.1.6:
    resolution: {integrity: sha512-tAag0jEcjwH+P2quUfipd7liWCNX2F8NvYjQp2wtInsZxnMlypdw0FtAOLxtvvkO+GSRRbmNi8m/5y42PQJYCQ==}
    dependencies:
      '@hapi/hoek': 8.5.1
    dev: false

  /@jest/console/24.9.0:
    resolution: {integrity: sha512-Zuj6b8TnKXi3q4ymac8EQfc3ea/uhLeCGThFqXeC8H9/raaH8ARPUTdId+XyGd03Z4In0/VjD2OYFcBF09fNLQ==}
    engines: {node: '>= 6'}
    dependencies:
      '@jest/source-map': 24.9.0
      chalk: 2.4.2
      slash: 2.0.0
    dev: false

  /@jest/core/24.9.0:
    resolution: {integrity: sha512-Fogg3s4wlAr1VX7q+rhV9RVnUv5tD7VuWfYy1+whMiWUrvl7U3QJSJyWcDio9Lq2prqYsZaeTv2Rz24pWGkJ2A==}
    engines: {node: '>= 6'}
    dependencies:
      '@jest/console': 24.9.0
      '@jest/reporters': 24.9.0
      '@jest/test-result': 24.9.0
      '@jest/transform': 24.9.0
      '@jest/types': 24.9.0
      ansi-escapes: 3.2.0
      chalk: 2.4.2
      exit: 0.1.2
      graceful-fs: 4.2.10
      jest-changed-files: 24.9.0
      jest-config: 24.9.0
      jest-haste-map: 24.9.0
      jest-message-util: 24.9.0
      jest-regex-util: 24.9.0
      jest-resolve: 24.9.0
      jest-resolve-dependencies: 24.9.0
      jest-runner: 24.9.0
      jest-runtime: 24.9.0
      jest-snapshot: 24.9.0
      jest-util: 24.9.0
      jest-validate: 24.9.0
      jest-watcher: 24.9.0
      micromatch: 3.1.10
      p-each-series: 1.0.0
      realpath-native: 1.1.0
      rimraf: 2.7.1
      slash: 2.0.0
      strip-ansi: 5.2.0
    transitivePeerDependencies:
      - bufferutil
      - supports-color
      - utf-8-validate
    dev: false

  /@jest/environment/24.9.0:
    resolution: {integrity: sha512-5A1QluTPhvdIPFYnO3sZC3smkNeXPVELz7ikPbhUj0bQjB07EoE9qtLrem14ZUYWdVayYbsjVwIiL4WBIMV4aQ==}
    engines: {node: '>= 6'}
    dependencies:
      '@jest/fake-timers': 24.9.0
      '@jest/transform': 24.9.0
      '@jest/types': 24.9.0
      jest-mock: 24.9.0
    transitivePeerDependencies:
      - supports-color
    dev: false

  /@jest/fake-timers/24.9.0:
    resolution: {integrity: sha512-eWQcNa2YSwzXWIMC5KufBh3oWRIijrQFROsIqt6v/NS9Io/gknw1jsAC9c+ih/RQX4A3O7SeWAhQeN0goKhT9A==}
    engines: {node: '>= 6'}
    dependencies:
      '@jest/types': 24.9.0
      jest-message-util: 24.9.0
      jest-mock: 24.9.0
    transitivePeerDependencies:
      - supports-color
    dev: false

  /@jest/reporters/24.9.0:
    resolution: {integrity: sha512-mu4X0yjaHrffOsWmVLzitKmmmWSQ3GGuefgNscUSWNiUNcEOSEQk9k3pERKEQVBb0Cnn88+UESIsZEMH3o88Gw==}
    engines: {node: '>= 6'}
    dependencies:
      '@jest/environment': 24.9.0
      '@jest/test-result': 24.9.0
      '@jest/transform': 24.9.0
      '@jest/types': 24.9.0
      chalk: 2.4.2
      exit: 0.1.2
      glob: 7.2.3
      istanbul-lib-coverage: 2.0.5
      istanbul-lib-instrument: 3.3.0
      istanbul-lib-report: 2.0.8
      istanbul-lib-source-maps: 3.0.6
      istanbul-reports: 2.2.7
      jest-haste-map: 24.9.0
      jest-resolve: 24.9.0
      jest-runtime: 24.9.0
      jest-util: 24.9.0
      jest-worker: 24.9.0
      node-notifier: 5.4.5
      slash: 2.0.0
      source-map: 0.6.1
      string-length: 2.0.0
    transitivePeerDependencies:
      - bufferutil
      - supports-color
      - utf-8-validate
    dev: false

  /@jest/source-map/24.9.0:
    resolution: {integrity: sha512-/Xw7xGlsZb4MJzNDgB7PW5crou5JqWiBQaz6xyPd3ArOg2nfn/PunV8+olXbbEZzNl591o5rWKE9BRDaFAuIBg==}
    engines: {node: '>= 6'}
    dependencies:
      callsites: 3.1.0
      graceful-fs: 4.2.10
      source-map: 0.6.1
    dev: false

  /@jest/test-result/24.9.0:
    resolution: {integrity: sha512-XEFrHbBonBJ8dGp2JmF8kP/nQI/ImPpygKHwQ/SY+es59Z3L5PI4Qb9TQQMAEeYsThG1xF0k6tmG0tIKATNiiA==}
    engines: {node: '>= 6'}
    dependencies:
      '@jest/console': 24.9.0
      '@jest/types': 24.9.0
      '@types/istanbul-lib-coverage': 2.0.4
    dev: false

  /@jest/test-sequencer/24.9.0:
    resolution: {integrity: sha512-6qqsU4o0kW1dvA95qfNog8v8gkRN9ph6Lz7r96IvZpHdNipP2cBcb07J1Z45mz/VIS01OHJ3pY8T5fUY38tg4A==}
    engines: {node: '>= 6'}
    dependencies:
      '@jest/test-result': 24.9.0
      jest-haste-map: 24.9.0
      jest-runner: 24.9.0
      jest-runtime: 24.9.0
    transitivePeerDependencies:
      - bufferutil
      - supports-color
      - utf-8-validate
    dev: false

  /@jest/transform/24.9.0:
    resolution: {integrity: sha512-TcQUmyNRxV94S0QpMOnZl0++6RMiqpbH/ZMccFB/amku6Uwvyb1cjYX7xkp5nGNkbX4QPH/FcB6q1HBTHynLmQ==}
    engines: {node: '>= 6'}
    dependencies:
      '@babel/core': 7.9.0
      '@jest/types': 24.9.0
      babel-plugin-istanbul: 5.2.0
      chalk: 2.4.2
      convert-source-map: 1.9.0
      fast-json-stable-stringify: 2.1.0
      graceful-fs: 4.2.10
      jest-haste-map: 24.9.0
      jest-regex-util: 24.9.0
      jest-util: 24.9.0
      micromatch: 3.1.10
      pirates: 4.0.5
      realpath-native: 1.1.0
      slash: 2.0.0
      source-map: 0.6.1
      write-file-atomic: 2.4.1
    transitivePeerDependencies:
      - supports-color
    dev: false

  /@jest/types/24.9.0:
    resolution: {integrity: sha512-XKK7ze1apu5JWQ5eZjHITP66AX+QsLlbaJRBGYr8pNzwcAE2JVkwnf0yqjHTsDRcjR0mujy/NmZMXw5kl+kGBw==}
    engines: {node: '>= 6'}
    dependencies:
      '@types/istanbul-lib-coverage': 2.0.4
      '@types/istanbul-reports': 1.1.2
      '@types/yargs': 13.0.12

  /@jest/types/25.5.0:
    resolution: {integrity: sha512-OXD0RgQ86Tu3MazKo8bnrkDRaDXXMGUqd+kTtLtK1Zb7CRzQcaSRPPPV37SvYTdevXEBVxe0HXylEjs8ibkmCw==}
    engines: {node: '>= 8.3'}
    dependencies:
      '@types/istanbul-lib-coverage': 2.0.4
      '@types/istanbul-reports': 1.1.2
      '@types/yargs': 15.0.14
      chalk: 3.0.0
    dev: true

  /@jridgewell/gen-mapping/0.1.1:
    resolution: {integrity: sha512-sQXCasFk+U8lWYEe66WxRDOE9PjVz4vSM51fTu3Hw+ClTpUSQb718772vH3pyS5pShp6lvQM7SxgIDXXXmOX7w==}
    engines: {node: '>=6.0.0'}
    dependencies:
      '@jridgewell/set-array': 1.1.2
      '@jridgewell/sourcemap-codec': 1.4.14
    dev: true

  /@jridgewell/gen-mapping/0.3.2:
    resolution: {integrity: sha512-mh65xKQAzI6iBcFzwv28KVWSmCkdRBWoOh+bYQGW3+6OZvbbN3TqMGo5hqYxQniRcH9F2VZIoJCm4pa3BPDK/A==}
    engines: {node: '>=6.0.0'}
    dependencies:
      '@jridgewell/set-array': 1.1.2
      '@jridgewell/sourcemap-codec': 1.4.14
      '@jridgewell/trace-mapping': 0.3.17

  /@jridgewell/resolve-uri/3.1.0:
    resolution: {integrity: sha512-F2msla3tad+Mfht5cJq7LSXcdudKTWCVYUgw6pLFOOHSTtZlj6SWNYAp+AhuqLmWdBO2X5hPrLcu8cVP8fy28w==}
    engines: {node: '>=6.0.0'}

  /@jridgewell/set-array/1.1.2:
    resolution: {integrity: sha512-xnkseuNADM0gt2bs+BvhO0p78Mk762YnZdsuzFV018NoG1Sj1SCQvpSqa7XUaTam5vAGasABV9qXASMKnFMwMw==}
    engines: {node: '>=6.0.0'}

  /@jridgewell/sourcemap-codec/1.4.14:
    resolution: {integrity: sha512-XPSJHWmi394fuUuzDnGz1wiKqWfo1yXecHQMRf2l6hztTO+nPru658AyDngaBe7isIxEkRsPR3FZh+s7iVa4Uw==}

  /@jridgewell/trace-mapping/0.3.17:
    resolution: {integrity: sha512-MCNzAp77qzKca9+W/+I0+sEpaUnZoeasnghNeVc41VZCEKaCH73Vq3BZZ/SzWIgrqE4H4ceI+p+b6C0mHf9T4g==}
    dependencies:
      '@jridgewell/resolve-uri': 3.1.0
      '@jridgewell/sourcemap-codec': 1.4.14

  /@mrmlnc/readdir-enhanced/2.2.1:
    resolution: {integrity: sha512-bPHp6Ji8b41szTOcaP63VlnbbO5Ny6dwAATtY6JTjh5N2OLrb5Qk/Th5cRkRQhkWCt+EJsYrNB0MiL+Gpn6e3g==}
    engines: {node: '>=4'}
    dependencies:
      call-me-maybe: 1.0.1
      glob-to-regexp: 0.3.0
    dev: false

  /@nodelib/fs.scandir/2.1.5:
    resolution: {integrity: sha512-vq24Bq3ym5HEQm2NKCr3yXDwjc7vTsEThRDnkp2DK9p1uqLR+DHurm/NOTo0KG7HYHU7eppKZj3MyqYuMBf62g==}
    engines: {node: '>= 8'}
    dependencies:
      '@nodelib/fs.stat': 2.0.5
      run-parallel: 1.2.0
    dev: true

  /@nodelib/fs.stat/1.1.3:
    resolution: {integrity: sha512-shAmDyaQC4H92APFoIaVDHCx5bStIocgvbwQyxPRrbUY20V1EYTbSDchWbuwlMG3V17cprZhA6+78JfB+3DTPw==}
    engines: {node: '>= 6'}
    dev: false

  /@nodelib/fs.stat/2.0.5:
    resolution: {integrity: sha512-RkhPPp2zrqDAQA/2jNhnztcPAlv64XdhIp7a7454A5ovI7Bukxgt7MX7udwAu3zg1DcpPU0rz3VV1SeaqvY4+A==}
    engines: {node: '>= 8'}
    dev: true

  /@nodelib/fs.walk/1.2.8:
    resolution: {integrity: sha512-oGB+UxlgWcgQkgwo8GcEGwemoTFt3FIO9ababBmaGwXIoBKZ+GTy0pP185beGg7Llih/NSHSV2XAs1lnznocSg==}
    engines: {node: '>= 8'}
    dependencies:
      '@nodelib/fs.scandir': 2.1.5
      fastq: 1.13.0
    dev: true

  /@sheerun/mutationobserver-shim/0.3.3:
    resolution: {integrity: sha512-DetpxZw1fzPD5xUBrIAoplLChO2VB8DlL5Gg+I1IR9b2wPqYIca2WSUxL5g1vLeR4MsQq1NeWriXAVffV+U1Fw==}
    dev: true

  /@stylelint/postcss-css-in-js/0.37.3_j55xdkkcxc32kvnyvx3y7casfm:
    resolution: {integrity: sha512-scLk3cSH1H9KggSniseb2KNAU5D9FWc3H7BxCSAIdtU9OWIyw0zkEZ9qEKHryRM+SExYXRKNb7tOOVNAsQ3iwg==}
    peerDependencies:
      postcss: '>=7.0.0'
      postcss-syntax: '>=0.36.2'
    dependencies:
      '@babel/core': 7.19.6
      postcss: 7.0.39
      postcss-syntax: 0.36.2_kei4jy7wdgbhc236h4oijypxom
    transitivePeerDependencies:
      - supports-color
    dev: true

  /@stylelint/postcss-markdown/0.36.2_j55xdkkcxc32kvnyvx3y7casfm:
    resolution: {integrity: sha512-2kGbqUVJUGE8dM+bMzXG/PYUWKkjLIkRLWNh39OaADkiabDRdw8ATFCgbMz5xdIcvwspPAluSL7uY+ZiTWdWmQ==}
    peerDependencies:
      postcss: '>=7.0.0'
      postcss-syntax: '>=0.36.2'
    dependencies:
      postcss: 7.0.39
      postcss-syntax: 0.36.2_kei4jy7wdgbhc236h4oijypxom
      remark: 13.0.0
      unist-util-find-all-after: 3.0.2
    transitivePeerDependencies:
      - supports-color
    dev: true

  /@svgr/babel-plugin-add-jsx-attribute/4.2.0:
    resolution: {integrity: sha512-j7KnilGyZzYr/jhcrSYS3FGWMZVaqyCG0vzMCwzvei0coIkczuYMcniK07nI0aHJINciujjH11T72ICW5eL5Ig==}
    engines: {node: '>=8'}
    dev: false

  /@svgr/babel-plugin-remove-jsx-attribute/4.2.0:
    resolution: {integrity: sha512-3XHLtJ+HbRCH4n28S7y/yZoEQnRpl0tvTZQsHqvaeNXPra+6vE5tbRliH3ox1yZYPCxrlqaJT/Mg+75GpDKlvQ==}
    engines: {node: '>=8'}
    dev: false

  /@svgr/babel-plugin-remove-jsx-empty-expression/4.2.0:
    resolution: {integrity: sha512-yTr2iLdf6oEuUE9MsRdvt0NmdpMBAkgK8Bjhl6epb+eQWk6abBaX3d65UZ3E3FWaOwePyUgNyNCMVG61gGCQ7w==}
    engines: {node: '>=8'}
    dev: false

  /@svgr/babel-plugin-replace-jsx-attribute-value/4.2.0:
    resolution: {integrity: sha512-U9m870Kqm0ko8beHawRXLGLvSi/ZMrl89gJ5BNcT452fAjtF2p4uRzXkdzvGJJJYBgx7BmqlDjBN/eCp5AAX2w==}
    engines: {node: '>=8'}
    dev: false

  /@svgr/babel-plugin-svg-dynamic-title/4.3.3:
    resolution: {integrity: sha512-w3Be6xUNdwgParsvxkkeZb545VhXEwjGMwExMVBIdPQJeyMQHqm9Msnb2a1teHBqUYL66qtwfhNkbj1iarCG7w==}
    engines: {node: '>=8'}
    dev: false

  /@svgr/babel-plugin-svg-em-dimensions/4.2.0:
    resolution: {integrity: sha512-C0Uy+BHolCHGOZ8Dnr1zXy/KgpBOkEUYY9kI/HseHVPeMbluaX3CijJr7D4C5uR8zrc1T64nnq/k63ydQuGt4w==}
    engines: {node: '>=8'}
    dev: false

  /@svgr/babel-plugin-transform-react-native-svg/4.2.0:
    resolution: {integrity: sha512-7YvynOpZDpCOUoIVlaaOUU87J4Z6RdD6spYN4eUb5tfPoKGSF9OG2NuhgYnq4jSkAxcpMaXWPf1cePkzmqTPNw==}
    engines: {node: '>=8'}
    dev: false

  /@svgr/babel-plugin-transform-svg-component/4.2.0:
    resolution: {integrity: sha512-hYfYuZhQPCBVotABsXKSCfel2slf/yvJY8heTVX1PCTaq/IgASq1IyxPPKJ0chWREEKewIU/JMSsIGBtK1KKxw==}
    engines: {node: '>=8'}
    dev: false

  /@svgr/babel-preset/4.3.3:
    resolution: {integrity: sha512-6PG80tdz4eAlYUN3g5GZiUjg2FMcp+Wn6rtnz5WJG9ITGEF1pmFdzq02597Hn0OmnQuCVaBYQE1OVFAnwOl+0A==}
    engines: {node: '>=8'}
    dependencies:
      '@svgr/babel-plugin-add-jsx-attribute': 4.2.0
      '@svgr/babel-plugin-remove-jsx-attribute': 4.2.0
      '@svgr/babel-plugin-remove-jsx-empty-expression': 4.2.0
      '@svgr/babel-plugin-replace-jsx-attribute-value': 4.2.0
      '@svgr/babel-plugin-svg-dynamic-title': 4.3.3
      '@svgr/babel-plugin-svg-em-dimensions': 4.2.0
      '@svgr/babel-plugin-transform-react-native-svg': 4.2.0
      '@svgr/babel-plugin-transform-svg-component': 4.2.0
    dev: false

  /@svgr/core/4.3.3:
    resolution: {integrity: sha512-qNuGF1QON1626UCaZamWt5yedpgOytvLj5BQZe2j1k1B8DUG4OyugZyfEwBeXozCUwhLEpsrgPrE+eCu4fY17w==}
    engines: {node: '>=8'}
    dependencies:
      '@svgr/plugin-jsx': 4.3.3
      camelcase: 5.3.1
      cosmiconfig: 5.2.1
    transitivePeerDependencies:
      - supports-color
    dev: false

  /@svgr/hast-util-to-babel-ast/4.3.2:
    resolution: {integrity: sha512-JioXclZGhFIDL3ddn4Kiq8qEqYM2PyDKV0aYno8+IXTLuYt6TOgHUbUAAFvqtb0Xn37NwP0BTHglejFoYr8RZg==}
    engines: {node: '>=8'}
    dependencies:
      '@babel/types': 7.19.4
    dev: false

  /@svgr/plugin-jsx/4.3.3:
    resolution: {integrity: sha512-cLOCSpNWQnDB1/v+SUENHH7a0XY09bfuMKdq9+gYvtuwzC2rU4I0wKGFEp1i24holdQdwodCtDQdFtJiTCWc+w==}
    engines: {node: '>=8'}
    dependencies:
      '@babel/core': 7.9.0
      '@svgr/babel-preset': 4.3.3
      '@svgr/hast-util-to-babel-ast': 4.3.2
      svg-parser: 2.0.4
    transitivePeerDependencies:
      - supports-color
    dev: false

  /@svgr/plugin-svgo/4.3.1:
    resolution: {integrity: sha512-PrMtEDUWjX3Ea65JsVCwTIXuSqa3CG9px+DluF1/eo9mlDrgrtFE7NE/DjdhjJgSM9wenlVBzkzneSIUgfUI/w==}
    engines: {node: '>=8'}
    dependencies:
      cosmiconfig: 5.2.1
      merge-deep: 3.0.3
      svgo: 1.3.2
    dev: false

  /@svgr/webpack/4.3.3:
    resolution: {integrity: sha512-bjnWolZ6KVsHhgyCoYRFmbd26p8XVbulCzSG53BDQqAr+JOAderYK7CuYrB3bDjHJuF6LJ7Wrr42+goLRV9qIg==}
    engines: {node: '>=8'}
    dependencies:
      '@babel/core': 7.8.4
      '@babel/plugin-transform-react-constant-elements': 7.18.12_@babel+core@7.8.4
      '@babel/preset-env': 7.19.4_@babel+core@7.8.4
      '@babel/preset-react': 7.18.6_@babel+core@7.8.4
      '@svgr/core': 4.3.3
      '@svgr/plugin-jsx': 4.3.3
      '@svgr/plugin-svgo': 4.3.1
      loader-utils: 1.4.0
    transitivePeerDependencies:
      - supports-color
    dev: false

  /@testing-library/dom/6.16.0:
    resolution: {integrity: sha512-lBD88ssxqEfz0wFL6MeUyyWZfV/2cjEZZV3YRpb2IoJRej/4f1jB0TzqIOznTpfR1r34CNesrubxwIlAQ8zgPA==}
    engines: {node: '>=8'}
    dependencies:
      '@babel/runtime': 7.19.4
      '@sheerun/mutationobserver-shim': 0.3.3
      '@types/testing-library__dom': 6.14.0
      aria-query: 4.2.2
      dom-accessibility-api: 0.3.0
      pretty-format: 25.5.0
      wait-for-expect: 3.0.2
    dev: true

  /@testing-library/jest-dom/4.2.4:
    resolution: {integrity: sha512-j31Bn0rQo12fhCWOUWy9fl7wtqkp7In/YP2p5ZFyRuiiB9Qs3g+hS4gAmDWONbAHcRmVooNJ5eOHQDCOmUFXHg==}
    engines: {node: '>=8', npm: '>=6'}
    dependencies:
      '@babel/runtime': 7.19.4
      chalk: 2.4.2
      css: 2.2.4
      css.escape: 1.5.1
      jest-diff: 24.9.0
      jest-matcher-utils: 24.9.0
      lodash: 4.17.21
      pretty-format: 24.9.0
      redent: 3.0.0
    dev: true

  /@testing-library/react/9.5.0_wcqkhtmu7mswc6yz4uyexck3ty:
    resolution: {integrity: sha512-di1b+D0p+rfeboHO5W7gTVeZDIK5+maEgstrZbWZSSvxDyfDRkkyBE1AJR5Psd6doNldluXlCWqXriUfqu/9Qg==}
    engines: {node: '>=8'}
    peerDependencies:
      react: '*'
      react-dom: '*'
    dependencies:
      '@babel/runtime': 7.19.4
      '@testing-library/dom': 6.16.0
      '@types/testing-library__react': 9.1.3
      react: 16.14.0
      react-dom: 16.14.0_react@16.14.0
    dev: true

  /@testing-library/user-event/7.2.1:
    resolution: {integrity: sha512-oZ0Ib5I4Z2pUEcoo95cT1cr6slco9WY7yiPpG+RGNkj8YcYgJnM7pXmYmorNOReh8MIGcKSqXyeGjxnr8YiZbA==}
    peerDependencies:
      '@testing-library/dom': '>=5'
    dev: true

  /@types/babel__core/7.1.19:
    resolution: {integrity: sha512-WEOTgRsbYkvA/KCsDwVEGkd7WAr1e3g31VHQ8zy5gul/V1qKullU/BU5I68X5v7V3GnB9eotmom4v5a5gjxorw==}
    dependencies:
      '@babel/parser': 7.19.6
      '@babel/types': 7.19.4
      '@types/babel__generator': 7.6.4
      '@types/babel__template': 7.4.1
      '@types/babel__traverse': 7.18.2
    dev: false

  /@types/babel__generator/7.6.4:
    resolution: {integrity: sha512-tFkciB9j2K755yrTALxD44McOrk+gfpIpvC3sxHjRawj6PfnQxrse4Clq5y/Rq+G3mrBurMax/lG8Qn2t9mSsg==}
    dependencies:
      '@babel/types': 7.19.4
    dev: false

  /@types/babel__template/7.4.1:
    resolution: {integrity: sha512-azBFKemX6kMg5Io+/rdGT0dkGreboUVR0Cdm3fz9QJWpaQGJRQXl7C+6hOTCZcMll7KFyEQpgbYI2lHdsS4U7g==}
    dependencies:
      '@babel/parser': 7.19.6
      '@babel/types': 7.19.4
    dev: false

  /@types/babel__traverse/7.18.2:
    resolution: {integrity: sha512-FcFaxOr2V5KZCviw1TnutEMVUVsGt4D2hP1TAfXZAMKuHYW3xQhe3jTxNPWutgCJ3/X1c5yX8ZoGVEItxKbwBg==}
    dependencies:
      '@babel/types': 7.19.4
    dev: false

  /@types/brainhubeu__react-carousel/1.15.0:
    resolution: {integrity: sha512-dUwOz4LkUhbqiGH8+ud8L1ygfNZ01Z6lmU0tJkikF51TH/9g14dg6cJE5tLnDCxkpnJm1/0xv0+aRYSsg1xvLg==}
    dependencies:
      '@types/react': 16.14.32
    dev: false

  /@types/classnames/2.3.1:
    resolution: {integrity: sha512-zeOWb0JGBoVmlQoznvqXbE0tEC/HONsnoUNH19Hc96NFsTAwTXbTqb8FMYkru1F/iqp7a18Ws3nWJvtA1sHD1A==}
    deprecated: This is a stub types definition. classnames provides its own type definitions, so you do not need this installed.
    dependencies:
      classnames: 2.3.2
    dev: false

  /@types/eslint-visitor-keys/1.0.0:
    resolution: {integrity: sha512-OCutwjDZ4aFS6PB1UZ988C4YgwlBHJd6wCeQqaLdmadZ/7e+w79+hbMUFC1QXDNCmdyoRfAFdm0RypzwR+Qpag==}
    dev: false

  /@types/glob/7.2.0:
    resolution: {integrity: sha1-vBtb86qS8lvV3TnzXFc2G9zlsus=}
    dependencies:
      '@types/minimatch': 5.1.2
      '@types/node': 12.20.55
    dev: false

  /@types/history/4.7.11:
    resolution: {integrity: sha512-qjDJRrmvBMiTx+jyLxvLfJU7UznFuokDv4f3WRuriHKERccVpFU+8XMQUAbDzoiJCsmexxRExQeMwwCdamSKDA==}
    dev: true

  /@types/http-proxy/1.17.9:
    resolution: {integrity: sha512-QsbSjA/fSk7xB+UXlCT3wHBy5ai9wOcNDWwZAtud+jXhwOM3l+EYZh8Lng4+/6n8uar0J7xILzqftJdJ/Wdfkw==}
    dependencies:
      '@types/node': 12.20.55
    dev: true

  /@types/istanbul-lib-coverage/2.0.4:
    resolution: {integrity: sha512-z/QT1XN4K4KYuslS23k62yDIDLwLFkzxOuMplDtObz0+y7VqJCaO2o+SPwHCvLFZh7xazvvoor2tA/hPz9ee7g==}

  /@types/istanbul-lib-report/3.0.0:
    resolution: {integrity: sha512-plGgXAPfVKFoYfa9NpYDAkseG+g6Jr294RqeqcqDixSbU34MZVJRi/P+7Y8GDpzkEwLaGZZOpKIEmeVZNtKsrg==}
    dependencies:
      '@types/istanbul-lib-coverage': 2.0.4

  /@types/istanbul-reports/1.1.2:
    resolution: {integrity: sha512-P/W9yOX/3oPZSpaYOCQzGqgCQRXn0FFO/V8bWrCQs+wLmvVVxk6CRBXALEvNs9OHIatlnlFokfhuDo2ug01ciw==}
    dependencies:
      '@types/istanbul-lib-coverage': 2.0.4
      '@types/istanbul-lib-report': 3.0.0

  /@types/jest/24.9.1:
    resolution: {integrity: sha512-Fb38HkXSVA4L8fGKEZ6le5bB8r6MRWlOCZbVuWZcmOMSCd2wCYOwN1ibj8daIoV9naq7aaOZjrLCoCMptKU/4Q==}
    dependencies:
      jest-diff: 24.9.0
    dev: true

  /@types/js-md5/0.4.3:
    resolution: {integrity: sha512-BIga/WEqTi35ccnGysOuO4RmwVnpajv9oDB/sDQSY2b7/Ac7RyYR30bv7otZwByMvOJV9Vqq6/O1DFAnOzE4Pg==}
    dev: false

  /@types/json-schema/7.0.11:
    resolution: {integrity: sha512-wOuvG1SN4Us4rez+tylwwwCV1psiNVOkJeM3AUWUNWg/jDQY2+HE/444y5gc+jBmRqASOm2Oeh5c1axHobwRKQ==}
    dev: false

  /@types/lodash/4.14.186:
    resolution: {integrity: sha512-eHcVlLXP0c2FlMPm56ITode2AgLMSa6aJ05JTTbYbI+7EMkCEE5qk2E41d5g2lCVTqRe0GnnRFurmlCsDODrPw==}
    dev: false

  /@types/mdast/3.0.10:
    resolution: {integrity: sha512-W864tg/Osz1+9f4lrGTZpCSO5/z4608eUp19tbozkq2HJK6i3z1kT0H9tlADXuYIb1YYOBByU4Jsqkk75q48qA==}
    dependencies:
      '@types/unist': 2.0.6
    dev: true

  /@types/minimatch/5.1.2:
    resolution: {integrity: sha512-K0VQKziLUWkVKiRVrx4a40iPaxTUefQmjtkQofBkYRcoaaL/8rhwDWww9qWbrgicNOgnpIsMxyNIUM4+n6dUIA==}
    dev: false

  /@types/minimist/1.2.2:
    resolution: {integrity: sha512-jhuKLIRrhvCPLqwPcx6INqmKeiA5EWrsCOPhrlFSrbrmU4ZMPjj5Ul/oLCMDO98XRUIwVm78xICz4EPCektzeQ==}
    dev: true

  /@types/node/12.20.55:
    resolution: {integrity: sha512-J8xLz7q2OFulZ2cyGTLE1TbbZcjpno7FaN6zdJNrgAdrJ+DZzh/uFR6YrTb4C+nXakvud8Q4+rbhoIWlYQbUFQ==}

  /@types/normalize-package-data/2.4.1:
    resolution: {integrity: sha512-Gj7cI7z+98M282Tqmp2K5EIsoouUEzbBJhQQzDE3jSIRk6r9gsz0oUokqIUR4u1R3dMHo0pDHM7sNOHyhulypw==}
    dev: true

  /@types/parse-json/4.0.0:
    resolution: {integrity: sha512-//oorEZjL6sbPcKUaCdIGlIUeH26mgzimjBB77G6XRgnDl/L5wOnpyBGRe/Mmf5CVW3PwEBE1NjiMZ/ssFh4wA==}

  /@types/prop-types/15.7.5:
    resolution: {integrity: sha512-JCB8C6SnDoQf0cNycqd/35A7MjcnK+ZTqE7judS6o7utxUCg6imJg3QK2qzHKszlTjcj2cn+NwMB2i96ubpj7w==}

  /@types/q/1.5.5:
    resolution: {integrity: sha512-L28j2FcJfSZOnL1WBjDYp2vUHCeIFlyYI/53EwD/rKUBQ7MtUUfbQWiyKJGpcnv4/WgrhWsFKrcPstcAt/J0tQ==}
    dev: false

  /@types/qrcode.react/1.0.2:
    resolution: {integrity: sha512-I9Oq5Cjlkgy3Tw7krCnCXLw2/zMhizkTere49OOcta23tkvH0xBTP0yInimTh0gstLRtb8Ki9NZVujE5UI6ffQ==}
    dependencies:
      '@types/react': 16.14.32
    dev: false

  /@types/react-copy-to-clipboard/4.3.0:
    resolution: {integrity: sha512-iideNPRyroENqsOFh1i2Dv3zkviYS9r/9qD9Uh3Z9NNoAAqqa2x53i7iGndGNnJFIo20wIu7Hgh77tx1io8bgw==}
    dependencies:
      '@types/react': 16.14.32
    dev: false

  /@types/react-dom/16.9.16:
    resolution: {integrity: sha512-Oqc0RY4fggGA3ltEgyPLc3IV9T73IGoWjkONbsyJ3ZBn+UPPCYpU2ec0i3cEbJuEdZtkqcCF2l1zf2pBdgUGSg==}
    dependencies:
      '@types/react': 16.14.32
    dev: true

  /@types/react-router-dom/5.3.3:
    resolution: {integrity: sha512-kpqnYK4wcdm5UaWI3fLcELopqLrHgLqNsdpHauzlQktfkHL3npOSwtj1Uz9oKBAzs7lFtVkV8j83voAz2D8fhw==}
    dependencies:
      '@types/history': 4.7.11
      '@types/react': 16.14.32
      '@types/react-router': 5.1.19
    dev: true

  /@types/react-router/5.1.19:
    resolution: {integrity: sha512-Fv/5kb2STAEMT3wHzdKQK2z8xKq38EDIGVrutYLmQVVLe+4orDFquU52hQrULnEHinMKv9FSA6lf9+uNT1ITtA==}
    dependencies:
      '@types/history': 4.7.11
      '@types/react': 16.14.32
    dev: true

  /@types/react-slick/0.23.10:
    resolution: {integrity: sha512-ZiqdencANDZy6sWOWJ54LDvebuXFEhDlHtXU9FFipQR2BcYU2QJxZhvJPW6YK7cocibUiNn+YvDTbt1HtCIBVA==}
    dependencies:
      '@types/react': 16.14.32
    dev: false

  /@types/react-transition-group/4.4.5:
    resolution: {integrity: sha512-juKD/eiSM3/xZYzjuzH6ZwpP+/lejltmiS3QEzV/vmb/Q8+HfDmxu+Baga8UEMGBqV88Nbg4l2hY/K2DkyaLLA==}
    dependencies:
      '@types/react': 16.14.32
    dev: false

  /@types/react/16.14.32:
    resolution: {integrity: sha512-hvEy4vGVADbtj/U6+CA5SRC5QFIjdxD7JslAie8EuAYZwhYY9bgforpXNyF1VFzhnkEOesDy1278t1wdjN74cw==}
    dependencies:
      '@types/prop-types': 15.7.5
      '@types/scheduler': 0.16.2
      csstype: 3.1.1

  /@types/scheduler/0.16.2:
    resolution: {integrity: sha512-hppQEBDmlwhFAXKJX2KnWLYu5yMfi91yazPb2l+lbJiwW+wdo1gNeRA+3RgNSO39WYX2euey41KEwnqesU2Jew==}

  /@types/stack-utils/1.0.1:
    resolution: {integrity: sha512-l42BggppR6zLmpfU6fq9HEa2oGPEI8yrSPL3GITjfRInppYFahObbIQOQK3UGxEnyQpltZLaPe75046NOZQikw==}
    dev: false

  /@types/testing-library__dom/6.14.0:
    resolution: {integrity: sha512-sMl7OSv0AvMOqn1UJ6j1unPMIHRXen0Ita1ujnMX912rrOcawe4f7wu0Zt9GIQhBhJvH2BaibqFgQ3lP+Pj2hA==}
    dependencies:
      pretty-format: 24.9.0
    dev: true

  /@types/testing-library__dom/7.5.0:
    resolution: {integrity: sha512-mj1aH4cj3XUpMEgVpognma5kHVtbm6U6cHZmEFzCRiXPvKkuHrFr3+yXdGLXvfFRBaQIVshPGHI+hGTOJlhS/g==}
    deprecated: This is a stub types definition. testing-library__dom provides its own type definitions, so you do not need this installed.
    dependencies:
      '@testing-library/dom': 6.16.0
    dev: true

  /@types/testing-library__react/9.1.3:
    resolution: {integrity: sha512-iCdNPKU3IsYwRK9JieSYAiX0+aYDXOGAmrC/3/M7AqqSDKnWWVv07X+Zk1uFSL7cMTUYzv4lQRfohucEocn5/w==}
    dependencies:
      '@types/react-dom': 16.9.16
      '@types/testing-library__dom': 7.5.0
      pretty-format: 25.5.0
    dev: true

  /@types/unist/2.0.6:
    resolution: {integrity: sha512-PBjIUxZHOuj0R15/xuwJYjFi+KZdNFrehocChv4g5hu6aFroHue8m0lBP0POdK2nKzbw0cgV1mws8+V/JAcEkQ==}
    dev: true

  /@types/yargs-parser/21.0.0:
    resolution: {integrity: sha512-iO9ZQHkZxHn4mSakYV0vFHAVDyEOIJQrV2uZ06HxEPcx+mt8swXoZHIbaaJ2crJYFfErySgktuTZ3BeLz+XmFA==}

  /@types/yargs/13.0.12:
    resolution: {integrity: sha512-qCxJE1qgz2y0hA4pIxjBR+PelCH0U5CK1XJXFwCNqfmliatKp47UCXXE9Dyk1OXBDLvsCF57TqQEJaeLfDYEOQ==}
    dependencies:
      '@types/yargs-parser': 21.0.0

  /@types/yargs/15.0.14:
    resolution: {integrity: sha512-yEJzHoxf6SyQGhBhIYGXQDSCkJjB6HohDShto7m8vaKg9Yp0Yn8+71J9eakh2bnPg6BfsH9PRMhiRTZnd4eXGQ==}
    dependencies:
      '@types/yargs-parser': 21.0.0
    dev: true

  /@typescript-eslint/eslint-plugin/2.34.0_fmavwhclprfd5wnbs7ocgoy2gu:
    resolution: {integrity: sha512-4zY3Z88rEE99+CNvTbXSyovv2z9PNOVffTWD2W8QF5s2prBQtwN2zadqERcrHpcR7O/+KMI3fcTAmUUhK/iQcQ==}
    engines: {node: ^8.10.0 || ^10.13.0 || >=11.10.1}
    peerDependencies:
      '@typescript-eslint/parser': ^2.0.0
      eslint: ^5.0.0 || ^6.0.0
      typescript: '*'
    peerDependenciesMeta:
      typescript:
        optional: true
    dependencies:
      '@typescript-eslint/experimental-utils': 2.34.0_z6m2zvrkqxyghb4a2ijhravsdi
      '@typescript-eslint/parser': 2.34.0_z6m2zvrkqxyghb4a2ijhravsdi
      eslint: 6.8.0
      functional-red-black-tree: 1.0.1
      regexpp: 3.2.0
      tsutils: 3.21.0_typescript@3.9.10
      typescript: 3.9.10
    transitivePeerDependencies:
      - supports-color
    dev: false

  /@typescript-eslint/experimental-utils/2.34.0_z6m2zvrkqxyghb4a2ijhravsdi:
    resolution: {integrity: sha512-eS6FTkq+wuMJ+sgtuNTtcqavWXqsflWcfBnlYhg/nS4aZ1leewkXGbvBhaapn1q6qf4M71bsR1tez5JTRMuqwA==}
    engines: {node: ^8.10.0 || ^10.13.0 || >=11.10.1}
    peerDependencies:
      eslint: '*'
    dependencies:
      '@types/json-schema': 7.0.11
      '@typescript-eslint/typescript-estree': 2.34.0_typescript@3.9.10
      eslint: 6.8.0
      eslint-scope: 5.1.1
      eslint-utils: 2.1.0
    transitivePeerDependencies:
      - supports-color
      - typescript
    dev: false

  /@typescript-eslint/parser/2.34.0_z6m2zvrkqxyghb4a2ijhravsdi:
    resolution: {integrity: sha512-03ilO0ucSD0EPTw2X4PntSIRFtDPWjrVq7C3/Z3VQHRC7+13YB55rcJI3Jt+YgeHbjUdJPcPa7b23rXCBokuyA==}
    engines: {node: ^8.10.0 || ^10.13.0 || >=11.10.1}
    peerDependencies:
      eslint: ^5.0.0 || ^6.0.0
      typescript: '*'
    peerDependenciesMeta:
      typescript:
        optional: true
    dependencies:
      '@types/eslint-visitor-keys': 1.0.0
      '@typescript-eslint/experimental-utils': 2.34.0_z6m2zvrkqxyghb4a2ijhravsdi
      '@typescript-eslint/typescript-estree': 2.34.0_typescript@3.9.10
      eslint: 6.8.0
      eslint-visitor-keys: 1.3.0
      typescript: 3.9.10
    transitivePeerDependencies:
      - supports-color
    dev: false

  /@typescript-eslint/typescript-estree/2.34.0_typescript@3.9.10:
    resolution: {integrity: sha512-OMAr+nJWKdlVM9LOqCqh3pQQPwxHAN7Du8DR6dmwCrAmxtiXQnhHJ6tBNtf+cggqfo51SG/FCwnKhXCIM7hnVg==}
    engines: {node: ^8.10.0 || ^10.13.0 || >=11.10.1}
    peerDependencies:
      typescript: '*'
    peerDependenciesMeta:
      typescript:
        optional: true
    dependencies:
      debug: 4.3.4
      eslint-visitor-keys: 1.3.0
      glob: 7.2.3
      is-glob: 4.0.3
      lodash: 4.17.21
      semver: 7.3.8
      tsutils: 3.21.0_typescript@3.9.10
      typescript: 3.9.10
    transitivePeerDependencies:
      - supports-color
    dev: false

  /@webassemblyjs/ast/1.8.5:
    resolution: {integrity: sha512-aJMfngIZ65+t71C3y2nBBg5FFG0Okt9m0XEgWZ7Ywgn1oMAT8cNwx00Uv1cQyHtidq0Xn94R4TAywO+LCQ+ZAQ==}
    dependencies:
      '@webassemblyjs/helper-module-context': 1.8.5
      '@webassemblyjs/helper-wasm-bytecode': 1.8.5
      '@webassemblyjs/wast-parser': 1.8.5
    dev: false

  /@webassemblyjs/floating-point-hex-parser/1.8.5:
    resolution: {integrity: sha512-9p+79WHru1oqBh9ewP9zW95E3XAo+90oth7S5Re3eQnECGq59ly1Ri5tsIipKGpiStHsUYmY3zMLqtk3gTcOtQ==}
    dev: false

  /@webassemblyjs/helper-api-error/1.8.5:
    resolution: {integrity: sha512-Za/tnzsvnqdaSPOUXHyKJ2XI7PDX64kWtURyGiJJZKVEdFOsdKUCPTNEVFZq3zJ2R0G5wc2PZ5gvdTRFgm81zA==}
    dev: false

  /@webassemblyjs/helper-buffer/1.8.5:
    resolution: {integrity: sha512-Ri2R8nOS0U6G49Q86goFIPNgjyl6+oE1abW1pS84BuhP1Qcr5JqMwRFT3Ah3ADDDYGEgGs1iyb1DGX+kAi/c/Q==}
    dev: false

  /@webassemblyjs/helper-code-frame/1.8.5:
    resolution: {integrity: sha512-VQAadSubZIhNpH46IR3yWO4kZZjMxN1opDrzePLdVKAZ+DFjkGD/rf4v1jap744uPVU6yjL/smZbRIIJTOUnKQ==}
    dependencies:
      '@webassemblyjs/wast-printer': 1.8.5
    dev: false

  /@webassemblyjs/helper-fsm/1.8.5:
    resolution: {integrity: sha512-kRuX/saORcg8se/ft6Q2UbRpZwP4y7YrWsLXPbbmtepKr22i8Z4O3V5QE9DbZK908dh5Xya4Un57SDIKwB9eow==}
    dev: false

  /@webassemblyjs/helper-module-context/1.8.5:
    resolution: {integrity: sha512-/O1B236mN7UNEU4t9X7Pj38i4VoU8CcMHyy3l2cV/kIF4U5KoHXDVqcDuOs1ltkac90IM4vZdHc52t1x8Yfs3g==}
    dependencies:
      '@webassemblyjs/ast': 1.8.5
      mamacro: 0.0.3
    dev: false

  /@webassemblyjs/helper-wasm-bytecode/1.8.5:
    resolution: {integrity: sha512-Cu4YMYG3Ddl72CbmpjU/wbP6SACcOPVbHN1dI4VJNJVgFwaKf1ppeFJrwydOG3NDHxVGuCfPlLZNyEdIYlQ6QQ==}
    dev: false

  /@webassemblyjs/helper-wasm-section/1.8.5:
    resolution: {integrity: sha512-VV083zwR+VTrIWWtgIUpqfvVdK4ff38loRmrdDBgBT8ADXYsEZ5mPQ4Nde90N3UYatHdYoDIFb7oHzMncI02tA==}
    dependencies:
      '@webassemblyjs/ast': 1.8.5
      '@webassemblyjs/helper-buffer': 1.8.5
      '@webassemblyjs/helper-wasm-bytecode': 1.8.5
      '@webassemblyjs/wasm-gen': 1.8.5
    dev: false

  /@webassemblyjs/ieee754/1.8.5:
    resolution: {integrity: sha512-aaCvQYrvKbY/n6wKHb/ylAJr27GglahUO89CcGXMItrOBqRarUMxWLJgxm9PJNuKULwN5n1csT9bYoMeZOGF3g==}
    dependencies:
      '@xtuc/ieee754': 1.2.0
    dev: false

  /@webassemblyjs/leb128/1.8.5:
    resolution: {integrity: sha512-plYUuUwleLIziknvlP8VpTgO4kqNaH57Y3JnNa6DLpu/sGcP6hbVdfdX5aHAV716pQBKrfuU26BJK29qY37J7A==}
    dependencies:
      '@xtuc/long': 4.2.2
    dev: false

  /@webassemblyjs/utf8/1.8.5:
    resolution: {integrity: sha512-U7zgftmQriw37tfD934UNInokz6yTmn29inT2cAetAsaU9YeVCveWEwhKL1Mg4yS7q//NGdzy79nlXh3bT8Kjw==}
    dev: false

  /@webassemblyjs/wasm-edit/1.8.5:
    resolution: {integrity: sha512-A41EMy8MWw5yvqj7MQzkDjU29K7UJq1VrX2vWLzfpRHt3ISftOXqrtojn7nlPsZ9Ijhp5NwuODuycSvfAO/26Q==}
    dependencies:
      '@webassemblyjs/ast': 1.8.5
      '@webassemblyjs/helper-buffer': 1.8.5
      '@webassemblyjs/helper-wasm-bytecode': 1.8.5
      '@webassemblyjs/helper-wasm-section': 1.8.5
      '@webassemblyjs/wasm-gen': 1.8.5
      '@webassemblyjs/wasm-opt': 1.8.5
      '@webassemblyjs/wasm-parser': 1.8.5
      '@webassemblyjs/wast-printer': 1.8.5
    dev: false

  /@webassemblyjs/wasm-gen/1.8.5:
    resolution: {integrity: sha512-BCZBT0LURC0CXDzj5FXSc2FPTsxwp3nWcqXQdOZE4U7h7i8FqtFK5Egia6f9raQLpEKT1VL7zr4r3+QX6zArWg==}
    dependencies:
      '@webassemblyjs/ast': 1.8.5
      '@webassemblyjs/helper-wasm-bytecode': 1.8.5
      '@webassemblyjs/ieee754': 1.8.5
      '@webassemblyjs/leb128': 1.8.5
      '@webassemblyjs/utf8': 1.8.5
    dev: false

  /@webassemblyjs/wasm-opt/1.8.5:
    resolution: {integrity: sha512-HKo2mO/Uh9A6ojzu7cjslGaHaUU14LdLbGEKqTR7PBKwT6LdPtLLh9fPY33rmr5wcOMrsWDbbdCHq4hQUdd37Q==}
    dependencies:
      '@webassemblyjs/ast': 1.8.5
      '@webassemblyjs/helper-buffer': 1.8.5
      '@webassemblyjs/wasm-gen': 1.8.5
      '@webassemblyjs/wasm-parser': 1.8.5
    dev: false

  /@webassemblyjs/wasm-parser/1.8.5:
    resolution: {integrity: sha512-pi0SYE9T6tfcMkthwcgCpL0cM9nRYr6/6fjgDtL6q/ZqKHdMWvxitRi5JcZ7RI4SNJJYnYNaWy5UUrHQy998lw==}
    dependencies:
      '@webassemblyjs/ast': 1.8.5
      '@webassemblyjs/helper-api-error': 1.8.5
      '@webassemblyjs/helper-wasm-bytecode': 1.8.5
      '@webassemblyjs/ieee754': 1.8.5
      '@webassemblyjs/leb128': 1.8.5
      '@webassemblyjs/utf8': 1.8.5
    dev: false

  /@webassemblyjs/wast-parser/1.8.5:
    resolution: {integrity: sha512-daXC1FyKWHF1i11obK086QRlsMsY4+tIOKgBqI1lxAnkp9xe9YMcgOxm9kLe+ttjs5aWV2KKE1TWJCN57/Btsg==}
    dependencies:
      '@webassemblyjs/ast': 1.8.5
      '@webassemblyjs/floating-point-hex-parser': 1.8.5
      '@webassemblyjs/helper-api-error': 1.8.5
      '@webassemblyjs/helper-code-frame': 1.8.5
      '@webassemblyjs/helper-fsm': 1.8.5
      '@xtuc/long': 4.2.2
    dev: false

  /@webassemblyjs/wast-printer/1.8.5:
    resolution: {integrity: sha512-w0U0pD4EhlnvRyeJzBqaVSJAo9w/ce7/WPogeXLzGkO6hzhr4GnQIZ4W4uUt5b9ooAaXPtnXlj0gzsXEOUNYMg==}
    dependencies:
      '@webassemblyjs/ast': 1.8.5
      '@webassemblyjs/wast-parser': 1.8.5
      '@xtuc/long': 4.2.2
    dev: false

  /@xtuc/ieee754/1.2.0:
    resolution: {integrity: sha512-DX8nKgqcGwsc0eJSqYt5lwP4DH5FlHnmuWWBRy7X0NcaGR0ZtuyeESgMwTYVEtxmsNGY+qit4QYT/MIYTOTPeA==}
    dev: false

  /@xtuc/long/4.2.2:
    resolution: {integrity: sha512-NuHqBY1PB/D8xU6s/thBgOAiAP7HOYDQ32+BFZILJ8ivkUkAHQnWfn6WhL79Owj1qmUnoN/YPhktdIoucipkAQ==}
    dev: false

  /abab/2.0.6:
    resolution: {integrity: sha512-j2afSsaIENvHZN2B8GOpF566vZ5WVk5opAiMTvWgaQT8DkbOqsTfvNAvHoRGU2zzP8cPoqys+xHTRDWW8L+/BA==}
    dev: false

  /abbrev/1.1.1:
    resolution: {integrity: sha512-nne9/IiQ/hzIhY6pdDnbBtz7DjPTKrY00P/zvPSm5pOFkl6xuGrGnXn/VtTNNfNtAfZ9/1RtehkszU9qcTii0Q==}

  /accepts/1.3.8:
    resolution: {integrity: sha512-PYAthTa2m2VKxuvSD3DPC/Gy+U+sOA1LAuT8mkmRuvw+NACSaeXEQ+NHcVF7rONl6qcaxV3Uuemwawk+7+SJLw==}
    engines: {node: '>= 0.6'}
    dependencies:
      mime-types: 2.1.35
      negotiator: 0.6.3
    dev: false

  /acorn-globals/4.3.4:
    resolution: {integrity: sha512-clfQEh21R+D0leSbUdWf3OcfqyaCSAQ8Ryq00bofSekfr9W8u1jyYZo6ir0xu9Gtcf7BjcHJpnbZH7JOCpP60A==}
    dependencies:
      acorn: 6.4.2
      acorn-walk: 6.2.0
    dev: false

  /acorn-jsx/5.3.2_acorn@7.4.1:
    resolution: {integrity: sha512-rq9s+JNhf0IChjtDXxllJ7g41oZk5SlXtp0LHwyA5cejwn7vKmKp4pPri6YEePv2PU65sAsegbXtIinmDFDXgQ==}
    peerDependencies:
      acorn: ^6.0.0 || ^7.0.0 || ^8.0.0
    dependencies:
      acorn: 7.4.1
    dev: false

  /acorn-walk/6.2.0:
    resolution: {integrity: sha512-7evsyfH1cLOCdAzZAd43Cic04yKydNx0cF+7tiA19p1XnLLPU4dpCQOqpjqwokFe//vS0QqfqqjCS2JkiIs0cA==}
    engines: {node: '>=0.4.0'}
    dev: false

  /acorn/5.7.4:
    resolution: {integrity: sha512-1D++VG7BhrtvQpNbBzovKNc1FLGGEE/oGe7b9xJm/RFHMBeUaUGpluV9RLjZa47YFdPcDAenEYuq9pQPcMdLJg==}
    engines: {node: '>=0.4.0'}
    hasBin: true
    dev: false

  /acorn/6.4.2:
    resolution: {integrity: sha512-XtGIhXwF8YM8bJhGxG5kXgjkEuNGLTkoYqVE+KMR+aspr4KGYmKYg7yUe3KghyQ9yheNwLnjmzh/7+gfDBmHCQ==}
    engines: {node: '>=0.4.0'}
    hasBin: true
    dev: false

  /acorn/7.4.1:
    resolution: {integrity: sha512-nQyp0o1/mNdbTO1PO6kHkwSrmgZ0MT/jCCpNiwbUjGoRN4dlBhqJtoQuCnEOKzgTVwg0ZWiCoQy6SxMebQVh8A==}
    engines: {node: '>=0.4.0'}
    hasBin: true
    dev: false

  /acorn/8.8.0:
    resolution: {integrity: sha512-QOxyigPVrpZ2GXT+PFyZTl6TtOFc5egxHIP9IlQ+RbupQuX4RkT/Bee4/kQuC02Xkzg84JcT7oLYtDIQxp+v7w==}
    engines: {node: '>=0.4.0'}
    hasBin: true
    dev: false

  /add-dom-event-listener/1.1.0:
    resolution: {integrity: sha512-WCxx1ixHT0GQU9hb0KI/mhgRQhnU+U3GvwY6ZvVjYq8rsihIGoaIOUbY0yMPBxLH5MDtr0kz3fisWGNcbWW7Jw==}
    dependencies:
      object-assign: 4.1.1
    dev: false

  /address/1.1.2:
    resolution: {integrity: sha512-aT6camzM4xEA54YVJYSqxz1kv4IHnQZRtThJJHhUMRExaU5spC7jX5ugSwTaTgJliIgs4VhZOk7htClvQ/LmRA==}
    engines: {node: '>= 0.12.0'}
    dev: false

  /adjust-sourcemap-loader/2.0.0:
    resolution: {integrity: sha512-4hFsTsn58+YjrU9qKzML2JSSDqKvN8mUGQ0nNIrfPi8hmIONT4L3uUaT6MKdMsZ9AjsU6D2xDkZxCkbQPxChrA==}
    dependencies:
      assert: 1.4.1
      camelcase: 5.0.0
      loader-utils: 1.2.3
      object-path: 0.11.4
      regex-parser: 2.2.10
    dev: false

  /aggregate-error/3.1.0:
    resolution: {integrity: sha512-4I7Td01quW/RpocfNayFdFVk1qSuoh0E7JrbRJ16nH01HhKFQ88INq9Sd+nd72zqRySlr9BmDA8xlEJ6vJMrYA==}
    engines: {node: '>=8'}
    dependencies:
      clean-stack: 2.2.0
      indent-string: 4.0.0
    dev: false

  /ajv-errors/1.0.1_ajv@6.12.6:
    resolution: {integrity: sha512-DCRfO/4nQ+89p/RK43i8Ezd41EqdGIU4ld7nGF8OQ14oc/we5rEntLCUa7+jrn3nn83BosfwZA0wb4pon2o8iQ==}
    peerDependencies:
      ajv: '>=5.0.0'
    dependencies:
      ajv: 6.12.6
    dev: false

  /ajv-keywords/3.5.2_ajv@6.12.6:
    resolution: {integrity: sha512-5p6WTN0DdTGVQk6VjcEju19IgaHudalcfabD7yhDGeA6bcQnmL+CpveLJq/3hvfwd1aof6L386Ougkx6RfyMIQ==}
    peerDependencies:
      ajv: ^6.9.1
    dependencies:
      ajv: 6.12.6
    dev: false

  /ajv/6.12.6:
    resolution: {integrity: sha512-j3fVLgvTo527anyYyJOGTYJbG+vnnQYvE0m5mmkc1TK+nxAppkCLMIL0aZ4dblVCNoGShhm+kzE4ZUykBoMg4g==}
    dependencies:
      fast-deep-equal: 3.1.3
      fast-json-stable-stringify: 2.1.0
      json-schema-traverse: 0.4.1
      uri-js: 4.4.1

  /ajv/8.11.0:
    resolution: {integrity: sha512-wGgprdCvMalC0BztXvitD2hC04YffAvtsUn93JbGXYLAtCUO4xd17mCCZQxUOItiBwZvJScWo8NIvQMQ71rdpg==}
    dependencies:
      fast-deep-equal: 3.1.3
      json-schema-traverse: 1.0.0
      require-from-string: 2.0.2
      uri-js: 4.4.1
    dev: true

  /alphanum-sort/1.0.2:
    resolution: {integrity: sha1-l6ERlkmyEa0zaR2fn0hqjsn74KM=}
    dev: false

  /amdefine/1.0.1:
    resolution: {integrity: sha1-SlKCrBZHKek2Gbz9OtFR+BfOkfU=}
    engines: {node: '>=0.4.2'}

  /amfe-flexible/2.2.1:
    resolution: {integrity: sha1-NT8AKJ5NOMqIoh6zhUQgMIrwtVk=}
    dev: false

  /ansi-colors/3.2.4:
    resolution: {integrity: sha512-hHUXGagefjN2iRrID63xckIvotOXOojhQKWIPUZ4mNUZ9nLZW+7FMNoE1lOkEhNWYsx/7ysGIuJYCiMAA9FnrA==}
    engines: {node: '>=6'}
    dev: false

  /ansi-escapes/3.2.0:
    resolution: {integrity: sha512-cBhpre4ma+U0T1oM5fXg7Dy1Jw7zzwv7lt/GoCpr+hDQJoYnKVPLL4dCvSEFMmQurOQvSrwT7SL/DAlhBI97RQ==}
    engines: {node: '>=4'}
    dev: false

  /ansi-escapes/4.3.2:
    resolution: {integrity: sha512-gKXj5ALrKWQLsYG9jlTRmR/xKluxHV+Z9QEwNIgCfM1/uwPMCuzVVnh5mwTd+OuBZcwSIMbqssNWRm1lE51QaQ==}
    engines: {node: '>=8'}
    dependencies:
      type-fest: 0.21.3
    dev: false

  /ansi-html/0.0.7:
    resolution: {integrity: sha1-gTWEAhliqenm/QOflA0S9WynhZ4=}
    engines: {'0': node >= 0.8.0}
    hasBin: true
    dev: false

  /ansi-regex/2.1.1:
    resolution: {integrity: sha1-w7M6te42DYbg5ijwRorn7yfWVN8=}
    engines: {node: '>=0.10.0'}

  /ansi-regex/3.0.1:
    resolution: {integrity: sha512-+O9Jct8wf++lXxxFc4hc8LsjaSq0HFzzL7cVsw8pRDIPdjKD2mT4ytDZlLuSBZ4cLKZFXIrMGO7DbQCtMJJMKw==}
    engines: {node: '>=4'}
    dev: false

  /ansi-regex/4.1.1:
    resolution: {integrity: sha512-ILlv4k/3f6vfQ4OoP2AGvirOktlQ98ZEL1k9FaQjxa3L1abBgbuTDAdPOpvbGncC0BTVQrl+OM8xZGK6tWXt7g==}
    engines: {node: '>=6'}

  /ansi-regex/5.0.1:
    resolution: {integrity: sha1-CCyyyJyf6GWaMRpTvWpNxTAdswQ=}
    engines: {node: '>=8'}

  /ansi-styles/2.2.1:
    resolution: {integrity: sha1-tDLdM1i2NM914eRmQ2gkBTPB3b4=}
    engines: {node: '>=0.10.0'}

  /ansi-styles/3.2.1:
    resolution: {integrity: sha512-VT0ZI6kZRdTh8YyJw3SMbYm/u+NqfsAxEpWO0Pf9sq8/e94WxxOpPKx9FR1FlyCtOVDNOQ+8ntlqFxiRc+r5qA==}
    engines: {node: '>=4'}
    dependencies:
      color-convert: 1.9.3

  /ansi-styles/4.3.0:
    resolution: {integrity: sha512-zbB9rCJAT1rbjiVDb2hqKFHNYLxgtk8NURxZ3IZwD3F6NtxbXZQCnnSi1Lkx+IDohdPlFp222wVALIheZJQSEg==}
    engines: {node: '>=8'}
    dependencies:
      color-convert: 2.0.1

  /antd-mobile/2.3.1_wcqkhtmu7mswc6yz4uyexck3ty:
    resolution: {integrity: sha512-CBegyQuQiNxmwQH4Ck619Y9UO7EVN8FuhnUlV7kmbSSxxy+V704dSGy7uLNn7AUePRK5UZfm6ifLsZix6khZfQ==}
    dependencies:
      array-tree-filter: 2.1.0
      babel-runtime: 6.26.0
      classnames: 2.3.2
      normalize.css: 7.0.0
      rc-checkbox: 2.0.3
      rc-collapse: 1.9.3_wcqkhtmu7mswc6yz4uyexck3ty
      rc-slider: 8.2.0_wcqkhtmu7mswc6yz4uyexck3ty
      rc-swipeout: 2.0.11
      rmc-calendar: 1.1.4_wcqkhtmu7mswc6yz4uyexck3ty
      rmc-cascader: 5.0.3_wcqkhtmu7mswc6yz4uyexck3ty
      rmc-date-picker: 6.0.10_wcqkhtmu7mswc6yz4uyexck3ty
      rmc-dialog: 1.1.1_wcqkhtmu7mswc6yz4uyexck3ty
      rmc-drawer: 0.4.11
      rmc-feedback: 2.0.0
      rmc-input-number: 1.0.5
      rmc-list-view: 0.11.5
      rmc-notification: 1.0.0_wcqkhtmu7mswc6yz4uyexck3ty
      rmc-nuka-carousel: 3.0.1
      rmc-picker: 5.0.10_wcqkhtmu7mswc6yz4uyexck3ty
      rmc-pull-to-refresh: 1.0.13
      rmc-steps: 1.0.1
      rmc-tabs: 1.2.29
      rmc-tooltip: 1.0.1_wcqkhtmu7mswc6yz4uyexck3ty
    transitivePeerDependencies:
      - react
      - react-dom
    dev: false

  /anymatch/2.0.0:
    resolution: {integrity: sha512-5teOsQWABXHHBFP9y3skS5P3d/WfWXpv3FUpy+LorMrNYaT9pI4oLMQX7jzQ2KklNpGpWHzdCXTDT2Y3XGlZBw==}
    dependencies:
      micromatch: 3.1.10
      normalize-path: 2.1.1
    transitivePeerDependencies:
      - supports-color
    dev: false

  /anymatch/2.0.0_supports-color@6.1.0:
    resolution: {integrity: sha512-5teOsQWABXHHBFP9y3skS5P3d/WfWXpv3FUpy+LorMrNYaT9pI4oLMQX7jzQ2KklNpGpWHzdCXTDT2Y3XGlZBw==}
    dependencies:
      micromatch: 3.1.10_supports-color@6.1.0
      normalize-path: 2.1.1
    transitivePeerDependencies:
      - supports-color
    dev: false

  /anymatch/3.1.2:
    resolution: {integrity: sha512-P43ePfOAIupkguHUycrc4qJ9kz8ZiuOUijaETwX7THt0Y/GNK7v0aa8rY816xWjZ7rJdA5XdMcpVFTKMq+RvWg==}
    engines: {node: '>= 8'}
    dependencies:
      normalize-path: 3.0.0
      picomatch: 2.3.1
    dev: false

  /aproba/1.2.0:
    resolution: {integrity: sha512-Y9J6ZjXtoYh8RnXVCMOU/ttDmk1aBjunq9vO0ta5x85WDQiQfUF9sIPBITdbiiIVcBo03Hi3jMxigBtsddlXRw==}

  /archiver-utils/2.1.0:
    resolution: {integrity: sha512-bEL/yUb/fNNiNTuUz979Z0Yg5L+LzLxGJz8x79lYmR54fmTIb6ob/hNQgkQnIUDWIFjZVQwl9Xs356I6BAMHfw==}
    engines: {node: '>= 6'}
    dependencies:
      glob: 7.2.3
      graceful-fs: 4.2.10
      lazystream: 1.0.1
      lodash.defaults: 4.2.0
      lodash.difference: 4.5.0
      lodash.flatten: 4.4.0
      lodash.isplainobject: 4.0.6
      lodash.union: 4.6.0
      normalize-path: 3.0.0
      readable-stream: 2.3.7
    dev: false

  /archiver/5.3.1:
    resolution: {integrity: sha512-8KyabkmbYrH+9ibcTScQ1xCJC/CGcugdVIwB+53f5sZziXgwUh3iXlAlANMxcZyDEfTHMe6+Z5FofV8nopXP7w==}
    engines: {node: '>= 10'}
    dependencies:
      archiver-utils: 2.1.0
      async: 3.2.4
      buffer-crc32: 0.2.13
      readable-stream: 3.6.0
      readdir-glob: 1.1.2
      tar-stream: 2.2.0
      zip-stream: 4.1.0
    dev: false

  /are-we-there-yet/1.1.7:
    resolution: {integrity: sha1-sVR0qTKtq0/4pQ2a36fk6SbyEUY=}
    dependencies:
      delegates: 1.0.0
      readable-stream: 2.3.7

  /argparse/1.0.10:
    resolution: {integrity: sha512-o5Roy6tNG4SL/FOkCAN6RzjiakZS25RLYFrcMttJqbdd8BWrnA+fGz57iN5Pb06pvBGvl5gQ0B48dJlslXvoTg==}
    dependencies:
      sprintf-js: 1.0.3
    dev: false

  /aria-query/3.0.0:
    resolution: {integrity: sha1-ZbP8wcoRVajJrmTW7uKX8V1RM8w=}
    dependencies:
      ast-types-flow: 0.0.7
      commander: 2.20.3
    dev: false

  /aria-query/4.2.2:
    resolution: {integrity: sha512-o/HelwhuKpTj/frsOsbNLNgnNGVIFsVP/SW2BSF14gVl7kAfMOJ6/8wUAUvG1R1NHKrfG+2sHZTu0yauT1qBrA==}
    engines: {node: '>=6.0'}
    dependencies:
      '@babel/runtime': 7.19.4
      '@babel/runtime-corejs3': 7.19.6
    dev: true

  /arity-n/1.0.4:
    resolution: {integrity: sha1-2edrEXM+CFacCEeuezmyhgswt0U=}
    dev: false

  /arr-diff/4.0.0:
    resolution: {integrity: sha1-1kYQdP6/7HHn4VI1dhoyml3HxSA=}
    engines: {node: '>=0.10.0'}
    dev: false

  /arr-flatten/1.1.0:
    resolution: {integrity: sha512-L3hKV5R/p5o81R7O02IGnwpDmkp6E982XhtbuwSe3O4qOtMMMtodicASA1Cny2U+aCXcNpml+m4dPsvsJ3jatg==}
    engines: {node: '>=0.10.0'}
    dev: false

  /arr-union/3.1.0:
    resolution: {integrity: sha1-45sJrqne+Gao8gbiiK9jkZuuOcQ=}
    engines: {node: '>=0.10.0'}
    dev: false

  /array-equal/1.0.0:
    resolution: {integrity: sha1-jCpe8kcv2ep0KwTHenUJO6J1fJM=}
    dev: false

  /array-find-index/1.0.2:
    resolution: {integrity: sha1-3wEKoSh+Fku9pvlyOwqWoexBh6E=}
    engines: {node: '>=0.10.0'}

  /array-flatten/1.1.1:
    resolution: {integrity: sha1-ml9pkFGx5wczKPKgCJaLZOopVdI=}
    dev: false

  /array-flatten/2.1.2:
    resolution: {integrity: sha512-hNfzcOV8W4NdualtqBFPyVO+54DSJuZGY9qT4pRroB6S9e3iiido2ISIC5h9R2sPJ8H3FHCIiEnsv1lPXO3KtQ==}
    dev: false

  /array-includes/3.1.5:
    resolution: {integrity: sha512-iSDYZMMyTPkiFasVqfuAQnWAYcvO/SeBSCGKePoEthjp4LEMTe4uLc7b025o4jAZpHhihh8xPo99TNWUWWkGDQ==}
    engines: {node: '>= 0.4'}
    dependencies:
      call-bind: 1.0.2
      define-properties: 1.1.4
      es-abstract: 1.20.4
      get-intrinsic: 1.1.3
      is-string: 1.0.7
    dev: false

  /array-tree-filter/2.1.0:
    resolution: {integrity: sha512-4ROwICNlNw/Hqa9v+rk5h22KjmzB1JGTMVKP2AKJBOCgb0yL0ASf0+YvCcLNNwquOHNX48jkeZIJ3a+oOQqKcw==}
    dev: false

  /array-union/1.0.2:
    resolution: {integrity: sha1-mjRBDk9OPaI96jdb5b5w8kd47Dk=}
    engines: {node: '>=0.10.0'}
    dependencies:
      array-uniq: 1.0.3
    dev: false

  /array-union/2.1.0:
    resolution: {integrity: sha512-HGyxoOTYUyCM6stUe6EJgnd4EoewAI7zMdfqO+kGjnlZmBDz/cR5pf8r/cR4Wq60sL/p0IkcjUEEPwS3GFrIyw==}
    engines: {node: '>=8'}
    dev: true

  /array-uniq/1.0.3:
    resolution: {integrity: sha1-r2rId6Jcx/dOBYiUdThY39sk/bY=}
    engines: {node: '>=0.10.0'}
    dev: false

  /array-unique/0.3.2:
    resolution: {integrity: sha1-qJS3XUvE9s1nnvMkSp/Y9Gri1Cg=}
    engines: {node: '>=0.10.0'}
    dev: false

  /array.prototype.flat/1.3.0:
    resolution: {integrity: sha512-12IUEkHsAhA4DY5s0FPgNXIdc8VRSqD9Zp78a5au9abH/SOBrsp082JOWFNTjkMozh8mqcdiKuaLGhPeYztxSw==}
    engines: {node: '>= 0.4'}
    dependencies:
      call-bind: 1.0.2
      define-properties: 1.1.4
      es-abstract: 1.20.4
      es-shim-unscopables: 1.0.0
    dev: false

  /array.prototype.reduce/1.0.4:
    resolution: {integrity: sha512-WnM+AjG/DvLRLo4DDl+r+SvCzYtD2Jd9oeBYMcEaI7t3fFrHY9M53/wdLcTvmZNQ70IU6Htj0emFkZ5TS+lrdw==}
    engines: {node: '>= 0.4'}
    dependencies:
      call-bind: 1.0.2
      define-properties: 1.1.4
      es-abstract: 1.20.4
      es-array-method-boxes-properly: 1.0.0
      is-string: 1.0.7
    dev: false

  /arrify/1.0.1:
    resolution: {integrity: sha1-iYUI2iIm84DfkEcoRWhJwVAaSw0=}
    engines: {node: '>=0.10.0'}

  /asap/2.0.6:
    resolution: {integrity: sha1-5QNHYR1+aQlDIIu9r+vLwvuGbUY=}
    dev: false

  /asn1.js/5.4.1:
    resolution: {integrity: sha512-+I//4cYPccV8LdmBLiX8CYvf9Sp3vQsrqu2QNXRcrbiWvcx/UdlFiqUJJzxRQxgsZmvhXhn4cSKeSmoFjVdupA==}
    dependencies:
      bn.js: 4.12.0
      inherits: 2.0.4
      minimalistic-assert: 1.0.1
      safer-buffer: 2.1.2
    dev: false

  /asn1/0.2.6:
    resolution: {integrity: sha1-DTp7tuZOAqkMAwOzHykoaOoJoI0=}
    dependencies:
      safer-buffer: 2.1.2

  /assert-plus/1.0.0:
    resolution: {integrity: sha1-8S4PPF13sLHN2RRpQuTpbB5N1SU=}
    engines: {node: '>=0.8'}

  /assert/1.4.1:
    resolution: {integrity: sha1-mZEtWRg2tab1s0XA8H7vwI/GXZE=}
    dependencies:
      util: 0.10.3
    dev: false

  /assert/1.5.0:
    resolution: {integrity: sha512-EDsgawzwoun2CZkCgtxJbv392v4nbk9XDD06zI+kQYoBM/3RBWLlEyJARDOmhAAosBjWACEkKL6S+lIZtcAubA==}
    dependencies:
      object-assign: 4.1.1
      util: 0.10.3
    dev: false

  /assign-symbols/1.0.0:
    resolution: {integrity: sha1-WWZ/QfrdTyDMvCu5a41Pf3jsA2c=}
    engines: {node: '>=0.10.0'}
    dev: false

  /ast-types-flow/0.0.7:
    resolution: {integrity: sha1-9wtzXGvKGlycItmCw+Oef+ujva0=}
    dev: false

  /astral-regex/1.0.0:
    resolution: {integrity: sha512-+Ryf6g3BKoRc7jfp7ad8tM4TtMiaWvbF/1/sQcZPkkS7ag3D5nMBCe2UfOTONtAkaG0tO0ij3C5Lwmf1EiyjHg==}
    engines: {node: '>=4'}
    dev: false

  /astral-regex/2.0.0:
    resolution: {integrity: sha512-Z7tMw1ytTXt5jqMcOP+OQteU1VuNK9Y02uuJtKQ1Sv69jXQKKg5cibLwGJow8yzZP+eAc18EmLGPal0bp36rvQ==}
    engines: {node: '>=8'}
    dev: true

  /async-each/1.0.3:
    resolution: {integrity: sha512-z/WhQ5FPySLdvREByI2vZiTWwCnF0moMJ1hK9YQwDTHKh6I7/uSckMetoRGb5UBZPC1z0jlw+n/XCgjeH7y1AQ==}
    dev: false

  /async-foreach/0.1.3:
    resolution: {integrity: sha1-NhIfhFwFeBct5Bmpfb6x0W7DRUI=}

  /async-limiter/1.0.1:
    resolution: {integrity: sha512-csOlWGAcRFJaI6m+F2WKdnMKr4HhdhFVBk0H/QbJFMCr+uO2kwohwXQPxw/9OCxp05r5ghVBFSyioixx3gfkNQ==}
    dev: false

  /async/2.6.4:
    resolution: {integrity: sha512-mzo5dfJYwAn29PeiJ0zvwTo04zj8HDJj0Mn8TD7sno7q12prdbnasKJHhkm2c1LgrhlJ0teaea8860oxi51mGA==}
    dependencies:
      lodash: 4.17.21
    dev: false

  /async/3.2.4:
    resolution: {integrity: sha512-iAB+JbDEGXhyIUavoDl9WP/Jj106Kz9DEn1DPgYw5ruDn0e3Wgi3sKFm55sASdGBNOQB8F59d9qQ7deqrHA8wQ==}
    dev: false

  /asynckit/0.4.0:
    resolution: {integrity: sha1-x57Zf380y48robyXkLzDZkdLS3k=}

  /atob/2.1.2:
    resolution: {integrity: sha512-Wm6ukoaOGJi/73p/cl2GvLjTI5JM1k/O14isD73YML8StrH/7/lRFgmg8nICZgD3bZZvjwCGxtMOD3wWNAu8cg==}
    engines: {node: '>= 4.5.0'}
    hasBin: true

  /autoprefixer/9.8.8:
    resolution: {integrity: sha1-/UvUWVOF+m8GWZ3nSaTV96R0lXo=}
    hasBin: true
    dependencies:
      browserslist: 4.21.4
      caniuse-lite: 1.0.30001423
      normalize-range: 0.1.2
      num2fraction: 1.2.2
      picocolors: 0.2.1
      postcss: 7.0.39
      postcss-value-parser: 4.2.0

  /aws-sign2/0.7.0:
    resolution: {integrity: sha1-tG6JCTSpWR8tL2+G1+ap8bP+dqg=}

  /aws4/1.11.0:
    resolution: {integrity: sha512-xh1Rl34h6Fi1DC2WWKfxUTVqRsNnr6LsKz2+hfwDxQJWmrx8+c7ylaqBMcHfl1U1r2dsifOvKX3LQuLNZ+XSvA==}

  /axios-mock-adapter/1.21.2_axios@0.19.2:
    resolution: {integrity: sha512-jzyNxU3JzB2XVhplZboUcF0YDs7xuExzoRSHXPHr+UQajaGmcTqvkkUADgkVI2WkGlpZ1zZlMVdcTMU0ejV8zQ==}
    peerDependencies:
      axios: '>= 0.17.0'
    dependencies:
      axios: 0.19.2
      fast-deep-equal: 3.1.3
      is-buffer: 2.0.5
    dev: false

  /axios/0.19.2:
    resolution: {integrity: sha512-fjgm5MvRHLhx+osE2xoekY70AhARk3a6hkN+3Io1jc00jtquGvxYlKlsFUhmUET0V5te6CcZI7lcv2Ym61mjHA==}
    dependencies:
      follow-redirects: 1.5.10
    transitivePeerDependencies:
      - supports-color
    dev: false

  /axobject-query/2.2.0:
    resolution: {integrity: sha512-Td525n+iPOOyUQIeBfcASuG6uJsDOITl7Mds5gFyerkWiX7qhUTdYUBlSgNMyVqtSJqwpt1kXGLdUt6SykLMRA==}
    dev: false

  /babel-code-frame/6.26.0:
    resolution: {integrity: sha1-Y/1D99weO7fONZR9uP42mj9Yx0s=}
    dependencies:
      chalk: 1.1.3
      esutils: 2.0.3
      js-tokens: 3.0.2
    dev: false

  /babel-eslint/10.0.3_eslint@6.8.0:
    resolution: {integrity: sha512-z3U7eMY6r/3f3/JB9mTsLjyxrv0Yb1zb8PCWCLpguxfCzBIZUwy23R1t/XKewP+8mEN2Ck8Dtr4q20z6ce6SoA==}
    engines: {node: '>=6'}
    peerDependencies:
      eslint: '>= 4.12.1'
    dependencies:
      '@babel/code-frame': 7.18.6
      '@babel/parser': 7.19.6
      '@babel/traverse': 7.19.6
      '@babel/types': 7.19.4
      eslint: 6.8.0
      eslint-visitor-keys: 1.3.0
      resolve: 1.15.0
    transitivePeerDependencies:
      - supports-color
    dev: false

  /babel-extract-comments/1.0.0:
    resolution: {integrity: sha512-qWWzi4TlddohA91bFwgt6zO/J0X+io7Qp184Fw0m2JYRSTZnJbFR8+07KmzudHCZgOiKRCrjhylwv9Xd8gfhVQ==}
    engines: {node: '>=4'}
    dependencies:
      babylon: 6.18.0
    dev: false

  /babel-jest/24.9.0_@babel+core@7.8.4:
    resolution: {integrity: sha512-ntuddfyiN+EhMw58PTNL1ph4C9rECiQXjI4nMMBKBaNjXvqLdkXpPRcMSr4iyBrJg/+wz9brFUD6RhOAT6r4Iw==}
    engines: {node: '>= 6'}
    peerDependencies:
      '@babel/core': ^7.0.0
    dependencies:
      '@babel/core': 7.8.4
      '@jest/transform': 24.9.0
      '@jest/types': 24.9.0
      '@types/babel__core': 7.1.19
      babel-plugin-istanbul: 5.2.0
      babel-preset-jest: 24.9.0_@babel+core@7.8.4
      chalk: 2.4.2
      slash: 2.0.0
    transitivePeerDependencies:
      - supports-color
    dev: false

  /babel-jest/24.9.0_@babel+core@7.9.0:
    resolution: {integrity: sha512-ntuddfyiN+EhMw58PTNL1ph4C9rECiQXjI4nMMBKBaNjXvqLdkXpPRcMSr4iyBrJg/+wz9brFUD6RhOAT6r4Iw==}
    engines: {node: '>= 6'}
    peerDependencies:
      '@babel/core': ^7.0.0
    dependencies:
      '@babel/core': 7.9.0
      '@jest/transform': 24.9.0
      '@jest/types': 24.9.0
      '@types/babel__core': 7.1.19
      babel-plugin-istanbul: 5.2.0
      babel-preset-jest: 24.9.0_@babel+core@7.9.0
      chalk: 2.4.2
      slash: 2.0.0
    transitivePeerDependencies:
      - supports-color
    dev: false

  /babel-loader/8.0.6_7z6h6nfmdlc43mjyviod6jqfeu:
    resolution: {integrity: sha512-4BmWKtBOBm13uoUwd08UwjZlaw3O9GWf456R9j+5YykFZ6LUIjIKLc0zEZf+hauxPOJs96C8k6FvYD09vWzhYw==}
    engines: {node: '>= 6.9'}
    peerDependencies:
      '@babel/core': ^7.0.0
      webpack: '>=2'
    dependencies:
      '@babel/core': 7.8.4
      find-cache-dir: 2.1.0
      loader-utils: 1.4.0
      mkdirp: 0.5.6
      pify: 4.0.1
      webpack: 4.41.5
    dev: false

  /babel-plugin-import/1.13.5:
    resolution: {integrity: sha512-IkqnoV+ov1hdJVofly9pXRJmeDm9EtROfrc5i6eII0Hix2xMs5FEm8FG3ExMvazbnZBbgHIt6qdO8And6lCloQ==}
    dependencies:
      '@babel/helper-module-imports': 7.18.6
    dev: true

  /babel-plugin-istanbul/5.2.0:
    resolution: {integrity: sha512-5LphC0USA8t4i1zCtjbbNb6jJj/9+X6P37Qfirc/70EQ34xKlMW+a1RHGwxGI+SwWpNwZ27HqvzAobeqaXwiZw==}
    engines: {node: '>=6'}
    dependencies:
      '@babel/helper-plugin-utils': 7.19.0
      find-up: 3.0.0
      istanbul-lib-instrument: 3.3.0
      test-exclude: 5.2.3
    transitivePeerDependencies:
      - supports-color
    dev: false

  /babel-plugin-jest-hoist/24.9.0:
    resolution: {integrity: sha512-2EMA2P8Vp7lG0RAzr4HXqtYwacfMErOuv1U3wrvxHX6rD1sV6xS3WXG3r8TRQ2r6w8OhvSdWt+z41hQNwNm3Xw==}
    engines: {node: '>= 6'}
    dependencies:
      '@types/babel__traverse': 7.18.2
    dev: false

  /babel-plugin-macros/2.8.0:
    resolution: {integrity: sha512-SEP5kJpfGYqYKpBrj5XU3ahw5p5GOHJ0U5ssOSQ/WBVdwkD2Dzlce95exQTs3jOVWPPKLBN2rlEWkCK7dSmLvg==}
    dependencies:
      '@babel/runtime': 7.9.0
      cosmiconfig: 6.0.0
      resolve: 1.22.1
    dev: false

  /babel-plugin-named-asset-import/0.3.8_@babel+core@7.8.4:
    resolution: {integrity: sha512-WXiAc++qo7XcJ1ZnTYGtLxmBCVbddAml3CEXgWaBzNzLNoxtQ8AiGEFDMOhot9XjTCQbvP5E77Fj9Gk924f00Q==}
    peerDependencies:
      '@babel/core': ^7.1.0
    dependencies:
      '@babel/core': 7.8.4
    dev: false

  /babel-plugin-polyfill-corejs2/0.3.3_@babel+core@7.8.4:
    resolution: {integrity: sha512-8hOdmFYFSZhqg2C/JgLUQ+t52o5nirNwaWM2B9LWteozwIvM14VSwdsCAUET10qT+kmySAlseadmfeeSWFCy+Q==}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/compat-data': 7.19.4
      '@babel/core': 7.8.4
      '@babel/helper-define-polyfill-provider': 0.3.3_@babel+core@7.8.4
      semver: 6.3.0
    transitivePeerDependencies:
      - supports-color
    dev: false

  /babel-plugin-polyfill-corejs3/0.6.0_@babel+core@7.8.4:
    resolution: {integrity: sha512-+eHqR6OPcBhJOGgsIar7xoAB1GcSwVUA3XjAd7HJNzOXT4wv6/H7KIdA/Nc60cvUlDbKApmqNvD1B1bzOt4nyA==}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.8.4
      '@babel/helper-define-polyfill-provider': 0.3.3_@babel+core@7.8.4
      core-js-compat: 3.25.5
    transitivePeerDependencies:
      - supports-color
    dev: false

  /babel-plugin-polyfill-regenerator/0.4.1_@babel+core@7.8.4:
    resolution: {integrity: sha512-NtQGmyQDXjQqQ+IzRkBVwEOz9lQ4zxAQZgoAYEtU9dJjnl1Oc98qnN7jcp+bE7O7aYzVpavXE3/VKXNzUbh7aw==}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.8.4
      '@babel/helper-define-polyfill-provider': 0.3.3_@babel+core@7.8.4
    transitivePeerDependencies:
      - supports-color
    dev: false

  /babel-plugin-syntax-object-rest-spread/6.13.0:
    resolution: {integrity: sha1-/WU28rzhODb/o6VFjEkDpZe7O/U=}
    dev: false

  /babel-plugin-transform-object-rest-spread/6.26.0:
    resolution: {integrity: sha1-DzZpLVD+9rfi1LOsFHgTepY7ewY=}
    dependencies:
      babel-plugin-syntax-object-rest-spread: 6.13.0
      babel-runtime: 6.26.0
    dev: false

  /babel-plugin-transform-react-remove-prop-types/0.4.24:
    resolution: {integrity: sha512-eqj0hVcJUR57/Ug2zE1Yswsw4LhuqqHhD+8v120T1cl3kjg76QwtyBrdIk4WVwK+lAhBJVYCd/v+4nc4y+8JsA==}
    dev: false

  /babel-preset-jest/24.9.0_@babel+core@7.8.4:
    resolution: {integrity: sha512-izTUuhE4TMfTRPF92fFwD2QfdXaZW08qvWTFCI51V8rW5x00UuPgc3ajRoWofXOuxjfcOM5zzSYsQS3H8KGCAg==}
    engines: {node: '>= 6'}
    peerDependencies:
      '@babel/core': ^7.0.0
    dependencies:
      '@babel/core': 7.8.4
      '@babel/plugin-syntax-object-rest-spread': 7.8.3_@babel+core@7.8.4
      babel-plugin-jest-hoist: 24.9.0
    dev: false

  /babel-preset-jest/24.9.0_@babel+core@7.9.0:
    resolution: {integrity: sha512-izTUuhE4TMfTRPF92fFwD2QfdXaZW08qvWTFCI51V8rW5x00UuPgc3ajRoWofXOuxjfcOM5zzSYsQS3H8KGCAg==}
    engines: {node: '>= 6'}
    peerDependencies:
      '@babel/core': ^7.0.0
    dependencies:
      '@babel/core': 7.9.0
      '@babel/plugin-syntax-object-rest-spread': 7.8.3_@babel+core@7.9.0
      babel-plugin-jest-hoist: 24.9.0
    dev: false

  /babel-preset-react-app/9.1.2:
    resolution: {integrity: sha512-k58RtQOKH21NyKtzptoAvtAODuAJJs3ZhqBMl456/GnXEQ/0La92pNmwgWoMn5pBTrsvk3YYXdY7zpY4e3UIxA==}
    dependencies:
      '@babel/core': 7.9.0
      '@babel/plugin-proposal-class-properties': 7.8.3_@babel+core@7.9.0
      '@babel/plugin-proposal-decorators': 7.8.3_@babel+core@7.9.0
      '@babel/plugin-proposal-nullish-coalescing-operator': 7.8.3_@babel+core@7.9.0
      '@babel/plugin-proposal-numeric-separator': 7.8.3_@babel+core@7.9.0
      '@babel/plugin-proposal-optional-chaining': 7.9.0_@babel+core@7.9.0
      '@babel/plugin-transform-flow-strip-types': 7.9.0_@babel+core@7.9.0
      '@babel/plugin-transform-react-display-name': 7.8.3_@babel+core@7.9.0
      '@babel/plugin-transform-runtime': 7.9.0_@babel+core@7.9.0
      '@babel/preset-env': 7.9.0_@babel+core@7.9.0
      '@babel/preset-react': 7.9.1_@babel+core@7.9.0
      '@babel/preset-typescript': 7.9.0_@babel+core@7.9.0
      '@babel/runtime': 7.9.0
      babel-plugin-macros: 2.8.0
      babel-plugin-transform-react-remove-prop-types: 0.4.24
    transitivePeerDependencies:
      - supports-color
    dev: false

  /babel-runtime/6.26.0:
    resolution: {integrity: sha1-llxwWGaOgrVde/4E/yM3vItWR/4=}
    dependencies:
      core-js: 2.6.12
      regenerator-runtime: 0.11.1
    dev: false

  /babylon/6.18.0:
    resolution: {integrity: sha512-q/UEjfGJ2Cm3oKV71DJz9d25TPnq5rhBVL2Q4fA5wcC3jcrdn7+SssEybFIxwAvvP+YCsCYNKughoF33GxgycQ==}
    hasBin: true
    dev: false

  /bail/1.0.5:
    resolution: {integrity: sha512-xFbRxM1tahm08yHBP16MMjVUAvDaBMD38zsM9EMAUN61omwLmKlOpB/Zku5QkjZ8TZ4vn53pj+t518cH0S03RQ==}
    dev: true

  /balanced-match/1.0.2:
    resolution: {integrity: sha512-3oSeUO0TMV67hN1AmbXsK4yaqU7tjiHlbxRDZOpH0KW9+CeX4bRAaX0Anxt0tx2MrpRpWwQaPwIlISEJhYU5Pw==}

  /balanced-match/2.0.0:
    resolution: {integrity: sha512-1ugUSr8BHXRnK23KfuYS+gVMC3LB8QGH9W1iGtDPsNWoQbgtXSExkBu2aDR4epiGWZOjZsj6lDl/N/AqqTC3UA==}
    dev: true

  /base/0.11.2:
    resolution: {integrity: sha512-5T6P4xPgpp0YDFvSWwEZ4NoE3aM4QBQXDzmVbraCkFj8zHM+mba8SyqB5DbZWyR7mYHo6Y7BdQo3MoA4m0TeQg==}
    engines: {node: '>=0.10.0'}
    dependencies:
      cache-base: 1.0.1
      class-utils: 0.3.6
      component-emitter: 1.3.0
      define-property: 1.0.0
      isobject: 3.0.1
      mixin-deep: 1.3.2
      pascalcase: 0.1.1
    dev: false

  /base64-arraybuffer/1.0.2:
    resolution: {integrity: sha512-I3yl4r9QB5ZRY3XuJVEPfc2XhZO6YweFPI+UovAzn+8/hb3oJ6lnysaFcjVpkCPfVWFUDvoZ8kmVDP7WyRtYtQ==}
    engines: {node: '>= 0.6.0'}
    dev: false

  /base64-js/1.5.1:
    resolution: {integrity: sha512-AKpaYlHn8t4SVbOHCy+b5+KKgvR4vrsD8vbvrbiQJps7fKDTkjkDry6ji0rUJjC0kzbNePLwzxq8iypo41qeWA==}
    dev: false

  /batch/0.6.1:
    resolution: {integrity: sha1-3DQxT05nkxgJP8dgJyUl+UvyXBY=}
    dev: false

  /bcrypt-pbkdf/1.0.2:
    resolution: {integrity: sha1-pDAdOJtqQ/m2f/PKEaP2Y342Dp4=}
    dependencies:
      tweetnacl: 0.14.5

  /bestzip/2.2.1:
    resolution: {integrity: sha512-XdAb87RXqOqF7C6UgQG9IqpEHJvS6IOUo0bXWEAebjSSdhDjsbcqFKdHpn5Q7QHz2pGr3Zmw4wgG3LlzdyDz7w==}
    engines: {node: '>=10'}
    hasBin: true
    dependencies:
      archiver: 5.3.1
      async: 3.2.4
      glob: 7.2.3
      which: 2.0.2
      yargs: 16.2.0
    dev: false

  /big.js/5.2.2:
    resolution: {integrity: sha512-vyL2OymJxmarO8gxMr0mhChsO9QGwhynfuu4+MHTAW6czfq9humCB7rKpUjDd9YUiDPU4mzpyupFSvOClAwbmQ==}
    dev: false

  /binary-extensions/1.13.1:
    resolution: {integrity: sha512-Un7MIEDdUC5gNpcGDV97op1Ywk748MpHcFTHoYs6qnj1Z3j7I53VG3nwZhKzoBZmbdRNnb6WRdFlwl7tSDuZGw==}
    engines: {node: '>=0.10.0'}
    dev: false

  /binary-extensions/2.2.0:
    resolution: {integrity: sha512-jDctJ/IVQbZoJykoeHbhXpOlNBqGNcwXJKJog42E5HDPUwQTSdjCHdihjj0DlnheQ7blbT6dHOafNAiS8ooQKA==}
    engines: {node: '>=8'}
    dev: false

  /bindings/1.5.0:
    resolution: {integrity: sha512-p2q/t/mhvuOj/UeLlV6566GD/guowlr0hHxClI0W9m7MWYkL1F0hLo+0Aexs9HSPCtR1SXQ0TD3MMKrXZajbiQ==}
    dependencies:
      file-uri-to-path: 1.0.0
    dev: false
    optional: true

  /bl/4.1.0:
    resolution: {integrity: sha512-1W07cM9gS6DcLperZfFSj+bWLtaPGSOHWhPiGzXmvVJbRLdG82sH/Kn8EtW1VqWVA54AKf2h5k5BbnIbwF3h6w==}
    dependencies:
      buffer: 5.7.1
      inherits: 2.0.4
      readable-stream: 3.6.0
    dev: false

  /block-stream/0.0.9:
    resolution: {integrity: sha1-E+v+d4oDIFz+A3UUgeu0szAMEmo=}
    engines: {node: 0.4 || >=0.5.8}
    dependencies:
      inherits: 2.0.4

  /bluebird/3.7.2:
    resolution: {integrity: sha512-XpNj6GDQzdfW+r2Wnn7xiSAd7TM3jzkxGXBGTtWKuSXv1xUV+azxAm8jdWZN06QTQk+2N2XB9jRDkvbmQmcRtg==}
    dev: false

  /bn.js/4.12.0:
    resolution: {integrity: sha512-c98Bf3tPniI+scsdk237ku1Dc3ujXQTSgyiPUDEOe7tRkhrqridvh8klBv0HCEso1OLOYcHuCv/cS6DNxKH+ZA==}
    dev: false

  /bn.js/5.2.1:
    resolution: {integrity: sha512-eXRvHzWyYPBuB4NBy0cmYQjGitUrtqwbvlzP3G6VFnNRbsZQIxQ10PbKKHt8gZ/HW/D/747aDl+QkDqg3KQLMQ==}
    dev: false

  /body-parser/1.20.1_supports-color@6.1.0:
    resolution: {integrity: sha512-jWi7abTbYwajOytWCQc37VulmWiRae5RyTpaCyDcS5/lMdtwSz5lOpDE67srw/HYe35f1z3fDQw+3txg7gNtWw==}
    engines: {node: '>= 0.8', npm: 1.2.8000 || >= 1.4.16}
    dependencies:
      bytes: 3.1.2
      content-type: 1.0.4
      debug: 2.6.9_supports-color@6.1.0
      depd: 2.0.0
      destroy: 1.2.0
      http-errors: 2.0.0
      iconv-lite: 0.4.24
      on-finished: 2.4.1
      qs: 6.11.0
      raw-body: 2.5.1
      type-is: 1.6.18
      unpipe: 1.0.0
    transitivePeerDependencies:
      - supports-color
    dev: false

  /bonjour/3.5.0:
    resolution: {integrity: sha1-jokKGD2O6aI5OzhExpGkK897yfU=}
    dependencies:
      array-flatten: 2.1.2
      deep-equal: 1.1.1
      dns-equal: 1.0.0
      dns-txt: 2.0.2
      multicast-dns: 6.2.3
      multicast-dns-service-types: 1.1.0
    dev: false

  /boolbase/1.0.0:
    resolution: {integrity: sha1-aN/1++YMUes3cl6p4+0xDcwed24=}
    dev: false

  /brace-expansion/1.1.11:
    resolution: {integrity: sha512-iCuPHDFgrHX7H2vEI/5xpz07zSHB00TpugqhmYtVmMO6518mCuRMoOYFldEBl0g187ufozdaHgWKcYFb61qGiA==}
    dependencies:
      balanced-match: 1.0.2
      concat-map: 0.0.1

  /brace-expansion/2.0.1:
    resolution: {integrity: sha512-XnAIvQ8eM+kC6aULx6wuQiwVsnzsi9d3WxzV3FpWTGA19F621kwdbsAcFKXgKUHZWsy+mY6iL1sHTxWEFCytDA==}
    dependencies:
      balanced-match: 1.0.2
    dev: false

  /braces/2.3.2:
    resolution: {integrity: sha512-aNdbnj9P8PjdXU4ybaWLK2IF3jc/EoDYbC7AazW6to3TRsfXxscC9UXOB5iDiEQrkyIbWp2SLQda4+QAa7nc3w==}
    engines: {node: '>=0.10.0'}
    dependencies:
      arr-flatten: 1.1.0
      array-unique: 0.3.2
      extend-shallow: 2.0.1
      fill-range: 4.0.0
      isobject: 3.0.1
      repeat-element: 1.1.4
      snapdragon: 0.8.2
      snapdragon-node: 2.1.1
      split-string: 3.1.0
      to-regex: 3.0.2
    transitivePeerDependencies:
      - supports-color
    dev: false

  /braces/2.3.2_supports-color@6.1.0:
    resolution: {integrity: sha512-aNdbnj9P8PjdXU4ybaWLK2IF3jc/EoDYbC7AazW6to3TRsfXxscC9UXOB5iDiEQrkyIbWp2SLQda4+QAa7nc3w==}
    engines: {node: '>=0.10.0'}
    dependencies:
      arr-flatten: 1.1.0
      array-unique: 0.3.2
      extend-shallow: 2.0.1
      fill-range: 4.0.0
      isobject: 3.0.1
      repeat-element: 1.1.4
      snapdragon: 0.8.2_supports-color@6.1.0
      snapdragon-node: 2.1.1
      split-string: 3.1.0
      to-regex: 3.0.2
    transitivePeerDependencies:
      - supports-color
    dev: false

  /braces/3.0.2:
    resolution: {integrity: sha512-b8um+L1RzM3WDSzvhm6gIz1yfTbBt6YTlcEKAvsmqCZZFw46z626lVj9j1yEPW33H5H+lBQpZMP1k8l+78Ha0A==}
    engines: {node: '>=8'}
    dependencies:
      fill-range: 7.0.1

  /brorand/1.1.0:
    resolution: {integrity: sha1-EsJe/kCkXjwyPrhnWgoM5XsiNx8=}
    dev: false

  /browser-process-hrtime/1.0.0:
    resolution: {integrity: sha512-9o5UecI3GhkpM6DrXr69PblIuWxPKk9Y0jHBRhdocZ2y7YECBFCsHm79Pr3OyR2AvjhDkabFJaDJMYRazHgsow==}
    dev: false

  /browser-resolve/1.11.3:
    resolution: {integrity: sha512-exDi1BYWB/6raKHmDTCicQfTkqwN5fioMFV4j8BsfMU4R2DK/QfZfK7kOVkmWCNANf0snkBzqGqAJBao9gZMdQ==}
    dependencies:
      resolve: 1.1.7
    dev: false

  /browserify-aes/1.2.0:
    resolution: {integrity: sha512-+7CHXqGuspUn/Sl5aO7Ea0xWGAtETPXNSAjHo48JfLdPWcMng33Xe4znFvQweqc/uzk5zSOI3H52CYnjCfb5hA==}
    dependencies:
      buffer-xor: 1.0.3
      cipher-base: 1.0.4
      create-hash: 1.2.0
      evp_bytestokey: 1.0.3
      inherits: 2.0.4
      safe-buffer: 5.2.1
    dev: false

  /browserify-cipher/1.0.1:
    resolution: {integrity: sha512-sPhkz0ARKbf4rRQt2hTpAHqn47X3llLkUGn+xEJzLjwY8LRs2p0v7ljvI5EyoRO/mexrNunNECisZs+gw2zz1w==}
    dependencies:
      browserify-aes: 1.2.0
      browserify-des: 1.0.2
      evp_bytestokey: 1.0.3
    dev: false

  /browserify-des/1.0.2:
    resolution: {integrity: sha512-BioO1xf3hFwz4kc6iBhI3ieDFompMhrMlnDFC4/0/vd5MokpuAc3R+LYbwTA9A5Yc9pq9UYPqffKpW2ObuwX5A==}
    dependencies:
      cipher-base: 1.0.4
      des.js: 1.0.1
      inherits: 2.0.4
      safe-buffer: 5.2.1
    dev: false

  /browserify-rsa/4.1.0:
    resolution: {integrity: sha512-AdEER0Hkspgno2aR97SAf6vi0y0k8NuOpGnVH3O99rcA5Q6sh8QxcngtHuJ6uXwnfAXNM4Gn1Gb7/MV1+Ymbog==}
    dependencies:
      bn.js: 5.2.1
      randombytes: 2.1.0
    dev: false

  /browserify-sign/4.2.1:
    resolution: {integrity: sha512-/vrA5fguVAKKAVTNJjgSm1tRQDHUU6DbwO9IROu/0WAzC8PKhucDSh18J0RMvVeHAn5puMd+QHC2erPRNf8lmg==}
    dependencies:
      bn.js: 5.2.1
      browserify-rsa: 4.1.0
      create-hash: 1.2.0
      create-hmac: 1.1.7
      elliptic: 6.5.4
      inherits: 2.0.4
      parse-asn1: 5.1.6
      readable-stream: 3.6.0
      safe-buffer: 5.2.1
    dev: false

  /browserify-zlib/0.2.0:
    resolution: {integrity: sha512-Z942RysHXmJrhqk88FmKBVq/v5tqmSkDz7p54G/MGyjMnCFFnC79XWNbg+Vta8W6Wb2qtSZTSxIGkJrRpCFEiA==}
    dependencies:
      pako: 1.0.11
    dev: false

  /browserslist/4.10.0:
    resolution: {integrity: sha512-TpfK0TDgv71dzuTsEAlQiHeWQ/tiPqgNZVdv046fvNtBZrjbv2O3TsWCDU0AWGJJKCF/KsjNdLzR9hXOsh/CfA==}
    hasBin: true
    dependencies:
      caniuse-lite: 1.0.30001423
      electron-to-chromium: 1.4.284
      node-releases: 1.1.77
      pkg-up: 3.1.0
    dev: false

  /browserslist/4.21.4:
    resolution: {integrity: sha512-CBHJJdDmgjl3daYjN5Cp5kbTf1mUhZoS+beLklHIvkOWscs83YAhLlF3Wsh/lciQYAcbBJgTOD44VtG31ZM4Hw==}
    engines: {node: ^6 || ^7 || ^8 || ^9 || ^10 || ^11 || ^12 || >=13.7}
    hasBin: true
    dependencies:
      caniuse-lite: 1.0.30001423
      electron-to-chromium: 1.4.284
      node-releases: 2.0.6
      update-browserslist-db: 1.0.10_browserslist@4.21.4

  /bser/2.1.1:
    resolution: {integrity: sha512-gQxTNE/GAfIIrmHLUE3oJyp5FO6HRBfhjnw4/wMmA63ZGDJnWBmgY/lyQBpnDUkGmAhbSe39tx2d/iTOAfglwQ==}
    dependencies:
      node-int64: 0.4.0
    dev: false

  /buffer-crc32/0.2.13:
    resolution: {integrity: sha1-DTM+PwDqxQqhRUq9MO+MKl2ackI=}
    dev: false

  /buffer-from/1.1.2:
    resolution: {integrity: sha512-E+XQCRwSbaaiChtv6k6Dwgc+bx+Bs6vuKJHHl5kox/BaKbhiXzqQOwK4cO22yElGp2OCmjwVhT3HmxgyPGnJfQ==}
    dev: false

  /buffer-indexof/1.1.1:
    resolution: {integrity: sha512-4/rOEg86jivtPTeOUUT61jJO1Ya1TrR/OkqCSZDyq84WJh3LuuiphBYJN+fm5xufIk4XAFcEwte/8WzC8If/1g==}
    dev: false

  /buffer-xor/1.0.3:
    resolution: {integrity: sha1-JuYe0UIvtw3ULm42cp7VHYVf6Nk=}
    dev: false

  /buffer/4.9.2:
    resolution: {integrity: sha512-xq+q3SRMOxGivLhBNaUdC64hDTQwejJ+H0T/NB1XMtTVEwNTrfFF3gAxiyW0Bu/xWEGhjVKgUcMhCrUy2+uCWg==}
    dependencies:
      base64-js: 1.5.1
      ieee754: 1.2.1
      isarray: 1.0.0
    dev: false

  /buffer/5.7.1:
    resolution: {integrity: sha512-EHcyIPBQ4BSGlvjB16k5KgAJ27CIsHY/2JBmCRReo48y9rQ3MaUzWX3KVlBa4U7MyX02HdVj0K7C3WaB3ju7FQ==}
    dependencies:
      base64-js: 1.5.1
      ieee754: 1.2.1
    dev: false

  /builtin-status-codes/3.0.0:
    resolution: {integrity: sha1-hZgoeOIbmOHGZCXgPQF0eI9Wnug=}
    dev: false

  /bytes/3.0.0:
    resolution: {integrity: sha1-0ygVQE1olpn4Wk6k+odV3ROpYEg=}
    engines: {node: '>= 0.8'}
    dev: false

  /bytes/3.1.2:
    resolution: {integrity: sha512-/Nf7TyzTx6S3yRJObOAV7956r8cr2+Oj8AC5dt8wSP3BQAoeX58NoHyCU8P8zGkNXStjTSi6fzO6F0pBdcYbEg==}
    engines: {node: '>= 0.8'}
    dev: false

  /cacache/12.0.4:
    resolution: {integrity: sha512-a0tMB40oefvuInr4Cwb3GerbL9xTj1D5yg0T5xrjGCGyfvbxseIXX7BAO/u/hIXdafzOI5JC3wDwHyf24buOAQ==}
    dependencies:
      bluebird: 3.7.2
      chownr: 1.1.4
      figgy-pudding: 3.5.2
      glob: 7.2.3
      graceful-fs: 4.2.10
      infer-owner: 1.0.4
      lru-cache: 5.1.1
      mississippi: 3.0.0
      mkdirp: 0.5.6
      move-concurrently: 1.0.1
      promise-inflight: 1.0.1_bluebird@3.7.2
      rimraf: 2.7.1
      ssri: 6.0.2
      unique-filename: 1.1.1
      y18n: 4.0.3
    dev: false

  /cacache/13.0.1:
    resolution: {integrity: sha512-5ZvAxd05HDDU+y9BVvcqYu2LLXmPnQ0hW62h32g4xBTgL/MppR4/04NHfj/ycM2y6lmTnbw6HVi+1eN0Psba6w==}
    engines: {node: '>= 8'}
    dependencies:
      chownr: 1.1.4
      figgy-pudding: 3.5.2
      fs-minipass: 2.1.0
      glob: 7.2.3
      graceful-fs: 4.2.10
      infer-owner: 1.0.4
      lru-cache: 5.1.1
      minipass: 3.3.4
      minipass-collect: 1.0.2
      minipass-flush: 1.0.5
      minipass-pipeline: 1.2.4
      mkdirp: 0.5.6
      move-concurrently: 1.0.1
      p-map: 3.0.0
      promise-inflight: 1.0.1_bluebird@3.7.2
      rimraf: 2.7.1
      ssri: 7.1.1
      unique-filename: 1.1.1
    transitivePeerDependencies:
      - bluebird
    dev: false

  /cache-base/1.0.1:
    resolution: {integrity: sha512-AKcdTnFSWATd5/GCPRxr2ChwIJ85CeyrEyjRHlKxQ56d4XJMGym0uAiKn0xbLOGOl3+yRpOTi484dVCEc5AUzQ==}
    engines: {node: '>=0.10.0'}
    dependencies:
      collection-visit: 1.0.0
      component-emitter: 1.3.0
      get-value: 2.0.6
      has-value: 1.0.0
      isobject: 3.0.1
      set-value: 2.0.1
      to-object-path: 0.3.0
      union-value: 1.0.1
      unset-value: 1.0.0
    dev: false

  /call-bind/1.0.2:
    resolution: {integrity: sha512-7O+FbCihrB5WGbFYesctwmTKae6rOiIzmz1icreWJ+0aA7LJfuqhEso2T9ncpcFtzMQtzXf2QGGueWJGTYsqrA==}
    dependencies:
      function-bind: 1.1.1
      get-intrinsic: 1.1.3
    dev: false

  /call-me-maybe/1.0.1:
    resolution: {integrity: sha1-JtII6onje1y95gJQoV8DHBak1ms=}
    dev: false

  /caller-callsite/2.0.0:
    resolution: {integrity: sha1-hH4PzgoiN1CpoCfFSzNzGtMVQTQ=}
    engines: {node: '>=4'}
    dependencies:
      callsites: 2.0.0
    dev: false

  /caller-path/2.0.0:
    resolution: {integrity: sha1-Ro+DBE42mrIBD6xfBs7uFbsssfQ=}
    engines: {node: '>=4'}
    dependencies:
      caller-callsite: 2.0.0
    dev: false

  /callsites/2.0.0:
    resolution: {integrity: sha1-BuuE8A7qQT2oav/vrL/7Ngk7PFA=}
    engines: {node: '>=4'}
    dev: false

  /callsites/3.1.0:
    resolution: {integrity: sha512-P8BjAsXvZS+VIDUI11hHCQEv74YT67YUi5JJFNWIqL235sBmjX4+qx9Muvls5ivyNENctx46xQLQ3aTuE7ssaQ==}
    engines: {node: '>=6'}

  /camel-case/4.1.2:
    resolution: {integrity: sha512-gxGWBrTT1JuMx6R+o5PTXMmUnhnVzLQ9SNutD4YqKtI6ap897t3tKECYla6gCWEkplXnlNybEkZg9GEGxKFCgw==}
    dependencies:
      pascal-case: 3.1.2
      tslib: 2.4.0
    dev: false

  /camelcase-keys/2.1.0:
    resolution: {integrity: sha1-MIvur/3ygRkFHvodkyITyRuPkuc=}
    engines: {node: '>=0.10.0'}
    dependencies:
      camelcase: 2.1.1
      map-obj: 1.0.1

  /camelcase-keys/6.2.2:
    resolution: {integrity: sha512-YrwaA0vEKazPBkn0ipTiMpSajYDSe+KjQfrjhcBMxJt/znbvlHd8Pw/Vamaz5EB4Wfhs3SUR3Z9mwRu/P3s3Yg==}
    engines: {node: '>=8'}
    dependencies:
      camelcase: 5.3.1
      map-obj: 4.3.0
      quick-lru: 4.0.1
    dev: true

  /camelcase/2.1.1:
    resolution: {integrity: sha1-fB0W1nmhu+WcoCys7PsBHiAfWh8=}
    engines: {node: '>=0.10.0'}

  /camelcase/5.0.0:
    resolution: {integrity: sha512-faqwZqnWxbxn+F1d399ygeamQNy3lPp/H9H6rNrqYh4FSVCtcY+3cub1MxA8o9mDd55mM8Aghuu/kuyYA6VTsA==}
    engines: {node: '>=6'}
    dev: false

  /camelcase/5.3.1:
    resolution: {integrity: sha512-L28STB170nwWS63UjtlEOE3dldQApaJXZkOI1uMFfzf3rRuPegHaHesyee+YxQ+W6SvRDQV6UrdOdRiR153wJg==}
    engines: {node: '>=6'}

  /caniuse-api/3.0.0:
    resolution: {integrity: sha512-bsTwuIg/BZZK/vreVTYYbSWoe2F+71P7K5QGEX+pT250DZbfU1MQ5prOKpPR+LL6uWKK3KMwMCAS74QB3Um1uw==}
    dependencies:
      browserslist: 4.21.4
      caniuse-lite: 1.0.30001423
      lodash.memoize: 4.1.2
      lodash.uniq: 4.5.0
    dev: false

  /caniuse-lite/1.0.30001423:
    resolution: {integrity: sha512-09iwWGOlifvE1XuHokFMP7eR38a0JnajoyL3/i87c8ZjRWRrdKo1fqjNfugfBD0UDBIOz0U+jtNhJ0EPm1VleQ==}

  /capture-exit/2.0.0:
    resolution: {integrity: sha512-PiT/hQmTonHhl/HFGN+Lx3JJUznrVYJ3+AQsnthneZbvW7x+f08Tk7yLJTLEOUvBTbduLeeBkxEaYXUOUrRq6g==}
    engines: {node: 6.* || 8.* || >= 10.*}
    dependencies:
      rsvp: 4.8.5
    dev: false

  /case-sensitive-paths-webpack-plugin/2.3.0:
    resolution: {integrity: sha512-/4YgnZS8y1UXXmC02xD5rRrBEu6T5ub+mQHLNRj0fzTRbgdBYhsNo2V5EqwgqrExjxsjtF/OpAKAMkKsxbD5XQ==}
    engines: {node: '>=4'}
    dev: false

  /caseless/0.12.0:
    resolution: {integrity: sha1-G2gcIf+EAzyCZUMJBolCDRhxUdw=}

  /chalk/1.1.3:
    resolution: {integrity: sha1-qBFcVeSnAv5NFQq9OHKCKn4J/Jg=}
    engines: {node: '>=0.10.0'}
    dependencies:
      ansi-styles: 2.2.1
      escape-string-regexp: 1.0.5
      has-ansi: 2.0.0
      strip-ansi: 3.0.1
      supports-color: 2.0.0

  /chalk/2.4.2:
    resolution: {integrity: sha512-Mti+f9lpJNcwF4tWV8/OrTTtF1gZi+f8FqlyAdouralcFWFQWF2+NgCHShjkCb+IFBLq9buZwE1xckQU4peSuQ==}
    engines: {node: '>=4'}
    dependencies:
      ansi-styles: 3.2.1
      escape-string-regexp: 1.0.5
      supports-color: 5.5.0

  /chalk/3.0.0:
    resolution: {integrity: sha512-4D3B6Wf41KOYRFdszmDqMCGq5VV/uMAB273JILmO+3jAlh8X4qDtdtgCR3fxtbLEMzSx22QdhnDcJvu2u1fVwg==}
    engines: {node: '>=8'}
    dependencies:
      ansi-styles: 4.3.0
      supports-color: 7.2.0
    dev: true

  /chalk/4.1.2:
    resolution: {integrity: sha512-oKnbhFyRIXpUuez8iBMmyEa4nbj4IOQyuhc/wy9kY7/WVPcwIO9VA668Pu8RkO7+0G76SLROeyw9CpQ061i4mA==}
    engines: {node: '>=10'}
    dependencies:
      ansi-styles: 4.3.0
      supports-color: 7.2.0

  /character-entities-legacy/1.1.4:
    resolution: {integrity: sha512-3Xnr+7ZFS1uxeiUDvV02wQ+QDbc55o97tIV5zHScSPJpcLm/r0DFPcoY3tYRp+VZukxuMeKgXYmsXQHO05zQeA==}
    dev: true

  /character-entities/1.2.4:
    resolution: {integrity: sha512-iBMyeEHxfVnIakwOuDXpVkc54HijNgCyQB2w0VfGQThle6NXn50zU6V/u+LDhxHcDUPojn6Kpga3PTAD8W1bQw==}
    dev: true

  /character-reference-invalid/1.1.4:
    resolution: {integrity: sha512-mKKUkUbhPpQlCOfIuZkvSEgktjPFIsZKRRbC6KWVEMvlzblj3i3asQv5ODsrwt0N3pHAEvjP8KTQPHkp0+6jOg==}
    dev: true

  /chardet/0.7.0:
    resolution: {integrity: sha512-mT8iDcrh03qDGRRmoA2hmBJnxpllMR+0/0qlzjqZES6NdiWDcZkCNAk4rPFZ9Q85r27unkiNNg8ZOiwZXBHwcA==}
    dev: false

  /chokidar/2.1.8:
    resolution: {integrity: sha512-ZmZUazfOzf0Nve7duiCKD23PFSCs4JPoYyccjUFF3aQkQadqBhfzhjkwBH2mNOG9cTBwhamM37EIsIkZw3nRgg==}
    deprecated: Chokidar 2 will break on node v14+. Upgrade to chokidar 3 with 15x less dependencies.
    dependencies:
      anymatch: 2.0.0
      async-each: 1.0.3
      braces: 2.3.2
      glob-parent: 3.1.0
      inherits: 2.0.4
      is-binary-path: 1.0.1
      is-glob: 4.0.3
      normalize-path: 3.0.0
      path-is-absolute: 1.0.1
      readdirp: 2.2.1
      upath: 1.2.0
    optionalDependencies:
      fsevents: 1.2.13
    transitivePeerDependencies:
      - supports-color
    dev: false
    optional: true

  /chokidar/2.1.8_supports-color@6.1.0:
    resolution: {integrity: sha512-ZmZUazfOzf0Nve7duiCKD23PFSCs4JPoYyccjUFF3aQkQadqBhfzhjkwBH2mNOG9cTBwhamM37EIsIkZw3nRgg==}
    deprecated: Chokidar 2 will break on node v14+. Upgrade to chokidar 3 with 15x less dependencies.
    dependencies:
      anymatch: 2.0.0_supports-color@6.1.0
      async-each: 1.0.3
      braces: 2.3.2_supports-color@6.1.0
      glob-parent: 3.1.0
      inherits: 2.0.4
      is-binary-path: 1.0.1
      is-glob: 4.0.3
      normalize-path: 3.0.0
      path-is-absolute: 1.0.1
      readdirp: 2.2.1_supports-color@6.1.0
      upath: 1.2.0
    optionalDependencies:
      fsevents: 1.2.13
    transitivePeerDependencies:
      - supports-color
    dev: false

  /chokidar/3.5.3:
    resolution: {integrity: sha512-Dr3sfKRP6oTcjf2JmUmFJfeVMvXBdegxB0iVQ5eb2V10uFJUCAS8OByZdVAyVb8xXNz3GjjTgj9kLWsZTqE6kw==}
    engines: {node: '>= 8.10.0'}
    dependencies:
      anymatch: 3.1.2
      braces: 3.0.2
      glob-parent: 5.1.2
      is-binary-path: 2.1.0
      is-glob: 4.0.3
      normalize-path: 3.0.0
      readdirp: 3.6.0
    optionalDependencies:
      fsevents: 2.3.2
    dev: false

  /chownr/1.1.4:
    resolution: {integrity: sha512-jJ0bqzaylmJtVnNgzTeSOs8DPavpbYgEr/b0YL8/2GO3xJEhInFmhKMUnEJQjZumK7KXGFhUy89PrsJWlakBVg==}
    dev: false

  /chrome-trace-event/1.0.3:
    resolution: {integrity: sha512-p3KULyQg4S7NIHixdwbGX+nFHkoBiA4YQmyWtjb8XngSKV124nJmRysgAeujbUVb15vh+RvFUfCPqU7rXk+hZg==}
    engines: {node: '>=6.0'}
    dev: false

  /ci-info/2.0.0:
    resolution: {integrity: sha512-5tK7EtrZ0N+OLFMthtqOj4fI2Jeb88C4CAZPu25LDVUgXJ0A3Js4PMGqrn0JU1W0Mh1/Z8wZzYPxqUrXeBboCQ==}
    dev: false

  /cipher-base/1.0.4:
    resolution: {integrity: sha512-Kkht5ye6ZGmwv40uUDZztayT2ThLQGfnj/T71N/XzeZeo3nf8foyW7zGTsPYkEya3m5f3cAypH+qe7YOrM1U2Q==}
    dependencies:
      inherits: 2.0.4
      safe-buffer: 5.2.1
    dev: false

  /class-utils/0.3.6:
    resolution: {integrity: sha512-qOhPa/Fj7s6TY8H8esGu5QNpMMQxz79h+urzrNYN6mn+9BnxlDGf5QZ+XeCDsxSjPqsSR56XOZOJmpeurnLMeg==}
    engines: {node: '>=0.10.0'}
    dependencies:
      arr-union: 3.1.0
      define-property: 0.2.5
      isobject: 3.0.1
      static-extend: 0.1.2
    dev: false

  /classnames/2.3.2:
    resolution: {integrity: sha512-CSbhY4cFEJRe6/GQzIk5qXZ4Jeg5pcsP7b5peFSDpffpe1cqjASH/n9UTjBwOp6XpMSTwQ8Za2K5V02ueA7Tmw==}
    dev: false

  /clean-css/4.2.4:
    resolution: {integrity: sha1-czv0brpOYHxokepXwkqYk1aDEXg=}
    engines: {node: '>= 4.0'}
    dependencies:
      source-map: 0.6.1
    dev: false

  /clean-stack/2.2.0:
    resolution: {integrity: sha512-4diC9HaTE+KRAMWhDhrGOECgWZxoevMc5TlkObMqNSsVU62PYzXZ/SMTjzyGAFF1YusgxGcSWTEXBhp0CPwQ1A==}
    engines: {node: '>=6'}
    dev: false

  /cli-cursor/3.1.0:
    resolution: {integrity: sha512-I/zHAwsKf9FqGoXM4WWRACob9+SNukZTd94DWF57E4toouRulbCxcUh6RKUEOQlYTHJnzkPMySvPNaaSLNfLZw==}
    engines: {node: '>=8'}
    dependencies:
      restore-cursor: 3.1.0
    dev: false

  /cli-width/2.2.1:
    resolution: {integrity: sha512-GRMWDxpOB6Dgk2E5Uo+3eEBvtOOlimMmpbFiKuLFnQzYDavtLFY3K5ona41jgN/WdRZtG7utuVSVTL4HbZHGkw==}
    dev: false

  /cli-width/3.0.0:
    resolution: {integrity: sha512-FxqpkPPwu1HjuN93Omfm4h8uIanXofW0RxVEW3k5RKx+mJJYSthzNhp32Kzxxy3YAEZ/Dc/EWN1vZRY0+kOhbw==}
    engines: {node: '>= 10'}
    dev: false

  /clipboard/2.0.11:
    resolution: {integrity: sha512-C+0bbOqkezLIsmWSvlsXS0Q0bmkugu7jcfMIACB+RDEntIzQIkdr148we28AfSloQLRdZlYL/QYyrq05j/3Faw==}
    dependencies:
      good-listener: 1.2.2
      select: 1.1.2
      tiny-emitter: 2.1.0
    dev: false

  /cliui/4.1.0:
    resolution: {integrity: sha512-4FG+RSG9DL7uEwRUZXZn3SS34DiDPfzP0VOiEwtUWlE+AR2EIg+hSyvrIgUUfhdgR/UkAeW2QHgeP+hWrXs7jQ==}
    dependencies:
      string-width: 2.1.1
      strip-ansi: 4.0.0
      wrap-ansi: 2.1.0
    dev: false

  /cliui/5.0.0:
    resolution: {integrity: sha512-PYeGSEmmHM6zvoef2w8TPzlrnNpXIjTipYK780YswmIP9vjxmd6Y2a3CB2Ks6/AU8NHjZugXvo8w3oWM2qnwXA==}
    dependencies:
      string-width: 3.1.0
      strip-ansi: 5.2.0
      wrap-ansi: 5.1.0

  /cliui/7.0.4:
    resolution: {integrity: sha512-OcRE68cOsVMXp1Yvonl/fzkQOyjLSu/8bhPDfQt0e0/Eb283TKP20Fs2MqoPsr9SwA595rRCA+QMzYc9nBP+JQ==}
    dependencies:
      string-width: 4.2.3
      strip-ansi: 6.0.1
      wrap-ansi: 7.0.0
    dev: false

  /clone-deep/0.2.4:
    resolution: {integrity: sha1-TnPdCen7lxzDhnDF3O2cGJZIHMY=}
    engines: {node: '>=0.10.0'}
    dependencies:
      for-own: 0.1.5
      is-plain-object: 2.0.4
      kind-of: 3.2.2
      lazy-cache: 1.0.4
      shallow-clone: 0.1.2
    dev: false

  /clone-deep/4.0.1:
    resolution: {integrity: sha512-neHB9xuzh/wk0dIHweyAXv2aPGZIVk3pLMe+/RNzINf17fe0OG96QroktYAUm7SM1PBnzTabaLboqqxDyMU+SQ==}
    engines: {node: '>=6'}
    dependencies:
      is-plain-object: 2.0.4
      kind-of: 6.0.3
      shallow-clone: 3.0.1
    dev: false

  /clone-regexp/2.2.0:
    resolution: {integrity: sha512-beMpP7BOtTipFuW8hrJvREQ2DrRu3BE7by0ZpibtfBA+qfHYvMGTc2Yb1JMYPKg/JUw0CHYvpg796aNTSW9z7Q==}
    engines: {node: '>=6'}
    dependencies:
      is-regexp: 2.1.0
    dev: true

  /co/4.6.0:
    resolution: {integrity: sha1-bqa989hTrlTMuOR7+gvz+QMfsYQ=}
    engines: {iojs: '>= 1.0.0', node: '>= 0.12.0'}
    dev: false

  /coa/2.0.2:
    resolution: {integrity: sha512-q5/jG+YQnSy4nRTV4F7lPepBJZ8qBNJJDBuJdoejDyLXgmL7IEo+Le2JDZudFTFt7mrCqIRaSjws4ygRCTCAXA==}
    engines: {node: '>= 4.0'}
    dependencies:
      '@types/q': 1.5.5
      chalk: 2.4.2
      q: 1.5.1
    dev: false

  /code-point-at/1.1.0:
    resolution: {integrity: sha1-DQcLTQQ6W+ozovGkDi7bPZpMz3c=}
    engines: {node: '>=0.10.0'}

  /collection-visit/1.0.0:
    resolution: {integrity: sha1-S8A3PBZLwykbTTaMgpzxqApZ3KA=}
    engines: {node: '>=0.10.0'}
    dependencies:
      map-visit: 1.0.0
      object-visit: 1.0.1
    dev: false

  /color-convert/1.9.3:
    resolution: {integrity: sha512-QfAUtd+vFdAtFQcC8CCyYt1fYWxSqAiK2cSD6zDB8N3cpsEBAvRxp9zOGg6G/SHHJYAT88/az/IuDGALsNVbGg==}
    dependencies:
      color-name: 1.1.3

  /color-convert/2.0.1:
    resolution: {integrity: sha512-RRECPsj7iu/xb5oKYcsFHSppFNnsj/52OVTRKb4zP5onXwVF3zVmmToNcOfGC+CRDpfK/U584fMg38ZHCaElKQ==}
    engines: {node: '>=7.0.0'}
    dependencies:
      color-name: 1.1.4

  /color-name/1.1.3:
    resolution: {integrity: sha1-p9BVi9icQveV3UIyj3QIMcpTvCU=}

  /color-name/1.1.4:
    resolution: {integrity: sha512-dOy+3AuW3a2wNbZHIuMZpTcgjGuLU/uBL/ubcZF9OXbDo8ff4O8yVp5Bf0efS8uEoYo5q4Fx7dY9OgQGXgAsQA==}

  /color-string/1.9.1:
    resolution: {integrity: sha512-shrVawQFojnZv6xM40anx4CkoDP+fZsw/ZerEMsW/pyzsRbElpsL/DBVW7q3ExxwusdNXI3lXpuhEZkzs8p5Eg==}
    dependencies:
      color-name: 1.1.4
      simple-swizzle: 0.2.2
    dev: false

  /color/3.2.1:
    resolution: {integrity: sha512-aBl7dZI9ENN6fUGC7mWpMTPNHmWUSNan9tuWN6ahh5ZLNk9baLJOnSMlrQkHcrfFgz2/RigjUVAjdx36VcemKA==}
    dependencies:
      color-convert: 1.9.3
      color-string: 1.9.1
    dev: false

  /combined-stream/1.0.8:
    resolution: {integrity: sha512-FQN4MRfuJeHf7cBbBMJFXhKSDq+2kAArBlmRBvcvFE5BB1HZKXtSFASDhdlz9zOYwxh8lDdnvmMOe/+5cdoEdg==}
    engines: {node: '>= 0.8'}
    dependencies:
      delayed-stream: 1.0.0

  /commander/2.20.3:
    resolution: {integrity: sha512-GpVkmM8vF2vQUkj2LvZmD35JxeJOLCwJ9cUkugyk2nuhbv3+mJvpLYYt+0+USMxE+oj+ey/lJEnhZw75x/OMcQ==}
    dev: false

  /commander/4.1.1:
    resolution: {integrity: sha512-NOKm8xhkzAjzFx8B2v5OAHT+u5pRQc2UCa2Vq9jYL/31o2wi9mxBA7LIFs3sV5VSC49z6pEhfbMULvShKj26WA==}
    engines: {node: '>= 6'}
    dev: false

  /common-tags/1.8.2:
    resolution: {integrity: sha1-lOuzwHbSYDJ0X9VPrOf2iO9aycY=}
    engines: {node: '>=4.0.0'}
    dev: false

  /commondir/1.0.1:
    resolution: {integrity: sha1-3dgA2gxmEnOTzKWVDqloo6rxJTs=}
    dev: false

  /component-classes/1.2.6:
    resolution: {integrity: sha1-xkI5TDYYpNiwuJGe/Mu9kw5c1pE=}
    dependencies:
      component-indexof: 0.0.3
    dev: false

  /component-emitter/1.3.0:
    resolution: {integrity: sha512-Rd3se6QB+sO1TwqZjscQrurpEPIfO0/yYnSin6Q/rD3mOutHvUrCAhJub3r90uNb+SESBuE0QYoB90YdfatsRg==}
    dev: false

  /component-indexof/0.0.3:
    resolution: {integrity: sha1-EdCRMSI5648yyPJa6csAL/6NPCQ=}
    dev: false

  /compose-function/3.0.3:
    resolution: {integrity: sha1-ntZ18TzFRQHTCVCkhv9qe6OrGF8=}
    dependencies:
      arity-n: 1.0.4
    dev: false

  /compress-commons/4.1.1:
    resolution: {integrity: sha512-QLdDLCKNV2dtoTorqgxngQCMA+gWXkM/Nwu7FpeBhk/RdkzimqC3jueb/FDmaZeXh+uby1jkBqE3xArsLBE5wQ==}
    engines: {node: '>= 10'}
    dependencies:
      buffer-crc32: 0.2.13
      crc32-stream: 4.0.2
      normalize-path: 3.0.0
      readable-stream: 3.6.0
    dev: false

  /compressible/2.0.18:
    resolution: {integrity: sha512-AF3r7P5dWxL8MxyITRMlORQNaOA2IkAFaTr4k7BUumjPtRpGDTZpl0Pb1XCO6JeDCBdp126Cgs9sMxqSjgYyRg==}
    engines: {node: '>= 0.6'}
    dependencies:
      mime-db: 1.52.0
    dev: false

  /compression/1.7.4_supports-color@6.1.0:
    resolution: {integrity: sha512-jaSIDzP9pZVS4ZfQ+TzvtiWhdpFhE2RDHz8QJkpX9SIpLq88VueF5jJw6t+6CUQcAoA6t+x89MLrWAqpfDE8iQ==}
    engines: {node: '>= 0.8.0'}
    dependencies:
      accepts: 1.3.8
      bytes: 3.0.0
      compressible: 2.0.18
      debug: 2.6.9_supports-color@6.1.0
      on-headers: 1.0.2
      safe-buffer: 5.1.2
      vary: 1.1.2
    transitivePeerDependencies:
      - supports-color
    dev: false

  /concat-map/0.0.1:
    resolution: {integrity: sha1-2Klr13/Wjfd5OnMDajug1UBdR3s=}

  /concat-stream/1.6.2:
    resolution: {integrity: sha512-27HBghJxjiZtIk3Ycvn/4kbJk/1uZuJFfuPEns6LaEvpvG1f0hTea8lilrouyo9mVc2GWdcEZ8OLoGmSADlrCw==}
    engines: {'0': node >= 0.8}
    dependencies:
      buffer-from: 1.1.2
      inherits: 2.0.4
      readable-stream: 2.3.7
      typedarray: 0.0.6
    dev: false

  /confusing-browser-globals/1.0.11:
    resolution: {integrity: sha512-JsPKdmh8ZkmnHxDk55FZ1TqVLvEQTvoByJZRN9jzI0UjxK/QgAmsphz7PGtqgPieQZ/CQcHWXCR7ATDNhGe+YA==}
    dev: false

  /connect-history-api-fallback/1.6.0:
    resolution: {integrity: sha512-e54B99q/OUoH64zYYRf3HBP5z24G38h5D3qXu23JGRoigpX5Ss4r9ZnDk3g0Z8uQC2x2lPaJ+UlWBc1ZWBWdLg==}
    engines: {node: '>=0.8'}
    dev: false

  /console-browserify/1.2.0:
    resolution: {integrity: sha512-ZMkYO/LkF17QvCPqM0gxw8yUzigAOZOSWSHg91FH6orS7vcEj5dVZTidN2fQ14yBSdg97RqhSNwLUXInd52OTA==}
    dev: false

  /console-control-strings/1.1.0:
    resolution: {integrity: sha1-PXz0Rk22RG6mRL9LOVB/mFEAjo4=}

  /constants-browserify/1.0.0:
    resolution: {integrity: sha1-wguW2MYXdIqvHBYCF2DNJ/y4y3U=}
    dev: false

  /contains-path/0.1.0:
    resolution: {integrity: sha1-/ozxhP9mcLa67wGp1IYaXL7EEgo=}
    engines: {node: '>=0.10.0'}
    dev: false

  /content-disposition/0.5.4:
    resolution: {integrity: sha512-FveZTNuGw04cxlAiWbzi6zTAL/lhehaWbTtgluJh4/E95DqMwTmha3KZN1aAWA8cFIhHzMZUvLevkw5Rqk+tSQ==}
    engines: {node: '>= 0.6'}
    dependencies:
      safe-buffer: 5.2.1
    dev: false

  /content-type/1.0.4:
    resolution: {integrity: sha512-hIP3EEPs8tB9AT1L+NUqtwOAps4mk2Zob89MWXMHjHWg9milF/j4osnnQLXBCBFBk/tvIG/tUc9mOUJiPBhPXA==}
    engines: {node: '>= 0.6'}
    dev: false

  /convert-source-map/0.3.5:
    resolution: {integrity: sha1-8dgClQr33SYxof6+BZZVDIarMZA=}
    dev: false

  /convert-source-map/1.7.0:
    resolution: {integrity: sha512-4FJkXzKXEDB1snCFZlLP4gpC3JILicCpGbzG9f9G7tGqGCzETQ2hWPrcinA9oU4wtf2biUaEH5065UnMeR33oA==}
    dependencies:
      safe-buffer: 5.1.2
    dev: false

  /convert-source-map/1.9.0:
    resolution: {integrity: sha512-ASFBup0Mz1uyiIjANan1jzLQami9z1PoYSZCiiYW2FczPbenXc45FZdBZLzOT+r6+iciuEModtmCti+hjaAk0A==}

  /cookie-signature/1.0.6:
    resolution: {integrity: sha1-4wOogrNCzD7oylE6eZmXNNqzriw=}
    dev: false

  /cookie/0.5.0:
    resolution: {integrity: sha512-YZ3GUyn/o8gfKJlnlX7g7xq4gyO6OSuhGPKaaGssGB2qgDUS0gPgtTvoyZLTt9Ab6dC4hfc9dV5arkvc/OCmrw==}
    engines: {node: '>= 0.6'}
    dev: false

  /copy-concurrently/1.0.5:
    resolution: {integrity: sha512-f2domd9fsVDFtaFcbaRZuYXwtdmnzqbADSwhSWYxYB/Q8zsdUUFMXVRwXGDMWmbEzAn1kdRrtI1T/KTFOL4X2A==}
    dependencies:
      aproba: 1.2.0
      fs-write-stream-atomic: 1.0.10
      iferr: 0.1.5
      mkdirp: 0.5.6
      rimraf: 2.7.1
      run-queue: 1.0.3
    dev: false

  /copy-descriptor/0.1.1:
    resolution: {integrity: sha1-Z29us8OZl8LuGsOpJP1hJHSPV40=}
    engines: {node: '>=0.10.0'}
    dev: false

  /copy-to-clipboard/3.3.2:
    resolution: {integrity: sha512-Vme1Z6RUDzrb6xAI7EZlVZ5uvOk2F//GaxKUxajDqm9LhOVM1inxNAD2vy+UZDYsd0uyA9s7b3/FVZPSxqrCfg==}
    dependencies:
      toggle-selection: 1.0.6
    dev: false

  /core-js-compat/3.25.5:
    resolution: {integrity: sha512-ovcyhs2DEBUIE0MGEKHP4olCUW/XYte3Vroyxuh38rD1wAO4dHohsovUC4eAOuzFxE6b+RXvBU3UZ9o0YhUTkA==}
    dependencies:
      browserslist: 4.21.4
    dev: false

  /core-js-pure/3.25.5:
    resolution: {integrity: sha512-oml3M22pHM+igfWHDfdLVq2ShWmjM2V4L+dQEBs0DWVIqEm9WHCwGAlZ6BmyBQGy5sFrJmcx+856D9lVKyGWYg==}
    requiresBuild: true
    dev: true

  /core-js/1.2.7:
    resolution: {integrity: sha1-ZSKUwUZR2yj6k70tX/KYOk8IxjY=}
    deprecated: core-js@<3 is no longer maintained and not recommended for usage due to the number of issues. Please, upgrade your dependencies to the actual version of core-js@3.
    dev: false

  /core-js/2.6.12:
    resolution: {integrity: sha512-Kb2wC0fvsWfQrgk8HU5lW6U/Lcs8+9aaYcy4ZFc6DDlo4nZ7n70dEgE5rtR0oG6ufKDUnrwfWL1mXR5ljDatrQ==}
    deprecated: core-js@<3 is no longer maintained and not recommended for usage due to the number of issues. Please, upgrade your dependencies to the actual version of core-js@3.
    requiresBuild: true
    dev: false

  /core-js/3.25.5:
    resolution: {integrity: sha512-nbm6eZSjm+ZuBQxCUPQKQCoUEfFOXjUZ8dTTyikyKaWrTYmAVbykQfwsKE5dBK88u3QCkCrzsx/PPlKfhsvgpw==}
    requiresBuild: true
    dev: false

  /core-util-is/1.0.2:
    resolution: {integrity: sha1-tf1UIgqivFq1eqtxQMlAdUUDwac=}

  /core-util-is/1.0.3:
    resolution: {integrity: sha512-ZQBvi1DcpJ4GDqanjucZ2Hj3wEO5pZDS89BWbkcrvdxksJorwUDDZamX9ldFkp9aw2lmBDLgkObEA4DWNJ9FYQ==}

  /cosmiconfig/5.2.1:
    resolution: {integrity: sha512-H65gsXo1SKjf8zmrJ67eJk8aIRKV5ff2D4uKZIBZShbhGSpEmsQOPW/SKMKYhSTrqR7ufy6RP69rPogdaPh/kA==}
    engines: {node: '>=4'}
    dependencies:
      import-fresh: 2.0.0
      is-directory: 0.3.1
      js-yaml: 3.14.1
      parse-json: 4.0.0
    dev: false

  /cosmiconfig/6.0.0:
    resolution: {integrity: sha512-xb3ZL6+L8b9JLLCx3ZdoZy4+2ECphCMo2PwqgP1tlfVq6M6YReyzBJtvWWtbDSpNr9hn96pkCiZqUcFEc+54Qg==}
    engines: {node: '>=8'}
    dependencies:
      '@types/parse-json': 4.0.0
      import-fresh: 3.3.0
      parse-json: 5.2.0
      path-type: 4.0.0
      yaml: 1.10.2
    dev: false

  /cosmiconfig/7.0.1:
    resolution: {integrity: sha512-a1YWNUV2HwGimB7dU2s1wUMurNKjpx60HxBB6xUM8Re+2s1g1IIfJvFR0/iCF+XHdE0GMTKTuLR32UQff4TEyQ==}
    engines: {node: '>=10'}
    dependencies:
      '@types/parse-json': 4.0.0
      import-fresh: 3.3.0
      parse-json: 5.2.0
      path-type: 4.0.0
      yaml: 1.10.2
    dev: true

  /crc-32/1.2.2:
    resolution: {integrity: sha512-ROmzCKrTnOwybPcJApAA6WBWij23HVfGVNKqqrZpuyZOHqK2CwHSvpGuyt/UNNvaIjEd8X5IFGp4Mh+Ie1IHJQ==}
    engines: {node: '>=0.8'}
    hasBin: true
    dev: false

  /crc32-stream/4.0.2:
    resolution: {integrity: sha512-DxFZ/Hk473b/muq1VJ///PMNLj0ZMnzye9thBpmjpJKCc5eMgB95aK8zCGrGfQ90cWo561Te6HK9D+j4KPdM6w==}
    engines: {node: '>= 10'}
    dependencies:
      crc-32: 1.2.2
      readable-stream: 3.6.0
    dev: false

  /create-ecdh/4.0.4:
    resolution: {integrity: sha512-mf+TCx8wWc9VpuxfP2ht0iSISLZnt0JgWlrOKZiNqyUZWnjIaCIVNQArMHnCZKfEYRg6IM7A+NeJoN8gf/Ws0A==}
    dependencies:
      bn.js: 4.12.0
      elliptic: 6.5.4
    dev: false

  /create-hash/1.2.0:
    resolution: {integrity: sha512-z00bCGNHDG8mHAkP7CtT1qVu+bFQUPjYq/4Iv3C3kWjTFV10zIjfSoeqXo9Asws8gwSHDGj/hl2u4OGIjapeCg==}
    dependencies:
      cipher-base: 1.0.4
      inherits: 2.0.4
      md5.js: 1.3.5
      ripemd160: 2.0.2
      sha.js: 2.4.11
    dev: false

  /create-hmac/1.1.7:
    resolution: {integrity: sha512-MJG9liiZ+ogc4TzUwuvbER1JRdgvUFSB5+VR/g5h82fGaIRWMWddtKBHi7/sVhfjQZ6SehlyhvQYrcYkaUIpLg==}
    dependencies:
      cipher-base: 1.0.4
      create-hash: 1.2.0
      inherits: 2.0.4
      ripemd160: 2.0.2
      safe-buffer: 5.2.1
      sha.js: 2.4.11
    dev: false

  /create-react-class/15.7.0:
    resolution: {integrity: sha512-QZv4sFWG9S5RUvkTYWbflxeZX+JG7Cz0Tn33rQBJ+WFQTqTfUTjMjiv9tnfXazjsO5r0KhPs+AqCjyrQX6h2ng==}
    dependencies:
      loose-envify: 1.4.0
      object-assign: 4.1.1
    dev: false

  /cross-env/7.0.3:
    resolution: {integrity: sha512-+/HKd6EgcQCJGh2PSjZuUitQBQynKor4wrFbRg4DtAgS1aWO+gU52xpH7M9ScGgXSYmAVS9bIJ8EzuaGw0oNAw==}
    engines: {node: '>=10.14', npm: '>=6', yarn: '>=1'}
    hasBin: true
    dependencies:
      cross-spawn: 7.0.3
    dev: false

  /cross-spawn/3.0.1:
    resolution: {integrity: sha1-ElYDfsufDF9549bvE14wdwGEuYI=}
    dependencies:
      lru-cache: 4.1.5
      which: 1.3.1

  /cross-spawn/6.0.5:
    resolution: {integrity: sha512-eTVLrBSt7fjbDygz805pMnstIs2VTBNkRm0qxZd+M7A5XDdxVRWO5MxGBXZhjY4cqLYLdtrGqRf8mBPmzwSpWQ==}
    engines: {node: '>=4.8'}
    dependencies:
      nice-try: 1.0.5
      path-key: 2.0.1
      semver: 5.7.1
      shebang-command: 1.2.0
      which: 1.3.1
    dev: false

  /cross-spawn/7.0.1:
    resolution: {integrity: sha512-u7v4o84SwFpD32Z8IIcPZ6z1/ie24O6RU3RbtL5Y316l3KuHVPx9ItBgWQ6VlfAFnRnTtMUrsQ9MUUTuEZjogg==}
    engines: {node: '>= 8'}
    dependencies:
      path-key: 3.1.1
      shebang-command: 2.0.0
      which: 2.0.2
    dev: false

  /cross-spawn/7.0.3:
    resolution: {integrity: sha512-iRDPJKUPVEND7dHPO8rkbOnPpyDygcDFtWjpeWNCgy8WP2rXcxXL8TskReQl6OrB2G7+UJrags1q15Fudc7G6w==}
    engines: {node: '>= 8'}
    dependencies:
      path-key: 3.1.1
      shebang-command: 2.0.0
      which: 2.0.2
    dev: false

  /crypto-browserify/3.12.0:
    resolution: {integrity: sha512-fz4spIh+znjO2VjL+IdhEpRJ3YN6sMzITSBijk6FK2UvTqruSQW+/cCZTSNsMiZNvUeq0CqurF+dAbyiGOY6Wg==}
    dependencies:
      browserify-cipher: 1.0.1
      browserify-sign: 4.2.1
      create-ecdh: 4.0.4
      create-hash: 1.2.0
      create-hmac: 1.1.7
      diffie-hellman: 5.0.3
      inherits: 2.0.4
      pbkdf2: 3.1.2
      public-encrypt: 4.0.3
      randombytes: 2.1.0
      randomfill: 1.0.4
    dev: false

  /css-animation/1.6.1:
    resolution: {integrity: sha512-/48+/BaEaHRY6kNQ2OIPzKf9A6g8WjZYjhiNDNuIVbsm5tXCGIAsHDjB4Xu1C4vXJtUWZo26O68OQkDpNBaPog==}
    dependencies:
      babel-runtime: 6.26.0
      component-classes: 1.2.6
    dev: false

  /css-blank-pseudo/0.1.4:
    resolution: {integrity: sha512-LHz35Hr83dnFeipc7oqFDmsjHdljj3TQtxGGiNWSOsTLIAubSm4TEz8qCaKFpk7idaQ1GfWscF4E6mgpBysA1w==}
    engines: {node: '>=6.0.0'}
    hasBin: true
    dependencies:
      postcss: 7.0.39
    dev: false

  /css-color-names/0.0.4:
    resolution: {integrity: sha1-gIrcLnnPhHOAabZGyyDsJ762KeA=}
    dev: false

  /css-declaration-sorter/4.0.1:
    resolution: {integrity: sha512-BcxQSKTSEEQUftYpBVnsH4SF05NTuBokb19/sBt6asXGKZ/6VP7PLG1CBCkFDYOnhXhPh0jMhO6xZ71oYHXHBA==}
    engines: {node: '>4'}
    dependencies:
      postcss: 7.0.39
      timsort: 0.3.0
    dev: false

  /css-has-pseudo/0.10.0:
    resolution: {integrity: sha512-Z8hnfsZu4o/kt+AuFzeGpLVhFOGO9mluyHBaA2bA8aCGTwah5sT3WV/fTHH8UNZUytOIImuGPrl/prlb4oX4qQ==}
    engines: {node: '>=6.0.0'}
    hasBin: true
    dependencies:
      postcss: 7.0.39
      postcss-selector-parser: 5.0.0
    dev: false

  /css-line-break/2.1.0:
    resolution: {integrity: sha512-FHcKFCZcAha3LwfVBhCQbW2nCNbkZXn7KVUJcsT5/P8YmfsVja0FMPJr0B903j/E69HUphKiV9iQArX8SDYA4w==}
    dependencies:
      utrie: 1.0.2
    dev: false

  /css-loader/3.4.2_webpack@4.41.5:
    resolution: {integrity: sha512-jYq4zdZT0oS0Iykt+fqnzVLRIeiPWhka+7BqPn+oSIpWJAHak5tmB/WZrJ2a21JhCeFyNnnlroSl8c+MtVndzA==}
    engines: {node: '>= 8.9.0'}
    peerDependencies:
      webpack: ^4.0.0 || ^5.0.0
    dependencies:
      camelcase: 5.3.1
      cssesc: 3.0.0
      icss-utils: 4.1.1
      loader-utils: 1.4.0
      normalize-path: 3.0.0
      postcss: 7.0.39
      postcss-modules-extract-imports: 2.0.0
      postcss-modules-local-by-default: 3.0.3
      postcss-modules-scope: 2.2.0
      postcss-modules-values: 3.0.0
      postcss-value-parser: 4.2.0
      schema-utils: 2.7.1
      webpack: 4.41.5
    dev: false

  /css-prefers-color-scheme/3.1.1:
    resolution: {integrity: sha512-MTu6+tMs9S3EUqzmqLXEcgNRbNkkD/TGFvowpeoWJn5Vfq7FMgsmRQs9X5NXAURiOBmOxm/lLjsDNXDE6k9bhg==}
    engines: {node: '>=6.0.0'}
    hasBin: true
    dependencies:
      postcss: 7.0.39
    dev: false

  /css-select-base-adapter/0.1.1:
    resolution: {integrity: sha512-jQVeeRG70QI08vSTwf1jHxp74JoZsr2XSgETae8/xC8ovSnL2WF87GTLO86Sbwdt2lK4Umg4HnnwMO4YF3Ce7w==}
    dev: false

  /css-select/2.1.0:
    resolution: {integrity: sha512-Dqk7LQKpwLoH3VovzZnkzegqNSuAziQyNZUcrdDM401iY+R5NkGBXGmtO05/yaXQziALuPogeG0b7UAgjnTJTQ==}
    dependencies:
      boolbase: 1.0.0
      css-what: 3.4.2
      domutils: 1.7.0
      nth-check: 1.0.2
    dev: false

  /css-select/4.3.0:
    resolution: {integrity: sha512-wPpOYtnsVontu2mODhA19JrqWxNsfdatRKd64kmpRbQgh1KtItko5sTnEpPdpSaJszTOhEMlF/RPz28qj4HqhQ==}
    dependencies:
      boolbase: 1.0.0
      css-what: 6.1.0
      domhandler: 4.3.1
      domutils: 2.8.0
      nth-check: 2.1.1
    dev: false

  /css-tree/1.0.0-alpha.37:
    resolution: {integrity: sha512-DMxWJg0rnz7UgxKT0Q1HU/L9BeJI0M6ksor0OgqOnF+aRCDWg/N2641HmVyU9KVIu0OVVWOb2IpC9A+BJRnejg==}
    engines: {node: '>=8.0.0'}
    dependencies:
      mdn-data: 2.0.4
      source-map: 0.6.1
    dev: false

  /css-tree/1.1.3:
    resolution: {integrity: sha512-tRpdppF7TRazZrjJ6v3stzv93qxRcSsFmW6cX0Zm2NVKpxE1WV1HblnghVv9TreireHkqI/VDEsfolRF1p6y7Q==}
    engines: {node: '>=8.0.0'}
    dependencies:
      mdn-data: 2.0.14
      source-map: 0.6.1
    dev: false

  /css-what/3.4.2:
    resolution: {integrity: sha512-ACUm3L0/jiZTqfzRM3Hi9Q8eZqd6IK37mMWPLz9PJxkLWllYeRf+EHUSHYEtFop2Eqytaq1FizFVh7XfBnXCDQ==}
    engines: {node: '>= 6'}
    dev: false

  /css-what/6.1.0:
    resolution: {integrity: sha512-HTUrgRJ7r4dsZKU6GjmpfRK1O76h97Z8MfS1G0FozR+oF2kG6Vfe8JE6zwrkbxigziPHinCJ+gCPjA9EaBDtRw==}
    engines: {node: '>= 6'}
    dev: false

  /css.escape/1.5.1:
    resolution: {integrity: sha1-QuJ9T6BK4y+TGktNQZH6nN3ul8s=}
    dev: true

  /css/2.2.4:
    resolution: {integrity: sha512-oUnjmWpy0niI3x/mPL8dVEI1l7MnG3+HHyRPHf+YFSbK+svOhXpmSOcDURUh2aOCgl2grzrOPt1nHLuCVFULLw==}
    dependencies:
      inherits: 2.0.4
      source-map: 0.6.1
      source-map-resolve: 0.5.3
      urix: 0.1.0

  /cssdb/4.4.0:
    resolution: {integrity: sha512-LsTAR1JPEM9TpGhl/0p3nQecC2LJ0kD8X5YARu1hk/9I1gril5vDtMZyNxcEpxxDj34YNck/ucjuoUd66K03oQ==}
    dev: false

  /cssesc/2.0.0:
    resolution: {integrity: sha512-MsCAG1z9lPdoO/IUMLSBWBSVxVtJ1395VGIQ+Fc2gNdkQ1hNDnQdw3YhA71WJCBW1vdwA0cAnk/DnW6bqoEUYg==}
    engines: {node: '>=4'}
    hasBin: true
    dev: false

  /cssesc/3.0.0:
    resolution: {integrity: sha512-/Tb/JcjK111nNScGob5MNtsntNM1aCNUDipB/TkwZFhyDrrE47SOx/18wF2bbjgc3ZzCSKW1T5nt5EbFoAz/Vg==}
    engines: {node: '>=4'}
    hasBin: true

  /cssnano-preset-default/4.0.8:
    resolution: {integrity: sha512-LdAyHuq+VRyeVREFmuxUZR1TXjQm8QQU/ktoo/x7bz+SdOge1YKc5eMN6pRW7YWBmyq59CqYba1dJ5cUukEjLQ==}
    engines: {node: '>=6.9.0'}
    dependencies:
      css-declaration-sorter: 4.0.1
      cssnano-util-raw-cache: 4.0.1
      postcss: 7.0.39
      postcss-calc: 7.0.5
      postcss-colormin: 4.0.3
      postcss-convert-values: 4.0.1
      postcss-discard-comments: 4.0.2
      postcss-discard-duplicates: 4.0.2
      postcss-discard-empty: 4.0.1
      postcss-discard-overridden: 4.0.1
      postcss-merge-longhand: 4.0.11
      postcss-merge-rules: 4.0.3
      postcss-minify-font-values: 4.0.2
      postcss-minify-gradients: 4.0.2
      postcss-minify-params: 4.0.2
      postcss-minify-selectors: 4.0.2
      postcss-normalize-charset: 4.0.1
      postcss-normalize-display-values: 4.0.2
      postcss-normalize-positions: 4.0.2
      postcss-normalize-repeat-style: 4.0.2
      postcss-normalize-string: 4.0.2
      postcss-normalize-timing-functions: 4.0.2
      postcss-normalize-unicode: 4.0.1
      postcss-normalize-url: 4.0.1
      postcss-normalize-whitespace: 4.0.2
      postcss-ordered-values: 4.1.2
      postcss-reduce-initial: 4.0.3
      postcss-reduce-transforms: 4.0.2
      postcss-svgo: 4.0.3
      postcss-unique-selectors: 4.0.1
    dev: false

  /cssnano-util-get-arguments/4.0.0:
    resolution: {integrity: sha1-7ToIKZ8h11dBsg87gfGU7UnMFQ8=}
    engines: {node: '>=6.9.0'}
    dev: false

  /cssnano-util-get-match/4.0.0:
    resolution: {integrity: sha1-wOTKB/U4a7F+xeUiULT1lhNlFW0=}
    engines: {node: '>=6.9.0'}
    dev: false

  /cssnano-util-raw-cache/4.0.1:
    resolution: {integrity: sha512-qLuYtWK2b2Dy55I8ZX3ky1Z16WYsx544Q0UWViebptpwn/xDBmog2TLg4f+DBMg1rJ6JDWtn96WHbOKDWt1WQA==}
    engines: {node: '>=6.9.0'}
    dependencies:
      postcss: 7.0.39
    dev: false

  /cssnano-util-same-parent/4.0.1:
    resolution: {integrity: sha512-WcKx5OY+KoSIAxBW6UBBRay1U6vkYheCdjyVNDm85zt5K9mHoGOfsOsqIszfAqrQQFIIKgjh2+FDgIj/zsl21Q==}
    engines: {node: '>=6.9.0'}
    dev: false

  /cssnano/4.1.11:
    resolution: {integrity: sha512-6gZm2htn7xIPJOHY824ERgj8cNPgPxyCSnkXc4v7YvNW+TdVfzgngHcEhy/8D11kUWRUMbke+tC+AUcUsnMz2g==}
    engines: {node: '>=6.9.0'}
    dependencies:
      cosmiconfig: 5.2.1
      cssnano-preset-default: 4.0.8
      is-resolvable: 1.1.0
      postcss: 7.0.39
    dev: false

  /csso/4.2.0:
    resolution: {integrity: sha512-wvlcdIbf6pwKEk7vHj8/Bkc0B4ylXZruLvOgs9doS5eOsOpuodOV2zJChSpkp+pRpYQLQMeF04nr3Z68Sta9jA==}
    engines: {node: '>=8.0.0'}
    dependencies:
      css-tree: 1.1.3
    dev: false

  /cssom/0.3.8:
    resolution: {integrity: sha512-b0tGHbfegbhPJpxpiBPU2sCkigAqtM9O121le6bbOlgyV+NyGyCmVfJ6QW9eRjz8CpNfWEOYBIMIGRYkLwsIYg==}
    dev: false

  /cssstyle/1.4.0:
    resolution: {integrity: sha512-GBrLZYZ4X4x6/QEoBnIrqb8B/f5l4+8me2dkom/j1Gtbxy0kBv6OGzKuAsGM75bkGwGAFkt56Iwg28S3XTZgSA==}
    dependencies:
      cssom: 0.3.8
    dev: false

  /csstype/3.1.1:
    resolution: {integrity: sha512-DJR/VvkAvSZW9bTouZue2sSxDwdTN92uHjqeKVm+0dAqdfNykRzQ95tay8aXMBAAPpUiq4Qcug2L7neoRh2Egw==}

  /currently-unhandled/0.4.1:
    resolution: {integrity: sha1-mI3zP+qxke95mmE2nddsF635V+o=}
    engines: {node: '>=0.10.0'}
    dependencies:
      array-find-index: 1.0.2

  /cyclist/1.0.1:
    resolution: {integrity: sha1-WW6WmP0MgOEgOMK4LW6xs1tiJNk=}
    dev: false

  /d/1.0.1:
    resolution: {integrity: sha512-m62ShEObQ39CfralilEQRjH6oAMtNCV1xJyEx5LpRYUVN+EviphDgUc/F3hnYbADmkiNs67Y+3ylmlG7Lnu+FA==}
    dependencies:
      es5-ext: 0.10.62
      type: 1.2.0
    dev: false

  /damerau-levenshtein/1.0.8:
    resolution: {integrity: sha512-sdQSFB7+llfUcQHUQO3+B8ERRj0Oa4w9POWMI/puGtuf7gFywGmkaLCElnudfTiKZV+NvHqL0ifzdrI8Ro7ESA==}
    dev: false

  /dashdash/1.14.1:
    resolution: {integrity: sha1-hTz6D3y+L+1d4gMmuN1YEDX24vA=}
    engines: {node: '>=0.10'}
    dependencies:
      assert-plus: 1.0.0

  /data-urls/1.1.0:
    resolution: {integrity: sha512-YTWYI9se1P55u58gL5GkQHW4P6VJBJ5iBT+B5a7i2Tjadhv52paJG0qHX4A0OR6/t52odI64KP2YvFpkDOi3eQ==}
    dependencies:
      abab: 2.0.6
      whatwg-mimetype: 2.3.0
      whatwg-url: 7.1.0
    dev: false

  /debug/2.6.9:
    resolution: {integrity: sha512-bC7ElrdJaJnPbAP+1EotYvqZsb3ecl5wi6Bfi6BJTUcNowp6cvspg0jXznRTKDjm/E7AdgFBVeAPVMNcKGsHMA==}
    peerDependencies:
      supports-color: '*'
    peerDependenciesMeta:
      supports-color:
        optional: true
    dependencies:
      ms: 2.0.0
    dev: false

  /debug/2.6.9_supports-color@6.1.0:
    resolution: {integrity: sha512-bC7ElrdJaJnPbAP+1EotYvqZsb3ecl5wi6Bfi6BJTUcNowp6cvspg0jXznRTKDjm/E7AdgFBVeAPVMNcKGsHMA==}
    peerDependencies:
      supports-color: '*'
    peerDependenciesMeta:
      supports-color:
        optional: true
    dependencies:
      ms: 2.0.0
      supports-color: 6.1.0
    dev: false

  /debug/3.1.0:
    resolution: {integrity: sha512-OX8XqP7/1a9cqkxYw2yXss15f26NKWBpDXQd0/uK/KPqdQhxbPa994hnzjcE2VqQpDslf55723cKPUOGSmMY3g==}
    peerDependencies:
      supports-color: '*'
    peerDependenciesMeta:
      supports-color:
        optional: true
    dependencies:
      ms: 2.0.0
    dev: false

  /debug/3.2.7:
    resolution: {integrity: sha512-CFjzYYAi4ThfiQvizrFQevTTXHtnCqWfe7x1AhgEscTz6ZbLbfoLRLPugTQyBth6f8ZERVUSyWHFD/7Wu4t1XQ==}
    peerDependencies:
      supports-color: '*'
    peerDependenciesMeta:
      supports-color:
        optional: true
    dependencies:
      ms: 2.1.3
    dev: false

  /debug/3.2.7_supports-color@6.1.0:
    resolution: {integrity: sha512-CFjzYYAi4ThfiQvizrFQevTTXHtnCqWfe7x1AhgEscTz6ZbLbfoLRLPugTQyBth6f8ZERVUSyWHFD/7Wu4t1XQ==}
    peerDependencies:
      supports-color: '*'
    peerDependenciesMeta:
      supports-color:
        optional: true
    dependencies:
      ms: 2.1.3
      supports-color: 6.1.0
    dev: false

  /debug/4.3.4:
    resolution: {integrity: sha512-PRWFHuSU3eDtQJPvnNY7Jcket1j0t5OuOsFzPPzsekD52Zl8qUfFIPEiswXqIvHWGVHOgX+7G/vCNNhehwxfkQ==}
    engines: {node: '>=6.0'}
    peerDependencies:
      supports-color: '*'
    peerDependenciesMeta:
      supports-color:
        optional: true
    dependencies:
      ms: 2.1.2

  /debug/4.3.4_supports-color@6.1.0:
    resolution: {integrity: sha512-PRWFHuSU3eDtQJPvnNY7Jcket1j0t5OuOsFzPPzsekD52Zl8qUfFIPEiswXqIvHWGVHOgX+7G/vCNNhehwxfkQ==}
    engines: {node: '>=6.0'}
    peerDependencies:
      supports-color: '*'
    peerDependenciesMeta:
      supports-color:
        optional: true
    dependencies:
      ms: 2.1.2
      supports-color: 6.1.0
    dev: false

  /decamelize-keys/1.1.0:
    resolution: {integrity: sha1-0XGoeTMlKAfrPLYdwcFEXQeN8tk=}
    engines: {node: '>=0.10.0'}
    dependencies:
      decamelize: 1.2.0
      map-obj: 1.0.1
    dev: true

  /decamelize/1.2.0:
    resolution: {integrity: sha1-9lNNFRSCabIDUue+4m9QH5oZEpA=}
    engines: {node: '>=0.10.0'}

  /decode-uri-component/0.2.0:
    resolution: {integrity: sha1-6zkTMzRYd1y4TNGh+uBiEGu4dUU=}
    engines: {node: '>=0.10'}

  /deep-equal/1.1.1:
    resolution: {integrity: sha512-yd9c5AdiqVcR+JjcwUQb9DkhJc8ngNr0MahEBGvDiJw8puWab2yZlh+nkasOnZP+EGTAP6rRp2JzJhJZzvNF8g==}
    dependencies:
      is-arguments: 1.1.1
      is-date-object: 1.0.5
      is-regex: 1.1.4
      object-is: 1.1.5
      object-keys: 1.1.1
      regexp.prototype.flags: 1.4.3
    dev: false

  /deep-is/0.1.4:
    resolution: {integrity: sha1-pvLc5hL63S7x9Rm3NVHxfoUZmDE=}
    dev: false

  /default-gateway/4.2.0:
    resolution: {integrity: sha512-h6sMrVB1VMWVrW13mSc6ia/DwYYw5MN6+exNu1OaJeFac5aSAvwM7lZ0NVfTABuSkQelr4h5oebg3KB1XPdjgA==}
    engines: {node: '>=6'}
    dependencies:
      execa: 1.0.0
      ip-regex: 2.1.0
    dev: false

  /define-properties/1.1.4:
    resolution: {integrity: sha512-uckOqKcfaVvtBdsVkdPv3XjveQJsNQqmhXgRi8uhvWWuPYZCNlzT8qAyblUgNoXdHdjMTzAqeGjAoli8f+bzPA==}
    engines: {node: '>= 0.4'}
    dependencies:
      has-property-descriptors: 1.0.0
      object-keys: 1.1.1
    dev: false

  /define-property/0.2.5:
    resolution: {integrity: sha1-w1se+RjsPJkPmlvFe+BKrOxcgRY=}
    engines: {node: '>=0.10.0'}
    dependencies:
      is-descriptor: 0.1.6
    dev: false

  /define-property/1.0.0:
    resolution: {integrity: sha1-dp66rz9KY6rTr56NMEybvnm/sOY=}
    engines: {node: '>=0.10.0'}
    dependencies:
      is-descriptor: 1.0.2
    dev: false

  /define-property/2.0.2:
    resolution: {integrity: sha512-jwK2UV4cnPpbcG7+VRARKTZPUWowwXA8bzH5NP6ud0oeAxyYPuGZUAC7hMugpCdz4BeSZl2Dl9k66CHJ/46ZYQ==}
    engines: {node: '>=0.10.0'}
    dependencies:
      is-descriptor: 1.0.2
      isobject: 3.0.1
    dev: false

  /del/4.1.1:
    resolution: {integrity: sha512-QwGuEUouP2kVwQenAsOof5Fv8K9t3D8Ca8NxcXKrIpEHjTXK5J2nXLdP+ALI1cgv8wj7KuwBhTwBkOZSJKM5XQ==}
    engines: {node: '>=6'}
    dependencies:
      '@types/glob': 7.2.0
      globby: 6.1.0
      is-path-cwd: 2.2.0
      is-path-in-cwd: 2.1.0
      p-map: 2.1.0
      pify: 4.0.1
      rimraf: 2.7.1
    dev: false

  /delayed-stream/1.0.0:
    resolution: {integrity: sha1-3zrhmayt+31ECqrgsp4icrJOxhk=}
    engines: {node: '>=0.4.0'}

  /delegate/3.2.0:
    resolution: {integrity: sha512-IofjkYBZaZivn0V8nnsMJGBr4jVLxHDheKSW88PyxS5QC4Vo9ZbZVvhzlSxY87fVq3STR6r+4cGepyHkcWOQSw==}
    dev: false

  /delegates/1.0.0:
    resolution: {integrity: sha1-hMbhWbgZBP3KWaDvRM2HDTElD5o=}

  /depd/1.1.2:
    resolution: {integrity: sha1-m81S4UwJd2PnSbJ0xDRu0uVgtak=}
    engines: {node: '>= 0.6'}
    dev: false

  /depd/2.0.0:
    resolution: {integrity: sha512-g7nH6P6dyDioJogAAGprGpCtVImJhpPk/roCzdb3fIh61/s/nPsfR6onyMwkCAR/OlC3yBC0lESvUoQEAssIrw==}
    engines: {node: '>= 0.8'}
    dev: false

  /des.js/1.0.1:
    resolution: {integrity: sha512-Q0I4pfFrv2VPd34/vfLrFOoRmlYj3OV50i7fskps1jZWK1kApMWWT9G6RRUeYedLcBDIhnSDaUvJMb3AhUlaEA==}
    dependencies:
      inherits: 2.0.4
      minimalistic-assert: 1.0.1
    dev: false

  /destroy/1.2.0:
    resolution: {integrity: sha512-2sJGJTaXIIaR1w4iJSNoN0hnMY7Gpc/n8D4qSCJw8QqFWXf7cuAgnEHxBpweaVcPevC2l3KpjYCx3NypQQgaJg==}
    engines: {node: '>= 0.8', npm: 1.2.8000 || >= 1.4.16}
    dev: false

  /detect-newline/2.1.0:
    resolution: {integrity: sha1-9B8cEL5LAOh7XxPaaAdZ8sW/0+I=}
    engines: {node: '>=0.10.0'}
    dev: false

  /detect-node/2.1.0:
    resolution: {integrity: sha512-T0NIuQpnTvFDATNuHN5roPwSBG83rFsuO+MXXH9/3N1eFbn4wcPjttvjMLEPWJ0RGUYgQE7cGgS3tNxbqCGM7g==}
    dev: false

  /detect-port-alt/1.1.6:
    resolution: {integrity: sha512-5tQykt+LqfJFBEYaDITx7S7cR7mJ/zQmLXZ2qt5w04ainYZw6tBf9dBunMjVeVOdYVRUzUOE4HkY5J7+uttb5Q==}
    engines: {node: '>= 4.2.1'}
    hasBin: true
    dependencies:
      address: 1.1.2
      debug: 2.6.9
    transitivePeerDependencies:
      - supports-color
    dev: false

  /diff-sequences/24.9.0:
    resolution: {integrity: sha512-Dj6Wk3tWyTE+Fo1rW8v0Xhwk80um6yFYKbuAxc9c3EZxIHFDYwbi34Uk42u1CdnIiVorvt4RmlSDjIPyzGC2ew==}
    engines: {node: '>= 6'}

  /diffie-hellman/5.0.3:
    resolution: {integrity: sha512-kqag/Nl+f3GwyK25fhUMYj81BUOrZ9IuJsjIcDE5icNM9FJHAVm3VcUDxdLPoQtTuUylWm6ZIknYJwwaPxsUzg==}
    dependencies:
      bn.js: 4.12.0
      miller-rabin: 4.0.1
      randombytes: 2.1.0
    dev: false

  /dir-glob/2.0.0:
    resolution: {integrity: sha512-37qirFDz8cA5fimp9feo43fSuRo2gHwaIn6dXL8Ber1dGwUosDrGZeCCXq57WnIqE4aQ+u3eQZzsk1yOzhdwag==}
    engines: {node: '>=4'}
    dependencies:
      arrify: 1.0.1
      path-type: 3.0.0
    dev: false

  /dir-glob/3.0.1:
    resolution: {integrity: sha512-WkrWp9GR4KXfKGYzOLmTuGVi1UWFfws377n9cc55/tb6DuqyF6pcQ5AbiHEshaDpY9v6oaSr2XCDidGmMwdzIA==}
    engines: {node: '>=8'}
    dependencies:
      path-type: 4.0.0
    dev: true

  /dns-equal/1.0.0:
    resolution: {integrity: sha1-s55/HabrCnW6nBcySzR1PEfgZU0=}
    dev: false

  /dns-packet/1.3.4:
    resolution: {integrity: sha512-BQ6F4vycLXBvdrJZ6S3gZewt6rcrks9KBgM9vrhW+knGRqc8uEdT7fuCwloc7nny5xNoMJ17HGH0R/6fpo8ECA==}
    dependencies:
      ip: 1.1.8
      safe-buffer: 5.2.1
    dev: false

  /dns-txt/2.0.2:
    resolution: {integrity: sha1-uR2Ab10nGI5Ks+fRB9iBocxGQrY=}
    dependencies:
      buffer-indexof: 1.1.1
    dev: false

  /doctrine/1.5.0:
    resolution: {integrity: sha1-N53Ocw9hZvds76TmcHoVmwLFpvo=}
    engines: {node: '>=0.10.0'}
    dependencies:
      esutils: 2.0.3
      isarray: 1.0.0
    dev: false

  /doctrine/2.1.0:
    resolution: {integrity: sha512-35mSku4ZXK0vfCuHEDAwt55dg2jNajHZ1odvF+8SSr82EsZY4QmXfuWso8oEd8zRhVObSN18aM0CjSdoBX7zIw==}
    engines: {node: '>=0.10.0'}
    dependencies:
      esutils: 2.0.3
    dev: false

  /doctrine/3.0.0:
    resolution: {integrity: sha512-yS+Q5i3hBf7GBkd4KG8a7eBNNWNGLTaEwwYWUijIYM7zrlYDM0BFXHjjPWlWZ1Rg7UaddZeIDmi9jF3HmqiQ2w==}
    engines: {node: '>=6.0.0'}
    dependencies:
      esutils: 2.0.3
    dev: false

  /dom-accessibility-api/0.3.0:
    resolution: {integrity: sha512-PzwHEmsRP3IGY4gv/Ug+rMeaTIyTJvadCb+ujYXYeIylbHJezIyNToe8KfEgHTCEYyC+/bUghYOGg8yMGlZ6vA==}
    dev: true

  /dom-align/1.12.3:
    resolution: {integrity: sha512-Gj9hZN3a07cbR6zviMUBOMPdWxYhbMI+x+WS0NAIu2zFZmbK8ys9R79g+iG9qLnlCwpFoaB+fKy8Pdv470GsPA==}
    dev: false

  /dom-converter/0.2.0:
    resolution: {integrity: sha512-gd3ypIPfOMr9h5jIKq8E3sHOTCjeirnl0WK5ZdS1AW0Odt0b1PaWaHdJ4Qk4klv+YB9aJBS7mESXjFoDQPu6DA==}
    dependencies:
      utila: 0.4.0
    dev: false

  /dom-helpers/5.2.1:
    resolution: {integrity: sha512-nRCa7CK3VTrM2NmGkIy4cbK7IZlgBE/PYMn55rrXefr5xXDP0LdtfPnblFDoVdcAfslJ7or6iqAUnx0CCGIWQA==}
    dependencies:
      '@babel/runtime': 7.19.4
      csstype: 3.1.1
    dev: false

  /dom-serializer/0.2.2:
    resolution: {integrity: sha512-2/xPb3ORsQ42nHYiSunXkDjPLBaEj/xTwUO4B7XCZQTRk7EBtTOPaygh10YAAh2OI1Qrp6NWfpAhzswj0ydt9g==}
    dependencies:
      domelementtype: 2.3.0
      entities: 2.2.0

  /dom-serializer/1.4.1:
    resolution: {integrity: sha512-VHwB3KfrcOOkelEG2ZOfxqLZdfkil8PtJi4P8N2MMXucZq2yLp75ClViUlOVwyoHEDjYU433Aq+5zWP61+RGag==}
    dependencies:
      domelementtype: 2.3.0
      domhandler: 4.3.1
      entities: 2.2.0
    dev: false

  /domain-browser/1.2.0:
    resolution: {integrity: sha512-jnjyiM6eRyZl2H+W8Q/zLMA481hzi0eszAaBUzIVnmYVDBbnLxVNnfu1HgEBvCbL+71FrxMl3E6lpKH7Ge3OXA==}
    engines: {node: '>=0.4', npm: '>=1.2'}
    dev: false

  /domelementtype/1.3.1:
    resolution: {integrity: sha512-BSKB+TSpMpFI/HOxCNr1O8aMOTZ8hT3pM3GQ0w/mWRmkhEDSFJkkyzz4XQsBV44BChwGkrDfMyjVD0eA2aFV3w==}

  /domelementtype/2.3.0:
    resolution: {integrity: sha512-OLETBj6w0OsagBwdXnPdN0cnMfF9opN69co+7ZrbfPGrdpPVNBUj02spi6B1N7wChLQiPn4CSH/zJvXw56gmHw==}

  /domexception/1.0.1:
    resolution: {integrity: sha512-raigMkn7CJNNo6Ihro1fzG7wr3fHuYVytzquZKX5n0yizGsTcYgzdIUwj1X9pK0VvjeihV+XiclP+DjwbsSKug==}
    dependencies:
      webidl-conversions: 4.0.2
    dev: false

  /domhandler/2.4.2:
    resolution: {integrity: sha512-JiK04h0Ht5u/80fdLMCEmV4zkNh2BcoMFBmZ/91WtYZ8qVXSKjiw7fXMgFPnHcSZgOo3XdinHvmnDUeMf5R4wA==}
    dependencies:
      domelementtype: 1.3.1
    dev: true

  /domhandler/4.3.1:
    resolution: {integrity: sha512-GrwoxYN+uWlzO8uhUXRl0P+kHE4GtVPfYzVLcUxPL7KNdHKj66vvlhiweIHqYYXWlw+T8iLMp42Lm67ghw4WMQ==}
    engines: {node: '>= 4'}
    dependencies:
      domelementtype: 2.3.0
    dev: false

  /domutils/1.7.0:
    resolution: {integrity: sha512-Lgd2XcJ/NjEw+7tFvfKxOzCYKZsdct5lczQ2ZaQY8Djz7pfAD3Gbp8ySJWtreII/vDlMVmxwa6pHmdxIYgttDg==}
    dependencies:
      dom-serializer: 0.2.2
      domelementtype: 1.3.1

  /domutils/2.8.0:
    resolution: {integrity: sha512-w96Cjofp72M5IIhpjgobBimYEfoPjx1Vx0BSX9P30WBdZW2WIKU0T1Bd0kz2eNZ9ikjKgHbEyKx8BB6H1L3h3A==}
    dependencies:
      dom-serializer: 1.4.1
      domelementtype: 2.3.0
      domhandler: 4.3.1
    dev: false

  /dot-case/3.0.4:
    resolution: {integrity: sha512-Kv5nKlh6yRrdrGvxeJ2e5y2eRUpkUosIW4A2AS38zwSz27zu7ufDwQPi5Jhs3XAlGNetl3bmnGhQsMtkKJnj3w==}
    dependencies:
      no-case: 3.0.4
      tslib: 2.4.0
    dev: false

  /dot-prop/5.3.0:
    resolution: {integrity: sha512-QM8q3zDe58hqUqjraQOmzZ1LIH9SWQJTlEKCH4kJ2oQvLZk7RbQXvtDM2XEq3fwkV9CCvvH4LA0AV+ogFsBM2Q==}
    engines: {node: '>=8'}
    dependencies:
      is-obj: 2.0.0
    dev: false

  /dotenv-expand/5.1.0:
    resolution: {integrity: sha512-YXQl1DSa4/PQyRfgrv6aoNjhasp/p4qs9FjJ4q4cQk+8m4r6k4ZSiEyytKG8f8W9gi8WsQtIObNmKd+tMzNTmA==}
    dev: false

  /dotenv/8.2.0:
    resolution: {integrity: sha512-8sJ78ElpbDJBHNeBzUbUVLsqKdccaa/BXF1uPTw3GrvQTBgrQrtObr2mUrE38vzYd8cEv+m/JBfDLioYcfXoaw==}
    engines: {node: '>=8'}
    dev: false

  /duplexer/0.1.2:
    resolution: {integrity: sha512-jtD6YG370ZCIi/9GTaJKQxWTZD045+4R4hTk/x1UyoqadyJ9x9CgSi1RlVDQF8U2sxLLSnFkCaMihqljHIWgMg==}
    dev: false

  /duplexify/3.7.1:
    resolution: {integrity: sha512-07z8uv2wMyS51kKhD1KsdXJg5WQ6t93RneqRxUHnskXVtlYYkLqM0gqStQZ3pj073g687jPCHrqNfCzawLYh5g==}
    dependencies:
      end-of-stream: 1.4.4
      inherits: 2.0.4
      readable-stream: 2.3.7
      stream-shift: 1.0.1
    dev: false

  /ecc-jsbn/0.1.2:
    resolution: {integrity: sha1-OoOpBOVDUyh4dMVkt1SThoSamMk=}
    dependencies:
      jsbn: 0.1.1
      safer-buffer: 2.1.2

  /ee-first/1.1.1:
    resolution: {integrity: sha1-WQxhFWsK4vTwJVcyoViyZrxWsh0=}
    dev: false

  /electron-to-chromium/1.4.284:
    resolution: {integrity: sha512-M8WEXFuKXMYMVr45fo8mq0wUrrJHheiKZf6BArTKk9ZBYCKJEOU5H8cdWgDT+qCVZf7Na4lVUaZsA+h6uA9+PA==}

  /elliptic/6.5.4:
    resolution: {integrity: sha512-iLhC6ULemrljPZb+QutR5TQGB+pdW6KGD5RSegS+8sorOZT+rdQFbsQFJgvN3eRqNALqJer4oQ16YvJHlU8hzQ==}
    dependencies:
      bn.js: 4.12.0
      brorand: 1.1.0
      hash.js: 1.1.7
      hmac-drbg: 1.0.1
      inherits: 2.0.4
      minimalistic-assert: 1.0.1
      minimalistic-crypto-utils: 1.0.1
    dev: false

  /emoji-regex/7.0.3:
    resolution: {integrity: sha512-CwBLREIQ7LvYFB0WyRvwhq5N5qPhc6PMjD6bYggFlI5YyDgl+0vxq5VHbMOFqLg7hfWzmu8T5Z1QofhmTIhItA==}

  /emoji-regex/8.0.0:
    resolution: {integrity: sha512-MSjYzcWNOA0ewAHpz0MxpYFvwg6yjy1NG3xteoqz644VCo/RPgnr1/GGt+ic3iJTzQ8Eu3TdM14SawnVUmGE6A==}

  /emojis-list/2.1.0:
    resolution: {integrity: sha1-TapNnbAPmBmIDHn6RXrlsJof04k=}
    engines: {node: '>= 0.10'}
    dev: false

  /emojis-list/3.0.0:
    resolution: {integrity: sha512-/kyM18EfinwXZbno9FyUGeFh87KC8HRQBQGildHZbEuRyWFOmv1U10o9BBp8XVZDVNNuQKyIGIu5ZYAAXJ0V2Q==}
    engines: {node: '>= 4'}
    dev: false

  /encodeurl/1.0.2:
    resolution: {integrity: sha1-rT/0yG7C0CkyL1oCw6mmBslbP1k=}
    engines: {node: '>= 0.8'}
    dev: false

  /encoding/0.1.13:
    resolution: {integrity: sha512-ETBauow1T35Y/WZMkio9jiM0Z5xjHHmJ4XmjZOq1l/dXz3lr2sRn87nJy20RupqSh1F2m3HHPSp8ShIPQJrJ3A==}
    dependencies:
      iconv-lite: 0.6.3
    dev: false

  /end-of-stream/1.4.4:
    resolution: {integrity: sha512-+uw1inIHVPQoaVuHzRyXd21icM+cnt4CzD5rW+NC1wjOUSTOs+Te7FOv7AhN7vS9x/oIyhLP5PR1H+phQAHu5Q==}
    dependencies:
      once: 1.4.0
    dev: false

  /enhanced-resolve/4.5.0:
    resolution: {integrity: sha512-Nv9m36S/vxpsI+Hc4/ZGRs0n9mXqSWGGq49zxb/cJfPAQMbUtttJAlNPS4AQzaBdw/pKskw5bMbekT/Y7W/Wlg==}
    engines: {node: '>=6.9.0'}
    dependencies:
      graceful-fs: 4.2.10
      memory-fs: 0.5.0
      tapable: 1.1.3
    dev: false

  /enquire.js/2.1.6:
    resolution: {integrity: sha1-PoeAybi4NQhMP2DhZtvDwqPImBQ=}
    dev: false

  /entities/1.1.2:
    resolution: {integrity: sha512-f2LZMYl1Fzu7YSBKg+RoROelpOaNrcGmE9AZubeDfrCEia483oW4MI4VyFd5VNHIgQ/7qm1I0wUHK1eJnn2y2w==}
    dev: true

  /entities/2.2.0:
    resolution: {integrity: sha512-p92if5Nz619I0w+akJrLZH0MX0Pb5DX39XOwQTtXSdQQOaYH03S1uIQp4mhOZtAXrxq4ViO67YTiLBo2638o9A==}

  /errno/0.1.8:
    resolution: {integrity: sha512-dJ6oBr5SQ1VSd9qkk7ByRgb/1SH4JZjCHSW/mr63/QcXO9zLVxvJ6Oy13nio03rxpSnVDDjFor75SjVeZWPW/A==}
    hasBin: true
    dependencies:
      prr: 1.0.1
    dev: false

  /error-ex/1.3.2:
    resolution: {integrity: sha512-7dFHNmqeFSEt2ZBsCriorKnn3Z2pj+fd9kmI6QoWw4//DL+icEBfc0U7qJCisqrTsKTjw4fNFy2pW9OqStD84g==}
    dependencies:
      is-arrayish: 0.2.1

  /es-abstract/1.20.4:
    resolution: {integrity: sha512-0UtvRN79eMe2L+UNEF1BwRe364sj/DXhQ/k5FmivgoSdpM90b8Jc0mDzKMGo7QS0BVbOP/bTwBKNnDc9rNzaPA==}
    engines: {node: '>= 0.4'}
    dependencies:
      call-bind: 1.0.2
      es-to-primitive: 1.2.1
      function-bind: 1.1.1
      function.prototype.name: 1.1.5
      get-intrinsic: 1.1.3
      get-symbol-description: 1.0.0
      has: 1.0.3
      has-property-descriptors: 1.0.0
      has-symbols: 1.0.3
      internal-slot: 1.0.3
      is-callable: 1.2.7
      is-negative-zero: 2.0.2
      is-regex: 1.1.4
      is-shared-array-buffer: 1.0.2
      is-string: 1.0.7
      is-weakref: 1.0.2
      object-inspect: 1.12.2
      object-keys: 1.1.1
      object.assign: 4.1.4
      regexp.prototype.flags: 1.4.3
      safe-regex-test: 1.0.0
      string.prototype.trimend: 1.0.5
      string.prototype.trimstart: 1.0.5
      unbox-primitive: 1.0.2
    dev: false

  /es-array-method-boxes-properly/1.0.0:
    resolution: {integrity: sha512-wd6JXUmyHmt8T5a2xreUwKcGPq6f1f+WwIJkijUqiGcJz1qqnZgP6XIK+QyIWU5lT7imeNxUll48bziG+TSYcA==}
    dev: false

  /es-shim-unscopables/1.0.0:
    resolution: {integrity: sha512-Jm6GPcCdC30eMLbZ2x8z2WuRwAws3zTBBKuusffYVUrNj/GVSUAZ+xKMaUpfNDR5IbyNA5LJbaecoUVbmUcB1w==}
    dependencies:
      has: 1.0.3
    dev: false

  /es-to-primitive/1.2.1:
    resolution: {integrity: sha512-QCOllgZJtaUo9miYBcLChTUaHNjJF3PYs1VidD7AwiEj1kYxKeQTctLAezAOH5ZKRH0g2IgPn6KwB4IT8iRpvA==}
    engines: {node: '>= 0.4'}
    dependencies:
      is-callable: 1.2.7
      is-date-object: 1.0.5
      is-symbol: 1.0.4
    dev: false

  /es5-ext/0.10.62:
    resolution: {integrity: sha512-BHLqn0klhEpnOKSrzn/Xsz2UIW8j+cGmo9JLzr8BiUapV8hPL9+FliFqjwr9ngW7jWdnxv6eO+/LqyhJVqgrjA==}
    engines: {node: '>=0.10'}
    requiresBuild: true
    dependencies:
      es6-iterator: 2.0.3
      es6-symbol: 3.1.3
      next-tick: 1.1.0
    dev: false

  /es6-iterator/2.0.3:
    resolution: {integrity: sha1-p96IkUGgWpSwhUQDstCg+/qY87c=}
    dependencies:
      d: 1.0.1
      es5-ext: 0.10.62
      es6-symbol: 3.1.3
    dev: false

  /es6-symbol/3.1.3:
    resolution: {integrity: sha512-NJ6Yn3FuDinBaBRWl/q5X/s4koRHBrgKAu+yGI6JCBeiu3qrcbJhwT2GeR/EXVfylRk8dpQVJoLEFhK+Mu31NA==}
    dependencies:
      d: 1.0.1
      ext: 1.7.0
    dev: false

  /escalade/3.1.1:
    resolution: {integrity: sha512-k0er2gUkLf8O0zKJiAhmkTnJlTvINGv7ygDNPbeIsX/TJjGJZHuh9B2UxbsaEkmlEo9MfhrSzmhIlhRlI2GXnw==}
    engines: {node: '>=6'}

  /escape-html/1.0.3:
    resolution: {integrity: sha1-Aljq5NPQwJdN4cFpGI7wBR0dGYg=}
    dev: false

  /escape-string-regexp/1.0.5:
    resolution: {integrity: sha1-G2HAViGQqN/2rjuyzwIAyhMLhtQ=}
    engines: {node: '>=0.8.0'}

  /escape-string-regexp/2.0.0:
    resolution: {integrity: sha512-UpzcLCXolUWcNu5HtVMHYdXJjArjsF9C0aNnquZYY4uW/Vu0miy5YoWvbV345HauVvcAUnpRuhMMcqTcGOY2+w==}
    engines: {node: '>=8'}
    dev: false

  /escodegen/1.14.3:
    resolution: {integrity: sha512-qFcX0XJkdg+PB3xjZZG/wKSuT1PnQWx57+TVSjIMmILd2yC/6ByYElPwJnslDsuWuSAp4AwJGumarAAmJch5Kw==}
    engines: {node: '>=4.0'}
    hasBin: true
    dependencies:
      esprima: 4.0.1
      estraverse: 4.3.0
      esutils: 2.0.3
      optionator: 0.8.3
    optionalDependencies:
      source-map: 0.6.1
    dev: false

  /eslint-config-react-app/5.2.1_dk4otncx5baoszk7lp2bv4qwoi:
    resolution: {integrity: sha512-pGIZ8t0mFLcV+6ZirRgYK6RVqUIKRIi9MmgzUEmrIknsn3AdO0I32asO86dJgloHq+9ZPl8UIg8mYrvgP5u2wQ==}
    peerDependencies:
      '@typescript-eslint/eslint-plugin': 2.x
      '@typescript-eslint/parser': 2.x
      babel-eslint: 10.x
      eslint: 6.x
      eslint-plugin-flowtype: 3.x || 4.x
      eslint-plugin-import: 2.x
      eslint-plugin-jsx-a11y: 6.x
      eslint-plugin-react: 7.x
      eslint-plugin-react-hooks: 1.x || 2.x
      typescript: '*'
    peerDependenciesMeta:
      typescript:
        optional: true
    dependencies:
      '@typescript-eslint/eslint-plugin': 2.34.0_fmavwhclprfd5wnbs7ocgoy2gu
      '@typescript-eslint/parser': 2.34.0_z6m2zvrkqxyghb4a2ijhravsdi
      babel-eslint: 10.0.3_eslint@6.8.0
      confusing-browser-globals: 1.0.11
      eslint: 6.8.0
      eslint-plugin-flowtype: 4.6.0_eslint@6.8.0
      eslint-plugin-import: 2.20.0_tbglwmj7t2rhd43mvxmptakoay
      eslint-plugin-jsx-a11y: 6.2.3_eslint@6.8.0
      eslint-plugin-react: 7.18.0_eslint@6.8.0
      eslint-plugin-react-hooks: 1.7.0_eslint@6.8.0
      typescript: 3.9.10
    dev: false

  /eslint-import-resolver-node/0.3.6:
    resolution: {integrity: sha512-0En0w03NRVMn9Uiyn8YRPDKvWjxCWkslUEhGNTdGx15RvPJYQ+lbOlqrlNI2vEAs4pDYK4f/HN2TbDmk5TP0iw==}
    dependencies:
      debug: 3.2.7
      resolve: 1.22.1
    transitivePeerDependencies:
      - supports-color
    dev: false

  /eslint-loader/3.0.3_khdbo4kspv3m2napu5lhju627y:
    resolution: {integrity: sha512-+YRqB95PnNvxNp1HEjQmvf9KNvCin5HXYYseOXVC2U0KEcw4IkQ2IQEBG46j7+gW39bMzeu0GsUhVbBY3Votpw==}
    engines: {node: '>= 8.9.0'}
    peerDependencies:
      eslint: ^5.0.0 || ^6.0.0
      webpack: ^4.0.0 || ^5.0.0
    dependencies:
      eslint: 6.8.0
      fs-extra: 8.1.0
      loader-fs-cache: 1.0.3
      loader-utils: 1.4.0
      object-hash: 2.2.0
      schema-utils: 2.7.1
      webpack: 4.41.5
    dev: false

  /eslint-module-utils/2.7.4_uzwbsh5misspn5xn77vsdpfkgi:
    resolution: {integrity: sha512-j4GT+rqzCoRKHwURX7pddtIPGySnX9Si/cgMI5ztrcqOPtk5dDEeZ34CQVPphnqkJytlc97Vuk05Um2mJ3gEQA==}
    engines: {node: '>=4'}
    peerDependencies:
      '@typescript-eslint/parser': '*'
      eslint: '*'
      eslint-import-resolver-node: '*'
      eslint-import-resolver-typescript: '*'
      eslint-import-resolver-webpack: '*'
    peerDependenciesMeta:
      '@typescript-eslint/parser':
        optional: true
      eslint:
        optional: true
      eslint-import-resolver-node:
        optional: true
      eslint-import-resolver-typescript:
        optional: true
      eslint-import-resolver-webpack:
        optional: true
    dependencies:
      '@typescript-eslint/parser': 2.34.0_z6m2zvrkqxyghb4a2ijhravsdi
      debug: 3.2.7
      eslint: 6.8.0
      eslint-import-resolver-node: 0.3.6
    transitivePeerDependencies:
      - supports-color
    dev: false

  /eslint-plugin-flowtype/4.6.0_eslint@6.8.0:
    resolution: {integrity: sha512-W5hLjpFfZyZsXfo5anlu7HM970JBDqbEshAJUkeczP6BFCIfJXuiIBQXyberLRtOStT0OGPF8efeTbxlHk4LpQ==}
    engines: {node: '>=4'}
    peerDependencies:
      eslint: '>=6.1.0'
    dependencies:
      eslint: 6.8.0
      lodash: 4.17.21
    dev: false

  /eslint-plugin-import/2.20.0_tbglwmj7t2rhd43mvxmptakoay:
    resolution: {integrity: sha512-NK42oA0mUc8Ngn4kONOPsPB1XhbUvNHqF+g307dPV28aknPoiNnKLFd9em4nkswwepdF5ouieqv5Th/63U7YJQ==}
    engines: {node: '>=4'}
    peerDependencies:
      '@typescript-eslint/parser': '*'
      eslint: 2.x - 6.x
    peerDependenciesMeta:
      '@typescript-eslint/parser':
        optional: true
    dependencies:
      '@typescript-eslint/parser': 2.34.0_z6m2zvrkqxyghb4a2ijhravsdi
      array-includes: 3.1.5
      array.prototype.flat: 1.3.0
      contains-path: 0.1.0
      debug: 2.6.9
      doctrine: 1.5.0
      eslint: 6.8.0
      eslint-import-resolver-node: 0.3.6
      eslint-module-utils: 2.7.4_uzwbsh5misspn5xn77vsdpfkgi
      has: 1.0.3
      minimatch: 3.1.2
      object.values: 1.1.5
      read-pkg-up: 2.0.0
      resolve: 1.15.0
    transitivePeerDependencies:
      - eslint-import-resolver-typescript
      - eslint-import-resolver-webpack
      - supports-color
    dev: false

  /eslint-plugin-jsx-a11y/6.2.3_eslint@6.8.0:
    resolution: {integrity: sha512-CawzfGt9w83tyuVekn0GDPU9ytYtxyxyFZ3aSWROmnRRFQFT2BiPJd7jvRdzNDi6oLWaS2asMeYSNMjWTV4eNg==}
    engines: {node: '>=4.0'}
    peerDependencies:
      eslint: ^3 || ^4 || ^5 || ^6
    dependencies:
      '@babel/runtime': 7.19.4
      aria-query: 3.0.0
      array-includes: 3.1.5
      ast-types-flow: 0.0.7
      axobject-query: 2.2.0
      damerau-levenshtein: 1.0.8
      emoji-regex: 7.0.3
      eslint: 6.8.0
      has: 1.0.3
      jsx-ast-utils: 2.4.1
    dev: false

  /eslint-plugin-react-hooks/1.7.0_eslint@6.8.0:
    resolution: {integrity: sha512-iXTCFcOmlWvw4+TOE8CLWj6yX1GwzT0Y6cUfHHZqWnSk144VmVIRcVGtUAzrLES7C798lmvnt02C7rxaOX1HNA==}
    engines: {node: '>=7'}
    peerDependencies:
      eslint: ^3.0.0 || ^4.0.0 || ^5.0.0 || ^6.0.0
    dependencies:
      eslint: 6.8.0
    dev: false

  /eslint-plugin-react/7.18.0_eslint@6.8.0:
    resolution: {integrity: sha512-p+PGoGeV4SaZRDsXqdj9OWcOrOpZn8gXoGPcIQTzo2IDMbAKhNDnME9myZWqO3Ic4R3YmwAZ1lDjWl2R2hMUVQ==}
    engines: {node: '>=4'}
    peerDependencies:
      eslint: ^3.0.0 || ^4.0.0 || ^5.0.0 || ^6.0.0
    dependencies:
      array-includes: 3.1.5
      doctrine: 2.1.0
      eslint: 6.8.0
      has: 1.0.3
      jsx-ast-utils: 2.4.1
      object.entries: 1.1.5
      object.fromentries: 2.0.5
      object.values: 1.1.5
      prop-types: 15.8.1
      resolve: 1.15.0
    dev: false

  /eslint-scope/4.0.3:
    resolution: {integrity: sha512-p7VutNr1O/QrxysMo3E45FjYDTeXBy0iTltPFNSqKAIfjDSXC+4dj+qfyuD8bfAXrW/y6lW3O76VaYNPKfpKrg==}
    engines: {node: '>=4.0.0'}
    dependencies:
      esrecurse: 4.3.0
      estraverse: 4.3.0
    dev: false

  /eslint-scope/5.1.1:
    resolution: {integrity: sha512-2NxwbF/hZ0KpepYN0cNbo+FN6XoK7GaHlQhgx/hIZl6Va0bF45RQOOwhLIy8lQDbuCiadSLCBnH2CFYquit5bw==}
    engines: {node: '>=8.0.0'}
    dependencies:
      esrecurse: 4.3.0
      estraverse: 4.3.0
    dev: false

  /eslint-utils/1.4.3:
    resolution: {integrity: sha512-fbBN5W2xdY45KulGXmLHZ3c3FHfVYmKg0IrAKGOkT/464PQsx2UeIzfz1RmEci+KLm1bBaAzZAh8+/E+XAeZ8Q==}
    engines: {node: '>=6'}
    dependencies:
      eslint-visitor-keys: 1.3.0
    dev: false

  /eslint-utils/2.1.0:
    resolution: {integrity: sha512-w94dQYoauyvlDc43XnGB8lU3Zt713vNChgt4EWwhXAP2XkBvndfxF0AgIqKOOasjPIPzj9JqgwkwbCYD0/V3Zg==}
    engines: {node: '>=6'}
    dependencies:
      eslint-visitor-keys: 1.3.0
    dev: false

  /eslint-visitor-keys/1.3.0:
    resolution: {integrity: sha512-6J72N8UNa462wa/KFODt/PJ3IU60SDpC3QXC1Hjc1BXXpfL2C9R5+AU7jhe0F6GREqVMh4Juu+NY7xn+6dipUQ==}
    engines: {node: '>=4'}
    dev: false

  /eslint/6.8.0:
    resolution: {integrity: sha512-K+Iayyo2LtyYhDSYwz5D5QdWw0hCacNzyq1Y821Xna2xSJj7cijoLLYmLxTQgcgZ9mC61nryMy9S7GRbYpI5Ig==}
    engines: {node: ^8.10.0 || ^10.13.0 || >=11.10.1}
    hasBin: true
    dependencies:
      '@babel/code-frame': 7.18.6
      ajv: 6.12.6
      chalk: 2.4.2
      cross-spawn: 6.0.5
      debug: 4.3.4
      doctrine: 3.0.0
      eslint-scope: 5.1.1
      eslint-utils: 1.4.3
      eslint-visitor-keys: 1.3.0
      espree: 6.2.1
      esquery: 1.4.0
      esutils: 2.0.3
      file-entry-cache: 5.0.1
      functional-red-black-tree: 1.0.1
      glob-parent: 5.1.2
      globals: 12.4.0
      ignore: 4.0.6
      import-fresh: 3.3.0
      imurmurhash: 0.1.4
      inquirer: 7.3.3
      is-glob: 4.0.3
      js-yaml: 3.14.1
      json-stable-stringify-without-jsonify: 1.0.1
      levn: 0.3.0
      lodash: 4.17.21
      minimatch: 3.1.2
      mkdirp: 0.5.6
      natural-compare: 1.4.0
      optionator: 0.8.3
      progress: 2.0.3
      regexpp: 2.0.1
      semver: 6.3.0
      strip-ansi: 5.2.0
      strip-json-comments: 3.1.1
      table: 5.4.6
      text-table: 0.2.0
      v8-compile-cache: 2.3.0
    transitivePeerDependencies:
      - supports-color
    dev: false

  /espree/6.2.1:
    resolution: {integrity: sha512-ysCxRQY3WaXJz9tdbWOwuWr5Y/XrPTGX9Kiz3yoUXwW0VZ4w30HTkQLaGx/+ttFjF8i+ACbArnB4ce68a9m5hw==}
    engines: {node: '>=6.0.0'}
    dependencies:
      acorn: 7.4.1
      acorn-jsx: 5.3.2_acorn@7.4.1
      eslint-visitor-keys: 1.3.0
    dev: false

  /esprima/4.0.1:
    resolution: {integrity: sha512-eGuFFw7Upda+g4p+QHvnW0RyTX/SVeJBDM/gCtMARO0cLuT2HcEKnTPvhjV6aGeqrCB/sbNop0Kszm0jsaWU4A==}
    engines: {node: '>=4'}
    hasBin: true
    dev: false

  /esquery/1.4.0:
    resolution: {integrity: sha512-cCDispWt5vHHtwMY2YrAQ4ibFkAL8RbH5YGBnZBc90MolvvfkkQcJro/aZiAQUlQ3qgrYS6D6v8Gc5G5CQsc9w==}
    engines: {node: '>=0.10'}
    dependencies:
      estraverse: 5.3.0
    dev: false

  /esrecurse/4.3.0:
    resolution: {integrity: sha512-KmfKL3b6G+RXvP8N1vr3Tq1kL/oCFgn2NYXEtqP8/L3pKapUA4G8cFVaoF3SU323CD4XypR/ffioHmkti6/Tag==}
    engines: {node: '>=4.0'}
    dependencies:
      estraverse: 5.3.0
    dev: false

  /estraverse/4.3.0:
    resolution: {integrity: sha512-39nnKffWz8xN1BU/2c79n9nB9HDzo0niYUqx6xyqUnyoAnQyyWpOTdZEeiCch8BBu515t4wp9ZmgVfVhn9EBpw==}
    engines: {node: '>=4.0'}
    dev: false

  /estraverse/5.3.0:
    resolution: {integrity: sha1-LupSkHAvJquP5TcDcP+GyWXSESM=}
    engines: {node: '>=4.0'}
    dev: false

  /esutils/2.0.3:
    resolution: {integrity: sha512-kVscqXk4OCp68SZ0dkgEKVi6/8ij300KBWTJq32P/dYeWTSwK41WyTxalN1eRmA5Z9UU/LX9D7FWSmV9SAYx6g==}
    engines: {node: '>=0.10.0'}
    dev: false

  /etag/1.8.1:
    resolution: {integrity: sha1-Qa4u62XvpiJorr/qg6x9eSmbCIc=}
    engines: {node: '>= 0.6'}
    dev: false

  /eventemitter3/4.0.7:
    resolution: {integrity: sha512-8guHBZCwKnFhYdHr2ysuRWErTwhoN2X8XELRlrRwpmfeY2jjuUN4taQMsULKUVo1K4DvZl+0pgfyoysHxvmvEw==}

  /events/3.3.0:
    resolution: {integrity: sha512-mQw+2fkQbALzQ7V0MY0IqdnXNOeTtP4r0lN9z7AAawCXgqea7bDii20AYrIBrFd/Hx0M2Ocz6S111CaFkUcb0Q==}
    engines: {node: '>=0.8.x'}
    dev: false

  /eventsource/1.1.2:
    resolution: {integrity: sha512-xAH3zWhgO2/3KIniEKYPr8plNSzlGINOUqYj0m0u7AB81iRw8b/3E73W6AuU+6klLbaSFmZnaETQ2lXPfAydrA==}
    engines: {node: '>=0.12.0'}
    dev: false

  /evp_bytestokey/1.0.3:
    resolution: {integrity: sha512-/f2Go4TognH/KvCISP7OUsHn85hT9nUkxxA9BEWxFn+Oj9o8ZNLm/40hdlgSLyuOimsrTKLUMEorQexp/aPQeA==}
    dependencies:
      md5.js: 1.3.5
      safe-buffer: 5.2.1
    dev: false

  /exec-sh/0.3.6:
    resolution: {integrity: sha512-nQn+hI3yp+oD0huYhKwvYI32+JFeq+XkNcD1GAo3Y/MjxsfVGmrrzrnzjWiNY6f+pUCP440fThsFh5gZrRAU/w==}
    dev: false

  /execa/1.0.0:
    resolution: {integrity: sha512-adbxcyWV46qiHyvSp50TKt05tB4tK3HcmF7/nxfAdhnox83seTDbwnaqKO4sXRy7roHAIFqJP/Rw/AuEbX61LA==}
    engines: {node: '>=6'}
    dependencies:
      cross-spawn: 6.0.5
      get-stream: 4.1.0
      is-stream: 1.1.0
      npm-run-path: 2.0.2
      p-finally: 1.0.0
      signal-exit: 3.0.7
      strip-eof: 1.0.0
    dev: false

  /execall/2.0.0:
    resolution: {integrity: sha512-0FU2hZ5Hh6iQnarpRtQurM/aAvp3RIbfvgLHrcqJYzhXyV2KFruhuChf9NC6waAhiUR7FFtlugkI4p7f2Fqlow==}
    engines: {node: '>=8'}
    dependencies:
      clone-regexp: 2.2.0
    dev: true

  /exenv/1.2.2:
    resolution: {integrity: sha1-KueOhdmJQVhnCwPUe+wfA72Ru50=}
    dev: false

  /exit/0.1.2:
    resolution: {integrity: sha1-BjJjj42HfMghB9MKD/8aF8uhzQw=}
    engines: {node: '>= 0.8.0'}
    dev: false

  /expand-brackets/2.1.4:
    resolution: {integrity: sha1-t3c14xXOMPa27/D4OwQVGiJEliI=}
    engines: {node: '>=0.10.0'}
    dependencies:
      debug: 2.6.9
      define-property: 0.2.5
      extend-shallow: 2.0.1
      posix-character-classes: 0.1.1
      regex-not: 1.0.2
      snapdragon: 0.8.2
      to-regex: 3.0.2
    transitivePeerDependencies:
      - supports-color
    dev: false

  /expand-brackets/2.1.4_supports-color@6.1.0:
    resolution: {integrity: sha1-t3c14xXOMPa27/D4OwQVGiJEliI=}
    engines: {node: '>=0.10.0'}
    dependencies:
      debug: 2.6.9_supports-color@6.1.0
      define-property: 0.2.5
      extend-shallow: 2.0.1
      posix-character-classes: 0.1.1
      regex-not: 1.0.2
      snapdragon: 0.8.2_supports-color@6.1.0
      to-regex: 3.0.2
    transitivePeerDependencies:
      - supports-color
    dev: false

  /expect/24.9.0:
    resolution: {integrity: sha512-wvVAx8XIol3Z5m9zvZXiyZOQ+sRJqNTIm6sGjdWlaZIeupQGO3WbYI+15D/AmEwZywL6wtJkbAbJtzkOfBuR0Q==}
    engines: {node: '>= 6'}
    dependencies:
      '@jest/types': 24.9.0
      ansi-styles: 3.2.1
      jest-get-type: 24.9.0
      jest-matcher-utils: 24.9.0
      jest-message-util: 24.9.0
      jest-regex-util: 24.9.0
    transitivePeerDependencies:
      - supports-color
    dev: false

  /express/4.18.2_supports-color@6.1.0:
    resolution: {integrity: sha512-5/PsL6iGPdfQ/lKM1UuielYgv3BUoJfz1aUwU9vHZ+J7gyvwdQXFEBIEIaxeGf0GIcreATNyBExtalisDbuMqQ==}
    engines: {node: '>= 0.10.0'}
    dependencies:
      accepts: 1.3.8
      array-flatten: 1.1.1
      body-parser: 1.20.1_supports-color@6.1.0
      content-disposition: 0.5.4
      content-type: 1.0.4
      cookie: 0.5.0
      cookie-signature: 1.0.6
      debug: 2.6.9_supports-color@6.1.0
      depd: 2.0.0
      encodeurl: 1.0.2
      escape-html: 1.0.3
      etag: 1.8.1
      finalhandler: 1.2.0_supports-color@6.1.0
      fresh: 0.5.2
      http-errors: 2.0.0
      merge-descriptors: 1.0.1
      methods: 1.1.2
      on-finished: 2.4.1
      parseurl: 1.3.3
      path-to-regexp: 0.1.7
      proxy-addr: 2.0.7
      qs: 6.11.0
      range-parser: 1.2.1
      safe-buffer: 5.2.1
      send: 0.18.0_supports-color@6.1.0
      serve-static: 1.15.0_supports-color@6.1.0
      setprototypeof: 1.2.0
      statuses: 2.0.1
      type-is: 1.6.18
      utils-merge: 1.0.1
      vary: 1.1.2
    transitivePeerDependencies:
      - supports-color
    dev: false

  /ext/1.7.0:
    resolution: {integrity: sha512-6hxeJYaL110a9b5TEJSj0gojyHQAmA2ch5Os+ySCiA1QGdS697XWY1pzsrSjqA9LDEEgdB/KypIlR59RcLuHYw==}
    dependencies:
      type: 2.7.2
    dev: false

  /extend-shallow/2.0.1:
    resolution: {integrity: sha1-Ua99YUrZqfYQ6huvu5idaxxWiQ8=}
    engines: {node: '>=0.10.0'}
    dependencies:
      is-extendable: 0.1.1
    dev: false

  /extend-shallow/3.0.2:
    resolution: {integrity: sha1-Jqcarwc7OfshJxcnRhMcJwQCjbg=}
    engines: {node: '>=0.10.0'}
    dependencies:
      assign-symbols: 1.0.0
      is-extendable: 1.0.1
    dev: false

  /extend/3.0.2:
    resolution: {integrity: sha512-fjquC59cD7CyW6urNXK0FBufkZcoiGG80wTuPujX590cB5Ttln20E2UB4S/WARVqhXffZl2LNgS+gQdPIIim/g==}

  /external-editor/3.1.0:
    resolution: {integrity: sha512-hMQ4CX1p1izmuLYyZqLMO/qGNw10wSv9QDCPfzXfyFrOaCSSoRfqE1Kf1s5an66J5JZC62NewG+mK49jOCtQew==}
    engines: {node: '>=4'}
    dependencies:
      chardet: 0.7.0
      iconv-lite: 0.4.24
      tmp: 0.0.33
    dev: false

  /extglob/2.0.4:
    resolution: {integrity: sha512-Nmb6QXkELsuBr24CJSkilo6UHHgbekK5UiZgfE6UHD3Eb27YC6oD+bhcT+tJ6cl8dmsgdQxnWlcry8ksBIBLpw==}
    engines: {node: '>=0.10.0'}
    dependencies:
      array-unique: 0.3.2
      define-property: 1.0.0
      expand-brackets: 2.1.4
      extend-shallow: 2.0.1
      fragment-cache: 0.2.1
      regex-not: 1.0.2
      snapdragon: 0.8.2
      to-regex: 3.0.2
    transitivePeerDependencies:
      - supports-color
    dev: false

  /extglob/2.0.4_supports-color@6.1.0:
    resolution: {integrity: sha512-Nmb6QXkELsuBr24CJSkilo6UHHgbekK5UiZgfE6UHD3Eb27YC6oD+bhcT+tJ6cl8dmsgdQxnWlcry8ksBIBLpw==}
    engines: {node: '>=0.10.0'}
    dependencies:
      array-unique: 0.3.2
      define-property: 1.0.0
      expand-brackets: 2.1.4_supports-color@6.1.0
      extend-shallow: 2.0.1
      fragment-cache: 0.2.1
      regex-not: 1.0.2
      snapdragon: 0.8.2_supports-color@6.1.0
      to-regex: 3.0.2
    transitivePeerDependencies:
      - supports-color
    dev: false

  /extsprintf/1.3.0:
    resolution: {integrity: sha1-lpGEQOMEGnpBT4xS48V06zw+HgU=}
    engines: {'0': node >=0.6.0}

  /fast-deep-equal/3.1.3:
    resolution: {integrity: sha512-f3qQ9oQy9j2AhBe/H9VC91wLmKBCCU/gDOnKNAYG5hswO7BLKj09Hc5HYNz9cGI++xlpDCIgDaitVs03ATR84Q==}

  /fast-glob/2.2.7:
    resolution: {integrity: sha512-g1KuQwHOZAmOZMuBtHdxDtju+T2RT8jgCC9aANsbpdiDDTSnjgfuVsIBNKbUeJI3oKMRExcfNDtJl4OhbffMsw==}
    engines: {node: '>=4.0.0'}
    dependencies:
      '@mrmlnc/readdir-enhanced': 2.2.1
      '@nodelib/fs.stat': 1.1.3
      glob-parent: 3.1.0
      is-glob: 4.0.3
      merge2: 1.4.1
      micromatch: 3.1.10
    transitivePeerDependencies:
      - supports-color
    dev: false

  /fast-glob/3.2.12:
    resolution: {integrity: sha512-DVj4CQIYYow0BlaelwK1pHl5n5cRSJfM60UA0zK891sVInoPri2Ekj7+e1CT3/3qxXenpI+nBBmQAcJPJgaj4w==}
    engines: {node: '>=8.6.0'}
    dependencies:
      '@nodelib/fs.stat': 2.0.5
      '@nodelib/fs.walk': 1.2.8
      glob-parent: 5.1.2
      merge2: 1.4.1
      micromatch: 4.0.5
    dev: true

  /fast-json-stable-stringify/2.1.0:
    resolution: {integrity: sha512-lhd/wF+Lk98HZoTCtlVraHtfh5XYijIjalXck7saUtuanSDyLMxnHhSXEDJqHxD7msR8D0uCmqlkwjCV8xvwHw==}

  /fast-levenshtein/2.0.6:
    resolution: {integrity: sha1-PYpcZog6FqMMqGQ+hR8Zuqd5eRc=}
    dev: false

  /fastest-levenshtein/1.0.16:
    resolution: {integrity: sha512-eRnCtTTtGZFpQCwhJiUOuxPQWRXVKYDn0b2PeHfXL6/Zi53SLAzAHfVhVWK2AryC/WH05kGfxhFIPvTF0SXQzg==}
    engines: {node: '>= 4.9.1'}
    dev: true

  /fastq/1.13.0:
    resolution: {integrity: sha1-YWdg+Ip1Jr38WWt8q4wYk4w2uYw=}
    dependencies:
      reusify: 1.0.4
    dev: true

  /faye-websocket/0.10.0:
    resolution: {integrity: sha1-TkkvjQTftviQA1B/btvy1QHnxvQ=}
    engines: {node: '>=0.4.0'}
    dependencies:
      websocket-driver: 0.7.4
    dev: false

  /faye-websocket/0.11.4:
    resolution: {integrity: sha512-CzbClwlXAuiRQAlUyfqPgvPoNKTckTPGfwZV4ZdAhVcP2lh9KUxJg2b5GkE7XbjKQ3YJnQ9z6D9ntLAlB+tP8g==}
    engines: {node: '>=0.8.0'}
    dependencies:
      websocket-driver: 0.7.4
    dev: false

  /fb-watchman/2.0.2:
    resolution: {integrity: sha512-p5161BqbuCaSnB8jIbzQHOlpgsPmK5rJVDfDKO91Axs5NC1uu3HRQm6wt9cd9/+GtQQIO53JdGXXoyDpTAsgYA==}
    dependencies:
      bser: 2.1.1
    dev: false

  /fbjs/0.8.18:
    resolution: {integrity: sha1-mDXgrduayi7/Uylc15yhz8fJZio=}
    dependencies:
      core-js: 1.2.7
      isomorphic-fetch: 2.2.1
      loose-envify: 1.4.0
      object-assign: 4.1.1
      promise: 7.3.1
      setimmediate: 1.0.5
      ua-parser-js: 0.7.32
    dev: false

  /figgy-pudding/3.5.2:
    resolution: {integrity: sha512-0btnI/H8f2pavGMN8w40mlSKOfTK2SVJmBfBeVIj3kNw0swwgzyRq0d5TJVOwodFmtvpPeWPN/MCcfuWF0Ezbw==}
    dev: false

  /figures/3.2.0:
    resolution: {integrity: sha512-yaduQFRKLXYOGgEn6AZau90j3ggSOyiqXU0F9JZfeXYhNa+Jk4X+s45A2zg5jns87GAFa34BBm2kXw4XpNcbdg==}
    engines: {node: '>=8'}
    dependencies:
      escape-string-regexp: 1.0.5
    dev: false

  /file-entry-cache/5.0.1:
    resolution: {integrity: sha512-bCg29ictuBaKUwwArK4ouCaqDgLZcysCFLmM/Yn/FDoqndh/9vNuQfXRDvTuXKLxfD/JtZQGKFT8MGcJBK644g==}
    engines: {node: '>=4'}
    dependencies:
      flat-cache: 2.0.1
    dev: false

  /file-entry-cache/6.0.1:
    resolution: {integrity: sha512-7Gps/XWymbLk2QLYK4NzpMOrYjMhdIxXuIvy2QBsLE6ljuodKvdkWs/cpyJJ3CVIVpH0Oi1Hvg1ovbMzLdFBBg==}
    engines: {node: ^10.12.0 || >=12.0.0}
    dependencies:
      flat-cache: 3.0.4
    dev: true

  /file-loader/4.3.0_webpack@4.41.5:
    resolution: {integrity: sha512-aKrYPYjF1yG3oX0kWRrqrSMfgftm7oJW5M+m4owoldH5C51C0RkIwB++JbRvEW3IU6/ZG5n8UvEcdgwOt2UOWA==}
    engines: {node: '>= 8.9.0'}
    peerDependencies:
      webpack: ^4.0.0
    dependencies:
      loader-utils: 1.4.0
      schema-utils: 2.7.1
      webpack: 4.41.5
    dev: false

  /file-uri-to-path/1.0.0:
    resolution: {integrity: sha512-0Zt+s3L7Vf1biwWZ29aARiVYLx7iMGnEUl9x33fbB/j3jR81u/O2LbqK+Bm1CDSNDKVtJ/YjwY7TUd5SkeLQLw==}
    dev: false
    optional: true

  /filesize/6.0.1:
    resolution: {integrity: sha512-u4AYWPgbI5GBhs6id1KdImZWn5yfyFrrQ8OWZdN7ZMfA8Bf4HcO0BGo9bmUIEV8yrp8I1xVfJ/dn90GtFNNJcg==}
    engines: {node: '>= 0.4.0'}
    dev: false

  /fill-range/4.0.0:
    resolution: {integrity: sha1-1USBHUKPmOsGpj3EAtJAPDKMOPc=}
    engines: {node: '>=0.10.0'}
    dependencies:
      extend-shallow: 2.0.1
      is-number: 3.0.0
      repeat-string: 1.6.1
      to-regex-range: 2.1.1
    dev: false

  /fill-range/7.0.1:
    resolution: {integrity: sha512-qOo9F+dMUmC2Lcb4BbVvnKJxTPjCm+RRpe4gDuGrzkL7mEVl/djYSu2OdQ2Pa302N4oqkSg9ir6jaLWJ2USVpQ==}
    engines: {node: '>=8'}
    dependencies:
      to-regex-range: 5.0.1

  /finalhandler/1.2.0_supports-color@6.1.0:
    resolution: {integrity: sha512-5uXcUVftlQMFnWC9qu/svkWv3GTd2PfUhK/3PLkYNAe7FbqJMt3515HaxE6eRL74GdsriiwujiawdaB1BpEISg==}
    engines: {node: '>= 0.8'}
    dependencies:
      debug: 2.6.9_supports-color@6.1.0
      encodeurl: 1.0.2
      escape-html: 1.0.3
      on-finished: 2.4.1
      parseurl: 1.3.3
      statuses: 2.0.1
      unpipe: 1.0.0
    transitivePeerDependencies:
      - supports-color
    dev: false

  /find-cache-dir/0.1.1:
    resolution: {integrity: sha1-yN765XyKUqinhPnjHFfHQumToLk=}
    engines: {node: '>=0.10.0'}
    dependencies:
      commondir: 1.0.1
      mkdirp: 0.5.6
      pkg-dir: 1.0.0
    dev: false

  /find-cache-dir/2.1.0:
    resolution: {integrity: sha512-Tq6PixE0w/VMFfCgbONnkiQIVol/JJL7nRMi20fqzA4NRs9AfeqMGeRdPi3wIhYkxjeBaWh2rxwapn5Tu3IqOQ==}
    engines: {node: '>=6'}
    dependencies:
      commondir: 1.0.1
      make-dir: 2.1.0
      pkg-dir: 3.0.0
    dev: false

  /find-cache-dir/3.3.2:
    resolution: {integrity: sha512-wXZV5emFEjrridIgED11OoUKLxiYjAcqot/NJdAkOhlJ+vGzwhOAfcG5OX1jP+S0PcjEn8bdMJv+g2jwQ3Onig==}
    engines: {node: '>=8'}
    dependencies:
      commondir: 1.0.1
      make-dir: 3.1.0
      pkg-dir: 4.2.0
    dev: false

  /find-up/1.1.2:
    resolution: {integrity: sha1-ay6YIrGizgpgq2TWEOzK1TyyTQ8=}
    engines: {node: '>=0.10.0'}
    dependencies:
      path-exists: 2.1.0
      pinkie-promise: 2.0.1

  /find-up/2.1.0:
    resolution: {integrity: sha1-RdG35QbHF93UgndaK3eSCjwMV6c=}
    engines: {node: '>=4'}
    dependencies:
      locate-path: 2.0.0
    dev: false

  /find-up/3.0.0:
    resolution: {integrity: sha512-1yD6RmLI1XBfxugvORwlck6f75tYL+iR0jqwsOrOxMZyGYqUuDhJ0l4AXdO1iX/FTs9cBAMEk1gWSEx1kSbylg==}
    engines: {node: '>=6'}
    dependencies:
      locate-path: 3.0.0

  /find-up/4.1.0:
    resolution: {integrity: sha512-PpOwAdQ/YlXQ2vj8a3h8IipDuYRi3wceVQQGYWxNINccq40Anw7BlsEXCMbt1Zt+OLA6Fq9suIpIWD0OsnISlw==}
    engines: {node: '>=8'}
    dependencies:
      locate-path: 5.0.0
      path-exists: 4.0.0

  /flat-cache/2.0.1:
    resolution: {integrity: sha512-LoQe6yDuUMDzQAEH8sgmh4Md6oZnc/7PjtwjNFSzveXqSHt6ka9fPBuso7IGf9Rz4uqnSnWiFH2B/zj24a5ReA==}
    engines: {node: '>=4'}
    dependencies:
      flatted: 2.0.2
      rimraf: 2.6.3
      write: 1.0.3
    dev: false

  /flat-cache/3.0.4:
    resolution: {integrity: sha512-dm9s5Pw7Jc0GvMYbshN6zchCA9RgQlzzEZX3vylR9IqFfS8XciblUXOKfW6SiuJ0e13eDYZoZV5wdrev7P3Nwg==}
    engines: {node: ^10.12.0 || >=12.0.0}
    dependencies:
      flatted: 3.2.7
      rimraf: 3.0.2
    dev: true

  /flatted/2.0.2:
    resolution: {integrity: sha512-r5wGx7YeOwNWNlCA0wQ86zKyDLMQr+/RB8xy74M4hTphfmjlijTSSXGuH8rnvKZnfT9i+75zmd8jcKdMR4O6jA==}
    dev: false

  /flatted/3.2.7:
    resolution: {integrity: sha512-5nqDSxl8nn5BSNxyR3n4I6eDmbolI6WT+QqR547RwxQapgjQBmtktdP+HTBb/a/zLsbzERTONyUB5pefh5TtjQ==}
    dev: true

  /flatten/1.0.3:
    resolution: {integrity: sha512-dVsPA/UwQ8+2uoFe5GHtiBMu48dWLTdsuEd7CKGlZlD78r1TTWBvDuFaFGKCo/ZfEr95Uk56vZoX86OsHkUeIg==}
    dev: false

  /flush-write-stream/1.1.1:
    resolution: {integrity: sha512-3Z4XhFZ3992uIq0XOqb9AreonueSYphE6oYbpt5+3u06JWklbsPkNv3ZKkP9Bz/r+1MWCaMoSQ28P85+1Yc77w==}
    dependencies:
      inherits: 2.0.4
      readable-stream: 2.3.7
    dev: false

  /follow-redirects/1.15.2:
    resolution: {integrity: sha512-VQLG33o04KaQ8uYi2tVNbdrWp1QWxNNea+nmIB4EVM28v0hmP17z7aG1+wAkNzVq4KeXTq3221ye5qTJP91JwA==}
    engines: {node: '>=4.0'}
    peerDependencies:
      debug: '*'
    peerDependenciesMeta:
      debug:
        optional: true
    dev: true

  /follow-redirects/1.15.2_debug@4.3.4:
    resolution: {integrity: sha512-VQLG33o04KaQ8uYi2tVNbdrWp1QWxNNea+nmIB4EVM28v0hmP17z7aG1+wAkNzVq4KeXTq3221ye5qTJP91JwA==}
    engines: {node: '>=4.0'}
    peerDependencies:
      debug: '*'
    peerDependenciesMeta:
      debug:
        optional: true
    dependencies:
      debug: 4.3.4_supports-color@6.1.0
    dev: false

  /follow-redirects/1.5.10:
    resolution: {integrity: sha512-0V5l4Cizzvqt5D44aTXbFZz+FtyXV1vrDN6qrelxtfYQKW0KO0W2T/hkE8xvGa/540LkZlkaUjO4ailYTFtHVQ==}
    engines: {node: '>=4.0'}
    dependencies:
      debug: 3.1.0
    transitivePeerDependencies:
      - supports-color
    dev: false

  /for-each/0.3.3:
    resolution: {integrity: sha512-jqYfLp7mo9vIyQf8ykW2v7A+2N4QjeCeI5+Dz9XraiO1ign81wjiH7Fb9vSOWvQfNtmSa4H2RoQTrrXivdUZmw==}
    dependencies:
      is-callable: 1.2.7
    dev: false

  /for-in/0.1.8:
    resolution: {integrity: sha1-2Hc5COMSVhCZUrH9ubP6hn0ndeE=}
    engines: {node: '>=0.10.0'}
    dev: false

  /for-in/1.0.2:
    resolution: {integrity: sha1-gQaNKVqBQuwKxybG4iAMMPttXoA=}
    engines: {node: '>=0.10.0'}
    dev: false

  /for-own/0.1.5:
    resolution: {integrity: sha1-UmXGgaTylNq78XyVCbZ2OqhFEM4=}
    engines: {node: '>=0.10.0'}
    dependencies:
      for-in: 1.0.2
    dev: false

  /forever-agent/0.6.1:
    resolution: {integrity: sha1-+8cfDEGt6zf5bFd60e1C2P2sypE=}

  /fork-ts-checker-webpack-plugin/3.1.1_rhie7mtatz3augq54a72vkrn4a:
    resolution: {integrity: sha512-DuVkPNrM12jR41KM2e+N+styka0EgLkTnXmNcXdgOM37vtGeY+oCBK/Jx0hzSeEU6memFCtWb4htrHPMDfwwUQ==}
    engines: {node: '>=6.11.5', yarn: '>=1.0.0'}
    peerDependencies:
      eslint: '>= 6'
      typescript: '>= 2.7'
      vue-template-compiler: '*'
      webpack: '>= 4'
    peerDependenciesMeta:
      eslint:
        optional: true
      vue-template-compiler:
        optional: true
    dependencies:
      babel-code-frame: 6.26.0
      chalk: 2.4.2
      chokidar: 3.5.3
      eslint: 6.8.0
      micromatch: 3.1.10
      minimatch: 3.1.2
      semver: 5.7.1
      tapable: 1.1.3
      typescript: 3.9.10
      webpack: 4.41.5
      worker-rpc: 0.1.1
    transitivePeerDependencies:
      - supports-color
    dev: false

  /form-data/2.3.3:
    resolution: {integrity: sha512-1lLKB2Mu3aGP1Q/2eCOx0fNbRMe7XdwktwOruhfqqd0rIJWwN4Dh+E3hrPSlDCXnSR7UtZ1N38rVXm+6+MEhJQ==}
    engines: {node: '>= 0.12'}
    dependencies:
      asynckit: 0.4.0
      combined-stream: 1.0.8
      mime-types: 2.1.35

  /forwarded/0.2.0:
    resolution: {integrity: sha512-buRG0fpBtRHSTCOASe6hD258tEubFoRLb4ZNA6NxMVHNw2gOcwHo9wyablzMzOA5z9xA9L1KNjk/Nt6MT9aYow==}
    engines: {node: '>= 0.6'}
    dev: false

  /fragment-cache/0.2.1:
    resolution: {integrity: sha1-QpD60n8T6Jvn8zeZxrxaCr//DRk=}
    engines: {node: '>=0.10.0'}
    dependencies:
      map-cache: 0.2.2
    dev: false

  /fresh/0.5.2:
    resolution: {integrity: sha1-PYyt2Q2XZWn6g1qx+OSyOhBWBac=}
    engines: {node: '>= 0.6'}
    dev: false

  /from2/2.3.0:
    resolution: {integrity: sha1-i/tVAr3kpNNs/e6gB/zKIdfjgq8=}
    dependencies:
      inherits: 2.0.4
      readable-stream: 2.3.7
    dev: false

  /fs-constants/1.0.0:
    resolution: {integrity: sha512-y6OAwoSIf7FyjMIv94u+b5rdheZEjzR63GTyZJm5qh4Bi+2YgwLCcI/fPFZkL5PSixOt6ZNKm+w+Hfp/Bciwow==}
    dev: false

  /fs-extra/4.0.3:
    resolution: {integrity: sha512-q6rbdDd1o2mAnQreO7YADIxf/Whx4AHBiRf6d+/cVT8h44ss+lHgxf1FemcqDnQt9X3ct4McHr+JMGlYSsK7Cg==}
    dependencies:
      graceful-fs: 4.2.10
      jsonfile: 4.0.0
      universalify: 0.1.2
    dev: false

  /fs-extra/7.0.1:
    resolution: {integrity: sha512-YJDaCJZEnBmcbw13fvdAM9AwNOJwOzrE4pqMqBq5nFiEqXUqHwlK4B+3pUw6JNvfSPtX05xFHtYy/1ni01eGCw==}
    engines: {node: '>=6 <7 || >=8'}
    dependencies:
      graceful-fs: 4.2.10
      jsonfile: 4.0.0
      universalify: 0.1.2
    dev: false

  /fs-extra/8.1.0:
    resolution: {integrity: sha512-yhlQgA6mnOJUKOsRUFsgJdQCvkKhcz8tlZG5HBQfReYZy46OwLcY+Zia0mtdHsOo9y/hP+CxMN0TU9QxoOtG4g==}
    engines: {node: '>=6 <7 || >=8'}
    dependencies:
      graceful-fs: 4.2.10
      jsonfile: 4.0.0
      universalify: 0.1.2
    dev: false

  /fs-minipass/2.1.0:
    resolution: {integrity: sha512-V/JgOLFCS+R6Vcq0slCuaeWEdNC3ouDlJMNIsacH2VtALiu9mV4LPrHc5cDl8k5aw6J8jwgWWpiTo5RYhmIzvg==}
    engines: {node: '>= 8'}
    dependencies:
      minipass: 3.3.4
    dev: false

  /fs-write-stream-atomic/1.0.10:
    resolution: {integrity: sha1-tH31NJPvkR33VzHnCp3tAYnbQMk=}
    dependencies:
      graceful-fs: 4.2.10
      iferr: 0.1.5
      imurmurhash: 0.1.4
      readable-stream: 2.3.7
    dev: false

  /fs.realpath/1.0.0:
    resolution: {integrity: sha1-FQStJSMVjKpA20onh8sBQRmU6k8=}

  /fsevents/1.2.13:
    resolution: {integrity: sha512-oWb1Z6mkHIskLzEJ/XWX0srkpkTQ7vaopMQkyaEIoq0fmtFVxOthb8cCxeT+p3ynTdkk/RZwbgG4brR5BeWECw==}
    engines: {node: '>= 4.0'}
    os: [darwin]
    deprecated: fsevents 1 will break on node v14+ and could be using insecure binaries. Upgrade to fsevents 2.
    requiresBuild: true
    dependencies:
      bindings: 1.5.0
      nan: 2.17.0
    dev: false
    optional: true

  /fsevents/2.1.2:
    resolution: {integrity: sha512-R4wDiBwZ0KzpgOWetKDug1FZcYhqYnUYKtfZYt4mD5SBz76q0KR4Q9o7GIPamsVPGmW3EYPPJ0dOOjvx32ldZA==}
    engines: {node: ^8.16.0 || ^10.6.0 || >=11.0.0}
    os: [darwin]
    requiresBuild: true
    dev: false
    optional: true

  /fsevents/2.3.2:
    resolution: {integrity: sha512-xiqMQR4xAeHTuB9uWm+fFRcIOgKBMiOBP+eXiyT7jsgVCq1bkVygt00oASowB7EdtpOHaaPgKt812P9ab+DDKA==}
    engines: {node: ^8.16.0 || ^10.6.0 || >=11.0.0}
    os: [darwin]
    requiresBuild: true
    dev: false
    optional: true

  /fstream/1.0.12:
    resolution: {integrity: sha512-WvJ193OHa0GHPEL+AycEJgxvBEwyfRkN1vhjca23OaPVMCaLCXTd5qAu82AjTcgP1UJmytkOKb63Ypde7raDIg==}
    engines: {node: '>=0.6'}
    dependencies:
      graceful-fs: 4.2.10
      inherits: 2.0.4
      mkdirp: 0.5.6
      rimraf: 2.7.1

  /function-bind/1.1.1:
    resolution: {integrity: sha512-yIovAzMX49sF8Yl58fSCWJ5svSLuaibPxXQJFLmBObTuCr0Mf1KiPopGM9NiFjiYBCbfaa2Fh6breQ6ANVTI0A==}

  /function.prototype.name/1.1.5:
    resolution: {integrity: sha1-zOBQX+H/uAUD5vnkbMZORqEqliE=}
    engines: {node: '>= 0.4'}
    dependencies:
      call-bind: 1.0.2
      define-properties: 1.1.4
      es-abstract: 1.20.4
      functions-have-names: 1.2.3
    dev: false

  /functional-red-black-tree/1.0.1:
    resolution: {integrity: sha1-GwqzvVU7Kg1jmdKcDj6gslIHgyc=}
    dev: false

  /functions-have-names/1.2.3:
    resolution: {integrity: sha512-xckBUXyTIqT97tq2x2AMb+g163b5JFysYk0x4qxNFwbfQkmNZoiRHb6sPzI9/QV33WeuvVYBUIiD4NzNIyqaRQ==}
    dev: false

  /gauge/2.7.4:
    resolution: {integrity: sha1-LANAXHU4w51+s3sxcCLjJfsBi/c=}
    dependencies:
      aproba: 1.2.0
      console-control-strings: 1.1.0
      has-unicode: 2.0.1
      object-assign: 4.1.1
      signal-exit: 3.0.7
      string-width: 1.0.2
      strip-ansi: 3.0.1
      wide-align: 1.1.5

  /gaze/1.1.3:
    resolution: {integrity: sha512-BRdNm8hbWzFzWHERTrejLqwHDfS4GibPoq5wjTPIoJHoBtKGPg3xAFfxmM+9ztbXelxcf2hwQcaz1PtmFeue8g==}
    engines: {node: '>= 4.0.0'}
    dependencies:
      globule: 1.3.4

  /gensync/1.0.0-beta.2:
    resolution: {integrity: sha512-3hN7NaskYvMDLQY55gnW3NQ+mesEAepTqlg+VEbj7zzqEMBVNhzcGYYeqFo/TlYz6eQiFcp1HcsCZO+nGgS8zg==}
    engines: {node: '>=6.9.0'}

  /get-caller-file/1.0.3:
    resolution: {integrity: sha512-3t6rVToeoZfYSGd8YoLFR2DJkiQrIiUrGcjvFX2mDw3bn6k2OtwHN0TNCLbBO+w8qTvimhDkv+LSscbJY1vE6w==}
    dev: false

  /get-caller-file/2.0.5:
    resolution: {integrity: sha512-DyFP3BM/3YHTQOCUL/w0OZHR0lpKeGrxotcHWcqNEdnltqFwXVfhEBQ94eIo34AfQpo0rGki4cyIiftY06h2Fg==}
    engines: {node: 6.* || 8.* || >= 10.*}

  /get-intrinsic/1.1.3:
    resolution: {integrity: sha512-QJVz1Tj7MS099PevUG5jvnt9tSkXN8K14dxQlikJuPt4uD9hHAHjLyLBiLR5zELelBdD9QNRAXZzsJx0WaDL9A==}
    dependencies:
      function-bind: 1.1.1
      has: 1.0.3
      has-symbols: 1.0.3
    dev: false

  /get-own-enumerable-property-symbols/3.0.2:
    resolution: {integrity: sha512-I0UBV/XOz1XkIJHEUDMZAbzCThU/H8DxmSfmdGcKPnVhu2VfFqr34jr9777IyaTYvxjedWhqVIilEDsCdP5G6g==}
    dev: false

  /get-stdin/4.0.1:
    resolution: {integrity: sha1-uWjGsKBDhDJJAui/Gl3zJXmkUP4=}
    engines: {node: '>=0.10.0'}

  /get-stdin/8.0.0:
    resolution: {integrity: sha512-sY22aA6xchAzprjyqmSEQv4UbAAzRN0L2dQB0NlN5acTTK9Don6nhoc3eAbUnpZiCANAMfd/+40kVdKfFygohg==}
    engines: {node: '>=10'}
    dev: true

  /get-stream/4.1.0:
    resolution: {integrity: sha512-GMat4EJ5161kIy2HevLlr4luNjBgvmj413KaQA7jt4V8B4RDsfpHk7WQ9GVqfYyyx8OS/L66Kox+rJRNklLK7w==}
    engines: {node: '>=6'}
    dependencies:
      pump: 3.0.0
    dev: false

  /get-symbol-description/1.0.0:
    resolution: {integrity: sha1-f9uByQAQH71WTdXxowr1qtweWNY=}
    engines: {node: '>= 0.4'}
    dependencies:
      call-bind: 1.0.2
      get-intrinsic: 1.1.3
    dev: false

  /get-value/2.0.6:
    resolution: {integrity: sha1-3BXKHGcjh8p2vTesCjlbogQqLCg=}
    engines: {node: '>=0.10.0'}
    dev: false

  /getpass/0.1.7:
    resolution: {integrity: sha1-Xv+OPmhNVprkyysSgmBOi6YhSfo=}
    dependencies:
      assert-plus: 1.0.0

  /glob-parent/3.1.0:
    resolution: {integrity: sha1-nmr2KZ2NO9K9QEMIMr0RPfkGxa4=}
    dependencies:
      is-glob: 3.1.0
      path-dirname: 1.0.2
    dev: false

  /glob-parent/5.1.2:
    resolution: {integrity: sha512-AOIgSQCepiJYwP3ARnGx+5VnTu2HBYdzbGP45eLw1vr3zB3vZLeyed1sC9hnbcOc9/SrMyM5RPQrkGz4aS9Zow==}
    engines: {node: '>= 6'}
    dependencies:
      is-glob: 4.0.3

  /glob-to-regexp/0.3.0:
    resolution: {integrity: sha1-jFoUlNIGbFcMw7/kSWF1rMTVAqs=}
    dev: false

  /glob/7.1.7:
    resolution: {integrity: sha512-OvD9ENzPLbegENnYP5UUfJIirTg4+XwMWGaQfQTY0JenxNvvIKP3U3/tAQSPIu/lHxXYSZmpXlUHeqAIdKzBLQ==}
    dependencies:
      fs.realpath: 1.0.0
      inflight: 1.0.6
      inherits: 2.0.4
      minimatch: 3.0.8
      once: 1.4.0
      path-is-absolute: 1.0.1

  /glob/7.2.3:
    resolution: {integrity: sha512-nFR0zLpU2YCaRxwoCJvL6UvCH2JFyFVIvwTLsIf21AuHlMskA1hhTdk+LlYJtOlYt9v6dvszD2BGRqBL+iQK9Q==}
    dependencies:
      fs.realpath: 1.0.0
      inflight: 1.0.6
      inherits: 2.0.4
      minimatch: 3.1.2
      once: 1.4.0
      path-is-absolute: 1.0.1

  /global-modules/2.0.0:
    resolution: {integrity: sha512-NGbfmJBp9x8IxyJSd1P+otYK8vonoJactOogrVfFRIAEY1ukil8RSKDz2Yo7wh1oihl51l/r6W4epkeKJHqL8A==}
    engines: {node: '>=6'}
    dependencies:
      global-prefix: 3.0.0

  /global-prefix/3.0.0:
    resolution: {integrity: sha512-awConJSVCHVGND6x3tmMaKcQvwXLhjdkmomy2W+Goaui8YPgYgXJZewhg3fWC+DlfqqQuWg8AwqjGTD2nAPVWg==}
    engines: {node: '>=6'}
    dependencies:
      ini: 1.3.8
      kind-of: 6.0.3
      which: 1.3.1

  /globals/11.12.0:
    resolution: {integrity: sha512-WOBp/EEGUiIsJSp7wcv/y6MO+lV9UoncWqxuFfm8eBwzWNgyfBd6Gz+IeKQ9jCmyhoH99g15M3T+QaVHFjizVA==}
    engines: {node: '>=4'}

  /globals/12.4.0:
    resolution: {integrity: sha512-BWICuzzDvDoH54NHKCseDanAhE3CeDorgDL5MT6LMXXj2WCnd9UC2szdk4AWLfjdgNBCXLUanXYcpBBKOSWGwg==}
    engines: {node: '>=8'}
    dependencies:
      type-fest: 0.8.1
    dev: false

  /globby/11.1.0:
    resolution: {integrity: sha512-jhIXaOzy1sb8IyocaruWSn1TjmnBVs8Ayhcy83rmxNJ8q2uWKCAj3CnJY+KpGSXCueAPc0i05kVvVKtP1t9S3g==}
    engines: {node: '>=10'}
    dependencies:
      array-union: 2.1.0
      dir-glob: 3.0.1
      fast-glob: 3.2.12
      ignore: 5.2.0
      merge2: 1.4.1
      slash: 3.0.0
    dev: true

  /globby/6.1.0:
    resolution: {integrity: sha1-9abXDoOV4hyFj7BInWTfAkJNUGw=}
    engines: {node: '>=0.10.0'}
    dependencies:
      array-union: 1.0.2
      glob: 7.2.3
      object-assign: 4.1.1
      pify: 2.3.0
      pinkie-promise: 2.0.1
    dev: false

  /globby/8.0.2:
    resolution: {integrity: sha512-yTzMmKygLp8RUpG1Ymu2VXPSJQZjNAZPD4ywgYEaG7e4tBJeUQBO8OpXrf1RCNcEs5alsoJYPAMiIHP0cmeC7w==}
    engines: {node: '>=4'}
    dependencies:
      array-union: 1.0.2
      dir-glob: 2.0.0
      fast-glob: 2.2.7
      glob: 7.2.3
      ignore: 3.3.10
      pify: 3.0.0
      slash: 1.0.0
    transitivePeerDependencies:
      - supports-color
    dev: false

  /globjoin/0.1.4:
    resolution: {integrity: sha1-L0SUrIkZ43Z8XLtpHp9GMyQoXUM=}
    dev: true

  /globule/1.3.4:
    resolution: {integrity: sha512-OPTIfhMBh7JbBYDpa5b+Q5ptmMWKwcNcFSR/0c6t8V4f3ZAVBEsKNY37QdVqmLRYSMhOUGYrY0QhSoEpzGr/Eg==}
    engines: {node: '>= 0.10'}
    dependencies:
      glob: 7.1.7
      lodash: 4.17.21
      minimatch: 3.0.8

  /gonzales-pe/4.3.0:
    resolution: {integrity: sha512-otgSPpUmdWJ43VXyiNgEYE4luzHCL2pz4wQ0OnDluC6Eg4Ko3Vexy/SrSynglw/eR+OhkzmqFCZa/OFa/RgAOQ==}
    engines: {node: '>=0.6.0'}
    hasBin: true
    dependencies:
      minimist: 1.2.7
    dev: true

  /good-listener/1.2.2:
    resolution: {integrity: sha1-1TswzfkxPf+33JoNR3CWqm0UXFA=}
    dependencies:
      delegate: 3.2.0
    dev: false

  /graceful-fs/4.2.10:
    resolution: {integrity: sha512-9ByhssR2fPVsNZj478qUUbKfmL0+t5BDVyjShtyZZLiK7ZDAArFFfopyOTj0M05wE2tJPisA4iTnnXl2YoPvOA==}

  /growly/1.3.0:
    resolution: {integrity: sha1-8QdIy+dq+WS3yWyTxrzCivEgwIE=}
    dev: false

  /gzip-size/5.1.1:
    resolution: {integrity: sha512-FNHi6mmoHvs1mxZAds4PpdCS6QG8B4C1krxJsMutgxl5t3+GlRTzzI3NEkifXx2pVsOvJdOGSmIgDhQ55FwdPA==}
    engines: {node: '>=6'}
    dependencies:
      duplexer: 0.1.2
      pify: 4.0.1
    dev: false

  /handle-thing/2.0.1:
    resolution: {integrity: sha512-9Qn4yBxelxoh2Ow62nP+Ka/kMnOXRi8BXnRaUwezLNhqelnN49xKz4F/dPP8OYLxLxq6JDtZb2i9XznUQbNPTg==}
    dev: false

  /har-schema/2.0.0:
    resolution: {integrity: sha1-qUwiJOvKwEeCoNkDVSHyRzW37JI=}
    engines: {node: '>=4'}

  /har-validator/5.1.5:
    resolution: {integrity: sha512-nmT2T0lljbxdQZfspsno9hgrG3Uir6Ks5afism62poxqBM6sDnMEuPmzTq8XN0OEwqKLLdh1jQI3qyE66Nzb3w==}
    engines: {node: '>=6'}
    deprecated: this library is no longer supported
    dependencies:
      ajv: 6.12.6
      har-schema: 2.0.0

  /hard-rejection/2.1.0:
    resolution: {integrity: sha512-VIZB+ibDhx7ObhAe7OVtoEbuP4h/MuOTHJ+J8h/eBXotJYl0fBgR72xDFCKgIh22OJZIOVNxBMWuhAr10r8HdA==}
    engines: {node: '>=6'}
    dev: true

  /harmony-reflect/1.6.2:
    resolution: {integrity: sha512-HIp/n38R9kQjDEziXyDTuW3vvoxxyxjxFzXLrBr18uB47GnSt+G9D29fqrpM5ZkspMcPICud3XsBJQ4Y2URg8g==}
    dev: false

  /has-ansi/2.0.0:
    resolution: {integrity: sha1-NPUEnOHs3ysGSa8+8k5F7TVBbZE=}
    engines: {node: '>=0.10.0'}
    dependencies:
      ansi-regex: 2.1.1

  /has-bigints/1.0.2:
    resolution: {integrity: sha512-tSvCKtBr9lkF0Ex0aQiP9N+OpV4zi2r/Nee5VkRDbaqv35RLYMzbwQfFSZZH0kR+Rd6302UJZ2p/bJCEoR3VoQ==}
    dev: false

  /has-flag/3.0.0:
    resolution: {integrity: sha1-tdRU3CGZriJWmfNGfloH87lVuv0=}
    engines: {node: '>=4'}

  /has-flag/4.0.0:
    resolution: {integrity: sha512-EykJT/Q1KjTWctppgIAgfSO0tKVuZUjhgMr17kqTumMl6Afv3EISleU7qZUzoXDFTAHTDC4NOoG/ZxU3EvlMPQ==}
    engines: {node: '>=8'}

  /has-property-descriptors/1.0.0:
    resolution: {integrity: sha512-62DVLZGoiEBDHQyqG4w9xCuZ7eJEwNmJRWw2VY84Oedb7WFcA27fiEVe8oUQx9hAUJ4ekurquucTGwsyO1XGdQ==}
    dependencies:
      get-intrinsic: 1.1.3
    dev: false

  /has-symbols/1.0.3:
    resolution: {integrity: sha512-l3LCuF6MgDNwTDKkdYGEihYjt5pRPbEg46rtlmnSPlUbgmB8LOIrKJbYYFBSbnPaJexMKtiPO8hmeRjRz2Td+A==}
    engines: {node: '>= 0.4'}
    dev: false

  /has-tostringtag/1.0.0:
    resolution: {integrity: sha512-kFjcSNhnlGV1kyoGk7OXKSawH5JOb/LzUc5w9B02hOTO0dfFRjbHQKvg1d6cf3HbeUmtU9VbbV3qzZ2Teh97WQ==}
    engines: {node: '>= 0.4'}
    dependencies:
      has-symbols: 1.0.3
    dev: false

  /has-unicode/2.0.1:
    resolution: {integrity: sha1-4Ob+aijPUROIVeCG0Wkedx3iqLk=}

  /has-value/0.3.1:
    resolution: {integrity: sha1-ex9YutpiyoJ+wKIHgCVlSEWZXh8=}
    engines: {node: '>=0.10.0'}
    dependencies:
      get-value: 2.0.6
      has-values: 0.1.4
      isobject: 2.1.0
    dev: false

  /has-value/1.0.0:
    resolution: {integrity: sha1-GLKB2lhbHFxR3vJMkw7SmgvmsXc=}
    engines: {node: '>=0.10.0'}
    dependencies:
      get-value: 2.0.6
      has-values: 1.0.0
      isobject: 3.0.1
    dev: false

  /has-values/0.1.4:
    resolution: {integrity: sha1-bWHeldkd/Km5oCCJrThL/49it3E=}
    engines: {node: '>=0.10.0'}
    dev: false

  /has-values/1.0.0:
    resolution: {integrity: sha1-lbC2P+whRmGab+V/51Yo1aOe/k8=}
    engines: {node: '>=0.10.0'}
    dependencies:
      is-number: 3.0.0
      kind-of: 4.0.0
    dev: false

  /has/1.0.3:
    resolution: {integrity: sha512-f2dvO0VU6Oej7RkWJGrehjbzMAjFp5/VKPp5tTpWIV4JHHZK1/BxbFRtf/siA2SWTe09caDmVtYYzWEIbBS4zw==}
    engines: {node: '>= 0.4.0'}
    dependencies:
      function-bind: 1.1.1

  /hash-base/3.1.0:
    resolution: {integrity: sha512-1nmYp/rhMDiE7AYkDw+lLwlAzz0AntGIe51F3RfFfEqyQ3feY2eI/NcwC6umIQVOASPMsWJLJScWKSSvzL9IVA==}
    engines: {node: '>=4'}
    dependencies:
      inherits: 2.0.4
      readable-stream: 3.6.0
      safe-buffer: 5.2.1
    dev: false

  /hash.js/1.1.7:
    resolution: {integrity: sha512-taOaskGt4z4SOANNseOviYDvjEJinIkRgmp7LbKP2YTTmVxWBl87s/uzK9r+44BclBSp2X7K1hqeNfz9JbBeXA==}
    dependencies:
      inherits: 2.0.4
      minimalistic-assert: 1.0.1
    dev: false

  /he/1.2.0:
    resolution: {integrity: sha512-F/1DnUGPopORZi0ni+CvrCgHQ5FyEAHRLSApuYWMmrbSwoN2Mn/7k+Gl38gJnR7yyDZk6WLXwiGod1JOWNDKGw==}
    hasBin: true
    dev: false

  /hex-color-regex/1.1.0:
    resolution: {integrity: sha512-l9sfDFsuqtOqKDsQdqrMRk0U85RZc0RtOR9yPI7mRVOa4FsR/BVnZ0shmQRM96Ji99kYZP/7hn1cedc1+ApsTQ==}
    dev: false

  /history/4.10.1:
    resolution: {integrity: sha512-36nwAD620w12kuzPAsyINPWJqlNbij+hpK1k9XRloDtym8mxzGYl2c17LnV6IAGB2Dmg4tEa7G7DlawS0+qjew==}
    dependencies:
      '@babel/runtime': 7.19.4
      loose-envify: 1.4.0
      resolve-pathname: 3.0.0
      tiny-invariant: 1.3.1
      tiny-warning: 1.0.3
      value-equal: 1.0.1
    dev: false

  /hmac-drbg/1.0.1:
    resolution: {integrity: sha1-0nRXAQJabHdabFRXk+1QL8DGSaE=}
    dependencies:
      hash.js: 1.1.7
      minimalistic-assert: 1.0.1
      minimalistic-crypto-utils: 1.0.1
    dev: false

  /hoist-non-react-statics/3.3.2:
    resolution: {integrity: sha512-/gGivxi8JPKWNm/W0jSmzcMPpfpPLc3dY/6GxhX2hQ9iGj3aDfklV4ET7NjKpSinLpJ5vafa9iiGIEZg10SfBw==}
    dependencies:
      react-is: 16.13.1
    dev: false

  /hosted-git-info/2.8.9:
    resolution: {integrity: sha512-mxIDAb9Lsm6DoOJ7xH+5+X4y1LU/4Hi50L9C5sIswK3JzULS4bwk1FvjdBgvYR4bzT4tuUQiC15FE2f5HbLvYw==}

  /hosted-git-info/4.1.0:
    resolution: {integrity: sha512-kyCuEOWjJqZuDbRHzL8V93NzQhwIB71oFWSyzVo+KPZI+pnQPPxucdkrOZvkLRnrf5URsQM+IJ09Dw29cRALIA==}
    engines: {node: '>=10'}
    dependencies:
      lru-cache: 6.0.0
    dev: true

  /hpack.js/2.1.6:
    resolution: {integrity: sha1-h3dMCUnlE/QuhFdbPEVoH63ioLI=}
    dependencies:
      inherits: 2.0.4
      obuf: 1.1.2
      readable-stream: 2.3.7
      wbuf: 1.7.3
    dev: false

  /hsl-regex/1.0.0:
    resolution: {integrity: sha1-1JMwx4ntgZ4nakwNJy3/owsY/m4=}
    dev: false

  /hsla-regex/1.0.0:
    resolution: {integrity: sha1-wc56MWjIxmFAM6S194d/OyJfnDg=}
    dev: false

  /html-encoding-sniffer/1.0.2:
    resolution: {integrity: sha512-71lZziiDnsuabfdYiUeWdCVyKuqwWi23L8YeIgV9jSSZHCtb6wB1BKWooH7L3tn4/FuZJMVWyNaIDr4RGmaSYw==}
    dependencies:
      whatwg-encoding: 1.0.5
    dev: false

  /html-entities/1.4.0:
    resolution: {integrity: sha512-8nxjcBcd8wovbeKx7h3wTji4e6+rhaVuPNpMqwWgnHh+N9ToqsCs6XztWRBPQ+UtzsoMAdKZtUENoVzU/EMtZA==}
    dev: false

  /html-escaper/2.0.2:
    resolution: {integrity: sha512-H2iMtd0I4Mt5eYiapRdIDjp+XzelXQ0tFE4JS7YFwFevXXMmOp9myNrUvCg0D6ws8iqkRPBfKHgbwig1SmlLfg==}
    dev: false

  /html-minifier-terser/5.1.1:
    resolution: {integrity: sha512-ZPr5MNObqnV/T9akshPKbVgyOqLmy+Bxo7juKCfTfnjNniTAMdy4hz21YQqoofMBJD2kdREaqPPdThoR78Tgxg==}
    engines: {node: '>=6'}
    hasBin: true
    dependencies:
      camel-case: 4.1.2
      clean-css: 4.2.4
      commander: 4.1.1
      he: 1.2.0
      param-case: 3.0.4
      relateurl: 0.2.7
      terser: 4.8.1
    dev: false

  /html-parse-stringify/3.0.1:
    resolution: {integrity: sha512-KknJ50kTInJ7qIScF3jeaFRpMpE8/lfiTdzf/twXyPBLAGrLRTmkz3AdTnKeh40X8k9L2fdYwEp/42WGXIRGcg==}
    dependencies:
      void-elements: 3.1.0
    dev: false

  /html-tags/3.2.0:
    resolution: {integrity: sha512-vy7ClnArOZwCnqZgvv+ddgHgJiAFXe3Ge9ML5/mBctVJoUoYPCdxVucOywjDARn6CVoh3dRSFdPHy2sX80L0Wg==}
    engines: {node: '>=8'}
    dev: true

  /html-webpack-plugin/4.0.0-beta.11_webpack@4.41.5:
    resolution: {integrity: sha512-4Xzepf0qWxf8CGg7/WQM5qBB2Lc/NFI7MhU59eUDTkuQp3skZczH4UA1d6oQyDEIoMDgERVhRyTdtUPZ5s5HBg==}
    engines: {node: '>=6.9'}
    peerDependencies:
      webpack: ^4.0.0
    dependencies:
      html-minifier-terser: 5.1.1
      loader-utils: 1.4.0
      lodash: 4.17.21
      pretty-error: 2.1.2
      tapable: 1.1.3
      util.promisify: 1.0.0
      webpack: 4.41.5
    dev: false

  /html2canvas/1.4.1:
    resolution: {integrity: sha512-fPU6BHNpsyIhr8yyMpTLLxAbkaK8ArIBcmZIRiBLiDhjeqvXolaEmDGmELFuX9I4xDcaKKcJl+TKZLqruBbmWA==}
    engines: {node: '>=8.0.0'}
    dependencies:
      css-line-break: 2.1.0
      text-segmentation: 1.0.3
    dev: false

  /htmlparser2/3.10.1:
    resolution: {integrity: sha512-IgieNijUMbkDovyoKObU1DUhm1iwNYE/fuifEoEHfd1oZKZDaONBSkal7Y01shxsM49R4XaMdGez3WnF9UfiCQ==}
    dependencies:
      domelementtype: 1.3.1
      domhandler: 2.4.2
      domutils: 1.7.0
      entities: 1.1.2
      inherits: 2.0.4
      readable-stream: 3.6.0
    dev: true

  /htmlparser2/6.1.0:
    resolution: {integrity: sha512-gyyPk6rgonLFEDGoeRgQNaEUvdJ4ktTmmUh/h2t7s+M8oPpIPxgNACWa+6ESR57kXstwqPiCut0V8NRpcwgU7A==}
    dependencies:
      domelementtype: 2.3.0
      domhandler: 4.3.1
      domutils: 2.8.0
      entities: 2.2.0
    dev: false

  /http-deceiver/1.2.7:
    resolution: {integrity: sha1-+nFolEq5pRnTN8sL7HKE3D5yPYc=}
    dev: false

  /http-errors/1.6.3:
    resolution: {integrity: sha1-i1VoC7S+KDoLW/TqLjhYC+HZMg0=}
    engines: {node: '>= 0.6'}
    dependencies:
      depd: 1.1.2
      inherits: 2.0.3
      setprototypeof: 1.1.0
      statuses: 1.5.0
    dev: false

  /http-errors/2.0.0:
    resolution: {integrity: sha512-FtwrG/euBzaEjYeRqOgly7G0qviiXoJWnvEH2Z1plBdXgbyjv34pHTSb9zoeHMyDy33+DWy5Wt9Wo+TURtOYSQ==}
    engines: {node: '>= 0.8'}
    dependencies:
      depd: 2.0.0
      inherits: 2.0.4
      setprototypeof: 1.2.0
      statuses: 2.0.1
      toidentifier: 1.0.1
    dev: false

  /http-parser-js/0.5.8:
    resolution: {integrity: sha512-SGeBX54F94Wgu5RH3X5jsDtf4eHyRogWX1XGT3b4HuW3tQPM4AaBzoUji/4AAJNXCEOWZ5O0DgZmJw1947gD5Q==}
    dev: false

  /http-proxy-middleware/0.19.1_tmpgdztspuwvsxzgjkhoqk7duq:
    resolution: {integrity: sha512-yHYTgWMQO8VvwNS22eLLloAkvungsKdKTLO8AJlftYIKNfJr3GK3zK0ZCfzDDGUBttdGc8xFy1mCitvNKQtC3Q==}
    engines: {node: '>=4.0.0'}
    dependencies:
      http-proxy: 1.18.1_debug@4.3.4
      is-glob: 4.0.3
      lodash: 4.17.21
      micromatch: 3.1.10_supports-color@6.1.0
    transitivePeerDependencies:
      - debug
      - supports-color
    dev: false

  /http-proxy-middleware/2.0.6:
    resolution: {integrity: sha512-ya/UeJ6HVBYxrgYotAZo1KvPWlgB48kUJLDePFeneHsVujFaW5WNj2NgWCAE//B1Dl02BIfYlpNgBy8Kf8Rjmw==}
    engines: {node: '>=12.0.0'}
    peerDependencies:
      '@types/express': ^4.17.13
    peerDependenciesMeta:
      '@types/express':
        optional: true
    dependencies:
      '@types/http-proxy': 1.17.9
      http-proxy: 1.18.1
      is-glob: 4.0.3
      is-plain-obj: 3.0.0
      micromatch: 4.0.5
    transitivePeerDependencies:
      - debug
    dev: true

  /http-proxy/1.18.1:
    resolution: {integrity: sha512-7mz/721AbnJwIVbnaSv1Cz3Am0ZLT/UBwkC92VlxhXv/k/BBQfM2fXElQNC27BVGr0uwUpplYPQM9LnaBMR5NQ==}
    engines: {node: '>=8.0.0'}
    dependencies:
      eventemitter3: 4.0.7
      follow-redirects: 1.15.2
      requires-port: 1.0.0
    transitivePeerDependencies:
      - debug
    dev: true

  /http-proxy/1.18.1_debug@4.3.4:
    resolution: {integrity: sha512-7mz/721AbnJwIVbnaSv1Cz3Am0ZLT/UBwkC92VlxhXv/k/BBQfM2fXElQNC27BVGr0uwUpplYPQM9LnaBMR5NQ==}
    engines: {node: '>=8.0.0'}
    dependencies:
      eventemitter3: 4.0.7
      follow-redirects: 1.15.2_debug@4.3.4
      requires-port: 1.0.0
    transitivePeerDependencies:
      - debug
    dev: false

  /http-signature/1.2.0:
    resolution: {integrity: sha1-muzZJRFHcvPZW2WmCruPfBj7rOE=}
    engines: {node: '>=0.8', npm: '>=1.3.7'}
    dependencies:
      assert-plus: 1.0.0
      jsprim: 1.4.2
      sshpk: 1.17.0

  /https-browserify/1.0.0:
    resolution: {integrity: sha1-7AbBDgo0wPL68Zn3/X/Hj//QPHM=}
    dev: false

  /i18next/19.9.2:
    resolution: {integrity: sha512-0i6cuo6ER6usEOtKajUUDj92zlG+KArFia0857xxiEHAQcUwh/RtOQocui1LPJwunSYT574Pk64aNva1kwtxZg==}
    dependencies:
      '@babel/runtime': 7.19.4
    dev: false

  /iconv-lite/0.4.24:
    resolution: {integrity: sha512-v3MXnZAcvnywkTUEZomIActle7RXXeedOR31wwl7VlyoXO4Qi9arvSenNQWne1TcRwhCL1HwLI21bEqdpj8/rA==}
    engines: {node: '>=0.10.0'}
    dependencies:
      safer-buffer: 2.1.2
    dev: false

  /iconv-lite/0.6.3:
    resolution: {integrity: sha512-4fCk79wshMdzMp2rH06qWrJE4iolqLhCUH+OiuIgU++RB0+94NlDL81atO7GX55uUKueo0txHNtvEyI6D7WdMw==}
    engines: {node: '>=0.10.0'}
    dependencies:
      safer-buffer: 2.1.2
    dev: false

  /icss-utils/4.1.1:
    resolution: {integrity: sha512-4aFq7wvWyMHKgxsH8QQtGpvbASCf+eM3wPRLI6R+MgAnTCZ6STYsRvttLvRWK0Nfif5piF394St3HeJDaljGPA==}
    engines: {node: '>= 6'}
    dependencies:
      postcss: 7.0.39
    dev: false

  /identity-obj-proxy/3.0.0:
    resolution: {integrity: sha1-lNK9qWCERT7zb7xarsN+D3nx/BQ=}
    engines: {node: '>=4'}
    dependencies:
      harmony-reflect: 1.6.2
    dev: false

  /ieee754/1.2.1:
    resolution: {integrity: sha512-dcyqhDvX1C46lXZcVqCpK+FtMRQVdIMN6/Df5js2zouUsqG7I6sFxitIC+7KYK29KdXOLHdu9zL4sFnoVQnqaA==}
    dev: false

  /iferr/0.1.5:
    resolution: {integrity: sha1-xg7taebY/bazEEofy8ocGS3FtQE=}
    dev: false

  /ignore/3.3.10:
    resolution: {integrity: sha512-Pgs951kaMm5GXP7MOvxERINe3gsaVjUWFm+UZPSq9xYriQAksyhg0csnS0KXSNRD5NmNdapXEpjxG49+AKh/ug==}
    dev: false

  /ignore/4.0.6:
    resolution: {integrity: sha512-cyFDKrqc/YdcWFniJhzI42+AzS+gNwmUzOSFcRCQYwySuBBBy/KjuxWLZ/FHEH6Moq1NizMOBWyTcv8O4OZIMg==}
    engines: {node: '>= 4'}
    dev: false

  /ignore/5.2.0:
    resolution: {integrity: sha512-CmxgYGiEPCLhfLnpPp1MoRmifwEIOgjcHXxOBjv7mY96c+eWScsOP9c112ZyLdWHi0FxHjI+4uVhKYp/gcdRmQ==}
    engines: {node: '>= 4'}
    dev: true

  /immer/1.10.0:
    resolution: {integrity: sha512-O3sR1/opvCDGLEVcvrGTMtLac8GJ5IwZC4puPrLuRj3l7ICKvkmA0vGuU9OW8mV9WIBRnaxp5GJh9IEAaNOoYg==}
    dev: false

  /import-cwd/2.1.0:
    resolution: {integrity: sha1-qmzzbnInYShcs3HsZRn1PiQ1sKk=}
    engines: {node: '>=4'}
    dependencies:
      import-from: 2.1.0
    dev: false

  /import-fresh/2.0.0:
    resolution: {integrity: sha1-2BNVwVYS04bGH53dOSLUMEgipUY=}
    engines: {node: '>=4'}
    dependencies:
      caller-path: 2.0.0
      resolve-from: 3.0.0
    dev: false

  /import-fresh/3.3.0:
    resolution: {integrity: sha512-veYYhQa+D1QBKznvhUHxb8faxlrwUnxseDAbAp457E0wLNio2bOSKnjYDhMj+YiAq61xrMGhQk9iXVk5FzgQMw==}
    engines: {node: '>=6'}
    dependencies:
      parent-module: 1.0.1
      resolve-from: 4.0.0

  /import-from/2.1.0:
    resolution: {integrity: sha1-M1238qev/VOqpHHUuAId7ja387E=}
    engines: {node: '>=4'}
    dependencies:
      resolve-from: 3.0.0
    dev: false

  /import-lazy/4.0.0:
    resolution: {integrity: sha512-rKtvo6a868b5Hu3heneU+L4yEQ4jYKLtjpnPeUdK7h0yzXGmyBTypknlkCvHFBqfX9YlorEiMM6Dnq/5atfHkw==}
    engines: {node: '>=8'}
    dev: true

  /import-local/2.0.0:
    resolution: {integrity: sha512-b6s04m3O+s3CGSbqDIyP4R6aAwAeYlVq9+WUWep6iHa8ETRf9yei1U48C5MmfJmV9AiLYYBKPMq/W+/WRpQmCQ==}
    engines: {node: '>=6'}
    hasBin: true
    dependencies:
      pkg-dir: 3.0.0
      resolve-cwd: 2.0.0
    dev: false

  /imurmurhash/0.1.4:
    resolution: {integrity: sha1-khi5srkoojixPcT7a21XbyMUU+o=}
    engines: {node: '>=0.8.19'}

  /in-publish/2.0.1:
    resolution: {integrity: sha512-oDM0kUSNFC31ShNxHKUyfZKy8ZeXZBWMjMdZHKLOk13uvT27VTL/QzRGfRUcevJhpkZAvlhPYuXkF7eNWrtyxQ==}
    hasBin: true

  /indent-string/2.1.0:
    resolution: {integrity: sha1-ji1INIdCEhtKghi3oTfppSBJ3IA=}
    engines: {node: '>=0.10.0'}
    dependencies:
      repeating: 2.0.1

  /indent-string/4.0.0:
    resolution: {integrity: sha512-EdDDZu4A2OyIK7Lr/2zG+w5jmbuk1DVBnEwREQvBzspBJkCEbRa8GxU1lghYcaGJCnRWibjDXlq779X1/y5xwg==}
    engines: {node: '>=8'}

  /indexes-of/1.0.1:
    resolution: {integrity: sha1-8w9xbI4r00bHtn0985FVZqfAVgc=}
    dev: false

  /infer-owner/1.0.4:
    resolution: {integrity: sha512-IClj+Xz94+d7irH5qRyfJonOdfTzuDaifE6ZPWfx0N0+/ATZCbuTPq2prFl526urkQd90WyUKIh1DfBQ2hMz9A==}
    dev: false

  /inflight/1.0.6:
    resolution: {integrity: sha1-Sb1jMdfQLQwJvJEKEHW6gWW1bfk=}
    dependencies:
      once: 1.4.0
      wrappy: 1.0.2

  /inherits/2.0.1:
    resolution: {integrity: sha1-sX0I0ya0Qj5Wjv9xn5GwscvfafE=}
    dev: false

  /inherits/2.0.3:
    resolution: {integrity: sha1-Yzwsg+PaQqUC9SRmAiSA9CCCYd4=}
    dev: false

  /inherits/2.0.4:
    resolution: {integrity: sha512-k/vGaX4/Yla3WzyMCvTQOXYeIHvqOKtnqBduzTHpzpQZzAskKMhZ2K+EnBiSM9zGSoIFeMpXKxa4dYeZIQqewQ==}

  /ini/1.3.8:
    resolution: {integrity: sha512-JV/yugV2uzW5iMRSiZAyDtQd+nxtUnjeLt0acNdw98kKLrvuRVyB80tsREOE7yvGVgalhZ6RNXCmEHkUKBKxew==}

  /inquirer/7.0.4:
    resolution: {integrity: sha512-Bu5Td5+j11sCkqfqmUTiwv+tWisMtP0L7Q8WrqA2C/BbBhy1YTdFrvjjlrKq8oagA/tLQBski2Gcx/Sqyi2qSQ==}
    engines: {node: '>=6.0.0'}
    dependencies:
      ansi-escapes: 4.3.2
      chalk: 2.4.2
      cli-cursor: 3.1.0
      cli-width: 2.2.1
      external-editor: 3.1.0
      figures: 3.2.0
      lodash: 4.17.21
      mute-stream: 0.0.8
      run-async: 2.4.1
      rxjs: 6.6.7
      string-width: 4.2.3
      strip-ansi: 5.2.0
      through: 2.3.8
    dev: false

  /inquirer/7.3.3:
    resolution: {integrity: sha512-JG3eIAj5V9CwcGvuOmoo6LB9kbAYT8HXffUl6memuszlwDC/qvFAJw49XJ5NROSFNPxp3iQg1GqkFhaY/CR0IA==}
    engines: {node: '>=8.0.0'}
    dependencies:
      ansi-escapes: 4.3.2
      chalk: 4.1.2
      cli-cursor: 3.1.0
      cli-width: 3.0.0
      external-editor: 3.1.0
      figures: 3.2.0
      lodash: 4.17.21
      mute-stream: 0.0.8
      run-async: 2.4.1
      rxjs: 6.6.7
      string-width: 4.2.3
      strip-ansi: 6.0.1
      through: 2.3.8
    dev: false

  /internal-ip/4.3.0:
    resolution: {integrity: sha512-S1zBo1D6zcsyuC6PMmY5+55YMILQ9av8lotMx447Bq6SAgo/sDK6y6uUKmuYhW7eacnIhFfsPmCNYdDzsnnDCg==}
    engines: {node: '>=6'}
    dependencies:
      default-gateway: 4.2.0
      ipaddr.js: 1.9.1
    dev: false

  /internal-slot/1.0.3:
    resolution: {integrity: sha512-O0DB1JC/sPyZl7cIo78n5dR7eUSwwpYPiXRhTzNxZVAMUuB8vlnRFyLxdrVToks6XPLVnFfbzaVd5WLjhgg+vA==}
    engines: {node: '>= 0.4'}
    dependencies:
      get-intrinsic: 1.1.3
      has: 1.0.3
      side-channel: 1.0.4
    dev: false

  /invariant/2.2.4:
    resolution: {integrity: sha512-phJfQVBuaJM5raOpJjSfkiD6BpbCE4Ns//LaXl6wGYtUBY83nWS6Rf9tXm2e8VaK60JEjYldbPif/A2B1C2gNA==}
    dependencies:
      loose-envify: 1.4.0
    dev: false

  /invert-kv/2.0.0:
    resolution: {integrity: sha512-wPVv/y/QQ/Uiirj/vh3oP+1Ww+AWehmi1g5fFWGPF6IpCBCDVrhgHRMvrLfdYcwDh3QJbGXDW4JAuzxElLSqKA==}
    engines: {node: '>=4'}
    dev: false

  /ip-regex/2.1.0:
    resolution: {integrity: sha1-+ni/XS5pE8kRzp+BnuUUa7bYROk=}
    engines: {node: '>=4'}
    dev: false

  /ip/1.1.8:
    resolution: {integrity: sha512-PuExPYUiu6qMBQb4l06ecm6T6ujzhmh+MeJcW9wa89PoAz5pvd4zPgN5WJV104mb6S2T1AwNIAaB70JNrLQWhg==}
    dev: false

  /ipaddr.js/1.9.1:
    resolution: {integrity: sha512-0KI/607xoxSToH7GjN1FfSbLoU0+btTicjsQSWQlh/hZykN8KpmMf7uYwPW3R+akZ6R/w18ZlXSHBYXiYUPO3g==}
    engines: {node: '>= 0.10'}
    dev: false

  /is-absolute-url/2.1.0:
    resolution: {integrity: sha1-UFMN+4T8yap9vnhS6Do3uTufKqY=}
    engines: {node: '>=0.10.0'}
    dev: false

  /is-absolute-url/3.0.3:
    resolution: {integrity: sha512-opmNIX7uFnS96NtPmhWQgQx6/NYFgsUXYMllcfzwWKUMwfo8kku1TvE6hkNcH+Q1ts5cMVrsY7j0bxXQDciu9Q==}
    engines: {node: '>=8'}
    dev: false

  /is-accessor-descriptor/0.1.6:
    resolution: {integrity: sha1-qeEss66Nh2cn7u84Q/igiXtcmNY=}
    engines: {node: '>=0.10.0'}
    dependencies:
      kind-of: 3.2.2
    dev: false

  /is-accessor-descriptor/1.0.0:
    resolution: {integrity: sha512-m5hnHTkcVsPfqx3AKlyttIPb7J+XykHvJP2B9bZDjlhLIoEq4XoK64Vg7boZlVWYK6LUY94dYPEE7Lh0ZkZKcQ==}
    engines: {node: '>=0.10.0'}
    dependencies:
      kind-of: 6.0.3
    dev: false

  /is-alphabetical/1.0.4:
    resolution: {integrity: sha512-DwzsA04LQ10FHTZuL0/grVDk4rFoVH1pjAToYwBrHSxcrBIGQuXrQMtD5U1b0U2XVgKZCTLLP8u2Qxqhy3l2Vg==}
    dev: true

  /is-alphanumerical/1.0.4:
    resolution: {integrity: sha512-UzoZUr+XfVz3t3v4KyGEniVL9BDRoQtY7tOyrRybkVNjDFWyo1yhXNGrrBTQxp3ib9BLAWs7k2YKBQsFRkZG9A==}
    dependencies:
      is-alphabetical: 1.0.4
      is-decimal: 1.0.4
    dev: true

  /is-arguments/1.1.1:
    resolution: {integrity: sha512-8Q7EARjzEnKpt/PCD7e1cgUS0a6X8u5tdSiMqXhojOdoV9TsMsiO+9VLC5vAmO8N7/GmXn7yjR8qnA6bVAEzfA==}
    engines: {node: '>= 0.4'}
    dependencies:
      call-bind: 1.0.2
      has-tostringtag: 1.0.0
    dev: false

  /is-arrayish/0.2.1:
    resolution: {integrity: sha1-d8mYQFJ6qOyxqLppe4BkWnqSap0=}

  /is-arrayish/0.3.2:
    resolution: {integrity: sha512-eVRqCvVlZbuw3GrM63ovNSNAeA1K16kaR/LRY/92w0zxQ5/1YzwblUX652i4Xs9RwAGjW9d9y6X88t8OaAJfWQ==}
    dev: false

  /is-bigint/1.0.4:
    resolution: {integrity: sha512-zB9CruMamjym81i2JZ3UMn54PKGsQzsJeo6xvN3HJJ4CAsQNB6iRutp2To77OfCNuoxspsIhzaPoO1zyCEhFOg==}
    dependencies:
      has-bigints: 1.0.2
    dev: false

  /is-binary-path/1.0.1:
    resolution: {integrity: sha1-dfFmQrSA8YenEcgUFh/TpKdlWJg=}
    engines: {node: '>=0.10.0'}
    dependencies:
      binary-extensions: 1.13.1
    dev: false

  /is-binary-path/2.1.0:
    resolution: {integrity: sha512-ZMERYes6pDydyuGidse7OsHxtbI7WVeUEozgR/g7rd0xUimYNlvZRE/K2MgZTjWy725IfelLeVcEM97mmtRGXw==}
    engines: {node: '>=8'}
    dependencies:
      binary-extensions: 2.2.0
    dev: false

  /is-boolean-object/1.1.2:
    resolution: {integrity: sha512-gDYaKHJmnj4aWxyj6YHyXVpdQawtVLHU5cb+eztPGczf6cjuTdwve5ZIEfgXqH4e57An1D1AKf8CZ3kYrQRqYA==}
    engines: {node: '>= 0.4'}
    dependencies:
      call-bind: 1.0.2
      has-tostringtag: 1.0.0
    dev: false

  /is-buffer/1.1.6:
    resolution: {integrity: sha512-NcdALwpXkTm5Zvvbk7owOUSvVvBKDgKP5/ewfXEznmQFfs4ZRmanOeKBTjRVjka3QFoN6XJ+9F3USqfHqTaU5w==}
    dev: false

  /is-buffer/2.0.5:
    resolution: {integrity: sha512-i2R6zNFDwgEHJyQUtJEk0XFi1i0dPFn/oqjK3/vPCcDeJvW5NQ83V8QbicfF1SupOaB0h8ntgBC2YiE7dfyctQ==}
    engines: {node: '>=4'}

  /is-callable/1.2.7:
    resolution: {integrity: sha512-1BC0BVFhS/p0qtw6enp8e+8OD0UrK0oFLztSjNzhcKA3WDuJxxAPXzPuPtKkjEY9UUoEWlX/8fgKeu2S8i9JTA==}
    engines: {node: '>= 0.4'}
    dev: false

  /is-ci/2.0.0:
    resolution: {integrity: sha512-YfJT7rkpQB0updsdHLGWrvhBJfcfzNNawYDNIyQXJz0IViGf75O8EBPKSdvw2rF+LGCsX4FZ8tcr3b19LcZq4w==}
    hasBin: true
    dependencies:
      ci-info: 2.0.0
    dev: false

  /is-color-stop/1.1.0:
    resolution: {integrity: sha1-z/9HGu5N1cnhWFmPvhKWe1za00U=}
    dependencies:
      css-color-names: 0.0.4
      hex-color-regex: 1.1.0
      hsl-regex: 1.0.0
      hsla-regex: 1.0.0
      rgb-regex: 1.0.1
      rgba-regex: 1.0.0
    dev: false

  /is-core-module/2.11.0:
    resolution: {integrity: sha512-RRjxlvLDkD1YJwDbroBHMb+cukurkDWNyHx7D3oNB5x9rb5ogcksMC5wHCadcXoo67gVr/+3GFySh3134zi6rw==}
    dependencies:
      has: 1.0.3

  /is-data-descriptor/0.1.4:
    resolution: {integrity: sha1-C17mSDiOLIYCgueT8YVv7D8wG1Y=}
    engines: {node: '>=0.10.0'}
    dependencies:
      kind-of: 3.2.2
    dev: false

  /is-data-descriptor/1.0.0:
    resolution: {integrity: sha512-jbRXy1FmtAoCjQkVmIVYwuuqDFUbaOeDjmed1tOGPrsMhtJA4rD9tkgA0F1qJ3gRFRXcHYVkdeaP50Q5rE/jLQ==}
    engines: {node: '>=0.10.0'}
    dependencies:
      kind-of: 6.0.3
    dev: false

  /is-date-object/1.0.5:
    resolution: {integrity: sha512-9YQaSxsAiSwcvS33MBk3wTCVnWK+HhF8VZR2jRxehM16QcVOdHqPn4VPHmRK4lSr38n9JriurInLcP90xsYNfQ==}
    engines: {node: '>= 0.4'}
    dependencies:
      has-tostringtag: 1.0.0
    dev: false

  /is-decimal/1.0.4:
    resolution: {integrity: sha512-RGdriMmQQvZ2aqaQq3awNA6dCGtKpiDFcOzrTWrDAT2MiWrKQVPmxLGHl7Y2nNu6led0kEyoX0enY0qXYsv9zw==}
    dev: true

  /is-descriptor/0.1.6:
    resolution: {integrity: sha512-avDYr0SB3DwO9zsMov0gKCESFYqCnE4hq/4z3TdUlukEy5t9C0YRq7HLrsN52NAcqXKaepeCD0n+B0arnVG3Hg==}
    engines: {node: '>=0.10.0'}
    dependencies:
      is-accessor-descriptor: 0.1.6
      is-data-descriptor: 0.1.4
      kind-of: 5.1.0
    dev: false

  /is-descriptor/1.0.2:
    resolution: {integrity: sha512-2eis5WqQGV7peooDyLmNEPUrps9+SXX5c9pL3xEB+4e9HnGuDa7mB7kHxHw4CbqS9k1T2hOH3miL8n8WtiYVtg==}
    engines: {node: '>=0.10.0'}
    dependencies:
      is-accessor-descriptor: 1.0.0
      is-data-descriptor: 1.0.0
      kind-of: 6.0.3
    dev: false

  /is-directory/0.3.1:
    resolution: {integrity: sha1-YTObbyR1/Hcv2cnYP1yFddwVSuE=}
    engines: {node: '>=0.10.0'}
    dev: false

  /is-docker/2.2.1:
    resolution: {integrity: sha512-F+i2BKsFrH66iaUFc0woD8sLy8getkwTwtOBjvs56Cx4CgJDeKQeqfz8wAYiSb8JOprWhHH5p77PbmYCvvUuXQ==}
    engines: {node: '>=8'}
    hasBin: true
    dev: false

  /is-extendable/0.1.1:
    resolution: {integrity: sha1-YrEQ4omkcUGOPsNqYX1HLjAd/Ik=}
    engines: {node: '>=0.10.0'}
    dev: false

  /is-extendable/1.0.1:
    resolution: {integrity: sha512-arnXMxT1hhoKo9k1LZdmlNyJdDDfy2v0fXjFlmok4+i8ul/6WlbVge9bhM74OpNPQPMGUToDtz+KXa1PneJxOA==}
    engines: {node: '>=0.10.0'}
    dependencies:
      is-plain-object: 2.0.4
    dev: false

  /is-extglob/2.1.1:
    resolution: {integrity: sha1-qIwCU1eR8C7TfHahueqXc8gz+MI=}
    engines: {node: '>=0.10.0'}

  /is-finite/1.1.0:
    resolution: {integrity: sha512-cdyMtqX/BOqqNBBiKlIVkytNHm49MtMlYyn1zxzvJKWmFMlGzm+ry5BBfYyeY9YmNKbRSo/o7OX9w9ale0wg3w==}
    engines: {node: '>=0.10.0'}

  /is-fullwidth-code-point/1.0.0:
    resolution: {integrity: sha1-754xOG8DGn8NZDr4L95QxFfvAMs=}
    engines: {node: '>=0.10.0'}
    dependencies:
      number-is-nan: 1.0.1

  /is-fullwidth-code-point/2.0.0:
    resolution: {integrity: sha1-o7MKXE8ZkYMWeqq5O+764937ZU8=}
    engines: {node: '>=4'}

  /is-fullwidth-code-point/3.0.0:
    resolution: {integrity: sha512-zymm5+u+sCsSWyD9qNaejV3DFvhCKclKdizYaJUuHA83RLjb7nSuGnddCHGv0hk+KY7BMAlsWeK4Ueg6EV6XQg==}
    engines: {node: '>=8'}

  /is-generator-fn/2.1.0:
    resolution: {integrity: sha512-cTIB4yPYL/Grw0EaSzASzg6bBy9gqCofvWN8okThAYIxKJZC+udlRAmGbM0XLeniEJSs8uEgHPGuHSe1XsOLSQ==}
    engines: {node: '>=6'}
    dev: false

  /is-glob/3.1.0:
    resolution: {integrity: sha1-e6WuJCF4BKxwcHuWkiVnSGzD6Eo=}
    engines: {node: '>=0.10.0'}
    dependencies:
      is-extglob: 2.1.1
    dev: false

  /is-glob/4.0.3:
    resolution: {integrity: sha1-ZPYeQsu7LuwgcanawLKLoeZdUIQ=}
    engines: {node: '>=0.10.0'}
    dependencies:
      is-extglob: 2.1.1

  /is-hexadecimal/1.0.4:
    resolution: {integrity: sha512-gyPJuv83bHMpocVYoqof5VDiZveEoGoFL8m3BXNb2VW8Xs+rz9kqO8LOQ5DH6EsuvilT1ApazU0pyl+ytbPtlw==}
    dev: true

  /is-negative-zero/2.0.2:
    resolution: {integrity: sha512-dqJvarLawXsFbNDeJW7zAz8ItJ9cd28YufuuFzh0G8pNHjJMnY08Dv7sYX2uF5UpQOwieAeOExEYAWWfu7ZZUA==}
    engines: {node: '>= 0.4'}
    dev: false

  /is-number-object/1.0.7:
    resolution: {integrity: sha512-k1U0IRzLMo7ZlYIfzRu23Oh6MiIFasgpb9X76eqfFZAqwH44UI4KTBvBYIZ1dSL9ZzChTB9ShHfLkR4pdW5krQ==}
    engines: {node: '>= 0.4'}
    dependencies:
      has-tostringtag: 1.0.0
    dev: false

  /is-number/3.0.0:
    resolution: {integrity: sha1-JP1iAaR4LPUFYcgQJ2r8fRLXEZU=}
    engines: {node: '>=0.10.0'}
    dependencies:
      kind-of: 3.2.2
    dev: false

  /is-number/7.0.0:
    resolution: {integrity: sha512-41Cifkg6e8TylSpdtTpeLVMqvSBEVzTttHvERD741+pnZ8ANv0004MRL43QKPDlK9cGvNp6NZWZUBlbGXYxxng==}
    engines: {node: '>=0.12.0'}

  /is-obj/1.0.1:
    resolution: {integrity: sha1-PkcprB9f3gJc19g6iW2rn09n2w8=}
    engines: {node: '>=0.10.0'}
    dev: false

  /is-obj/2.0.0:
    resolution: {integrity: sha512-drqDG3cbczxxEJRoOXcOjtdp1J/lyp1mNn0xaznRs8+muBhgQcrnbspox5X5fOw0HnMnbfDzvnEMEtqDEJEo8w==}
    engines: {node: '>=8'}
    dev: false

  /is-path-cwd/2.2.0:
    resolution: {integrity: sha512-w942bTcih8fdJPJmQHFzkS76NEP8Kzzvmw92cXsazb8intwLqPibPPdXf4ANdKV3rYMuuQYGIWtvz9JilB3NFQ==}
    engines: {node: '>=6'}
    dev: false

  /is-path-in-cwd/2.1.0:
    resolution: {integrity: sha512-rNocXHgipO+rvnP6dk3zI20RpOtrAM/kzbB258Uw5BWr3TpXi861yzjo16Dn4hUox07iw5AyeMLHWsujkjzvRQ==}
    engines: {node: '>=6'}
    dependencies:
      is-path-inside: 2.1.0
    dev: false

  /is-path-inside/2.1.0:
    resolution: {integrity: sha512-wiyhTzfDWsvwAW53OBWF5zuvaOGlZ6PwYxAbPVDhpm+gM09xKQGjBq/8uYN12aDvMxnAnq3dxTyoSoRNmg5YFg==}
    engines: {node: '>=6'}
    dependencies:
      path-is-inside: 1.0.2
    dev: false

  /is-plain-obj/1.1.0:
    resolution: {integrity: sha1-caUMhCnfync8kqOQpKA7OfzVHT4=}
    engines: {node: '>=0.10.0'}

  /is-plain-obj/2.1.0:
    resolution: {integrity: sha512-YWnfyRwxL/+SsrWYfOpUtz5b3YD+nyfkHvjbcanzk8zgyO4ASD67uVMRt8k5bM4lLMDnXfriRhOpemw+NfT1eA==}
    engines: {node: '>=8'}
    dev: true

  /is-plain-obj/3.0.0:
    resolution: {integrity: sha512-gwsOE28k+23GP1B6vFl1oVh/WOzmawBrKwo5Ev6wMKzPkaXaCDIQKzLnvsA42DRlbVTWorkgTKIviAKCWkfUwA==}
    engines: {node: '>=10'}
    dev: true

  /is-plain-object/2.0.4:
    resolution: {integrity: sha512-h5PpgXkWitc38BBMYawTYMWJHFZJVnBquFE57xFpjB8pJFiF6gZ+bU+WyI/yqXiFR5mdLsgYNaPe8uao6Uv9Og==}
    engines: {node: '>=0.10.0'}
    dependencies:
      isobject: 3.0.1
    dev: false

  /is-regex/1.1.4:
    resolution: {integrity: sha512-kvRdxDsxZjhzUX07ZnLydzS1TU/TJlTUHHY4YLL87e37oUA49DfkLqgy+VjFocowy29cKvcSiu+kIv728jTTVg==}
    engines: {node: '>= 0.4'}
    dependencies:
      call-bind: 1.0.2
      has-tostringtag: 1.0.0
    dev: false

  /is-regexp/1.0.0:
    resolution: {integrity: sha1-/S2INUXEa6xaYz57mgnof6LLUGk=}
    engines: {node: '>=0.10.0'}
    dev: false

  /is-regexp/2.1.0:
    resolution: {integrity: sha512-OZ4IlER3zmRIoB9AqNhEggVxqIH4ofDns5nRrPS6yQxXE1TPCUpFznBfRQmQa8uC+pXqjMnukiJBxCisIxiLGA==}
    engines: {node: '>=6'}
    dev: true

  /is-resolvable/1.1.0:
    resolution: {integrity: sha512-qgDYXFSR5WvEfuS5dMj6oTMEbrrSaM0CrFk2Yiq/gXnBvD9pMa2jGXxyhGLfvhZpuMZe18CJpFxAt3CRs42NMg==}
    dev: false

  /is-root/2.1.0:
    resolution: {integrity: sha512-AGOriNp96vNBd3HtU+RzFEc75FfR5ymiYv8E553I71SCeXBiMsVDUtdio1OEFvrPyLIQ9tVR5RxXIFe5PUFjMg==}
    engines: {node: '>=6'}
    dev: false

  /is-shared-array-buffer/1.0.2:
    resolution: {integrity: sha512-sqN2UDu1/0y6uvXyStCOzyhAjCSlHceFoMKJW8W9EU9cvic/QdsZ0kEU93HEy3IUEFZIiH/3w+AH/UQbPHNdhA==}
    dependencies:
      call-bind: 1.0.2
    dev: false

  /is-stream/1.1.0:
    resolution: {integrity: sha1-EtSj3U5o4Lec6428hBc66A2RykQ=}
    engines: {node: '>=0.10.0'}
    dev: false

  /is-string/1.0.7:
    resolution: {integrity: sha512-tE2UXzivje6ofPW7l23cjDOMa09gb7xlAqG6jG5ej6uPV32TlWP3NKPigtaGeHNu9fohccRYvIiZMfOOnOYUtg==}
    engines: {node: '>= 0.4'}
    dependencies:
      has-tostringtag: 1.0.0
    dev: false

  /is-symbol/1.0.4:
    resolution: {integrity: sha512-C/CPBqKWnvdcxqIARxyOh4v1UUEOCHpgDa0WYgpKDFMszcrPcffg5uhwSgPCLD2WWxmq6isisz87tzT01tuGhg==}
    engines: {node: '>= 0.4'}
    dependencies:
      has-symbols: 1.0.3
    dev: false

  /is-typedarray/1.0.0:
    resolution: {integrity: sha1-5HnICFjfDBsR3dppQPlgEfzaSpo=}

  /is-unicode-supported/0.1.0:
    resolution: {integrity: sha512-knxG2q4UC3u8stRGyAVJCOdxFmv5DZiRcdlIaAQXAbSfJya+OhopNotLQrstBhququ4ZpuKbDc/8S6mgXgPFPw==}
    engines: {node: '>=10'}
    dev: true

  /is-utf8/0.2.1:
    resolution: {integrity: sha1-Sw2hRCEE0bM2NA6AeX6GXPOffXI=}

  /is-weakref/1.0.2:
    resolution: {integrity: sha512-qctsuLZmIQ0+vSSMfoVvyFe2+GSEvnmZ2ezTup1SBse9+twCCeial6EEi3Nc2KFcf6+qz2FBPnjXsk8xhKSaPQ==}
    dependencies:
      call-bind: 1.0.2
    dev: false

  /is-windows/1.0.2:
    resolution: {integrity: sha512-eXK1UInq2bPmjyX6e3VHIzMLobc4J94i4AWn+Hpq3OU5KkrRC96OAcR3PRJ/pGu6m8TRnBHP9dkXQVsT/COVIA==}
    engines: {node: '>=0.10.0'}
    dev: false

  /is-wsl/1.1.0:
    resolution: {integrity: sha1-HxbkqiKwTRM2tmGIpmrzxgDDpm0=}
    engines: {node: '>=4'}
    dev: false

  /is-wsl/2.2.0:
    resolution: {integrity: sha512-fKzAra0rGJUUBwGBgNkHZuToZcn+TtXHpeCgmkMJMMYx1sQDYaCSyjJBSCa2nH1DGm7s3n1oBnohoVTBaN7Lww==}
    engines: {node: '>=8'}
    dependencies:
      is-docker: 2.2.1
    dev: false

  /isarray/0.0.1:
    resolution: {integrity: sha1-ihis/Kmo9Bd+Cav8YDiTmwXR7t8=}
    dev: false

  /isarray/1.0.0:
    resolution: {integrity: sha1-u5NdSFgsuhaMBoNJV6VKPgcSTxE=}

  /isexe/2.0.0:
    resolution: {integrity: sha1-6PvzdNxVb/iUehDcsFctYz8s+hA=}

  /isobject/2.1.0:
    resolution: {integrity: sha1-8GVWEJaj8dou9GJy+BXIQNh+DIk=}
    engines: {node: '>=0.10.0'}
    dependencies:
      isarray: 1.0.0
    dev: false

  /isobject/3.0.1:
    resolution: {integrity: sha1-TkMekrEalzFjaqH5yNHMvP2reN8=}
    engines: {node: '>=0.10.0'}
    dev: false

  /isomorphic-fetch/2.2.1:
    resolution: {integrity: sha1-YRrhrPFPXoH3KVB0coGf6XM1WKk=}
    dependencies:
      node-fetch: 1.7.3
      whatwg-fetch: 3.6.2
    dev: false

  /isstream/0.1.2:
    resolution: {integrity: sha1-R+Y/evVa+m+S4VAOaQ64uFKcCZo=}

  /istanbul-lib-coverage/2.0.5:
    resolution: {integrity: sha512-8aXznuEPCJvGnMSRft4udDRDtb1V3pkQkMMI5LI+6HuQz5oQ4J2UFn1H82raA3qJtyOLkkwVqICBQkjnGtn5mA==}
    engines: {node: '>=6'}
    dev: false

  /istanbul-lib-instrument/3.3.0:
    resolution: {integrity: sha512-5nnIN4vo5xQZHdXno/YDXJ0G+I3dAm4XgzfSVTPLQpj/zAV2dV6Juy0yaf10/zrJOJeHoN3fraFe+XRq2bFVZA==}
    engines: {node: '>=6'}
    dependencies:
      '@babel/generator': 7.19.6
      '@babel/parser': 7.19.6
      '@babel/template': 7.18.10
      '@babel/traverse': 7.19.6
      '@babel/types': 7.19.4
      istanbul-lib-coverage: 2.0.5
      semver: 6.3.0
    transitivePeerDependencies:
      - supports-color
    dev: false

  /istanbul-lib-report/2.0.8:
    resolution: {integrity: sha512-fHBeG573EIihhAblwgxrSenp0Dby6tJMFR/HvlerBsrCTD5bkUuoNtn3gVh29ZCS824cGGBPn7Sg7cNk+2xUsQ==}
    engines: {node: '>=6'}
    dependencies:
      istanbul-lib-coverage: 2.0.5
      make-dir: 2.1.0
      supports-color: 6.1.0
    dev: false

  /istanbul-lib-source-maps/3.0.6:
    resolution: {integrity: sha512-R47KzMtDJH6X4/YW9XTx+jrLnZnscW4VpNN+1PViSYTejLVPWv7oov+Duf8YQSPyVRUvueQqz1TcsC6mooZTXw==}
    engines: {node: '>=6'}
    dependencies:
      debug: 4.3.4
      istanbul-lib-coverage: 2.0.5
      make-dir: 2.1.0
      rimraf: 2.7.1
      source-map: 0.6.1
    transitivePeerDependencies:
      - supports-color
    dev: false

  /istanbul-reports/2.2.7:
    resolution: {integrity: sha512-uu1F/L1o5Y6LzPVSVZXNOoD/KXpJue9aeLRd0sM9uMXfZvzomB0WxVamWb5ue8kA2vVWEmW7EG+A5n3f1kqHKg==}
    engines: {node: '>=6'}
    dependencies:
      html-escaper: 2.0.2
    dev: false

  /jest-changed-files/24.9.0:
    resolution: {integrity: sha512-6aTWpe2mHF0DhL28WjdkO8LyGjs3zItPET4bMSeXU6T3ub4FPMw+mcOcbdGXQOAfmLcxofD23/5Bl9Z4AkFwqg==}
    engines: {node: '>= 6'}
    dependencies:
      '@jest/types': 24.9.0
      execa: 1.0.0
      throat: 4.1.0
    dev: false

  /jest-cli/24.9.0:
    resolution: {integrity: sha512-+VLRKyitT3BWoMeSUIHRxV/2g8y9gw91Jh5z2UmXZzkZKpbC08CSehVxgHUwTpy+HwGcns/tqafQDJW7imYvGg==}
    engines: {node: '>= 6'}
    hasBin: true
    dependencies:
      '@jest/core': 24.9.0
      '@jest/test-result': 24.9.0
      '@jest/types': 24.9.0
      chalk: 2.4.2
      exit: 0.1.2
      import-local: 2.0.0
      is-ci: 2.0.0
      jest-config: 24.9.0
      jest-util: 24.9.0
      jest-validate: 24.9.0
      prompts: 2.4.2
      realpath-native: 1.1.0
      yargs: 13.3.2
    transitivePeerDependencies:
      - bufferutil
      - supports-color
      - utf-8-validate
    dev: false

  /jest-config/24.9.0:
    resolution: {integrity: sha512-RATtQJtVYQrp7fvWg6f5y3pEFj9I+H8sWw4aKxnDZ96mob5i5SD6ZEGWgMLXQ4LE8UurrjbdlLWdUeo+28QpfQ==}
    engines: {node: '>= 6'}
    dependencies:
      '@babel/core': 7.9.0
      '@jest/test-sequencer': 24.9.0
      '@jest/types': 24.9.0
      babel-jest: 24.9.0_@babel+core@7.9.0
      chalk: 2.4.2
      glob: 7.2.3
      jest-environment-jsdom: 24.9.0
      jest-environment-node: 24.9.0
      jest-get-type: 24.9.0
      jest-jasmine2: 24.9.0
      jest-regex-util: 24.9.0
      jest-resolve: 24.9.0
      jest-util: 24.9.0
      jest-validate: 24.9.0
      micromatch: 3.1.10
      pretty-format: 24.9.0
      realpath-native: 1.1.0
    transitivePeerDependencies:
      - bufferutil
      - supports-color
      - utf-8-validate
    dev: false

  /jest-diff/24.9.0:
    resolution: {integrity: sha512-qMfrTs8AdJE2iqrTp0hzh7kTd2PQWrsFyj9tORoKmu32xjPjeE4NyjVRDz8ybYwqS2ik8N4hsIpiVTyFeo2lBQ==}
    engines: {node: '>= 6'}
    dependencies:
      chalk: 2.4.2
      diff-sequences: 24.9.0
      jest-get-type: 24.9.0
      pretty-format: 24.9.0

  /jest-docblock/24.9.0:
    resolution: {integrity: sha512-F1DjdpDMJMA1cN6He0FNYNZlo3yYmOtRUnktrT9Q37njYzC5WEaDdmbynIgy0L/IvXvvgsG8OsqhLPXTpfmZAA==}
    engines: {node: '>= 6'}
    dependencies:
      detect-newline: 2.1.0
    dev: false

  /jest-each/24.9.0:
    resolution: {integrity: sha512-ONi0R4BvW45cw8s2Lrx8YgbeXL1oCQ/wIDwmsM3CqM/nlblNCPmnC3IPQlMbRFZu3wKdQ2U8BqM6lh3LJ5Bsog==}
    engines: {node: '>= 6'}
    dependencies:
      '@jest/types': 24.9.0
      chalk: 2.4.2
      jest-get-type: 24.9.0
      jest-util: 24.9.0
      pretty-format: 24.9.0
    transitivePeerDependencies:
      - supports-color
    dev: false

  /jest-environment-jsdom-fourteen/1.0.1:
    resolution: {integrity: sha512-DojMX1sY+at5Ep+O9yME34CdidZnO3/zfPh8UW+918C5fIZET5vCjfkegixmsi7AtdYfkr4bPlIzmWnlvQkP7Q==}
    dependencies:
      '@jest/environment': 24.9.0
      '@jest/fake-timers': 24.9.0
      '@jest/types': 24.9.0
      jest-mock: 24.9.0
      jest-util: 24.9.0
      jsdom: 14.1.0
    transitivePeerDependencies:
      - bufferutil
      - supports-color
      - utf-8-validate
    dev: false

  /jest-environment-jsdom/24.9.0:
    resolution: {integrity: sha512-Zv9FV9NBRzLuALXjvRijO2351DRQeLYXtpD4xNvfoVFw21IOKNhZAEUKcbiEtjTkm2GsJ3boMVgkaR7rN8qetA==}
    engines: {node: '>= 6'}
    dependencies:
      '@jest/environment': 24.9.0
      '@jest/fake-timers': 24.9.0
      '@jest/types': 24.9.0
      jest-mock: 24.9.0
      jest-util: 24.9.0
      jsdom: 11.12.0
    transitivePeerDependencies:
      - bufferutil
      - supports-color
      - utf-8-validate
    dev: false

  /jest-environment-node/24.9.0:
    resolution: {integrity: sha512-6d4V2f4nxzIzwendo27Tr0aFm+IXWa0XEUnaH6nU0FMaozxovt+sfRvh4J47wL1OvF83I3SSTu0XK+i4Bqe7uA==}
    engines: {node: '>= 6'}
    dependencies:
      '@jest/environment': 24.9.0
      '@jest/fake-timers': 24.9.0
      '@jest/types': 24.9.0
      jest-mock: 24.9.0
      jest-util: 24.9.0
    transitivePeerDependencies:
      - supports-color
    dev: false

  /jest-get-type/24.9.0:
    resolution: {integrity: sha512-lUseMzAley4LhIcpSP9Jf+fTrQ4a1yHQwLNeeVa2cEmbCGeoZAtYPOIv8JaxLD/sUpKxetKGP+gsHl8f8TSj8Q==}
    engines: {node: '>= 6'}

  /jest-haste-map/24.9.0:
    resolution: {integrity: sha512-kfVFmsuWui2Sj1Rp1AJ4D9HqJwE4uwTlS/vO+eRUaMmd54BFpli2XhMQnPC2k4cHFVbB2Q2C+jtI1AGLgEnCjQ==}
    engines: {node: '>= 6'}
    dependencies:
      '@jest/types': 24.9.0
      anymatch: 2.0.0
      fb-watchman: 2.0.2
      graceful-fs: 4.2.10
      invariant: 2.2.4
      jest-serializer: 24.9.0
      jest-util: 24.9.0
      jest-worker: 24.9.0
      micromatch: 3.1.10
      sane: 4.1.0
      walker: 1.0.8
    optionalDependencies:
      fsevents: 1.2.13
    transitivePeerDependencies:
      - supports-color
    dev: false

  /jest-jasmine2/24.9.0:
    resolution: {integrity: sha512-Cq7vkAgaYKp+PsX+2/JbTarrk0DmNhsEtqBXNwUHkdlbrTBLtMJINADf2mf5FkowNsq8evbPc07/qFO0AdKTzw==}
    engines: {node: '>= 6'}
    dependencies:
      '@babel/traverse': 7.19.6
      '@jest/environment': 24.9.0
      '@jest/test-result': 24.9.0
      '@jest/types': 24.9.0
      chalk: 2.4.2
      co: 4.6.0
      expect: 24.9.0
      is-generator-fn: 2.1.0
      jest-each: 24.9.0
      jest-matcher-utils: 24.9.0
      jest-message-util: 24.9.0
      jest-runtime: 24.9.0
      jest-snapshot: 24.9.0
      jest-util: 24.9.0
      pretty-format: 24.9.0
      throat: 4.1.0
    transitivePeerDependencies:
      - supports-color
    dev: false

  /jest-leak-detector/24.9.0:
    resolution: {integrity: sha512-tYkFIDsiKTGwb2FG1w8hX9V0aUb2ot8zY/2nFg087dUageonw1zrLMP4W6zsRO59dPkTSKie+D4rhMuP9nRmrA==}
    engines: {node: '>= 6'}
    dependencies:
      jest-get-type: 24.9.0
      pretty-format: 24.9.0
    dev: false

  /jest-matcher-utils/24.9.0:
    resolution: {integrity: sha512-OZz2IXsu6eaiMAwe67c1T+5tUAtQyQx27/EMEkbFAGiw52tB9em+uGbzpcgYVpA8wl0hlxKPZxrly4CXU/GjHA==}
    engines: {node: '>= 6'}
    dependencies:
      chalk: 2.4.2
      jest-diff: 24.9.0
      jest-get-type: 24.9.0
      pretty-format: 24.9.0

  /jest-message-util/24.9.0:
    resolution: {integrity: sha512-oCj8FiZ3U0hTP4aSui87P4L4jC37BtQwUMqk+zk/b11FR19BJDeZsZAvIHutWnmtw7r85UmR3CEWZ0HWU2mAlw==}
    engines: {node: '>= 6'}
    dependencies:
      '@babel/code-frame': 7.18.6
      '@jest/test-result': 24.9.0
      '@jest/types': 24.9.0
      '@types/stack-utils': 1.0.1
      chalk: 2.4.2
      micromatch: 3.1.10
      slash: 2.0.0
      stack-utils: 1.0.5
    transitivePeerDependencies:
      - supports-color
    dev: false

  /jest-mock/24.9.0:
    resolution: {integrity: sha512-3BEYN5WbSq9wd+SyLDES7AHnjH9A/ROBwmz7l2y+ol+NtSFO8DYiEBzoO1CeFc9a8DYy10EO4dDFVv/wN3zl1w==}
    engines: {node: '>= 6'}
    dependencies:
      '@jest/types': 24.9.0
    dev: false

  /jest-pnp-resolver/1.2.2_jest-resolve@24.9.0:
    resolution: {integrity: sha512-olV41bKSMm8BdnuMsewT4jqlZ8+3TCARAXjZGT9jcoSnrfUnRCqnMoF9XEeoWjbzObpqF9dRhHQj0Xb9QdF6/w==}
    engines: {node: '>=6'}
    peerDependencies:
      jest-resolve: '*'
    peerDependenciesMeta:
      jest-resolve:
        optional: true
    dependencies:
      jest-resolve: 24.9.0
    dev: false

  /jest-regex-util/24.9.0:
    resolution: {integrity: sha512-05Cmb6CuxaA+Ys6fjr3PhvV3bGQmO+2p2La4hFbU+W5uOc479f7FdLXUWXw4pYMAhhSZIuKHwSXSu6CsSBAXQA==}
    engines: {node: '>= 6'}
    dev: false

  /jest-resolve-dependencies/24.9.0:
    resolution: {integrity: sha512-Fm7b6AlWnYhT0BXy4hXpactHIqER7erNgIsIozDXWl5dVm+k8XdGVe1oTg1JyaFnOxarMEbax3wyRJqGP2Pq+g==}
    engines: {node: '>= 6'}
    dependencies:
      '@jest/types': 24.9.0
      jest-regex-util: 24.9.0
      jest-snapshot: 24.9.0
    transitivePeerDependencies:
      - supports-color
    dev: false

  /jest-resolve/24.9.0:
    resolution: {integrity: sha512-TaLeLVL1l08YFZAt3zaPtjiVvyy4oSA6CRe+0AFPPVX3Q/VI0giIWWoAvoS5L96vj9Dqxj4fB5p2qrHCmTU/MQ==}
    engines: {node: '>= 6'}
    dependencies:
      '@jest/types': 24.9.0
      browser-resolve: 1.11.3
      chalk: 2.4.2
      jest-pnp-resolver: 1.2.2_jest-resolve@24.9.0
      realpath-native: 1.1.0
    dev: false

  /jest-runner/24.9.0:
    resolution: {integrity: sha512-KksJQyI3/0mhcfspnxxEOBueGrd5E4vV7ADQLT9ESaCzz02WnbdbKWIf5Mkaucoaj7obQckYPVX6JJhgUcoWWg==}
    engines: {node: '>= 6'}
    dependencies:
      '@jest/console': 24.9.0
      '@jest/environment': 24.9.0
      '@jest/test-result': 24.9.0
      '@jest/types': 24.9.0
      chalk: 2.4.2
      exit: 0.1.2
      graceful-fs: 4.2.10
      jest-config: 24.9.0
      jest-docblock: 24.9.0
      jest-haste-map: 24.9.0
      jest-jasmine2: 24.9.0
      jest-leak-detector: 24.9.0
      jest-message-util: 24.9.0
      jest-resolve: 24.9.0
      jest-runtime: 24.9.0
      jest-util: 24.9.0
      jest-worker: 24.9.0
      source-map-support: 0.5.21
      throat: 4.1.0
    transitivePeerDependencies:
      - bufferutil
      - supports-color
      - utf-8-validate
    dev: false

  /jest-runtime/24.9.0:
    resolution: {integrity: sha512-8oNqgnmF3v2J6PVRM2Jfuj8oX3syKmaynlDMMKQ4iyzbQzIG6th5ub/lM2bCMTmoTKM3ykcUYI2Pw9xwNtjMnw==}
    engines: {node: '>= 6'}
    hasBin: true
    dependencies:
      '@jest/console': 24.9.0
      '@jest/environment': 24.9.0
      '@jest/source-map': 24.9.0
      '@jest/transform': 24.9.0
      '@jest/types': 24.9.0
      '@types/yargs': 13.0.12
      chalk: 2.4.2
      exit: 0.1.2
      glob: 7.2.3
      graceful-fs: 4.2.10
      jest-config: 24.9.0
      jest-haste-map: 24.9.0
      jest-message-util: 24.9.0
      jest-mock: 24.9.0
      jest-regex-util: 24.9.0
      jest-resolve: 24.9.0
      jest-snapshot: 24.9.0
      jest-util: 24.9.0
      jest-validate: 24.9.0
      realpath-native: 1.1.0
      slash: 2.0.0
      strip-bom: 3.0.0
      yargs: 13.3.2
    transitivePeerDependencies:
      - bufferutil
      - supports-color
      - utf-8-validate
    dev: false

  /jest-serializer/24.9.0:
    resolution: {integrity: sha512-DxYipDr8OvfrKH3Kel6NdED3OXxjvxXZ1uIY2I9OFbGg+vUkkg7AGvi65qbhbWNPvDckXmzMPbK3u3HaDO49bQ==}
    engines: {node: '>= 6'}
    dev: false

  /jest-snapshot/24.9.0:
    resolution: {integrity: sha512-uI/rszGSs73xCM0l+up7O7a40o90cnrk429LOiK3aeTvfC0HHmldbd81/B7Ix81KSFe1lwkbl7GnBGG4UfuDew==}
    engines: {node: '>= 6'}
    dependencies:
      '@babel/types': 7.19.4
      '@jest/types': 24.9.0
      chalk: 2.4.2
      expect: 24.9.0
      jest-diff: 24.9.0
      jest-get-type: 24.9.0
      jest-matcher-utils: 24.9.0
      jest-message-util: 24.9.0
      jest-resolve: 24.9.0
      mkdirp: 0.5.6
      natural-compare: 1.4.0
      pretty-format: 24.9.0
      semver: 6.3.0
    transitivePeerDependencies:
      - supports-color
    dev: false

  /jest-util/24.9.0:
    resolution: {integrity: sha512-x+cZU8VRmOJxbA1K5oDBdxQmdq0OIdADarLxk0Mq+3XS4jgvhG/oKGWcIDCtPG0HgjxOYvF+ilPJQsAyXfbNOg==}
    engines: {node: '>= 6'}
    dependencies:
      '@jest/console': 24.9.0
      '@jest/fake-timers': 24.9.0
      '@jest/source-map': 24.9.0
      '@jest/test-result': 24.9.0
      '@jest/types': 24.9.0
      callsites: 3.1.0
      chalk: 2.4.2
      graceful-fs: 4.2.10
      is-ci: 2.0.0
      mkdirp: 0.5.6
      slash: 2.0.0
      source-map: 0.6.1
    transitivePeerDependencies:
      - supports-color
    dev: false

  /jest-validate/24.9.0:
    resolution: {integrity: sha512-HPIt6C5ACwiqSiwi+OfSSHbK8sG7akG8eATl+IPKaeIjtPOeBUd/g3J7DghugzxrGjI93qS/+RPKe1H6PqvhRQ==}
    engines: {node: '>= 6'}
    dependencies:
      '@jest/types': 24.9.0
      camelcase: 5.3.1
      chalk: 2.4.2
      jest-get-type: 24.9.0
      leven: 3.1.0
      pretty-format: 24.9.0
    dev: false

  /jest-watch-typeahead/0.4.2:
    resolution: {integrity: sha512-f7VpLebTdaXs81rg/oj4Vg/ObZy2QtGzAmGLNsqUS5G5KtSN68tFcIsbvNODfNyQxU78g7D8x77o3bgfBTR+2Q==}
    dependencies:
      ansi-escapes: 4.3.2
      chalk: 2.4.2
      jest-regex-util: 24.9.0
      jest-watcher: 24.9.0
      slash: 3.0.0
      string-length: 3.1.0
      strip-ansi: 5.2.0
    transitivePeerDependencies:
      - supports-color
    dev: false

  /jest-watcher/24.9.0:
    resolution: {integrity: sha512-+/fLOfKPXXYJDYlks62/4R4GoT+GU1tYZed99JSCOsmzkkF7727RqKrjNAxtfO4YpGv11wybgRvCjR73lK2GZw==}
    engines: {node: '>= 6'}
    dependencies:
      '@jest/test-result': 24.9.0
      '@jest/types': 24.9.0
      '@types/yargs': 13.0.12
      ansi-escapes: 3.2.0
      chalk: 2.4.2
      jest-util: 24.9.0
      string-length: 2.0.0
    transitivePeerDependencies:
      - supports-color
    dev: false

  /jest-worker/24.9.0:
    resolution: {integrity: sha512-51PE4haMSXcHohnSMdM42anbvZANYTqMrr52tVKPqqsPJMzoP6FYYDVqahX/HrAoKEKz3uUPzSvKs9A3qR4iVw==}
    engines: {node: '>= 6'}
    dependencies:
      merge-stream: 2.0.0
      supports-color: 6.1.0
    dev: false

  /jest-worker/25.5.0:
    resolution: {integrity: sha512-/dsSmUkIy5EBGfv/IjjqmFxrNAUpBERfGs1oHROyD7yxjG/w+t0GOJDX8O1k32ySmd7+a5IhnJU2qQFcJ4n1vw==}
    engines: {node: '>= 8.3'}
    dependencies:
      merge-stream: 2.0.0
      supports-color: 7.2.0
    dev: false

  /jest/24.9.0:
    resolution: {integrity: sha512-YvkBL1Zm7d2B1+h5fHEOdyjCG+sGMz4f8D86/0HiqJ6MB4MnDc8FgP5vdWsGnemOQro7lnYo8UakZ3+5A0jxGw==}
    engines: {node: '>= 6'}
    hasBin: true
    dependencies:
      import-local: 2.0.0
      jest-cli: 24.9.0
    transitivePeerDependencies:
      - bufferutil
      - supports-color
      - utf-8-validate
    dev: false

  /js-base64/2.6.4:
    resolution: {integrity: sha512-pZe//GGmwJndub7ZghVHz7vjb2LgC1m8B07Au3eYqeqv9emhESByMXxaEgkUkEqJe87oBbSniGYoQNIBklc7IQ==}

  /js-base64/3.7.2:
    resolution: {integrity: sha1-gW0R2BqK/yQWA9Gc5XYeE+Qdd0U=}
    dev: false

  /js-md5/0.7.3:
    resolution: {integrity: sha512-ZC41vPSTLKGwIRjqDh8DfXoCrdQIyBgspJVPXHBGu4nZlAEvG3nf+jO9avM9RmLiGakg7vz974ms99nEV0tmTQ==}
    dev: false

  /js-tokens/3.0.2:
    resolution: {integrity: sha1-mGbfOVECEw449/mWvOtlRDIJwls=}
    dev: false

  /js-tokens/4.0.0:
    resolution: {integrity: sha512-RdJUflcE3cUzKiMqQgsCu06FPu9UdIJO0beYbPhHN4k6apgJtifcoCtT9bcxOpYBtpD2kCM6Sbzg4CausW/PKQ==}

  /js-yaml/3.14.1:
    resolution: {integrity: sha512-okMH7OXXJ7YrN9Ok3/SXrnu4iX9yOk+25nqX4imS2npuvTYDmo/QEZoqwZkYaIDk3jVvBOTOIEgEhaLOynBS9g==}
    hasBin: true
    dependencies:
      argparse: 1.0.10
      esprima: 4.0.1
    dev: false

  /jsbn/0.1.1:
    resolution: {integrity: sha1-peZUwuWi3rXyAdls77yoDA7y9RM=}

  /jsdom/11.12.0:
    resolution: {integrity: sha512-y8Px43oyiBM13Zc1z780FrfNLJCXTL40EWlty/LXUtcjykRBNgLlCjWXpfSPBl2iv+N7koQN+dvqszHZgT/Fjw==}
    dependencies:
      abab: 2.0.6
      acorn: 5.7.4
      acorn-globals: 4.3.4
      array-equal: 1.0.0
      cssom: 0.3.8
      cssstyle: 1.4.0
      data-urls: 1.1.0
      domexception: 1.0.1
      escodegen: 1.14.3
      html-encoding-sniffer: 1.0.2
      left-pad: 1.3.0
      nwsapi: 2.2.2
      parse5: 4.0.0
      pn: 1.1.0
      request: 2.88.2
      request-promise-native: 1.0.9_request@2.88.2
      sax: 1.2.4
      symbol-tree: 3.2.4
      tough-cookie: 2.5.0
      w3c-hr-time: 1.0.2
      webidl-conversions: 4.0.2
      whatwg-encoding: 1.0.5
      whatwg-mimetype: 2.3.0
      whatwg-url: 6.5.0
      ws: 5.2.3
      xml-name-validator: 3.0.0
    transitivePeerDependencies:
      - bufferutil
      - utf-8-validate
    dev: false

  /jsdom/14.1.0:
    resolution: {integrity: sha512-O901mfJSuTdwU2w3Sn+74T+RnDVP+FuV5fH8tcPWyqrseRAb0s5xOtPgCFiPOtLcyK7CLIJwPyD83ZqQWvA5ng==}
    engines: {node: '>=8'}
    dependencies:
      abab: 2.0.6
      acorn: 6.4.2
      acorn-globals: 4.3.4
      array-equal: 1.0.0
      cssom: 0.3.8
      cssstyle: 1.4.0
      data-urls: 1.1.0
      domexception: 1.0.1
      escodegen: 1.14.3
      html-encoding-sniffer: 1.0.2
      nwsapi: 2.2.2
      parse5: 5.1.0
      pn: 1.1.0
      request: 2.88.2
      request-promise-native: 1.0.9_request@2.88.2
      saxes: 3.1.11
      symbol-tree: 3.2.4
      tough-cookie: 2.5.0
      w3c-hr-time: 1.0.2
      w3c-xmlserializer: 1.1.2
      webidl-conversions: 4.0.2
      whatwg-encoding: 1.0.5
      whatwg-mimetype: 2.3.0
      whatwg-url: 7.1.0
      ws: 6.2.2
      xml-name-validator: 3.0.0
    transitivePeerDependencies:
      - bufferutil
      - utf-8-validate
    dev: false

  /jsesc/0.5.0:
    resolution: {integrity: sha1-597mbjXW/Bb3EP6R1c9p9w8IkR0=}
    hasBin: true
    dev: false

  /jsesc/2.5.2:
    resolution: {integrity: sha512-OYu7XEzjkCQ3C5Ps3QIZsQfNpqoJyZZA99wd9aWd05NCtC5pWOkShK2mkL6HXQR6/Cy2lbNdPlZBpuQHXE63gA==}
    engines: {node: '>=4'}
    hasBin: true

  /json-parse-better-errors/1.0.2:
    resolution: {integrity: sha512-mrqyZKfX5EhL7hvqcV6WG1yYjnjeuYDzDhhcAAUrq8Po85NBQBJP+ZDUT75qZQ98IkUoBqdkExkukOU7Ts2wrw==}
    dev: false

  /json-parse-even-better-errors/2.3.1:
    resolution: {integrity: sha512-xyFwyhro/JEof6Ghe2iz2NcXoj2sloNsWr/XsERDK/oiPCfaNhl5ONfp+jQdAZRQQ0IJWNzH9zIZF7li91kh2w==}

  /json-schema-traverse/0.4.1:
    resolution: {integrity: sha512-xbbCH5dCYU5T8LcEhhuh7HJ88HXuW3qsI3Y0zOZFKfZEHcpWiHU/Jxzk629Brsab/mMiHQti9wMP+845RPe3Vg==}

  /json-schema-traverse/1.0.0:
    resolution: {integrity: sha512-NM8/P9n3XjXhIZn1lLhkFaACTOURQXjWhV4BA/RnOv8xvgqtqpAX9IO4mRQxSx1Rlo4tqzeqb0sOlruaOy3dug==}
    dev: true

  /json-schema/0.4.0:
    resolution: {integrity: sha1-995M9u+rg4666zI2R0y7paGTCrU=}

  /json-stable-stringify-without-jsonify/1.0.1:
    resolution: {integrity: sha1-nbe1lJatPzz+8wp1FC0tkwrXJlE=}
    dev: false

  /json-stable-stringify/1.0.1:
    resolution: {integrity: sha1-mnWdOcXy/1A/1TAGRu1EX4jE+a8=}
    dependencies:
      jsonify: 0.0.1
    dev: false

  /json-stringify-safe/5.0.1:
    resolution: {integrity: sha1-Epai1Y/UXxmg9s4B1lcB4sc1tus=}

  /json2mq/0.2.0:
    resolution: {integrity: sha1-tje9O6nqvhIsg+lyBIOusQ0skEo=}
    dependencies:
      string-convert: 0.2.1
    dev: false

  /json3/3.3.3:
    resolution: {integrity: sha512-c7/8mbUsKigAbLkD5B010BK4D9LZm7A1pNItkEwiUZRpIN66exu/e7YQWysGun+TRKaJp8MhemM+VkfWv42aCA==}
    dev: false

  /json5/1.0.1:
    resolution: {integrity: sha512-aKS4WQjPenRxiQsC93MNfjx+nbF4PAdYzmd/1JIj8HYzqfbu86beTuNgXDzPknWk0n0uARlyewZo4s++ES36Ow==}
    hasBin: true
    dependencies:
      minimist: 1.2.7
    dev: false

  /json5/2.2.1:
    resolution: {integrity: sha512-1hqLFMSrGHRHxav9q9gNjJ5EXznIxGVO09xQRrwplcS8qs28pZ8s8hupZAmqDwZUmVZ2Qb2jnyPOWcDH8m8dlA==}
    engines: {node: '>=6'}
    hasBin: true

  /jsonfile/4.0.0:
    resolution: {integrity: sha1-h3Gq4HmbZAdrdmQPygWPnBDjPss=}
    optionalDependencies:
      graceful-fs: 4.2.10
    dev: false

  /jsonify/0.0.1:
    resolution: {integrity: sha512-2/Ki0GcmuqSrgFyelQq9M05y7PS0mEwuIzrf3f1fPqkVDVRvZrPZtVSMHxdgo8Aq0sxAOb/cr2aqqA3LeWHVPg==}
    dev: false

  /jsprim/1.4.2:
    resolution: {integrity: sha512-P2bSOMAc/ciLz6DzgjVlGJP9+BrJWu5UDGK70C2iweC5QBIeFf0ZXRvGjEj2uYgrY2MkAAhsSWHDWlFtEroZWw==}
    engines: {node: '>=0.6.0'}
    dependencies:
      assert-plus: 1.0.0
      extsprintf: 1.3.0
      json-schema: 0.4.0
      verror: 1.10.0

  /jsx-ast-utils/2.4.1:
    resolution: {integrity: sha512-z1xSldJ6imESSzOjd3NNkieVJKRlKYSOtMG8SFyCj2FIrvSaSuli/WjpBkEzCBoR9bYYYFgqJw61Xhu7Lcgk+w==}
    engines: {node: '>=4.0'}
    dependencies:
      array-includes: 3.1.5
      object.assign: 4.1.4
    dev: false

  /killable/1.0.1:
    resolution: {integrity: sha512-LzqtLKlUwirEUyl/nicirVmNiPvYs7l5n8wOPP7fyJVpUPkvCnW/vuiXGpylGUlnPDnB7311rARzAt3Mhswpjg==}
    dev: false

  /kind-of/2.0.1:
    resolution: {integrity: sha1-AY7HpM5+OobLkUG+UZ0kyPqpgbU=}
    engines: {node: '>=0.10.0'}
    dependencies:
      is-buffer: 1.1.6
    dev: false

  /kind-of/3.2.2:
    resolution: {integrity: sha1-MeohpzS6ubuw8yRm2JOupR5KPGQ=}
    engines: {node: '>=0.10.0'}
    dependencies:
      is-buffer: 1.1.6
    dev: false

  /kind-of/4.0.0:
    resolution: {integrity: sha1-IIE989cSkosgc3hpGkUGb65y3Vc=}
    engines: {node: '>=0.10.0'}
    dependencies:
      is-buffer: 1.1.6
    dev: false

  /kind-of/5.1.0:
    resolution: {integrity: sha512-NGEErnH6F2vUuXDh+OlbcKW7/wOcfdRHaZ7VWtqCztfHri/++YKmP51OdWeGPuqCOba6kk2OTe5d02VmTB80Pw==}
    engines: {node: '>=0.10.0'}
    dev: false

  /kind-of/6.0.3:
    resolution: {integrity: sha512-dcS1ul+9tmeD95T+x28/ehLgd9mENa3LsvDTtzm3vyBEO7RPptvAD+t44WVXaUjTBRcrpFeFlC8WCruUR456hw==}
    engines: {node: '>=0.10.0'}

  /kleur/3.0.3:
    resolution: {integrity: sha512-eTIzlVOSUR+JxdDFepEYcBMtZ9Qqdef+rnzWdRZuMbOywu5tO2w2N7rqjoANZ5k9vywhL6Br1VRjUIgTQx4E8w==}
    engines: {node: '>=6'}
    dev: false

  /klona/2.0.5:
    resolution: {integrity: sha1-0WZXTZAHY5XZljqnqSj6u412r7w=}
    engines: {node: '>= 8'}
    dev: true

  /known-css-properties/0.21.0:
    resolution: {integrity: sha512-sZLUnTqimCkvkgRS+kbPlYW5o8q5w1cu+uIisKpEWkj31I8mx8kNG162DwRav8Zirkva6N5uoFsm9kzK4mUXjw==}
    dev: true

  /last-call-webpack-plugin/3.0.0:
    resolution: {integrity: sha512-7KI2l2GIZa9p2spzPIVZBYyNKkN+e/SQPpnjlTiPhdbDW3F86tdKKELxKpzJ5sgU19wQWsACULZmpTPYHeWO5w==}
    dependencies:
      lodash: 4.17.21
      webpack-sources: 1.4.3
    dev: false

  /lazy-cache/0.2.7:
    resolution: {integrity: sha1-f+3fLctu23fRHvHRF6tf/fCrG2U=}
    engines: {node: '>=0.10.0'}
    dev: false

  /lazy-cache/1.0.4:
    resolution: {integrity: sha1-odePw6UEdMuAhF07O24dpJpEbo4=}
    engines: {node: '>=0.10.0'}
    dev: false

  /lazystream/1.0.1:
    resolution: {integrity: sha1-SUyDEGLx+UCCUexE2xy6KSQqJjg=}
    engines: {node: '>= 0.6.3'}
    dependencies:
      readable-stream: 2.3.7
    dev: false

  /lcid/2.0.0:
    resolution: {integrity: sha512-avPEb8P8EGnwXKClwsNUgryVjllcRqtMYa49NTsbQagYuT1DcXnl1915oxWjoyGrXR6zH/Y0Zc96xWsPcoDKeA==}
    engines: {node: '>=6'}
    dependencies:
      invert-kv: 2.0.0
    dev: false

  /left-pad/1.3.0:
    resolution: {integrity: sha512-XI5MPzVNApjAyhQzphX8BkmKsKUxD4LdyK24iZeQGinBN9yTQT3bFlCBy/aVx2HrNcqQGsdot8ghrjyrvMCoEA==}
    deprecated: use String.prototype.padStart()
    dev: false

  /leven/3.1.0:
    resolution: {integrity: sha512-qsda+H8jTaUaN/x5vzW2rzc+8Rw4TAQ/4KjB46IwK5VH+IlVeeeje/EoZRpiXvIqjFgK84QffqPztGI3VBLG1A==}
    engines: {node: '>=6'}
    dev: false

  /levenary/1.1.1:
    resolution: {integrity: sha512-mkAdOIt79FD6irqjYSs4rdbnlT5vRonMEvBVPVb3XmevfS8kgRXwfes0dhPdEtzTWD/1eNE/Bm/G1iRt6DcnQQ==}
    engines: {node: '>= 6'}
    dependencies:
      leven: 3.1.0
    dev: false

  /levn/0.3.0:
    resolution: {integrity: sha1-OwmSTt+fCDwEkP3UwLxEIeBHZO4=}
    engines: {node: '>= 0.8.0'}
    dependencies:
      prelude-ls: 1.1.2
      type-check: 0.3.2
    dev: false

  /lines-and-columns/1.2.4:
    resolution: {integrity: sha1-7KKE910pZQeTCdwK2SVauy68FjI=}

  /load-json-file/1.1.0:
    resolution: {integrity: sha1-lWkFcI1YtLq0wiYbBPWfMcmTdMA=}
    engines: {node: '>=0.10.0'}
    dependencies:
      graceful-fs: 4.2.10
      parse-json: 2.2.0
      pify: 2.3.0
      pinkie-promise: 2.0.1
      strip-bom: 2.0.0

  /load-json-file/2.0.0:
    resolution: {integrity: sha1-eUfkIUmvgNaWy/eXvKq8/h/inKg=}
    engines: {node: '>=4'}
    dependencies:
      graceful-fs: 4.2.10
      parse-json: 2.2.0
      pify: 2.3.0
      strip-bom: 3.0.0
    dev: false

  /load-json-file/4.0.0:
    resolution: {integrity: sha1-L19Fq5HjMhYjT9U62rZo607AmTs=}
    engines: {node: '>=4'}
    dependencies:
      graceful-fs: 4.2.10
      parse-json: 4.0.0
      pify: 3.0.0
      strip-bom: 3.0.0
    dev: false

  /loader-fs-cache/1.0.3:
    resolution: {integrity: sha512-ldcgZpjNJj71n+2Mf6yetz+c9bM4xpKtNds4LbqXzU/PTdeAX0g3ytnU1AJMEcTk2Lex4Smpe3Q/eCTsvUBxbA==}
    dependencies:
      find-cache-dir: 0.1.1
      mkdirp: 0.5.6
    dev: false

  /loader-runner/2.4.0:
    resolution: {integrity: sha512-Jsmr89RcXGIwivFY21FcRrisYZfvLMTWx5kOLc+JTxtpBOG6xML0vzbc6SEQG2FO9/4Fc3wW4LVcB5DmGflaRw==}
    engines: {node: '>=4.3.0 <5.0.0 || >=5.10'}
    dev: false

  /loader-utils/1.2.3:
    resolution: {integrity: sha512-fkpz8ejdnEMG3s37wGL07iSBDg99O9D5yflE9RGNH3hRdx9SOwYfnGYdZOUIZitN8E+E2vkq3MUMYMvPYl5ZZA==}
    engines: {node: '>=4.0.0'}
    dependencies:
      big.js: 5.2.2
      emojis-list: 2.1.0
      json5: 1.0.1
    dev: false

  /loader-utils/1.4.0:
    resolution: {integrity: sha512-qH0WSMBtn/oHuwjy/NucEgbx5dbxxnxup9s4PVXJUDHZBQY+s0NWA9rJf53RBnQZxfch7euUui7hpoAPvALZdA==}
    engines: {node: '>=4.0.0'}
    dependencies:
      big.js: 5.2.2
      emojis-list: 3.0.0
      json5: 1.0.1
    dev: false

  /locate-path/2.0.0:
    resolution: {integrity: sha1-K1aLJl7slExtnA3pw9u7ygNUzY4=}
    engines: {node: '>=4'}
    dependencies:
      p-locate: 2.0.0
      path-exists: 3.0.0
    dev: false

  /locate-path/3.0.0:
    resolution: {integrity: sha512-7AO748wWnIhNqAuaty2ZWHkQHRSNfPVIsPIfwEOWO22AmaoVrWavlOcMR5nzTLNYvp36X220/maaRsrec1G65A==}
    engines: {node: '>=6'}
    dependencies:
      p-locate: 3.0.0
      path-exists: 3.0.0

  /locate-path/5.0.0:
    resolution: {integrity: sha512-t7hw9pI+WvuwNJXwk5zVHpyhIqzg2qTlklJOf0mVxGSbe3Fp2VieZcduNYjaLDoy6p9uGpQEGWG87WpMKlNq8g==}
    engines: {node: '>=8'}
    dependencies:
      p-locate: 4.1.0

  /lodash._reinterpolate/3.0.0:
    resolution: {integrity: sha1-DM8tiRZq8Ds2Y8eWU4t1rG4RTZ0=}
    dev: false

  /lodash.debounce/4.0.8:
    resolution: {integrity: sha1-gteb/zCmfEAF/9XiUVMArZyk168=}
    dev: false

  /lodash.defaults/4.2.0:
    resolution: {integrity: sha1-0JF4cW/+pN3p5ft7N/bwgCJ0WAw=}
    dev: false

  /lodash.difference/4.5.0:
    resolution: {integrity: sha1-nMtOUF1Ia5FlE0V3KIWi3yf9AXw=}
    dev: false

  /lodash.flatten/4.4.0:
    resolution: {integrity: sha1-8xwiIlqWMtK7+OSt2+8kCqdlph8=}
    dev: false

  /lodash.isplainobject/4.0.6:
    resolution: {integrity: sha1-fFJqUtibRcRcxpC4gWO+BJf1UMs=}
    dev: false

  /lodash.memoize/4.1.2:
    resolution: {integrity: sha1-vMbEmkKihA7Zl/Mj6tpezRguC/4=}
    dev: false

  /lodash.sortby/4.7.0:
    resolution: {integrity: sha1-7dFMgk4sycHgsKG0K7UhBRakJDg=}
    dev: false

  /lodash.template/4.5.0:
    resolution: {integrity: sha512-84vYFxIkmidUiFxidA/KjjH9pAycqW+h980j7Fuz5qxRtO9pgB7MDFTdys1N7A5mcucRiDyEq4fusljItR1T/A==}
    dependencies:
      lodash._reinterpolate: 3.0.0
      lodash.templatesettings: 4.2.0
    dev: false

  /lodash.templatesettings/4.2.0:
    resolution: {integrity: sha512-stgLz+i3Aa9mZgnjr/O+v9ruKZsPsndy7qPZOchbqk2cnTU1ZaldKK+v7m54WoKIyxiuMZTKT2H81F8BeAc3ZQ==}
    dependencies:
      lodash._reinterpolate: 3.0.0
    dev: false

  /lodash.truncate/4.4.2:
    resolution: {integrity: sha1-WjUNoLERO4N+z//VgSy+WNbq4ZM=}
    dev: true

  /lodash.union/4.6.0:
    resolution: {integrity: sha1-SLtQiECfFvGCFmZkHETdGqrjzYg=}
    dev: false

  /lodash.uniq/4.5.0:
    resolution: {integrity: sha1-0CJTc662Uq3BvILklFM5qEJ1R3M=}
    dev: false

  /lodash/4.17.21:
    resolution: {integrity: sha512-v2kDEe57lecTulaDIuNTPy3Ry4gLGJ6Z1O3vE1krgXZNrsQ+LFTGHVxVjcXPs17LhbZVGedAJv8XZ1tvj5FvSg==}

  /log-symbols/4.1.0:
    resolution: {integrity: sha512-8XPvpAA8uyhfteu8pIvQxpJZ7SYYdpUivZpGy6sFsBuKRY/7rQGavedeB8aK+Zkyq6upMFVL/9AW6vOYzfRyLg==}
    engines: {node: '>=10'}
    dependencies:
      chalk: 4.1.2
      is-unicode-supported: 0.1.0
    dev: true

  /loglevel/1.8.0:
    resolution: {integrity: sha1-5+xzpX4ee0GctsasBr8FC2c1YRQ=}
    engines: {node: '>= 0.6.0'}
    dev: false

  /longest-streak/2.0.4:
    resolution: {integrity: sha512-vM6rUVCVUJJt33bnmHiZEvr7wPT78ztX7rojL+LW51bHtLh6HTjx84LA5W4+oa6aKEJA7jJu5LR6vQRBpA5DVg==}
    dev: true

  /loose-envify/1.4.0:
    resolution: {integrity: sha512-lyuxPGr/Wfhrlem2CL/UcnUc1zcqKAImBDzukY7Y5F/yQiNdko6+fRLevlw1HgMySw7f611UIY408EtxRSoK3Q==}
    hasBin: true
    dependencies:
      js-tokens: 4.0.0

  /loud-rejection/1.6.0:
    resolution: {integrity: sha1-W0b4AUft7leIcPCG0Eghz5mOVR8=}
    engines: {node: '>=0.10.0'}
    dependencies:
      currently-unhandled: 0.4.1
      signal-exit: 3.0.7

  /lower-case/2.0.2:
    resolution: {integrity: sha512-7fm3l3NAF9WfN6W3JOmf5drwpVqX78JtoGJ3A6W0a6ZnldM41w2fV5D490psKFTpMds8TJse/eHLFFsNHHjHgg==}
    dependencies:
      tslib: 2.4.0
    dev: false

  /lru-cache/4.1.5:
    resolution: {integrity: sha512-sWZlbEP2OsHNkXrMl5GYk/jKk70MBng6UU4YI/qGDYbgf6YbP4EvmqISbXCoJiRKs+1bSpFHVgQxvJ17F2li5g==}
    dependencies:
      pseudomap: 1.0.2
      yallist: 2.1.2

  /lru-cache/5.1.1:
    resolution: {integrity: sha512-KpNARQA3Iwv+jTA0utUVVbrh+Jlrr1Fv0e56GGzAFOXN7dk/FviaDW8LHmK52DlcH4WP2n6gI8vN1aesBFgo9w==}
    dependencies:
      yallist: 3.1.1
    dev: false

  /lru-cache/6.0.0:
    resolution: {integrity: sha512-Jo6dJ04CmSjuznwJSS3pUeWmd/H0ffTlkXXgwZi+eq1UCmqQwCh+eLsYOYCwY991i2Fah4h1BEMCx4qThGbsiA==}
    engines: {node: '>=10'}
    dependencies:
      yallist: 4.0.0

  /make-dir/2.1.0:
    resolution: {integrity: sha512-LS9X+dc8KLxXCb8dni79fLIIUA5VyZoyjSMCwTluaXA0o27cCK0bhXkpgw+sTXVpPy/lSO57ilRixqk0vDmtRA==}
    engines: {node: '>=6'}
    dependencies:
      pify: 4.0.1
      semver: 5.7.1
    dev: false

  /make-dir/3.1.0:
    resolution: {integrity: sha512-g3FeP20LNwhALb/6Cz6Dd4F2ngze0jz7tbzrD2wAV+o9FeNHe4rL+yK2md0J/fiSf1sa1ADhXqi5+oVwOM/eGw==}
    engines: {node: '>=8'}
    dependencies:
      semver: 6.3.0
    dev: false

  /makeerror/1.0.12:
    resolution: {integrity: sha1-Pl3SB5qC6BLpg8xmEMSiyw6qgBo=}
    dependencies:
      tmpl: 1.0.5
    dev: false

  /mamacro/0.0.3:
    resolution: {integrity: sha512-qMEwh+UujcQ+kbz3T6V+wAmO2U8veoq2w+3wY8MquqwVA3jChfwY+Tk52GZKDfACEPjuZ7r2oJLejwpt8jtwTA==}
    dev: false

  /map-age-cleaner/0.1.3:
    resolution: {integrity: sha512-bJzx6nMoP6PDLPBFmg7+xRKeFZvFboMrGlxmNj9ClvX53KrmvM5bXFXEWjbz4cz1AFn+jWJ9z/DJSz7hrs0w3w==}
    engines: {node: '>=6'}
    dependencies:
      p-defer: 1.0.0
    dev: false

  /map-cache/0.2.2:
    resolution: {integrity: sha1-wyq9C9ZSXZsFFkW7TyasXcmKDb8=}
    engines: {node: '>=0.10.0'}
    dev: false

  /map-obj/1.0.1:
    resolution: {integrity: sha1-2TPOuSBdgr3PSIb2dCvcK03qFG0=}
    engines: {node: '>=0.10.0'}

  /map-obj/4.3.0:
    resolution: {integrity: sha1-kwT5Buk/qucIgNoQKp8d8OqLsFo=}
    engines: {node: '>=8'}
    dev: true

  /map-visit/1.0.0:
    resolution: {integrity: sha1-7Nyo8TFE5mDxtb1B8S80edmN+48=}
    engines: {node: '>=0.10.0'}
    dependencies:
      object-visit: 1.0.1
    dev: false

  /mathml-tag-names/2.1.3:
    resolution: {integrity: sha512-APMBEanjybaPzUrfqU0IMU5I0AswKMH7k8OTLs0vvV4KZpExkTkY87nR/zpbuTPj+gARop7aGUbl11pnDfW6xg==}
    dev: true

  /md5.js/1.3.5:
    resolution: {integrity: sha512-xitP+WxNPcTTOgnTJcrhM0xvdPepipPSf3I8EIpGKeFLjt3PlJLIDG3u8EX53ZIubkb+5U2+3rELYpEhHhzdkg==}
    dependencies:
      hash-base: 3.1.0
      inherits: 2.0.4
      safe-buffer: 5.2.1
    dev: false

  /mdast-util-from-markdown/0.8.5:
    resolution: {integrity: sha512-2hkTXtYYnr+NubD/g6KGBS/0mFmBcifAsI0yIWRiRo0PjVs6SSOSOdtzbp6kSGnShDN6G5aWZpKQ2lWRy27mWQ==}
    dependencies:
      '@types/mdast': 3.0.10
      mdast-util-to-string: 2.0.0
      micromark: 2.11.4
      parse-entities: 2.0.0
      unist-util-stringify-position: 2.0.3
    transitivePeerDependencies:
      - supports-color
    dev: true

  /mdast-util-to-markdown/0.6.5:
    resolution: {integrity: sha512-XeV9sDE7ZlOQvs45C9UKMtfTcctcaj/pGwH8YLbMHoMOXNNCn2LsqVQOqrF1+/NU8lKDAqozme9SCXWyo9oAcQ==}
    dependencies:
      '@types/unist': 2.0.6
      longest-streak: 2.0.4
      mdast-util-to-string: 2.0.0
      parse-entities: 2.0.0
      repeat-string: 1.6.1
      zwitch: 1.0.5
    dev: true

  /mdast-util-to-string/2.0.0:
    resolution: {integrity: sha512-AW4DRS3QbBayY/jJmD8437V1Gombjf8RSOUCMFBuo5iHi58AGEgVCKQ+ezHkZZDpAQS75hcBMpLqjpJTjtUL7w==}
    dev: true

  /mdn-data/2.0.14:
    resolution: {integrity: sha512-dn6wd0uw5GsdswPFfsgMp5NSB0/aDe6fK94YJV/AJDYXL6HVLWBsxeq7js7Ad+mU2K9LAlwpk6kN2D5mwCPVow==}
    dev: false

  /mdn-data/2.0.4:
    resolution: {integrity: sha512-iV3XNKw06j5Q7mi6h+9vbx23Tv7JkjEVgKHW4pimwyDGWm0OIQntJJ+u1C6mg6mK1EaTv42XQ7w76yuzH7M2cA==}
    dev: false

  /media-typer/0.3.0:
    resolution: {integrity: sha1-hxDXrwqmJvj/+hzgAWhUUmMlV0g=}
    engines: {node: '>= 0.6'}
    dev: false

  /mem/4.3.0:
    resolution: {integrity: sha512-qX2bG48pTqYRVmDB37rn/6PT7LcR8T7oAX3bf99u1Tt1nzxYfxkgqDwUwolPlXweM0XzBOBFzSx4kfp7KP1s/w==}
    engines: {node: '>=6'}
    dependencies:
      map-age-cleaner: 0.1.3
      mimic-fn: 2.1.0
      p-is-promise: 2.1.0
    dev: false

  /memory-fs/0.4.1:
    resolution: {integrity: sha1-OpoguEYlI+RHz7x+i7gO1me/xVI=}
    dependencies:
      errno: 0.1.8
      readable-stream: 2.3.7
    dev: false

  /memory-fs/0.5.0:
    resolution: {integrity: sha512-jA0rdU5KoQMC0e6ppoNRtpp6vjFq6+NY7r8hywnC7V+1Xj/MtHwGIbB1QaK/dunyjWteJzmkpd7ooeWg10T7GA==}
    engines: {node: '>=4.3.0 <5.0.0 || >=5.10'}
    dependencies:
      errno: 0.1.8
      readable-stream: 2.3.7
    dev: false

  /meow/3.7.0:
    resolution: {integrity: sha1-cstmi0JSKCkKu/qFaJJYcwioAfs=}
    engines: {node: '>=0.10.0'}
    dependencies:
      camelcase-keys: 2.1.0
      decamelize: 1.2.0
      loud-rejection: 1.6.0
      map-obj: 1.0.1
      minimist: 1.2.7
      normalize-package-data: 2.5.0
      object-assign: 4.1.1
      read-pkg-up: 1.0.1
      redent: 1.0.0
      trim-newlines: 1.0.0

  /meow/9.0.0:
    resolution: {integrity: sha512-+obSblOQmRhcyBt62furQqRAQpNyWXo8BuQ5bN7dG8wmwQ+vwHKp/rCFD4CrTP8CsDQD1sjoZ94K417XEUk8IQ==}
    engines: {node: '>=10'}
    dependencies:
      '@types/minimist': 1.2.2
      camelcase-keys: 6.2.2
      decamelize: 1.2.0
      decamelize-keys: 1.1.0
      hard-rejection: 2.1.0
      minimist-options: 4.1.0
      normalize-package-data: 3.0.3
      read-pkg-up: 7.0.1
      redent: 3.0.0
      trim-newlines: 3.0.1
      type-fest: 0.18.1
      yargs-parser: 20.2.9
    dev: true

  /merge-deep/3.0.3:
    resolution: {integrity: sha512-qtmzAS6t6grwEkNrunqTBdn0qKwFgNWvlxUbAV8es9M7Ot1EbyApytCnvE0jALPa46ZpKDUo527kKiaWplmlFA==}
    engines: {node: '>=0.10.0'}
    dependencies:
      arr-union: 3.1.0
      clone-deep: 0.2.4
      kind-of: 3.2.2
    dev: false

  /merge-descriptors/1.0.1:
    resolution: {integrity: sha1-sAqqVW3YtEVoFQ7J0blT8/kMu2E=}
    dev: false

  /merge-stream/2.0.0:
    resolution: {integrity: sha512-abv/qOcuPfk3URPfDzmZU1LKmuw8kT+0nIHvKrKgFrwifol/doWcdA4ZqsWQ8ENrFKkd67Mfpo/LovbIUsbt3w==}
    dev: false

  /merge2/1.4.1:
    resolution: {integrity: sha512-8q7VEgMJW4J8tcfVPy8g09NcQwZdbwFEqhe/WZkoIzjn/3TGDwtOCYtXGxA3O8tPzpczCCDgv+P2P5y00ZJOOg==}
    engines: {node: '>= 8'}

  /methods/1.1.2:
    resolution: {integrity: sha1-VSmk1nZUE07cxSZmVoNbD4Ua/O4=}
    engines: {node: '>= 0.6'}
    dev: false

  /microevent.ts/0.1.1:
    resolution: {integrity: sha512-jo1OfR4TaEwd5HOrt5+tAZ9mqT4jmpNAusXtyfNzqVm9uiSYFZlKM1wYL4oU7azZW/PxQW53wM0S6OR1JHNa2g==}
    dev: false

  /micromark/2.11.4:
    resolution: {integrity: sha512-+WoovN/ppKolQOFIAajxi7Lu9kInbPxFuTBVEavFcL8eAfVstoc5MocPmqBeAdBOJV00uaVjegzH4+MA0DN/uA==}
    dependencies:
      debug: 4.3.4
      parse-entities: 2.0.0
    transitivePeerDependencies:
      - supports-color
    dev: true

  /micromatch/3.1.10:
    resolution: {integrity: sha512-MWikgl9n9M3w+bpsY3He8L+w9eF9338xRl8IAO5viDizwSzziFEyUzo2xrrloB64ADbTf8uA8vRqqttDTOmccg==}
    engines: {node: '>=0.10.0'}
    dependencies:
      arr-diff: 4.0.0
      array-unique: 0.3.2
      braces: 2.3.2
      define-property: 2.0.2
      extend-shallow: 3.0.2
      extglob: 2.0.4
      fragment-cache: 0.2.1
      kind-of: 6.0.3
      nanomatch: 1.2.13
      object.pick: 1.3.0
      regex-not: 1.0.2
      snapdragon: 0.8.2
      to-regex: 3.0.2
    transitivePeerDependencies:
      - supports-color
    dev: false

  /micromatch/3.1.10_supports-color@6.1.0:
    resolution: {integrity: sha512-MWikgl9n9M3w+bpsY3He8L+w9eF9338xRl8IAO5viDizwSzziFEyUzo2xrrloB64ADbTf8uA8vRqqttDTOmccg==}
    engines: {node: '>=0.10.0'}
    dependencies:
      arr-diff: 4.0.0
      array-unique: 0.3.2
      braces: 2.3.2_supports-color@6.1.0
      define-property: 2.0.2
      extend-shallow: 3.0.2
      extglob: 2.0.4_supports-color@6.1.0
      fragment-cache: 0.2.1
      kind-of: 6.0.3
      nanomatch: 1.2.13_supports-color@6.1.0
      object.pick: 1.3.0
      regex-not: 1.0.2
      snapdragon: 0.8.2_supports-color@6.1.0
      to-regex: 3.0.2
    transitivePeerDependencies:
      - supports-color
    dev: false

  /micromatch/4.0.5:
    resolution: {integrity: sha512-DMy+ERcEW2q8Z2Po+WNXuw3c5YaUSFjAO5GsJqfEl7UjvtIuFKO6ZrKvcItdy98dwFI2N1tg3zNIdKaQT+aNdA==}
    engines: {node: '>=8.6'}
    dependencies:
      braces: 3.0.2
      picomatch: 2.3.1
    dev: true

  /miller-rabin/4.0.1:
    resolution: {integrity: sha512-115fLhvZVqWwHPbClyntxEVfVDfl9DLLTuJvq3g2O/Oxi8AiNouAHvDSzHS0viUJc+V5vm3eq91Xwqn9dp4jRA==}
    hasBin: true
    dependencies:
      bn.js: 4.12.0
      brorand: 1.1.0
    dev: false

  /mime-db/1.52.0:
    resolution: {integrity: sha512-sPU4uV7dYlvtWJxwwxHD0PuihVNiE7TyAbQ5SWxDCB9mUYvOgroQOwYQQOKPJ8CIbE+1ETVlOoK1UC2nU3gYvg==}
    engines: {node: '>= 0.6'}

  /mime-types/2.1.35:
    resolution: {integrity: sha512-ZDY+bPm5zTTF+YpCrAU9nK0UgICYPT0QtT1NZWFv4s++TNkcgVaT0g6+4R2uI4MjQjzysHB1zxuWL50hzaeXiw==}
    engines: {node: '>= 0.6'}
    dependencies:
      mime-db: 1.52.0

  /mime/1.6.0:
    resolution: {integrity: sha512-x0Vn8spI+wuJ1O6S7gnbaQg8Pxh4NNHb7KSINmEWKiPE4RKOplvijn+NkmYmmRgP68mc70j2EbeTFRsrswaQeg==}
    engines: {node: '>=4'}
    hasBin: true
    dev: false

  /mime/2.6.0:
    resolution: {integrity: sha1-oqaCqVzU0MsdYlfij4PafjWAA2c=}
    engines: {node: '>=4.0.0'}
    hasBin: true
    dev: false

  /mimic-fn/2.1.0:
    resolution: {integrity: sha512-OqbOk5oEQeAZ8WXWydlu9HJjz9WVdEIvamMCcXmuqUYjTknH/sqsWvhQ3vgwKFRR1HpjvNBKQ37nbJgYzGqGcg==}
    engines: {node: '>=6'}
    dev: false

  /min-indent/1.0.1:
    resolution: {integrity: sha512-I9jwMn07Sy/IwOj3zVkVik2JTvgpaykDZEigL6Rx6N9LbMywwUSMtxET+7lVoDLLd3O3IXwJwvuuns8UB/HeAg==}
    engines: {node: '>=4'}
    dev: true

  /mini-css-extract-plugin/0.9.0_webpack@4.41.5:
    resolution: {integrity: sha512-lp3GeY7ygcgAmVIcRPBVhIkf8Us7FZjA+ILpal44qLdSu11wmjKQ3d9k15lfD7pO4esu9eUIAW7qiYIBppv40A==}
    engines: {node: '>= 6.9.0'}
    peerDependencies:
      webpack: ^4.4.0
    dependencies:
      loader-utils: 1.4.0
      normalize-url: 1.9.1
      schema-utils: 1.0.0
      webpack: 4.41.5
      webpack-sources: 1.4.3
    dev: false

  /minimalistic-assert/1.0.1:
    resolution: {integrity: sha512-UtJcAD4yEaGtjPezWuO9wC4nwUnVH/8/Im3yEHQP4b67cXlD/Qr9hdITCU1xDbSEXg2XKNaP8jsReV7vQd00/A==}
    dev: false

  /minimalistic-crypto-utils/1.0.1:
    resolution: {integrity: sha1-9sAMHAsIIkblxNmd+4x8CDsrWCo=}
    dev: false

  /minimatch/3.0.4:
    resolution: {integrity: sha512-yJHVQEhyqPLUTgt9B83PXu6W3rx4MvvHvSUvToogpwoGDOUQ+yDrR0HRot+yOCdCO7u4hX3pWft6kWBBcqh0UA==}
    dependencies:
      brace-expansion: 1.1.11
    dev: false

  /minimatch/3.0.8:
    resolution: {integrity: sha512-6FsRAQsxQ61mw+qP1ZzbL9Bc78x2p5OqNgNpnoAFLTrX8n5Kxph0CsnhmKKNXTWjXqU5L0pGPR7hYk+XWZr60Q==}
    dependencies:
      brace-expansion: 1.1.11

  /minimatch/3.1.2:
    resolution: {integrity: sha512-J7p63hRiAjw1NDEww1W7i37+ByIrOWO5XQQAzZ3VOcL0PNybwpfmV/N05zFAzwQ9USyEcX6t3UO+K5aqBQOIHw==}
    dependencies:
      brace-expansion: 1.1.11

  /minimatch/5.1.0:
    resolution: {integrity: sha512-9TPBGGak4nHfGZsPBohm9AWg6NoT7QTCehS3BIJABslyZbzxfV78QM2Y6+i741OPZIafFAaiiEMh5OyIrJPgtg==}
    engines: {node: '>=10'}
    dependencies:
      brace-expansion: 2.0.1
    dev: false

  /minimist-options/4.1.0:
    resolution: {integrity: sha512-Q4r8ghd80yhO/0j1O3B2BjweX3fiHg9cdOwjJd2J76Q135c+NDxGCqdYKQ1SKBuFfgWbAUzBfvYjPUEeNgqN1A==}
    engines: {node: '>= 6'}
    dependencies:
      arrify: 1.0.1
      is-plain-obj: 1.1.0
      kind-of: 6.0.3
    dev: true

  /minimist/1.2.7:
    resolution: {integrity: sha512-bzfL1YUZsP41gmu/qjrEk0Q6i2ix/cVeAhbCbqH9u3zYutS1cLg00qhrD0M2MVdCcx4Sc0UpP2eBWo9rotpq6g==}

  /minipass-collect/1.0.2:
    resolution: {integrity: sha512-6T6lH0H8OG9kITm/Jm6tdooIbogG9e0tLgpY6mphXSm/A9u8Nq1ryBG+Qspiub9LjWlBPsPS3tWQ/Botq4FdxA==}
    engines: {node: '>= 8'}
    dependencies:
      minipass: 3.3.4
    dev: false

  /minipass-flush/1.0.5:
    resolution: {integrity: sha512-JmQSYYpPUqX5Jyn1mXaRwOda1uQ8HP5KAT/oDSLCzt1BYRhQU0/hDtsB1ufZfEEzMZ9aAVmsBw8+FWsIXlClWw==}
    engines: {node: '>= 8'}
    dependencies:
      minipass: 3.3.4
    dev: false

  /minipass-pipeline/1.2.4:
    resolution: {integrity: sha512-xuIq7cIOt09RPRJ19gdi4b+RiNvDFYe5JH+ggNvBqGqpQXcru3PcRmOZuHBKWK1Txf9+cQ+HMVN4d6z46LZP7A==}
    engines: {node: '>=8'}
    dependencies:
      minipass: 3.3.4
    dev: false

  /minipass/3.3.4:
    resolution: {integrity: sha512-I9WPbWHCGu8W+6k1ZiGpPu0GkoKBeorkfKNuAFBNS1HNFJvke82sxvI5bzcCNpWPorkOO5QQ+zomzzwRxejXiw==}
    engines: {node: '>=8'}
    dependencies:
      yallist: 4.0.0
    dev: false

  /mississippi/3.0.0:
    resolution: {integrity: sha512-x471SsVjUtBRtcvd4BzKE9kFC+/2TeWgKCgw0bZcw1b9l2X3QX5vCWgF+KaZaYm87Ss//rHnWryupDrgLvmSkA==}
    engines: {node: '>=4.0.0'}
    dependencies:
      concat-stream: 1.6.2
      duplexify: 3.7.1
      end-of-stream: 1.4.4
      flush-write-stream: 1.1.1
      from2: 2.3.0
      parallel-transform: 1.2.0
      pump: 3.0.0
      pumpify: 1.5.1
      stream-each: 1.2.3
      through2: 2.0.5
    dev: false

  /mixin-deep/1.3.2:
    resolution: {integrity: sha512-WRoDn//mXBiJ1H40rqa3vH0toePwSsGb45iInWlTySa+Uu4k3tYUSxa2v1KqAiLtvlrSzaExqS1gtk96A9zvEA==}
    engines: {node: '>=0.10.0'}
    dependencies:
      for-in: 1.0.2
      is-extendable: 1.0.1
    dev: false

  /mixin-object/2.0.1:
    resolution: {integrity: sha1-T7lJRB2rGCVA8f4DW6YOGUel5X4=}
    engines: {node: '>=0.10.0'}
    dependencies:
      for-in: 0.1.8
      is-extendable: 0.1.1
    dev: false

  /mkdirp/0.5.6:
    resolution: {integrity: sha512-FP+p8RB8OWpF3YZBCrP5gtADmtXApB5AMLn+vdyA+PyxCjrCs00mjyUozssO33cwDeT3wNGdLxJ5M//YqtHAJw==}
    hasBin: true
    dependencies:
      minimist: 1.2.7

  /move-concurrently/1.0.1:
    resolution: {integrity: sha1-viwAX9oy4LKa8fBdfEszIUxwH5I=}
    dependencies:
      aproba: 1.2.0
      copy-concurrently: 1.0.5
      fs-write-stream-atomic: 1.0.10
      mkdirp: 0.5.6
      rimraf: 2.7.1
      run-queue: 1.0.3
    dev: false

  /ms/2.0.0:
    resolution: {integrity: sha1-VgiurfwAvmwpAd9fmGF4jeDVl8g=}
    dev: false

  /ms/2.1.2:
    resolution: {integrity: sha512-sGkPx+VjMtmA6MX27oA4FBFELFCZZ4S4XqeGOXCv68tT+jb3vk/RyaKWP0PTKyWtmLSM0b+adUTEvbs1PEaH2w==}

  /ms/2.1.3:
    resolution: {integrity: sha512-6FlzubTLZG3J2a/NVCAleEhjzq5oxgHyaCU9yYXvcLsvoVaHJq/s5xXI6/XXP6tz7R9xAOtHnSO/tXtF3WRTlA==}
    dev: false

  /multicast-dns-service-types/1.1.0:
    resolution: {integrity: sha1-iZ8R2WhuXgXLkbNdXw5jt3PPyQE=}
    dev: false

  /multicast-dns/6.2.3:
    resolution: {integrity: sha512-ji6J5enbMyGRHIAkAOu3WdV8nggqviKCEKtXcOqfphZZtQrmHKycfynJ2V7eVPUA4NhJ6V7Wf4TmGbTwKE9B6g==}
    hasBin: true
    dependencies:
      dns-packet: 1.3.4
      thunky: 1.1.0
    dev: false

  /mute-stream/0.0.8:
    resolution: {integrity: sha512-nnbWWOkoWyUsTjKrhgD0dcz22mdkSnpYqbEjIm2nhwhuxlSkpywJmBo8h0ZqJdkp73mb90SssHkN4rsRaBAfAA==}
    dev: false

  /nan/2.17.0:
    resolution: {integrity: sha512-2ZTgtl0nJsO0KQCjEpxcIr5D+Yv90plTitZt9JBfQvVJDS5seMl3FOvsh3+9CoYWXf/1l5OaZzzF6nDm4cagaQ==}

  /nanomatch/1.2.13:
    resolution: {integrity: sha512-fpoe2T0RbHwBTBUOftAfBPaDEi06ufaUai0mE6Yn1kacc3SnTErfb/h+X94VXzI64rKFHYImXSvdwGGCmwOqCA==}
    engines: {node: '>=0.10.0'}
    dependencies:
      arr-diff: 4.0.0
      array-unique: 0.3.2
      define-property: 2.0.2
      extend-shallow: 3.0.2
      fragment-cache: 0.2.1
      is-windows: 1.0.2
      kind-of: 6.0.3
      object.pick: 1.3.0
      regex-not: 1.0.2
      snapdragon: 0.8.2
      to-regex: 3.0.2
    transitivePeerDependencies:
      - supports-color
    dev: false

  /nanomatch/1.2.13_supports-color@6.1.0:
    resolution: {integrity: sha512-fpoe2T0RbHwBTBUOftAfBPaDEi06ufaUai0mE6Yn1kacc3SnTErfb/h+X94VXzI64rKFHYImXSvdwGGCmwOqCA==}
    engines: {node: '>=0.10.0'}
    dependencies:
      arr-diff: 4.0.0
      array-unique: 0.3.2
      define-property: 2.0.2
      extend-shallow: 3.0.2
      fragment-cache: 0.2.1
      is-windows: 1.0.2
      kind-of: 6.0.3
      object.pick: 1.3.0
      regex-not: 1.0.2
      snapdragon: 0.8.2_supports-color@6.1.0
      to-regex: 3.0.2
    transitivePeerDependencies:
      - supports-color
    dev: false

  /natural-compare/1.4.0:
    resolution: {integrity: sha1-Sr6/7tdUHywnrPspvbvRXI1bpPc=}
    dev: false

  /negotiator/0.6.3:
    resolution: {integrity: sha512-+EUsqGPLsM+j/zdChZjsnX51g4XrHFOIXwfnCVPGlQk/k5giakcKsuxCObBRu6DSm9opw/O6slWbJdghQM4bBg==}
    engines: {node: '>= 0.6'}
    dev: false

  /neo-async/2.6.2:
    resolution: {integrity: sha512-Yd3UES5mWCSqR+qNT93S3UoYUkqAZ9lLg8a7g9rimsWmYGK8cVToA4/sF3RrshdyV3sAGMXVUmpMYOw+dLpOuw==}

  /next-tick/1.1.0:
    resolution: {integrity: sha512-CXdUiJembsNjuToQvxayPZF9Vqht7hewsvy2sOWafLvi2awflj9mOC6bHIg50orX8IJvWKY9wYQ/zB2kogPslQ==}
    dev: false

  /nice-try/1.0.5:
    resolution: {integrity: sha512-1nh45deeb5olNY7eX82BkPO7SSxR5SSYJiPTrTdFUVYwAl8CKMA5N9PjTYkHiRjisVcxcQ1HXdLhx2qxxJzLNQ==}
    dev: false

  /no-case/3.0.4:
    resolution: {integrity: sha512-fgAN3jGAh+RoxUGZHTSOLJIqUc2wmoBwGR4tbpNAKmmovFoWq0OdRkb0VkldReO2a2iBT/OEulG9XSUc10r3zg==}
    dependencies:
      lower-case: 2.0.2
      tslib: 2.4.0
    dev: false

  /node-fetch/1.7.3:
    resolution: {integrity: sha512-NhZ4CsKx7cYm2vSrBAr2PvFOe6sWDf0UYLRqA6svUYg7+/TSfVAu49jYC4BvQ4Sms9SZgdqGBgroqfDhJdTyKQ==}
    dependencies:
      encoding: 0.1.13
      is-stream: 1.1.0
    dev: false

  /node-forge/0.10.0:
    resolution: {integrity: sha512-PPmu8eEeG9saEUvI97fm4OYxXVB6bFvyNTyiUOBichBpFG8A1Ljw3bY62+5oOjDEMHRnd0Y7HQ+x7uzxOzC6JA==}
    engines: {node: '>= 6.0.0'}
    dev: false

  /node-gyp/3.8.0:
    resolution: {integrity: sha512-3g8lYefrRRzvGeSowdJKAKyks8oUpLEd/DyPV4eMhVlhJ0aNaZqIrNUIPuEWWTAoPqyFkfGrM67MC69baqn6vA==}
    engines: {node: '>= 0.8.0'}
    hasBin: true
    dependencies:
      fstream: 1.0.12
      glob: 7.2.3
      graceful-fs: 4.2.10
      mkdirp: 0.5.6
      nopt: 3.0.6
      npmlog: 4.1.2
      osenv: 0.1.5
      request: 2.88.2
      rimraf: 2.7.1
      semver: 5.3.0
      tar: 2.2.2
      which: 1.3.1

  /node-int64/0.4.0:
    resolution: {integrity: sha1-h6kGXNs1XTGC2PlM4RGIuCXGijs=}
    dev: false

  /node-libs-browser/2.2.1:
    resolution: {integrity: sha512-h/zcD8H9kaDZ9ALUWwlBUDo6TKF8a7qBSCSEGfjTVIYeqsioSKaAX+BN7NgiMGp6iSIXZ3PxgCu8KS3b71YK5Q==}
    dependencies:
      assert: 1.5.0
      browserify-zlib: 0.2.0
      buffer: 4.9.2
      console-browserify: 1.2.0
      constants-browserify: 1.0.0
      crypto-browserify: 3.12.0
      domain-browser: 1.2.0
      events: 3.3.0
      https-browserify: 1.0.0
      os-browserify: 0.3.0
      path-browserify: 0.0.1
      process: 0.11.10
      punycode: 1.4.1
      querystring-es3: 0.2.1
      readable-stream: 2.3.7
      stream-browserify: 2.0.2
      stream-http: 2.8.3
      string_decoder: 1.3.0
      timers-browserify: 2.0.12
      tty-browserify: 0.0.0
      url: 0.11.0
      util: 0.11.1
      vm-browserify: 1.1.2
    dev: false

  /node-notifier/5.4.5:
    resolution: {integrity: sha512-tVbHs7DyTLtzOiN78izLA85zRqB9NvEXkAf014Vx3jtSvn/xBl6bR8ZYifj+dFcFrKI21huSQgJZ6ZtL3B4HfQ==}
    dependencies:
      growly: 1.3.0
      is-wsl: 1.1.0
      semver: 5.7.1
      shellwords: 0.1.1
      which: 1.3.1
    dev: false

  /node-releases/1.1.77:
    resolution: {integrity: sha1-ULDP7ehV3TdOdYW/Io/zTlfBwy4=}
    dev: false

  /node-releases/2.0.6:
    resolution: {integrity: sha512-PiVXnNuFm5+iYkLBNeq5211hvO38y63T0i2KKh2KnUs3RpzJ+JtODFjkD8yjLwnDkTYF1eKXheUwdssR+NRZdg==}

  /node-sass/4.14.1:
    resolution: {integrity: sha512-sjCuOlvGyCJS40R8BscF5vhVlQjNN069NtQ1gSxyK1u9iqvn6tf7O1R4GNowVZfiZUCRt5MmMs1xd+4V/7Yr0g==}
    engines: {node: '>=0.10.0'}
    hasBin: true
    requiresBuild: true
    dependencies:
      async-foreach: 0.1.3
      chalk: 1.1.3
      cross-spawn: 3.0.1
      gaze: 1.1.3
      get-stdin: 4.0.1
      glob: 7.2.3
      in-publish: 2.0.1
      lodash: 4.17.21
      meow: 3.7.0
      mkdirp: 0.5.6
      nan: 2.17.0
      node-gyp: 3.8.0
      npmlog: 4.1.2
      request: 2.88.2
      sass-graph: 2.2.5
      stdout-stream: 1.4.1
      true-case-path: 1.0.3

  /nopt/3.0.6:
    resolution: {integrity: sha1-xkZdvwirzU2zWTF/eaxopkayj/k=}
    hasBin: true
    dependencies:
      abbrev: 1.1.1

  /normalize-package-data/2.5.0:
    resolution: {integrity: sha512-/5CMN3T0R4XTj4DcGaexo+roZSdSFW/0AOOTROrjxzCG1wrWXEsGbRKevjlIL+ZDE4sZlJr5ED4YW0yqmkK+eA==}
    dependencies:
      hosted-git-info: 2.8.9
      resolve: 1.22.1
      semver: 5.7.1
      validate-npm-package-license: 3.0.4

  /normalize-package-data/3.0.3:
    resolution: {integrity: sha512-p2W1sgqij3zMMyRC067Dg16bfzVH+w7hyegmpIvZ4JNjqtGOVAIvLmjBx3yP7YTe9vKJgkoNOPjwQGogDoMXFA==}
    engines: {node: '>=10'}
    dependencies:
      hosted-git-info: 4.1.0
      is-core-module: 2.11.0
      semver: 7.3.8
      validate-npm-package-license: 3.0.4
    dev: true

  /normalize-path/2.1.1:
    resolution: {integrity: sha1-GrKLVW4Zg2Oowab35vogE3/mrtk=}
    engines: {node: '>=0.10.0'}
    dependencies:
      remove-trailing-separator: 1.1.0
    dev: false

  /normalize-path/3.0.0:
    resolution: {integrity: sha512-6eZs5Ls3WtCisHWp9S2GUy8dqkpGi4BVSz3GaqiE6ezub0512ESztXUwUB6C6IKbQkY2Pnb/mD4WYojCRwcwLA==}
    engines: {node: '>=0.10.0'}
    dev: false

  /normalize-range/0.1.2:
    resolution: {integrity: sha1-LRDAa9/TEuqXd2laTShDlFa3WUI=}
    engines: {node: '>=0.10.0'}

  /normalize-selector/0.2.0:
    resolution: {integrity: sha1-0LFF62kRicY6eNIB3E/bEpPvDAM=}
    dev: true

  /normalize-url/1.9.1:
    resolution: {integrity: sha1-LMDWazHqIwNkWENuNiDYWVTGbDw=}
    engines: {node: '>=4'}
    dependencies:
      object-assign: 4.1.1
      prepend-http: 1.0.4
      query-string: 4.3.4
      sort-keys: 1.1.2
    dev: false

  /normalize-url/3.3.0:
    resolution: {integrity: sha512-U+JJi7duF1o+u2pynbp2zXDW2/PADgC30f0GsHZtRh+HOcXHnw137TrNlyxxRvWW5fjKd3bcLHPxofWuCjaeZg==}
    engines: {node: '>=6'}
    dev: false

  /normalize.css/7.0.0:
    resolution: {integrity: sha1-q/sd2CRwZ04DIrU86xqvQSk45L8=}
    dev: false

  /npm-run-path/2.0.2:
    resolution: {integrity: sha1-NakjLfo11wZ7TLLd8jV7GHFTbF8=}
    engines: {node: '>=4'}
    dependencies:
      path-key: 2.0.1
    dev: false

  /npmlog/4.1.2:
    resolution: {integrity: sha512-2uUqazuKlTaSI/dC8AzicUck7+IrEaOnN/e0jd3Xtt1KcGpwx30v50mL7oPyr/h9bL3E4aZccVwpwP+5W9Vjkg==}
    dependencies:
      are-we-there-yet: 1.1.7
      console-control-strings: 1.1.0
      gauge: 2.7.4
      set-blocking: 2.0.0

  /nth-check/1.0.2:
    resolution: {integrity: sha512-WeBOdju8SnzPN5vTUJYxYUxLeXpCaVP5i5e0LF8fg7WORF2Wd7wFX/pk0tYZk7s8T+J7VLy0Da6J1+wCT0AtHg==}
    dependencies:
      boolbase: 1.0.0
    dev: false

  /nth-check/2.1.1:
    resolution: {integrity: sha512-lqjrjmaOoAnWfMmBPL+XNnynZh2+swxiX3WUE0s4yEHI6m+AwrK2UZOimIRl3X/4QctVqS8AiZjFqyOGrMXb/w==}
    dependencies:
      boolbase: 1.0.0
    dev: false

  /num2fraction/1.2.2:
    resolution: {integrity: sha1-b2gragJ6Tp3fpFZM0lidHU5mnt4=}

  /number-is-nan/1.0.1:
    resolution: {integrity: sha1-CXtgK1NCKlIsGvuHkDGDNpQaAR0=}
    engines: {node: '>=0.10.0'}

  /nwsapi/2.2.2:
    resolution: {integrity: sha512-90yv+6538zuvUMnN+zCr8LuV6bPFdq50304114vJYJ8RDyK8D5O9Phpbd6SZWgI7PwzmmfN1upeOJlvybDSgCw==}
    dev: false

  /oauth-sign/0.9.0:
    resolution: {integrity: sha512-fexhUFFPTGV8ybAtSIGbV6gOkSv8UtRbDBnAyLQw4QPKkgNlsH2ByPGtMUqdWkos6YCRmAqViwgZrJc/mRDzZQ==}

  /object-assign/4.1.1:
    resolution: {integrity: sha1-IQmtx5ZYh8/AXLvUQsrIv7s2CGM=}
    engines: {node: '>=0.10.0'}

  /object-copy/0.1.0:
    resolution: {integrity: sha1-fn2Fi3gb18mRpBupde04EnVOmYw=}
    engines: {node: '>=0.10.0'}
    dependencies:
      copy-descriptor: 0.1.1
      define-property: 0.2.5
      kind-of: 3.2.2
    dev: false

  /object-hash/2.2.0:
    resolution: {integrity: sha512-gScRMn0bS5fH+IuwyIFgnh9zBdo4DV+6GhygmWM9HyNJSgS0hScp1f5vjtm7oIIOiT9trXrShAkLFSc2IqKNgw==}
    engines: {node: '>= 6'}
    dev: false

  /object-inspect/1.12.2:
    resolution: {integrity: sha512-z+cPxW0QGUp0mcqcsgQyLVRDoXFQbXOwBaqyF7VIgI4TWNQsDHrBpUQslRmIfAoYWdYzs6UlKJtB2XJpTaNSpQ==}
    dev: false

  /object-is/1.1.5:
    resolution: {integrity: sha512-3cyDsyHgtmi7I7DfSSI2LDp6SK2lwvtbg0p0R1e0RvTqF5ceGx+K2dfSjm1bKDMVCFEDAQvy+o8c6a7VujOddw==}
    engines: {node: '>= 0.4'}
    dependencies:
      call-bind: 1.0.2
      define-properties: 1.1.4
    dev: false

  /object-keys/1.1.1:
    resolution: {integrity: sha512-NuAESUOUMrlIXOfHKzD6bpPu3tYt3xvjNdRIQ+FeT0lNb4K8WR70CaDxhuNguS2XG+GjkyMwOzsN5ZktImfhLA==}
    engines: {node: '>= 0.4'}
    dev: false

  /object-path/0.11.4:
    resolution: {integrity: sha1-NwrnUvvzfePqcKhhwju6iRVpGUk=}
    engines: {node: '>=0.10.0'}
    dev: false

  /object-visit/1.0.1:
    resolution: {integrity: sha1-95xEk68MU3e1n+OdOV5BBC3QRbs=}
    engines: {node: '>=0.10.0'}
    dependencies:
      isobject: 3.0.1
    dev: false

  /object.assign/4.1.4:
    resolution: {integrity: sha512-1mxKf0e58bvyjSCtKYY4sRe9itRk3PJpquJOjeIkz885CczcI4IvJJDLPS72oowuSh+pBxUFROpX+TU++hxhZQ==}
    engines: {node: '>= 0.4'}
    dependencies:
      call-bind: 1.0.2
      define-properties: 1.1.4
      has-symbols: 1.0.3
      object-keys: 1.1.1
    dev: false

  /object.entries/1.1.5:
    resolution: {integrity: sha1-4azdF8TeLNltWghIfPuduE2IGGE=}
    engines: {node: '>= 0.4'}
    dependencies:
      call-bind: 1.0.2
      define-properties: 1.1.4
      es-abstract: 1.20.4
    dev: false

  /object.fromentries/2.0.5:
    resolution: {integrity: sha1-ezeyBRCcIedB5gVyf+iwrV+gglE=}
    engines: {node: '>= 0.4'}
    dependencies:
      call-bind: 1.0.2
      define-properties: 1.1.4
      es-abstract: 1.20.4
    dev: false

  /object.getownpropertydescriptors/2.1.4:
    resolution: {integrity: sha512-sccv3L/pMModT6dJAYF3fzGMVcb38ysQ0tEE6ixv2yXJDtEIPph268OlAdJj5/qZMZDq2g/jqvwppt36uS/uQQ==}
    engines: {node: '>= 0.8'}
    dependencies:
      array.prototype.reduce: 1.0.4
      call-bind: 1.0.2
      define-properties: 1.1.4
      es-abstract: 1.20.4
    dev: false

  /object.pick/1.3.0:
    resolution: {integrity: sha1-h6EKxMFpS9Lhy/U1kaZhQftd10c=}
    engines: {node: '>=0.10.0'}
    dependencies:
      isobject: 3.0.1
    dev: false

  /object.values/1.1.5:
    resolution: {integrity: sha1-lZ9j486e8QhyAzMIITHkpFm3Fqw=}
    engines: {node: '>= 0.4'}
    dependencies:
      call-bind: 1.0.2
      define-properties: 1.1.4
      es-abstract: 1.20.4
    dev: false

  /obuf/1.1.2:
    resolution: {integrity: sha512-PX1wu0AmAdPqOL1mWhqmlOd8kOIZQwGZw6rh7uby9fTc5lhaOWFLX3I6R1hrF9k3zUY40e6igsLGkDXK92LJNg==}
    dev: false

  /on-finished/2.4.1:
    resolution: {integrity: sha512-oVlzkg3ENAhCk2zdv7IJwd/QUD4z2RxRwpkcGY8psCVcCYZNq4wYnVWALHM+brtuJjePWiYF/ClmuDr8Ch5+kg==}
    engines: {node: '>= 0.8'}
    dependencies:
      ee-first: 1.1.1
    dev: false

  /on-headers/1.0.2:
    resolution: {integrity: sha512-pZAE+FJLoyITytdqK0U5s+FIpjN0JP3OzFi/u8Rx+EV5/W+JTWGXG8xFzevE7AjBfDqHv/8vL8qQsIhHnqRkrA==}
    engines: {node: '>= 0.8'}
    dev: false

  /once/1.4.0:
    resolution: {integrity: sha1-WDsap3WWHUsROsF9nFC6753Xa9E=}
    dependencies:
      wrappy: 1.0.2

  /onetime/5.1.2:
    resolution: {integrity: sha512-kbpaSSGJTWdAY5KPVeMOKXSrPtr8C8C7wodJbcsd51jRnmD+GZu8Y0VoU6Dm5Z4vWr0Ig/1NKuWRKf7j5aaYSg==}
    engines: {node: '>=6'}
    dependencies:
      mimic-fn: 2.1.0
    dev: false

  /open/7.4.2:
    resolution: {integrity: sha512-MVHddDVweXZF3awtlAS+6pgKLlm/JgxZ90+/NBurBoQctVOOB/zDdVjcyPzQ+0laDGbsWgrRkflI65sQeOgT9Q==}
    engines: {node: '>=8'}
    dependencies:
      is-docker: 2.2.1
      is-wsl: 2.2.0
    dev: false

  /opn/5.5.0:
    resolution: {integrity: sha512-PqHpggC9bLV0VeWcdKhkpxY+3JTzetLSqTCWL/z/tFIbI6G8JCjondXklT1JinczLz2Xib62sSp0T/gKT4KksA==}
    engines: {node: '>=4'}
    dependencies:
      is-wsl: 1.1.0
    dev: false

  /optimize-css-assets-webpack-plugin/5.0.3_webpack@4.41.5:
    resolution: {integrity: sha512-q9fbvCRS6EYtUKKSwI87qm2IxlyJK5b4dygW1rKUBT6mMDhdG5e5bZT63v6tnJR9F9FB/H5a0HTmtw+laUBxKA==}
    peerDependencies:
      webpack: ^4.0.0
    dependencies:
      cssnano: 4.1.11
      last-call-webpack-plugin: 3.0.0
      webpack: 4.41.5
    dev: false

  /optionator/0.8.3:
    resolution: {integrity: sha512-+IW9pACdk3XWmmTXG8m3upGUJst5XRGzxMRjXzAuJ1XnIFNvfhjjIuYkDvysnPQ7qzqVzLt78BCruntqRhWQbA==}
    engines: {node: '>= 0.8.0'}
    dependencies:
      deep-is: 0.1.4
      fast-levenshtein: 2.0.6
      levn: 0.3.0
      prelude-ls: 1.1.2
      type-check: 0.3.2
      word-wrap: 1.2.3
    dev: false

  /os-browserify/0.3.0:
    resolution: {integrity: sha1-hUNzx/XCMVkU/Jv8a9gjj92h7Cc=}
    dev: false

  /os-homedir/1.0.2:
    resolution: {integrity: sha1-/7xJiDNuDoM94MFox+8VISGqf7M=}
    engines: {node: '>=0.10.0'}

  /os-locale/3.1.0:
    resolution: {integrity: sha512-Z8l3R4wYWM40/52Z+S265okfFj8Kt2cC2MKY+xNi3kFs+XGI7WXu/I309QQQYbRW4ijiZ+yxs9pqEhJh0DqW3Q==}
    engines: {node: '>=6'}
    dependencies:
      execa: 1.0.0
      lcid: 2.0.0
      mem: 4.3.0
    dev: false

  /os-tmpdir/1.0.2:
    resolution: {integrity: sha1-u+Z0BseaqFxc/sdm/lc0VV36EnQ=}
    engines: {node: '>=0.10.0'}

  /osenv/0.1.5:
    resolution: {integrity: sha512-0CWcCECdMVc2Rw3U5w9ZjqX6ga6ubk1xDVKxtBQPK7wis/0F2r9T6k4ydGYhecl7YUBxBVxhL5oisPsNxAPe2g==}
    dependencies:
      os-homedir: 1.0.2
      os-tmpdir: 1.0.2

  /p-defer/1.0.0:
    resolution: {integrity: sha1-n26xgvbJqozXQwBKfU+WsZaw+ww=}
    engines: {node: '>=4'}
    dev: false

  /p-each-series/1.0.0:
    resolution: {integrity: sha1-kw89Et0fUOdDRFeiLNbwSsatf3E=}
    engines: {node: '>=4'}
    dependencies:
      p-reduce: 1.0.0
    dev: false

  /p-finally/1.0.0:
    resolution: {integrity: sha1-P7z7FbiZpEEjs0ttzBi3JDNqLK4=}
    engines: {node: '>=4'}
    dev: false

  /p-is-promise/2.1.0:
    resolution: {integrity: sha512-Y3W0wlRPK8ZMRbNq97l4M5otioeA5lm1z7bkNkxCka8HSPjR0xRWmpCmc9utiaLP9Jb1eD8BgeIxTW4AIF45Pg==}
    engines: {node: '>=6'}
    dev: false

  /p-limit/1.3.0:
    resolution: {integrity: sha512-vvcXsLAJ9Dr5rQOPk7toZQZJApBl2K4J6dANSsEuh6QI41JYcsS/qhTGa9ErIUUgK3WNQoJYvylxvjqmiqEA9Q==}
    engines: {node: '>=4'}
    dependencies:
      p-try: 1.0.0
    dev: false

  /p-limit/2.3.0:
    resolution: {integrity: sha512-//88mFWSJx8lxCzwdAABTJL2MyWB12+eIY7MDL2SqLmAkeKU9qxRvWuSyTjm3FUmpBEMuFfckAIqEaVGUDxb6w==}
    engines: {node: '>=6'}
    dependencies:
      p-try: 2.2.0

  /p-locate/2.0.0:
    resolution: {integrity: sha1-IKAQOyIqcMj9OcwuWAaA893l7EM=}
    engines: {node: '>=4'}
    dependencies:
      p-limit: 1.3.0
    dev: false

  /p-locate/3.0.0:
    resolution: {integrity: sha512-x+12w/To+4GFfgJhBEpiDcLozRJGegY+Ei7/z0tSLkMmxGZNybVMSfWj9aJn8Z5Fc7dBUNJOOVgPv2H7IwulSQ==}
    engines: {node: '>=6'}
    dependencies:
      p-limit: 2.3.0

  /p-locate/4.1.0:
    resolution: {integrity: sha512-R79ZZ/0wAxKGu3oYMlz8jy/kbhsNrS7SKZ7PxEHBgJ5+F2mtFW2fK2cOtBh1cHYkQsbzFV7I+EoRKe6Yt0oK7A==}
    engines: {node: '>=8'}
    dependencies:
      p-limit: 2.3.0

  /p-map/2.1.0:
    resolution: {integrity: sha512-y3b8Kpd8OAN444hxfBbFfj1FY/RjtTd8tzYwhUqNYXx0fXx2iX4maP4Qr6qhIKbQXI02wTLAda4fYUbDagTUFw==}
    engines: {node: '>=6'}
    dev: false

  /p-map/3.0.0:
    resolution: {integrity: sha512-d3qXVTF/s+W+CdJ5A29wywV2n8CQQYahlgz2bFiA+4eVNJbHJodPZ+/gXwPGh0bOqA+j8S+6+ckmvLGPk1QpxQ==}
    engines: {node: '>=8'}
    dependencies:
      aggregate-error: 3.1.0
    dev: false

  /p-reduce/1.0.0:
    resolution: {integrity: sha1-GMKw3ZNqRpClKfgjH1ig/bakffo=}
    engines: {node: '>=4'}
    dev: false

  /p-retry/3.0.1:
    resolution: {integrity: sha512-XE6G4+YTTkT2a0UWb2kjZe8xNwf8bIbnqpc/IS/idOBVhyves0mK5OJgeocjx7q5pvX/6m23xuzVPYT1uGM73w==}
    engines: {node: '>=6'}
    dependencies:
      retry: 0.12.0
    dev: false

  /p-try/1.0.0:
    resolution: {integrity: sha1-y8ec26+P1CKOE/Yh8rGiN8GyB7M=}
    engines: {node: '>=4'}
    dev: false

  /p-try/2.2.0:
    resolution: {integrity: sha512-R4nPAVTAU0B9D35/Gk3uJf/7XYbQcyohSKdvAxIRSNghFl4e71hVoGnBNQz9cWaXxO2I10KTC+3jMdvvoKw6dQ==}
    engines: {node: '>=6'}

  /pako/1.0.11:
    resolution: {integrity: sha512-4hLB8Py4zZce5s4yd9XzopqwVv/yGNhV1Bl8NTmCq1763HeK2+EwVTv+leGeL13Dnh2wfbqowVPXCIO0z4taYw==}
    dev: false

  /parallel-transform/1.2.0:
    resolution: {integrity: sha512-P2vSmIu38uIlvdcU7fDkyrxj33gTUy/ABO5ZUbGowxNCopBq/OoD42bP4UmMrJoPyk4Uqf0mu3mtWBhHCZD8yg==}
    dependencies:
      cyclist: 1.0.1
      inherits: 2.0.4
      readable-stream: 2.3.7
    dev: false

  /param-case/3.0.4:
    resolution: {integrity: sha512-RXlj7zCYokReqWpOPH9oYivUzLYZ5vAPIfEmCTNViosC78F8F0H9y7T7gG2M39ymgutxF5gcFEsyZQSph9Bp3A==}
    dependencies:
      dot-case: 3.0.4
      tslib: 2.4.0
    dev: false

  /parent-module/1.0.1:
    resolution: {integrity: sha512-GQ2EWRpQV8/o+Aw8YqtfZZPfNRWZYkbidE9k5rpl/hC3vtHHBfGm2Ifi6qWV+coDGkrUKZAxE3Lot5kcsRlh+g==}
    engines: {node: '>=6'}
    dependencies:
      callsites: 3.1.0

  /parse-asn1/5.1.6:
    resolution: {integrity: sha512-RnZRo1EPU6JBnra2vGHj0yhp6ebyjBZpmUCLHWiFhxlzvBCCpAuZ7elsBp1PVAbQN0/04VD/19rfzlBSwLstMw==}
    dependencies:
      asn1.js: 5.4.1
      browserify-aes: 1.2.0
      evp_bytestokey: 1.0.3
      pbkdf2: 3.1.2
      safe-buffer: 5.2.1
    dev: false

  /parse-entities/2.0.0:
    resolution: {integrity: sha512-kkywGpCcRYhqQIchaWqZ875wzpS/bMKhz5HnN3p7wveJTkTtyAB/AlnS0f8DFSqYW1T82t6yEAkEcB+A1I3MbQ==}
    dependencies:
      character-entities: 1.2.4
      character-entities-legacy: 1.1.4
      character-reference-invalid: 1.1.4
      is-alphanumerical: 1.0.4
      is-decimal: 1.0.4
      is-hexadecimal: 1.0.4
    dev: true

  /parse-json/2.2.0:
    resolution: {integrity: sha1-9ID0BDTvgHQfhGkJn43qGPVaTck=}
    engines: {node: '>=0.10.0'}
    dependencies:
      error-ex: 1.3.2

  /parse-json/4.0.0:
    resolution: {integrity: sha1-vjX1Qlvh9/bHRxhPmKeIy5lHfuA=}
    engines: {node: '>=4'}
    dependencies:
      error-ex: 1.3.2
      json-parse-better-errors: 1.0.2
    dev: false

  /parse-json/5.2.0:
    resolution: {integrity: sha512-ayCKvm/phCGxOkYRSCM82iDwct8/EonSEgCSxWxD7ve6jHggsFl4fZVQBPRNgQoKiuV/odhFrGzQXZwbifC8Rg==}
    engines: {node: '>=8'}
    dependencies:
      '@babel/code-frame': 7.18.6
      error-ex: 1.3.2
      json-parse-even-better-errors: 2.3.1
      lines-and-columns: 1.2.4

  /parse5/4.0.0:
    resolution: {integrity: sha512-VrZ7eOd3T1Fk4XWNXMgiGBK/z0MG48BWG2uQNU4I72fkQuKUTZpl+u9k+CxEG0twMVzSmXEEz12z5Fnw1jIQFA==}
    dev: false

  /parse5/5.1.0:
    resolution: {integrity: sha512-fxNG2sQjHvlVAYmzBZS9YlDp6PTSSDwa98vkD4QgVDDCAo84z5X1t5XyJQ62ImdLXx5NdIIfihey6xpum9/gRQ==}
    dev: false

  /parseurl/1.3.3:
    resolution: {integrity: sha512-CiyeOxFT/JZyN5m0z9PfXw4SCBJ6Sygz1Dpl0wqjlhDEGGBP1GnsUVEL0p63hoG1fcj3fHynXi9NYO4nWOL+qQ==}
    engines: {node: '>= 0.8'}
    dev: false

  /pascal-case/3.1.2:
    resolution: {integrity: sha512-uWlGT3YSnK9x3BQJaOdcZwrnV6hPpd8jFH1/ucpiLRPh/2zCVJKS19E4GvYHvaCcACn3foXZ0cLB9Wrx1KGe5g==}
    dependencies:
      no-case: 3.0.4
      tslib: 2.4.0
    dev: false

  /pascalcase/0.1.1:
    resolution: {integrity: sha1-s2PlXoAGym/iF4TS2yK9FdeRfxQ=}
    engines: {node: '>=0.10.0'}
    dev: false

  /path-browserify/0.0.1:
    resolution: {integrity: sha512-BapA40NHICOS+USX9SN4tyhq+A2RrN/Ws5F0Z5aMHDp98Fl86lX8Oti8B7uN93L4Ifv4fHOEA+pQw87gmMO/lQ==}
    dev: false

  /path-dirname/1.0.2:
    resolution: {integrity: sha1-zDPSTVJeCZpTiMAzbG4yuRYGCeA=}
    dev: false

  /path-exists/2.1.0:
    resolution: {integrity: sha1-D+tsZPD8UY2adU3V77YscCJ2H0s=}
    engines: {node: '>=0.10.0'}
    dependencies:
      pinkie-promise: 2.0.1

  /path-exists/3.0.0:
    resolution: {integrity: sha1-zg6+ql94yxiSXqfYENe1mwEP1RU=}
    engines: {node: '>=4'}

  /path-exists/4.0.0:
    resolution: {integrity: sha512-ak9Qy5Q7jYb2Wwcey5Fpvg2KoAc/ZIhLSLOSBmRmygPsGwkVVt0fZa0qrtMz+m6tJTAHfZQ8FnmB4MG4LWy7/w==}
    engines: {node: '>=8'}

  /path-is-absolute/1.0.1:
    resolution: {integrity: sha1-F0uSaHNVNP+8es5r9TpanhtcX18=}
    engines: {node: '>=0.10.0'}

  /path-is-inside/1.0.2:
    resolution: {integrity: sha1-NlQX3t5EQw0cEa9hAn+s8HS9/FM=}
    dev: false

  /path-key/2.0.1:
    resolution: {integrity: sha1-QRyttXTFoUDTpLGRDUDYDMn0C0A=}
    engines: {node: '>=4'}
    dev: false

  /path-key/3.1.1:
    resolution: {integrity: sha512-ojmeN0qd+y0jszEtoY48r0Peq5dwMEkIlCOu6Q5f41lfkswXuKtYrhgoTpLnyIcHm24Uhqx+5Tqm2InSwLhE6Q==}
    engines: {node: '>=8'}
    dev: false

  /path-parse/1.0.7:
    resolution: {integrity: sha512-LDJzPVEEEPR+y48z93A0Ed0yXb8pAByGWo/k5YYdYgpY2/2EsOsksJrq7lOHxryrVOn1ejG6oAp8ahvOIQD8sw==}

  /path-to-regexp/0.1.7:
    resolution: {integrity: sha1-32BBeABfUi8V60SQ5yR6G/qmf4w=}
    dev: false

  /path-to-regexp/1.8.0:
    resolution: {integrity: sha512-n43JRhlUKUAlibEJhPeir1ncUID16QnEjNpwzNdO3Lm4ywrBpBZ5oLD0I6br9evr1Y9JTqwRtAh7JLoOzAQdVA==}
    dependencies:
      isarray: 0.0.1
    dev: false

  /path-type/1.1.0:
    resolution: {integrity: sha1-WcRPfuSR2nBNpBXaWkBwuk+P5EE=}
    engines: {node: '>=0.10.0'}
    dependencies:
      graceful-fs: 4.2.10
      pify: 2.3.0
      pinkie-promise: 2.0.1

  /path-type/2.0.0:
    resolution: {integrity: sha1-8BLMuEFbcJb8LaoQVMPXI4lZTHM=}
    engines: {node: '>=4'}
    dependencies:
      pify: 2.3.0
    dev: false

  /path-type/3.0.0:
    resolution: {integrity: sha512-T2ZUsdZFHgA3u4e5PfPbjd7HDDpxPnQb5jN0SrDsjNSuVXHJqtwTnWqG0B1jZrgmJ/7lj1EmVIByWt1gxGkWvg==}
    engines: {node: '>=4'}
    dependencies:
      pify: 3.0.0
    dev: false

  /path-type/4.0.0:
    resolution: {integrity: sha512-gDKb8aZMDeD/tZWs9P6+q0J9Mwkdl6xMV8TjnGP3qJVJ06bdMgkbBlLU8IdfOsIsFz2BW1rNVT3XuNEl8zPAvw==}
    engines: {node: '>=8'}

  /pbkdf2/3.1.2:
    resolution: {integrity: sha512-iuh7L6jA7JEGu2WxDwtQP1ddOpaJNC4KlDEFfdQajSGgGPNi4OyDc2R7QnbY2bR9QjBVGwgvTdNJZoE7RaxUMA==}
    engines: {node: '>=0.12'}
    dependencies:
      create-hash: 1.2.0
      create-hmac: 1.1.7
      ripemd160: 2.0.2
      safe-buffer: 5.2.1
      sha.js: 2.4.11
    dev: false

  /performance-now/2.1.0:
    resolution: {integrity: sha1-Ywn04OX6kT7BxpMHrjZLSzd8nns=}

  /picocolors/0.2.1:
    resolution: {integrity: sha1-VwZw95NkaFHRuhNZlpYqutWHhZ8=}

  /picocolors/1.0.0:
    resolution: {integrity: sha1-y1vcdP8/UYkiNur3nWi8RFZKuBw=}

  /picomatch/2.3.1:
    resolution: {integrity: sha512-JU3teHTNjmE2VCGFzuY8EXzCDVwEqB2a8fsIvwaStHhAWJEeVd1o1QD80CU6+ZdEXXSLbSsuLwJjkCBWqRQUVA==}
    engines: {node: '>=8.6'}

  /pify/2.3.0:
    resolution: {integrity: sha1-7RQaasBDqEnqWISY59yosVMw6Qw=}
    engines: {node: '>=0.10.0'}

  /pify/3.0.0:
    resolution: {integrity: sha1-5aSs0sEB/fPZpNB/DbxNtJ3SgXY=}
    engines: {node: '>=4'}
    dev: false

  /pify/4.0.1:
    resolution: {integrity: sha512-uB80kBFb/tfd68bVleG9T5GGsGPjJrLAUpR5PZIrhBnIaRTQRjqdJSsIKkOP6OAIFbj7GOrcudc5pNjZ+geV2g==}
    engines: {node: '>=6'}
    dev: false

  /pinkie-promise/2.0.1:
    resolution: {integrity: sha1-ITXW36ejWMBprJsXh3YogihFD/o=}
    engines: {node: '>=0.10.0'}
    dependencies:
      pinkie: 2.0.4

  /pinkie/2.0.4:
    resolution: {integrity: sha1-clVrgM+g1IqXToDnckjoDtT3+HA=}
    engines: {node: '>=0.10.0'}

  /pirates/4.0.5:
    resolution: {integrity: sha512-8V9+HQPupnaXMA23c5hvl69zXvTwTzyAYasnkb0Tts4XvO4CliqONMOnvlq26rkhLC3nWDFBJf73LU1e1VZLaQ==}
    engines: {node: '>= 6'}
    dev: false

  /pkg-dir/1.0.0:
    resolution: {integrity: sha1-ektQio1bstYp1EcFb/TpyTFM89Q=}
    engines: {node: '>=0.10.0'}
    dependencies:
      find-up: 1.1.2
    dev: false

  /pkg-dir/3.0.0:
    resolution: {integrity: sha512-/E57AYkoeQ25qkxMj5PBOVgF8Kiu/h7cYS30Z5+R7WaiCCBfLq58ZI/dSeaEKb9WVJV5n/03QwrN3IeWIFllvw==}
    engines: {node: '>=6'}
    dependencies:
      find-up: 3.0.0
    dev: false

  /pkg-dir/4.2.0:
    resolution: {integrity: sha512-HRDzbaKjC+AOWVXxAU/x54COGeIv9eb+6CkDSQoNTt4XyWoIJvuPsXizxu/Fr23EiekbtZwmh1IcIG/l/a10GQ==}
    engines: {node: '>=8'}
    dependencies:
      find-up: 4.1.0
    dev: false

  /pkg-up/3.1.0:
    resolution: {integrity: sha512-nDywThFk1i4BQK4twPQ6TA4RT8bDY96yeuCVBWL3ePARCiEKDRSrNGbFIgUJpLp+XeIR65v8ra7WuJOFUBtkMA==}
    engines: {node: '>=8'}
    dependencies:
      find-up: 3.0.0
    dev: false

  /pn/1.1.0:
    resolution: {integrity: sha512-2qHaIQr2VLRFoxe2nASzsV6ef4yOOH+Fi9FBOVH6cqeSgUnoyySPZkxzLuzd+RYOQTRpROA0ztTMqxROKSb/nA==}
    dev: false

  /pnp-webpack-plugin/1.6.0_typescript@3.9.10:
    resolution: {integrity: sha512-ZcMGn/xF/fCOq+9kWMP9vVVxjIkMCja72oy3lziR7UHy0hHFZ57iVpQ71OtveVbmzeCmphBg8pxNdk/hlK99aQ==}
    engines: {node: '>=6'}
    dependencies:
      ts-pnp: 1.1.5_typescript@3.9.10
    transitivePeerDependencies:
      - typescript
    dev: false

  /portfinder/1.0.32_supports-color@6.1.0:
    resolution: {integrity: sha512-on2ZJVVDXRADWE6jnQaX0ioEylzgBpQk8r55NE4wjXW1ZxO+BgDlY6DXwj20i0V8eB4SenDQ00WEaxfiIQPcxg==}
    engines: {node: '>= 0.12.0'}
    dependencies:
      async: 2.6.4
      debug: 3.2.7_supports-color@6.1.0
      mkdirp: 0.5.6
    transitivePeerDependencies:
      - supports-color
    dev: false

  /posix-character-classes/0.1.1:
    resolution: {integrity: sha1-AerA/jta9xoqbAL+q7jB/vfgDqs=}
    engines: {node: '>=0.10.0'}
    dev: false

  /postcss-attribute-case-insensitive/4.0.2:
    resolution: {integrity: sha512-clkFxk/9pcdb4Vkn0hAHq3YnxBQ2p0CGD1dy24jN+reBck+EWxMbxSUqN4Yj7t0w8csl87K6p0gxBe1utkJsYA==}
    dependencies:
      postcss: 7.0.39
      postcss-selector-parser: 6.0.10
    dev: false

  /postcss-browser-comments/3.0.0_browserslist@4.21.4:
    resolution: {integrity: sha512-qfVjLfq7HFd2e0HW4s1dvU8X080OZdG46fFbIBFjW7US7YPDcWfRvdElvwMJr2LI6hMmD+7LnH2HcmXTs+uOig==}
    engines: {node: '>=8.0.0'}
    peerDependencies:
      browserslist: ^4
    dependencies:
      browserslist: 4.21.4
      postcss: 7.0.39
    dev: false

  /postcss-calc/7.0.5:
    resolution: {integrity: sha512-1tKHutbGtLtEZF6PT4JSihCHfIVldU72mZ8SdZHIYriIZ9fh9k9aWSppaT8rHsyI3dX+KSR+W+Ix9BMY3AODrg==}
    dependencies:
      postcss: 7.0.39
      postcss-selector-parser: 6.0.10
      postcss-value-parser: 4.2.0
    dev: false

  /postcss-color-functional-notation/2.0.1:
    resolution: {integrity: sha512-ZBARCypjEDofW4P6IdPVTLhDNXPRn8T2s1zHbZidW6rPaaZvcnCS2soYFIQJrMZSxiePJ2XIYTlcb2ztr/eT2g==}
    engines: {node: '>=6.0.0'}
    dependencies:
      postcss: 7.0.39
      postcss-values-parser: 2.0.1
    dev: false

  /postcss-color-gray/5.0.0:
    resolution: {integrity: sha512-q6BuRnAGKM/ZRpfDascZlIZPjvwsRye7UDNalqVz3s7GDxMtqPY6+Q871liNxsonUw8oC61OG+PSaysYpl1bnw==}
    engines: {node: '>=6.0.0'}
    dependencies:
      '@csstools/convert-colors': 1.4.0
      postcss: 7.0.39
      postcss-values-parser: 2.0.1
    dev: false

  /postcss-color-hex-alpha/5.0.3:
    resolution: {integrity: sha512-PF4GDel8q3kkreVXKLAGNpHKilXsZ6xuu+mOQMHWHLPNyjiUBOr75sp5ZKJfmv1MCus5/DWUGcK9hm6qHEnXYw==}
    engines: {node: '>=6.0.0'}
    dependencies:
      postcss: 7.0.39
      postcss-values-parser: 2.0.1
    dev: false

  /postcss-color-mod-function/3.0.3:
    resolution: {integrity: sha512-YP4VG+xufxaVtzV6ZmhEtc+/aTXH3d0JLpnYfxqTvwZPbJhWqp8bSY3nfNzNRFLgB4XSaBA82OE4VjOOKpCdVQ==}
    engines: {node: '>=6.0.0'}
    dependencies:
      '@csstools/convert-colors': 1.4.0
      postcss: 7.0.39
      postcss-values-parser: 2.0.1
    dev: false

  /postcss-color-rebeccapurple/4.0.1:
    resolution: {integrity: sha512-aAe3OhkS6qJXBbqzvZth2Au4V3KieR5sRQ4ptb2b2O8wgvB3SJBsdG+jsn2BZbbwekDG8nTfcCNKcSfe/lEy8g==}
    engines: {node: '>=6.0.0'}
    dependencies:
      postcss: 7.0.39
      postcss-values-parser: 2.0.1
    dev: false

  /postcss-colormin/4.0.3:
    resolution: {integrity: sha512-WyQFAdDZpExQh32j0U0feWisZ0dmOtPl44qYmJKkq9xFWY3p+4qnRzCHeNrkeRhwPHz9bQ3mo0/yVkaply0MNw==}
    engines: {node: '>=6.9.0'}
    dependencies:
      browserslist: 4.21.4
      color: 3.2.1
      has: 1.0.3
      postcss: 7.0.39
      postcss-value-parser: 3.3.1
    dev: false

  /postcss-convert-values/4.0.1:
    resolution: {integrity: sha512-Kisdo1y77KUC0Jmn0OXU/COOJbzM8cImvw1ZFsBgBgMgb1iL23Zs/LXRe3r+EZqM3vGYKdQ2YJVQ5VkJI+zEJQ==}
    engines: {node: '>=6.9.0'}
    dependencies:
      postcss: 7.0.39
      postcss-value-parser: 3.3.1
    dev: false

  /postcss-custom-media/7.0.8:
    resolution: {integrity: sha512-c9s5iX0Ge15o00HKbuRuTqNndsJUbaXdiNsksnVH8H4gdc+zbLzr/UasOwNG6CTDpLFekVY4672eWdiiWu2GUg==}
    engines: {node: '>=6.0.0'}
    dependencies:
      postcss: 7.0.39
    dev: false

  /postcss-custom-properties/8.0.11:
    resolution: {integrity: sha512-nm+o0eLdYqdnJ5abAJeXp4CEU1c1k+eB2yMCvhgzsds/e0umabFrN6HoTy/8Q4K5ilxERdl/JD1LO5ANoYBeMA==}
    engines: {node: '>=6.0.0'}
    dependencies:
      postcss: 7.0.39
      postcss-values-parser: 2.0.1
    dev: false

  /postcss-custom-selectors/5.1.2:
    resolution: {integrity: sha512-DSGDhqinCqXqlS4R7KGxL1OSycd1lydugJ1ky4iRXPHdBRiozyMHrdu0H3o7qNOCiZwySZTUI5MV0T8QhCLu+w==}
    engines: {node: '>=6.0.0'}
    dependencies:
      postcss: 7.0.39
      postcss-selector-parser: 5.0.0
    dev: false

  /postcss-dir-pseudo-class/5.0.0:
    resolution: {integrity: sha512-3pm4oq8HYWMZePJY+5ANriPs3P07q+LW6FAdTlkFH2XqDdP4HeeJYMOzn0HYLhRSjBO3fhiqSwwU9xEULSrPgw==}
    engines: {node: '>=4.0.0'}
    dependencies:
      postcss: 7.0.39
      postcss-selector-parser: 5.0.0
    dev: false

  /postcss-discard-comments/4.0.2:
    resolution: {integrity: sha512-RJutN259iuRf3IW7GZyLM5Sw4GLTOH8FmsXBnv8Ab/Tc2k4SR4qbV4DNbyyY4+Sjo362SyDmW2DQ7lBSChrpkg==}
    engines: {node: '>=6.9.0'}
    dependencies:
      postcss: 7.0.39
    dev: false

  /postcss-discard-duplicates/4.0.2:
    resolution: {integrity: sha512-ZNQfR1gPNAiXZhgENFfEglF93pciw0WxMkJeVmw8eF+JZBbMD7jp6C67GqJAXVZP2BWbOztKfbsdmMp/k8c6oQ==}
    engines: {node: '>=6.9.0'}
    dependencies:
      postcss: 7.0.39
    dev: false

  /postcss-discard-empty/4.0.1:
    resolution: {integrity: sha512-B9miTzbznhDjTfjvipfHoqbWKwd0Mj+/fL5s1QOz06wufguil+Xheo4XpOnc4NqKYBCNqqEzgPv2aPBIJLox0w==}
    engines: {node: '>=6.9.0'}
    dependencies:
      postcss: 7.0.39
    dev: false

  /postcss-discard-overridden/4.0.1:
    resolution: {integrity: sha512-IYY2bEDD7g1XM1IDEsUT4//iEYCxAmP5oDSFMVU/JVvT7gh+l4fmjciLqGgwjdWpQIdb0Che2VX00QObS5+cTg==}
    engines: {node: '>=6.9.0'}
    dependencies:
      postcss: 7.0.39
    dev: false

  /postcss-double-position-gradients/1.0.0:
    resolution: {integrity: sha512-G+nV8EnQq25fOI8CH/B6krEohGWnF5+3A6H/+JEpOncu5dCnkS1QQ6+ct3Jkaepw1NGVqqOZH6lqrm244mCftA==}
    engines: {node: '>=6.0.0'}
    dependencies:
      postcss: 7.0.39
      postcss-values-parser: 2.0.1
    dev: false

  /postcss-env-function/2.0.2:
    resolution: {integrity: sha512-rwac4BuZlITeUbiBq60h/xbLzXY43qOsIErngWa4l7Mt+RaSkT7QBjXVGTcBHupykkblHMDrBFh30zchYPaOUw==}
    engines: {node: '>=6.0.0'}
    dependencies:
      postcss: 7.0.39
      postcss-values-parser: 2.0.1
    dev: false

  /postcss-flexbugs-fixes/4.1.0:
    resolution: {integrity: sha512-jr1LHxQvStNNAHlgco6PzY308zvLklh7SJVYuWUwyUQncofaAlD2l+P/gxKHOdqWKe7xJSkVLFF/2Tp+JqMSZA==}
    dependencies:
      postcss: 7.0.39
    dev: false

  /postcss-focus-visible/4.0.0:
    resolution: {integrity: sha512-Z5CkWBw0+idJHSV6+Bgf2peDOFf/x4o+vX/pwcNYrWpXFrSfTkQ3JQ1ojrq9yS+upnAlNRHeg8uEwFTgorjI8g==}
    engines: {node: '>=6.0.0'}
    dependencies:
      postcss: 7.0.39
    dev: false

  /postcss-focus-within/3.0.0:
    resolution: {integrity: sha512-W0APui8jQeBKbCGZudW37EeMCjDeVxKgiYfIIEo8Bdh5SpB9sxds/Iq8SEuzS0Q4YFOlG7EPFulbbxujpkrV2w==}
    engines: {node: '>=6.0.0'}
    dependencies:
      postcss: 7.0.39
    dev: false

  /postcss-font-variant/4.0.1:
    resolution: {integrity: sha512-I3ADQSTNtLTTd8uxZhtSOrTCQ9G4qUVKPjHiDk0bV75QSxXjVWiJVJ2VLdspGUi9fbW9BcjKJoRvxAH1pckqmA==}
    dependencies:
      postcss: 7.0.39
    dev: false

  /postcss-gap-properties/2.0.0:
    resolution: {integrity: sha512-QZSqDaMgXCHuHTEzMsS2KfVDOq7ZFiknSpkrPJY6jmxbugUPTuSzs/vuE5I3zv0WAS+3vhrlqhijiprnuQfzmg==}
    engines: {node: '>=6.0.0'}
    dependencies:
      postcss: 7.0.39
    dev: false

  /postcss-html/0.36.0_j55xdkkcxc32kvnyvx3y7casfm:
    resolution: {integrity: sha512-HeiOxGcuwID0AFsNAL0ox3mW6MHH5cstWN1Z3Y+n6H+g12ih7LHdYxWwEA/QmrebctLjo79xz9ouK3MroHwOJw==}
    peerDependencies:
      postcss: '>=5.0.0'
      postcss-syntax: '>=0.36.0'
    dependencies:
      htmlparser2: 3.10.1
      postcss: 7.0.39
      postcss-syntax: 0.36.2_kei4jy7wdgbhc236h4oijypxom
    dev: true

  /postcss-image-set-function/3.0.1:
    resolution: {integrity: sha512-oPTcFFip5LZy8Y/whto91L9xdRHCWEMs3e1MdJxhgt4jy2WYXfhkng59fH5qLXSCPN8k4n94p1Czrfe5IOkKUw==}
    engines: {node: '>=6.0.0'}
    dependencies:
      postcss: 7.0.39
      postcss-values-parser: 2.0.1
    dev: false

  /postcss-initial/3.0.4:
    resolution: {integrity: sha512-3RLn6DIpMsK1l5UUy9jxQvoDeUN4gP939tDcKUHD/kM8SGSKbFAnvkpFpj3Bhtz3HGk1jWY5ZNWX6mPta5M9fg==}
    dependencies:
      postcss: 7.0.39
    dev: false

  /postcss-lab-function/2.0.1:
    resolution: {integrity: sha512-whLy1IeZKY+3fYdqQFuDBf8Auw+qFuVnChWjmxm/UhHWqNHZx+B99EwxTvGYmUBqe3Fjxs4L1BoZTJmPu6usVg==}
    engines: {node: '>=6.0.0'}
    dependencies:
      '@csstools/convert-colors': 1.4.0
      postcss: 7.0.39
      postcss-values-parser: 2.0.1
    dev: false

  /postcss-less/3.1.4:
    resolution: {integrity: sha512-7TvleQWNM2QLcHqvudt3VYjULVB49uiW6XzEUFmvwHzvsOEF5MwBrIXZDJQvJNFGjJQTzSzZnDoCJ8h/ljyGXA==}
    engines: {node: '>=6.14.4'}
    dependencies:
      postcss: 7.0.39
    dev: true

  /postcss-load-config/2.1.2:
    resolution: {integrity: sha512-/rDeGV6vMUo3mwJZmeHfEDvwnTKKqQ0S7OHUi/kJvvtx3aWtyWG2/0ZWnzCt2keEclwN6Tf0DST2v9kITdOKYw==}
    engines: {node: '>= 4'}
    dependencies:
      cosmiconfig: 5.2.1
      import-cwd: 2.1.0
    dev: false

  /postcss-loader/3.0.0:
    resolution: {integrity: sha512-cLWoDEY5OwHcAjDnkyRQzAXfs2jrKjXpO/HQFcc5b5u/r7aa471wdmChmwfnv7x2u840iat/wi0lQ5nbRgSkUA==}
    engines: {node: '>= 6'}
    dependencies:
      loader-utils: 1.4.0
      postcss: 7.0.39
      postcss-load-config: 2.1.2
      schema-utils: 1.0.0
    dev: false

  /postcss-logical/3.0.0:
    resolution: {integrity: sha512-1SUKdJc2vuMOmeItqGuNaC+N8MzBWFWEkAnRnLpFYj1tGGa7NqyVBujfRtgNa2gXR+6RkGUiB2O5Vmh7E2RmiA==}
    engines: {node: '>=6.0.0'}
    dependencies:
      postcss: 7.0.39
    dev: false

  /postcss-media-minmax/4.0.0:
    resolution: {integrity: sha512-fo9moya6qyxsjbFAYl97qKO9gyre3qvbMnkOZeZwlsW6XYFsvs2DMGDlchVLfAd8LHPZDxivu/+qW2SMQeTHBw==}
    engines: {node: '>=6.0.0'}
    dependencies:
      postcss: 7.0.39
    dev: false

  /postcss-media-query-parser/0.2.3:
    resolution: {integrity: sha1-J7Ocb02U+Bsac7j3Y1HGCeXO8kQ=}
    dev: true

  /postcss-merge-longhand/4.0.11:
    resolution: {integrity: sha512-alx/zmoeXvJjp7L4mxEMjh8lxVlDFX1gqWHzaaQewwMZiVhLo42TEClKaeHbRf6J7j82ZOdTJ808RtN0ZOZwvw==}
    engines: {node: '>=6.9.0'}
    dependencies:
      css-color-names: 0.0.4
      postcss: 7.0.39
      postcss-value-parser: 3.3.1
      stylehacks: 4.0.3
    dev: false

  /postcss-merge-rules/4.0.3:
    resolution: {integrity: sha512-U7e3r1SbvYzO0Jr3UT/zKBVgYYyhAz0aitvGIYOYK5CPmkNih+WDSsS5tvPrJ8YMQYlEMvsZIiqmn7HdFUaeEQ==}
    engines: {node: '>=6.9.0'}
    dependencies:
      browserslist: 4.21.4
      caniuse-api: 3.0.0
      cssnano-util-same-parent: 4.0.1
      postcss: 7.0.39
      postcss-selector-parser: 3.1.2
      vendors: 1.0.4
    dev: false

  /postcss-minify-font-values/4.0.2:
    resolution: {integrity: sha512-j85oO6OnRU9zPf04+PZv1LYIYOprWm6IA6zkXkrJXyRveDEuQggG6tvoy8ir8ZwjLxLuGfNkCZEQG7zan+Hbtg==}
    engines: {node: '>=6.9.0'}
    dependencies:
      postcss: 7.0.39
      postcss-value-parser: 3.3.1
    dev: false

  /postcss-minify-gradients/4.0.2:
    resolution: {integrity: sha512-qKPfwlONdcf/AndP1U8SJ/uzIJtowHlMaSioKzebAXSG4iJthlWC9iSWznQcX4f66gIWX44RSA841HTHj3wK+Q==}
    engines: {node: '>=6.9.0'}
    dependencies:
      cssnano-util-get-arguments: 4.0.0
      is-color-stop: 1.1.0
      postcss: 7.0.39
      postcss-value-parser: 3.3.1
    dev: false

  /postcss-minify-params/4.0.2:
    resolution: {integrity: sha512-G7eWyzEx0xL4/wiBBJxJOz48zAKV2WG3iZOqVhPet/9geefm/Px5uo1fzlHu+DOjT+m0Mmiz3jkQzVHe6wxAWg==}
    engines: {node: '>=6.9.0'}
    dependencies:
      alphanum-sort: 1.0.2
      browserslist: 4.21.4
      cssnano-util-get-arguments: 4.0.0
      postcss: 7.0.39
      postcss-value-parser: 3.3.1
      uniqs: 2.0.0
    dev: false

  /postcss-minify-selectors/4.0.2:
    resolution: {integrity: sha512-D5S1iViljXBj9kflQo4YutWnJmwm8VvIsU1GeXJGiG9j8CIg9zs4voPMdQDUmIxetUOh60VilsNzCiAFTOqu3g==}
    engines: {node: '>=6.9.0'}
    dependencies:
      alphanum-sort: 1.0.2
      has: 1.0.3
      postcss: 7.0.39
      postcss-selector-parser: 3.1.2
    dev: false

  /postcss-modules-extract-imports/2.0.0:
    resolution: {integrity: sha512-LaYLDNS4SG8Q5WAWqIJgdHPJrDDr/Lv775rMBFUbgjTz6j34lUznACHcdRWroPvXANP2Vj7yNK57vp9eFqzLWQ==}
    engines: {node: '>= 6'}
    dependencies:
      postcss: 7.0.39
    dev: false

  /postcss-modules-local-by-default/3.0.3:
    resolution: {integrity: sha512-e3xDq+LotiGesympRlKNgaJ0PCzoUIdpH0dj47iWAui/kyTgh3CiAr1qP54uodmJhl6p9rN6BoNcdEDVJx9RDw==}
    engines: {node: '>= 6'}
    dependencies:
      icss-utils: 4.1.1
      postcss: 7.0.39
      postcss-selector-parser: 6.0.10
      postcss-value-parser: 4.2.0
    dev: false

  /postcss-modules-scope/2.2.0:
    resolution: {integrity: sha512-YyEgsTMRpNd+HmyC7H/mh3y+MeFWevy7V1evVhJWewmMbjDHIbZbOXICC2y+m1xI1UVfIT1HMW/O04Hxyu9oXQ==}
    engines: {node: '>= 6'}
    dependencies:
      postcss: 7.0.39
      postcss-selector-parser: 6.0.10
    dev: false

  /postcss-modules-values/3.0.0:
    resolution: {integrity: sha512-1//E5jCBrZ9DmRX+zCtmQtRSV6PV42Ix7Bzj9GbwJceduuf7IqP8MgeTXuRDHOWj2m0VzZD5+roFWDuU8RQjcg==}
    dependencies:
      icss-utils: 4.1.1
      postcss: 7.0.39
    dev: false

  /postcss-nesting/7.0.1:
    resolution: {integrity: sha512-FrorPb0H3nuVq0Sff7W2rnc3SmIcruVC6YwpcS+k687VxyxO33iE1amna7wHuRVzM8vfiYofXSBHNAZ3QhLvYg==}
    engines: {node: '>=6.0.0'}
    dependencies:
      postcss: 7.0.39
    dev: false

  /postcss-normalize-charset/4.0.1:
    resolution: {integrity: sha512-gMXCrrlWh6G27U0hF3vNvR3w8I1s2wOBILvA87iNXaPvSNo5uZAMYsZG7XjCUf1eVxuPfyL4TJ7++SGZLc9A3g==}
    engines: {node: '>=6.9.0'}
    dependencies:
      postcss: 7.0.39
    dev: false

  /postcss-normalize-display-values/4.0.2:
    resolution: {integrity: sha512-3F2jcsaMW7+VtRMAqf/3m4cPFhPD3EFRgNs18u+k3lTJJlVe7d0YPO+bnwqo2xg8YiRpDXJI2u8A0wqJxMsQuQ==}
    engines: {node: '>=6.9.0'}
    dependencies:
      cssnano-util-get-match: 4.0.0
      postcss: 7.0.39
      postcss-value-parser: 3.3.1
    dev: false

  /postcss-normalize-positions/4.0.2:
    resolution: {integrity: sha512-Dlf3/9AxpxE+NF1fJxYDeggi5WwV35MXGFnnoccP/9qDtFrTArZ0D0R+iKcg5WsUd8nUYMIl8yXDCtcrT8JrdA==}
    engines: {node: '>=6.9.0'}
    dependencies:
      cssnano-util-get-arguments: 4.0.0
      has: 1.0.3
      postcss: 7.0.39
      postcss-value-parser: 3.3.1
    dev: false

  /postcss-normalize-repeat-style/4.0.2:
    resolution: {integrity: sha512-qvigdYYMpSuoFs3Is/f5nHdRLJN/ITA7huIoCyqqENJe9PvPmLhNLMu7QTjPdtnVf6OcYYO5SHonx4+fbJE1+Q==}
    engines: {node: '>=6.9.0'}
    dependencies:
      cssnano-util-get-arguments: 4.0.0
      cssnano-util-get-match: 4.0.0
      postcss: 7.0.39
      postcss-value-parser: 3.3.1
    dev: false

  /postcss-normalize-string/4.0.2:
    resolution: {integrity: sha512-RrERod97Dnwqq49WNz8qo66ps0swYZDSb6rM57kN2J+aoyEAJfZ6bMx0sx/F9TIEX0xthPGCmeyiam/jXif0eA==}
    engines: {node: '>=6.9.0'}
    dependencies:
      has: 1.0.3
      postcss: 7.0.39
      postcss-value-parser: 3.3.1
    dev: false

  /postcss-normalize-timing-functions/4.0.2:
    resolution: {integrity: sha512-acwJY95edP762e++00Ehq9L4sZCEcOPyaHwoaFOhIwWCDfik6YvqsYNxckee65JHLKzuNSSmAdxwD2Cud1Z54A==}
    engines: {node: '>=6.9.0'}
    dependencies:
      cssnano-util-get-match: 4.0.0
      postcss: 7.0.39
      postcss-value-parser: 3.3.1
    dev: false

  /postcss-normalize-unicode/4.0.1:
    resolution: {integrity: sha512-od18Uq2wCYn+vZ/qCOeutvHjB5jm57ToxRaMeNuf0nWVHaP9Hua56QyMF6fs/4FSUnVIw0CBPsU0K4LnBPwYwg==}
    engines: {node: '>=6.9.0'}
    dependencies:
      browserslist: 4.21.4
      postcss: 7.0.39
      postcss-value-parser: 3.3.1
    dev: false

  /postcss-normalize-url/4.0.1:
    resolution: {integrity: sha512-p5oVaF4+IHwu7VpMan/SSpmpYxcJMtkGppYf0VbdH5B6hN8YNmVyJLuY9FmLQTzY3fag5ESUUHDqM+heid0UVA==}
    engines: {node: '>=6.9.0'}
    dependencies:
      is-absolute-url: 2.1.0
      normalize-url: 3.3.0
      postcss: 7.0.39
      postcss-value-parser: 3.3.1
    dev: false

  /postcss-normalize-whitespace/4.0.2:
    resolution: {integrity: sha512-tO8QIgrsI3p95r8fyqKV+ufKlSHh9hMJqACqbv2XknufqEDhDvbguXGBBqxw9nsQoXWf0qOqppziKJKHMD4GtA==}
    engines: {node: '>=6.9.0'}
    dependencies:
      postcss: 7.0.39
      postcss-value-parser: 3.3.1
    dev: false

  /postcss-normalize/8.0.1:
    resolution: {integrity: sha512-rt9JMS/m9FHIRroDDBGSMsyW1c0fkvOJPy62ggxSHUldJO7B195TqFMqIf+lY5ezpDcYOV4j86aUp3/XbxzCCQ==}
    engines: {node: '>=8.0.0'}
    dependencies:
      '@csstools/normalize.css': 10.1.0
      browserslist: 4.21.4
      postcss: 7.0.39
      postcss-browser-comments: 3.0.0_browserslist@4.21.4
      sanitize.css: 10.0.0
    dev: false

  /postcss-ordered-values/4.1.2:
    resolution: {integrity: sha512-2fCObh5UanxvSxeXrtLtlwVThBvHn6MQcu4ksNT2tsaV2Fg76R2CV98W7wNSlX+5/pFwEyaDwKLLoEV7uRybAw==}
    engines: {node: '>=6.9.0'}
    dependencies:
      cssnano-util-get-arguments: 4.0.0
      postcss: 7.0.39
      postcss-value-parser: 3.3.1
    dev: false

  /postcss-overflow-shorthand/2.0.0:
    resolution: {integrity: sha512-aK0fHc9CBNx8jbzMYhshZcEv8LtYnBIRYQD5i7w/K/wS9c2+0NSR6B3OVMu5y0hBHYLcMGjfU+dmWYNKH0I85g==}
    engines: {node: '>=6.0.0'}
    dependencies:
      postcss: 7.0.39
    dev: false

  /postcss-page-break/2.0.0:
    resolution: {integrity: sha512-tkpTSrLpfLfD9HvgOlJuigLuk39wVTbbd8RKcy8/ugV2bNBUW3xU+AIqyxhDrQr1VUj1RmyJrBn1YWrqUm9zAQ==}
    dependencies:
      postcss: 7.0.39
    dev: false

  /postcss-place/4.0.1:
    resolution: {integrity: sha512-Zb6byCSLkgRKLODj/5mQugyuj9bvAAw9LqJJjgwz5cYryGeXfFZfSXoP1UfveccFmeq0b/2xxwcTEVScnqGxBg==}
    engines: {node: '>=6.0.0'}
    dependencies:
      postcss: 7.0.39
      postcss-values-parser: 2.0.1
    dev: false

  /postcss-preset-env/6.7.0:
    resolution: {integrity: sha512-eU4/K5xzSFwUFJ8hTdTQzo2RBLbDVt83QZrAvI07TULOkmyQlnYlpwep+2yIK+K+0KlZO4BvFcleOCCcUtwchg==}
    engines: {node: '>=6.0.0'}
    dependencies:
      autoprefixer: 9.8.8
      browserslist: 4.21.4
      caniuse-lite: 1.0.30001423
      css-blank-pseudo: 0.1.4
      css-has-pseudo: 0.10.0
      css-prefers-color-scheme: 3.1.1
      cssdb: 4.4.0
      postcss: 7.0.39
      postcss-attribute-case-insensitive: 4.0.2
      postcss-color-functional-notation: 2.0.1
      postcss-color-gray: 5.0.0
      postcss-color-hex-alpha: 5.0.3
      postcss-color-mod-function: 3.0.3
      postcss-color-rebeccapurple: 4.0.1
      postcss-custom-media: 7.0.8
      postcss-custom-properties: 8.0.11
      postcss-custom-selectors: 5.1.2
      postcss-dir-pseudo-class: 5.0.0
      postcss-double-position-gradients: 1.0.0
      postcss-env-function: 2.0.2
      postcss-focus-visible: 4.0.0
      postcss-focus-within: 3.0.0
      postcss-font-variant: 4.0.1
      postcss-gap-properties: 2.0.0
      postcss-image-set-function: 3.0.1
      postcss-initial: 3.0.4
      postcss-lab-function: 2.0.1
      postcss-logical: 3.0.0
      postcss-media-minmax: 4.0.0
      postcss-nesting: 7.0.1
      postcss-overflow-shorthand: 2.0.0
      postcss-page-break: 2.0.0
      postcss-place: 4.0.1
      postcss-pseudo-class-any-link: 6.0.0
      postcss-replace-overflow-wrap: 3.0.0
      postcss-selector-matches: 4.0.0
      postcss-selector-not: 4.0.1
    dev: false

  /postcss-pseudo-class-any-link/6.0.0:
    resolution: {integrity: sha512-lgXW9sYJdLqtmw23otOzrtbDXofUdfYzNm4PIpNE322/swES3VU9XlXHeJS46zT2onFO7V1QFdD4Q9LiZj8mew==}
    engines: {node: '>=6.0.0'}
    dependencies:
      postcss: 7.0.39
      postcss-selector-parser: 5.0.0
    dev: false

  /postcss-reduce-initial/4.0.3:
    resolution: {integrity: sha512-gKWmR5aUulSjbzOfD9AlJiHCGH6AEVLaM0AV+aSioxUDd16qXP1PCh8d1/BGVvpdWn8k/HiK7n6TjeoXN1F7DA==}
    engines: {node: '>=6.9.0'}
    dependencies:
      browserslist: 4.21.4
      caniuse-api: 3.0.0
      has: 1.0.3
      postcss: 7.0.39
    dev: false

  /postcss-reduce-transforms/4.0.2:
    resolution: {integrity: sha512-EEVig1Q2QJ4ELpJXMZR8Vt5DQx8/mo+dGWSR7vWXqcob2gQLyQGsionYcGKATXvQzMPn6DSN1vTN7yFximdIAg==}
    engines: {node: '>=6.9.0'}
    dependencies:
      cssnano-util-get-match: 4.0.0
      has: 1.0.3
      postcss: 7.0.39
      postcss-value-parser: 3.3.1
    dev: false

  /postcss-replace-overflow-wrap/3.0.0:
    resolution: {integrity: sha512-2T5hcEHArDT6X9+9dVSPQdo7QHzG4XKclFT8rU5TzJPDN7RIRTbO9c4drUISOVemLj03aezStHCR2AIcr8XLpw==}
    dependencies:
      postcss: 7.0.39
    dev: false

  /postcss-resolve-nested-selector/0.1.1:
    resolution: {integrity: sha1-Kcy8fDfe36wwTp//C/FZaz9qDk4=}
    dev: true

  /postcss-safe-parser/4.0.1:
    resolution: {integrity: sha512-xZsFA3uX8MO3yAda03QrG3/Eg1LN3EPfjjf07vke/46HERLZyHrTsQ9E1r1w1W//fWEhtYNndo2hQplN2cVpCQ==}
    engines: {node: '>=6.0.0'}
    dependencies:
      postcss: 7.0.39
    dev: false

  /postcss-safe-parser/4.0.2:
    resolution: {integrity: sha512-Uw6ekxSWNLCPesSv/cmqf2bY/77z11O7jZGPax3ycZMFU/oi2DMH9i89AdHc1tRwFg/arFoEwX0IS3LCUxJh1g==}
    engines: {node: '>=6.0.0'}
    dependencies:
      postcss: 7.0.39
    dev: true

  /postcss-sass/0.4.4:
    resolution: {integrity: sha512-BYxnVYx4mQooOhr+zer0qWbSPYnarAy8ZT7hAQtbxtgVf8gy+LSLT/hHGe35h14/pZDTw1DsxdbrwxBN++H+fg==}
    dependencies:
      gonzales-pe: 4.3.0
      postcss: 7.0.39
    dev: true

  /postcss-scss/2.1.1:
    resolution: {integrity: sha512-jQmGnj0hSGLd9RscFw9LyuSVAa5Bl1/KBPqG1NQw9w8ND55nY4ZEsdlVuYJvLPpV+y0nwTV5v/4rHPzZRihQbA==}
    engines: {node: '>=6.0.0'}
    dependencies:
      postcss: 7.0.39
    dev: true

  /postcss-selector-matches/4.0.0:
    resolution: {integrity: sha512-LgsHwQR/EsRYSqlwdGzeaPKVT0Ml7LAT6E75T8W8xLJY62CE4S/l03BWIt3jT8Taq22kXP08s2SfTSzaraoPww==}
    dependencies:
      balanced-match: 1.0.2
      postcss: 7.0.39
    dev: false

  /postcss-selector-not/4.0.1:
    resolution: {integrity: sha512-YolvBgInEK5/79C+bdFMyzqTg6pkYqDbzZIST/PDMqa/o3qtXenD05apBG2jLgT0/BQ77d4U2UK12jWpilqMAQ==}
    dependencies:
      balanced-match: 1.0.2
      postcss: 7.0.39
    dev: false

  /postcss-selector-parser/3.1.2:
    resolution: {integrity: sha512-h7fJ/5uWuRVyOtkO45pnt1Ih40CEleeyCHzipqAZO2e5H20g25Y48uYnFUiShvY4rZWNJ/Bib/KVPmanaCtOhA==}
    engines: {node: '>=8'}
    dependencies:
      dot-prop: 5.3.0
      indexes-of: 1.0.1
      uniq: 1.0.1
    dev: false

  /postcss-selector-parser/5.0.0:
    resolution: {integrity: sha512-w+zLE5Jhg6Liz8+rQOWEAwtwkyqpfnmsinXjXg6cY7YIONZZtgvE0v2O0uhQBs0peNomOJwWRKt6JBfTdTd3OQ==}
    engines: {node: '>=4'}
    dependencies:
      cssesc: 2.0.0
      indexes-of: 1.0.1
      uniq: 1.0.1
    dev: false

  /postcss-selector-parser/6.0.10:
    resolution: {integrity: sha512-IQ7TZdoaqbT+LCpShg46jnZVlhWD2w6iQYAcYXfHARZ7X1t/UGhhceQDs5X0cGqKvYlHNOuv7Oa1xmb0oQuA3w==}
    engines: {node: '>=4'}
    dependencies:
      cssesc: 3.0.0
      util-deprecate: 1.0.2

  /postcss-svgo/4.0.3:
    resolution: {integrity: sha512-NoRbrcMWTtUghzuKSoIm6XV+sJdvZ7GZSc3wdBN0W19FTtp2ko8NqLsgoh/m9CzNhU3KLPvQmjIwtaNFkaFTvw==}
    engines: {node: '>=6.9.0'}
    dependencies:
      postcss: 7.0.39
      postcss-value-parser: 3.3.1
      svgo: 1.3.2
    dev: false

  /postcss-syntax/0.36.2_kei4jy7wdgbhc236h4oijypxom:
    resolution: {integrity: sha512-nBRg/i7E3SOHWxF3PpF5WnJM/jQ1YpY9000OaVXlAQj6Zp/kIqJxEDWIZ67tAd7NLuk7zqN4yqe9nc0oNAOs1w==}
    peerDependencies:
      postcss: '>=5.0.0'
      postcss-html: '*'
      postcss-jsx: '*'
      postcss-less: '*'
      postcss-markdown: '*'
      postcss-scss: '*'
    peerDependenciesMeta:
      postcss-html:
        optional: true
      postcss-jsx:
        optional: true
      postcss-less:
        optional: true
      postcss-markdown:
        optional: true
      postcss-scss:
        optional: true
    dependencies:
      postcss: 7.0.39
      postcss-html: 0.36.0_j55xdkkcxc32kvnyvx3y7casfm
      postcss-less: 3.1.4
      postcss-scss: 2.1.1
    dev: true

  /postcss-unique-selectors/4.0.1:
    resolution: {integrity: sha512-+JanVaryLo9QwZjKrmJgkI4Fn8SBgRO6WXQBJi7KiAVPlmxikB5Jzc4EvXMT2H0/m0RjrVVm9rGNhZddm/8Spg==}
    engines: {node: '>=6.9.0'}
    dependencies:
      alphanum-sort: 1.0.2
      postcss: 7.0.39
      uniqs: 2.0.0
    dev: false

  /postcss-value-parser/3.3.1:
    resolution: {integrity: sha512-pISE66AbVkp4fDQ7VHBwRNXzAAKJjw4Vw7nWI/+Q3vuly7SNfgYXvm6i5IgFylHGK5sP/xHAbB7N49OS4gWNyQ==}
    dev: false

  /postcss-value-parser/4.2.0:
    resolution: {integrity: sha512-1NNCs6uurfkVbeXG4S8JFT9t19m45ICnif8zWLd5oPSZ50QnwMfK+H3jv408d4jw/7Bttv5axS5IiHoLaVNHeQ==}

  /postcss-values-parser/2.0.1:
    resolution: {integrity: sha512-2tLuBsA6P4rYTNKCXYG/71C7j1pU6pK503suYOmn4xYrQIzW+opD+7FAFNuGSdZC/3Qfy334QbeMu7MEb8gOxg==}
    engines: {node: '>=6.14.4'}
    dependencies:
      flatten: 1.0.3
      indexes-of: 1.0.1
      uniq: 1.0.1
    dev: false

  /postcss/7.0.21:
    resolution: {integrity: sha512-uIFtJElxJo29QC753JzhidoAhvp/e/Exezkdhfmt8AymWT6/5B7W1WmponYWkHk2eg6sONyTch0A3nkMPun3SQ==}
    engines: {node: '>=6.0.0'}
    dependencies:
      chalk: 2.4.2
      source-map: 0.6.1
      supports-color: 6.1.0
    dev: false

  /postcss/7.0.39:
    resolution: {integrity: sha1-liQ3XZZWMOLh8sAqk1yCpZy0gwk=}
    engines: {node: '>=6.0.0'}
    dependencies:
      picocolors: 0.2.1
      source-map: 0.6.1

  /prelude-ls/1.1.2:
    resolution: {integrity: sha1-IZMqVJ9eUv/ZqCf1cOBL5iqX2lQ=}
    engines: {node: '>= 0.8.0'}
    dev: false

  /prepend-http/1.0.4:
    resolution: {integrity: sha1-1PRWKwzjaW5BrFLQ4ALlemNdxtw=}
    engines: {node: '>=0.10.0'}
    dev: false

  /pretty-bytes/5.6.0:
    resolution: {integrity: sha512-FFw039TmrBqFK8ma/7OL3sDz/VytdtJr044/QUJtH0wK9lb9jLq9tJyIxUwtQJHwar2BqtiA4iCWSwo9JLkzFg==}
    engines: {node: '>=6'}
    dev: false

  /pretty-error/2.1.2:
    resolution: {integrity: sha512-EY5oDzmsX5wvuynAByrmY0P0hcp+QpnAKbJng2A2MPjVKXCxrDSUkzghVJ4ZGPIv+JC4gX8fPUWscC0RtjsWGw==}
    dependencies:
      lodash: 4.17.21
      renderkid: 2.0.7
    dev: false

  /pretty-format/24.9.0:
    resolution: {integrity: sha512-00ZMZUiHaJrNfk33guavqgvfJS30sLYf0f8+Srklv0AMPodGGHcoHgksZ3OThYnIvOd+8yMCn0YiEOogjlgsnA==}
    engines: {node: '>= 6'}
    dependencies:
      '@jest/types': 24.9.0
      ansi-regex: 4.1.1
      ansi-styles: 3.2.1
      react-is: 16.13.1

  /pretty-format/25.5.0:
    resolution: {integrity: sha512-kbo/kq2LQ/A/is0PQwsEHM7Ca6//bGPPvU6UnsdDRSKTWxT/ru/xb88v4BJf6a69H+uTytOEsTusT9ksd/1iWQ==}
    engines: {node: '>= 8.3'}
    dependencies:
      '@jest/types': 25.5.0
      ansi-regex: 5.0.1
      ansi-styles: 4.3.0
      react-is: 16.13.1
    dev: true

  /process-nextick-args/2.0.1:
    resolution: {integrity: sha512-3ouUOpQhtgrbOa17J7+uxOTpITYWaGP7/AhoR3+A+/1e9skrzelGi/dXzEYyvbxubEF6Wn2ypscTKiKJFFn1ag==}

  /process/0.11.10:
    resolution: {integrity: sha1-czIwDoQBYb2j5podHZGn1LwW8YI=}
    engines: {node: '>= 0.6.0'}
    dev: false

  /progress/2.0.3:
    resolution: {integrity: sha512-7PiHtLll5LdnKIMw100I+8xJXR5gW2QwWYkT6iJva0bXitZKa/XMrSbdmg3r2Xnaidz9Qumd0VPaMrZlF9V9sA==}
    engines: {node: '>=0.4.0'}
    dev: false

  /promise-inflight/1.0.1_bluebird@3.7.2:
    resolution: {integrity: sha1-mEcocL8igTL8vdhoEputEsPAKeM=}
    peerDependencies:
      bluebird: '*'
    peerDependenciesMeta:
      bluebird:
        optional: true
    dependencies:
      bluebird: 3.7.2
    dev: false

  /promise/7.3.1:
    resolution: {integrity: sha512-nolQXZ/4L+bP/UGlkfaIujX9BKxGwmQ9OT4mOt5yvy8iK1h3wqTEJCijzGANTCCl9nWjY41juyAn2K3Q1hLLTg==}
    dependencies:
      asap: 2.0.6
    dev: false

  /promise/8.2.0:
    resolution: {integrity: sha512-+CMAlLHqwRYwBMXKCP+o8ns7DN+xHDUiI+0nArsiJ9y+kJVPLFxEaSw6Ha9s9H0tftxg2Yzl25wqj9G7m5wLZg==}
    dependencies:
      asap: 2.0.6
    dev: false

  /prompts/2.4.2:
    resolution: {integrity: sha1-e1fnOzpIAprRDr1E90sBcipMsGk=}
    engines: {node: '>= 6'}
    dependencies:
      kleur: 3.0.3
      sisteransi: 1.0.5
    dev: false

  /prop-types/15.8.1:
    resolution: {integrity: sha512-oj87CgZICdulUohogVAR7AjlC0327U4el4L6eAvOqCeudMDVU0NThNaV+b9Df4dXgSP1gXMTnPdhfe/2qDH5cg==}
    dependencies:
      loose-envify: 1.4.0
      object-assign: 4.1.1
      react-is: 16.13.1

  /proxy-addr/2.0.7:
    resolution: {integrity: sha512-llQsMLSUDUPT44jdrU/O37qlnifitDP+ZwrmmZcoSKyLKvtZxpyV0n2/bD/N4tBAAZ/gJEdZU7KMraoK1+XYAg==}
    engines: {node: '>= 0.10'}
    dependencies:
      forwarded: 0.2.0
      ipaddr.js: 1.9.1
    dev: false

  /prr/1.0.1:
    resolution: {integrity: sha1-0/wRS6BplaRexok/SEzrHXj19HY=}
    dev: false

  /pseudomap/1.0.2:
    resolution: {integrity: sha1-8FKijacOYYkX7wqKw0wa5aaChrM=}

  /psl/1.9.0:
    resolution: {integrity: sha512-E/ZsdU4HLs/68gYzgGTkMicWTLPdAftJLfJFlLUAAKZGkStNU72sZjT66SnMDVOfOWY/YAoiD7Jxa9iHvngcag==}

  /public-encrypt/4.0.3:
    resolution: {integrity: sha512-zVpa8oKZSz5bTMTFClc1fQOnyyEzpl5ozpi1B5YcvBrdohMjH2rfsBtyXcuNuwjsDIXmBYlF2N5FlJYhR29t8Q==}
    dependencies:
      bn.js: 4.12.0
      browserify-rsa: 4.1.0
      create-hash: 1.2.0
      parse-asn1: 5.1.6
      randombytes: 2.1.0
      safe-buffer: 5.2.1
    dev: false

  /pump/2.0.1:
    resolution: {integrity: sha512-ruPMNRkN3MHP1cWJc9OWr+T/xDP0jhXYCLfJcBuX54hhfIBnaQmAUMfDcG4DM5UMWByBbJY69QSphm3jtDKIkA==}
    dependencies:
      end-of-stream: 1.4.4
      once: 1.4.0
    dev: false

  /pump/3.0.0:
    resolution: {integrity: sha512-LwZy+p3SFs1Pytd/jYct4wpv49HiYCqd9Rlc5ZVdk0V+8Yzv6jR5Blk3TRmPL1ft69TxP0IMZGJ+WPFU2BFhww==}
    dependencies:
      end-of-stream: 1.4.4
      once: 1.4.0
    dev: false

  /pumpify/1.5.1:
    resolution: {integrity: sha512-oClZI37HvuUJJxSKKrC17bZ9Cu0ZYhEAGPsPUy9KlMUmv9dKX2o77RUmq7f3XjIxbwyGwYzbzQ1L2Ks8sIradQ==}
    dependencies:
      duplexify: 3.7.1
      inherits: 2.0.4
      pump: 2.0.1
    dev: false

  /punycode/1.3.2:
    resolution: {integrity: sha1-llOgNvt8HuQjQvIyXM7v6jkmxI0=}
    dev: false

  /punycode/1.4.1:
    resolution: {integrity: sha1-wNWmOycYgArY4esPpSachN1BhF4=}
    dev: false

  /punycode/2.1.1:
    resolution: {integrity: sha512-XRsRjdf+j5ml+y/6GKHPZbrF/8p2Yga0JPtdqTIY2Xe5ohJPD9saDJJLPvp9+NSBprVvevdXZybnj2cv8OEd0A==}
    engines: {node: '>=6'}

  /q/1.5.1:
    resolution: {integrity: sha1-fjL3W0E4EpHQRhHxvxQQmsAGUdc=}
    engines: {node: '>=0.6.0', teleport: '>=0.2.0'}
    dev: false

  /qr.js/0.0.0:
    resolution: {integrity: sha1-ys6GOG9ZoNuAUPqQ2baw6IoeNk8=}
    dev: false

  /qrcode.react/1.0.1_react@16.14.0:
    resolution: {integrity: sha512-8d3Tackk8IRLXTo67Y+c1rpaiXjoz/Dd2HpcMdW//62/x8J1Nbho14Kh8x974t9prsLHN6XqVgcnRiBGFptQmg==}
    peerDependencies:
      react: ^15.5.3 || ^16.0.0 || ^17.0.0
    dependencies:
      loose-envify: 1.4.0
      prop-types: 15.8.1
      qr.js: 0.0.0
      react: 16.14.0
    dev: false

  /qs/6.11.0:
    resolution: {integrity: sha512-MvjoMCJwEarSbUYk5O+nmoSzSutSsTwF85zcHPQ9OrlFoZOYIjaqBAJIqIXjptyD5vThxGq52Xu/MaJzRkIk4Q==}
    engines: {node: '>=0.6'}
    dependencies:
      side-channel: 1.0.4
    dev: false

  /qs/6.5.3:
    resolution: {integrity: sha512-qxXIEh4pCGfHICj1mAJQ2/2XVZkjCDTcEgfoSQxc/fYivUZxTkk7L3bDBJSoNrEzXI17oUO5Dp07ktqE5KzczA==}
    engines: {node: '>=0.6'}

  /query-string/4.3.4:
    resolution: {integrity: sha1-u7aTucqRXCMlFbIosaArYJBD2+s=}
    engines: {node: '>=0.10.0'}
    dependencies:
      object-assign: 4.1.1
      strict-uri-encode: 1.1.0
    dev: false

  /querystring-es3/0.2.1:
    resolution: {integrity: sha1-nsYfeQSYdXB9aUFFlv2Qek1xHnM=}
    engines: {node: '>=0.4.x'}
    dev: false

  /querystring/0.2.0:
    resolution: {integrity: sha1-sgmEkgO7Jd+CDadW50cAWHhSFiA=}
    engines: {node: '>=0.4.x'}
    dev: false

  /querystringify/2.2.0:
    resolution: {integrity: sha512-FIqgj2EUvTa7R50u0rGsyTftzjYmv/a3hO345bZNrqabNqjtgiDMgmo4mkUjd+nzU5oF3dClKqFIPUKybUyqoQ==}
    dev: false

  /queue-microtask/1.2.3:
    resolution: {integrity: sha512-NuaNSa6flKT5JaSYQzJok04JzTL1CA6aGhv5rfLW3PgqA+M2ChpZQnAC8h8i4ZFkBS8X5RqkDBHA7r4hej3K9A==}
    dev: true

  /quick-lru/4.0.1:
    resolution: {integrity: sha512-ARhCpm70fzdcvNQfPoy49IaanKkTlRWF2JMzqhcJbhSFRZv7nPTvZJdcY7301IPmvW+/p0RgIWnQDLJxifsQ7g==}
    engines: {node: '>=8'}
    dev: true

  /raf/3.4.1:
    resolution: {integrity: sha512-Sq4CW4QhwOHE8ucn6J34MqtZCeWFP2aQSmrlroYgqAV1PjStIhJXxYuTgUIfkEk7zTLjmIjLmU5q+fbD1NnOJA==}
    dependencies:
      performance-now: 2.1.0
    dev: false

  /randombytes/2.1.0:
    resolution: {integrity: sha512-vYl3iOX+4CKUWuxGi9Ukhie6fsqXqS9FE2Zaic4tNFD2N2QQaXOMFbuKK4QmDHC0JO6B1Zp41J0LpT0oR68amQ==}
    dependencies:
      safe-buffer: 5.2.1
    dev: false

  /randomfill/1.0.4:
    resolution: {integrity: sha512-87lcbR8+MhcWcUiQ+9e+Rwx8MyR2P7qnt15ynUlbm3TU/fjbgz4GsvfSUDTemtCCtVCqb4ZcEFlyPNTh9bBTLw==}
    dependencies:
      randombytes: 2.1.0
      safe-buffer: 5.2.1
    dev: false

  /range-parser/1.2.1:
    resolution: {integrity: sha512-Hrgsx+orqoygnmhFbKaHE6c296J+HTAQXoxEF6gNupROmmGJRoyzfG3ccAveqCBrwr/2yxQ5BVd/GTl5agOwSg==}
    engines: {node: '>= 0.6'}
    dev: false

  /raw-body/2.5.1:
    resolution: {integrity: sha512-qqJBtEyVgS0ZmPGdCFPWJ3FreoqvG4MVQln/kCgF7Olq95IbOp0/BWyMwbdtn4VTvkM8Y7khCQ2Xgk/tcrCXig==}
    engines: {node: '>= 0.8'}
    dependencies:
      bytes: 3.1.2
      http-errors: 2.0.0
      iconv-lite: 0.4.24
      unpipe: 1.0.0
    dev: false

  /rc-align/2.4.5:
    resolution: {integrity: sha512-nv9wYUYdfyfK+qskThf4BQUSIadeI/dCsfaMZfNEoxm9HwOIioQ+LyqmMK6jWHAZQgOzMLaqawhuBXlF63vgjw==}
    dependencies:
      babel-runtime: 6.26.0
      dom-align: 1.12.3
      prop-types: 15.8.1
      rc-util: 4.21.1
    dev: false

  /rc-animate/2.11.1_wcqkhtmu7mswc6yz4uyexck3ty:
    resolution: {integrity: sha512-1NyuCGFJG/0Y+9RKh5y/i/AalUCA51opyyS/jO2seELpgymZm2u9QV3xwODwEuzkmeQ1BDPxMLmYLcTJedPlkQ==}
    peerDependencies:
      react: '>=16.9.0'
      react-dom: '>=16.9.0'
    dependencies:
      babel-runtime: 6.26.0
      classnames: 2.3.2
      css-animation: 1.6.1
      prop-types: 15.8.1
      raf: 3.4.1
      rc-util: 4.21.1
      react: 16.14.0
      react-dom: 16.14.0_react@16.14.0
      react-lifecycles-compat: 3.0.4
    dev: false

  /rc-checkbox/2.0.3:
    resolution: {integrity: sha1-Q2qdUIlI4iSYDwU16nOLSBd6jyU=}
    dependencies:
      babel-runtime: 6.26.0
      classnames: 2.3.2
      prop-types: 15.8.1
      rc-util: 4.21.1
    dev: false

  /rc-collapse/1.9.3_wcqkhtmu7mswc6yz4uyexck3ty:
    resolution: {integrity: sha512-8cG+FzudmgFCC9zRGKXJZA36zoI9Dmyjp6UDi8N80sXUch0JOpsZDxgcFzw4HPpPpK/dARtTilEe9zyuspnW0w==}
    dependencies:
      classnames: 2.3.2
      css-animation: 1.6.1
      prop-types: 15.8.1
      rc-animate: 2.11.1_wcqkhtmu7mswc6yz4uyexck3ty
    transitivePeerDependencies:
      - react
      - react-dom
    dev: false

  /rc-gesture/0.0.22:
    resolution: {integrity: sha512-6G6qrCE0MUTXyjh/powj91XkjRjoFL4HiJLPU5lALXHvGX+/efcUjGYUrHrrw0mwQdmrmg4POqnY/bibns+G3g==}
    dependencies:
      babel-runtime: 6.26.0
    dev: false

  /rc-slider/8.2.0_wcqkhtmu7mswc6yz4uyexck3ty:
    resolution: {integrity: sha1-rjfRcUTK1g4dpurA7k/8/qCwpug=}
    dependencies:
      babel-runtime: 6.26.0
      classnames: 2.3.2
      prop-types: 15.8.1
      rc-tooltip: 3.7.3_wcqkhtmu7mswc6yz4uyexck3ty
      rc-util: 4.21.1
      shallowequal: 1.1.0
      warning: 3.0.0
    transitivePeerDependencies:
      - react
      - react-dom
    dev: false

  /rc-swipeout/2.0.11:
    resolution: {integrity: sha512-d37Lgn4RX4OOQyuA2BFo0rGlUwrmZk5q83srH3ixJ1Y1jidr2GKjgJDbNeGUVZPNfYBL91Elu6+xfVGftWf4Lg==}
    dependencies:
      babel-runtime: 6.26.0
      classnames: 2.3.2
      rc-gesture: 0.0.22
      react-native-swipeout: 2.3.6
    dev: false

  /rc-tooltip/3.7.3_wcqkhtmu7mswc6yz4uyexck3ty:
    resolution: {integrity: sha512-dE2ibukxxkrde7wH9W8ozHKUO4aQnPZ6qBHtrTH9LoO836PjDdiaWO73fgPB05VfJs9FbZdmGPVEbXCeOP99Ww==}
    dependencies:
      babel-runtime: 6.26.0
      prop-types: 15.8.1
      rc-trigger: 2.6.5_wcqkhtmu7mswc6yz4uyexck3ty
    transitivePeerDependencies:
      - react
      - react-dom
    dev: false

  /rc-trigger/2.6.5_wcqkhtmu7mswc6yz4uyexck3ty:
    resolution: {integrity: sha512-m6Cts9hLeZWsTvWnuMm7oElhf+03GOjOLfTuU0QmdB9ZrW7jR2IpI5rpNM7i9MvAAlMAmTx5Zr7g3uu/aMvZAw==}
    dependencies:
      babel-runtime: 6.26.0
      classnames: 2.3.2
      prop-types: 15.8.1
      rc-align: 2.4.5
      rc-animate: 2.11.1_wcqkhtmu7mswc6yz4uyexck3ty
      rc-util: 4.21.1
      react-lifecycles-compat: 3.0.4
    transitivePeerDependencies:
      - react
      - react-dom
    dev: false

  /rc-util/4.21.1:
    resolution: {integrity: sha512-Z+vlkSQVc1l8O2UjR3WQ+XdWlhj5q9BMQNLk2iOBch75CqPfrJyGtcWMcnhRlNuDu0Ndtt4kLVO8JI8BrABobg==}
    dependencies:
      add-dom-event-listener: 1.1.0
      prop-types: 15.8.1
      react-is: 16.13.1
      react-lifecycles-compat: 3.0.4
      shallowequal: 1.1.0
    dev: false

  /react-app-polyfill/1.0.6:
    resolution: {integrity: sha512-OfBnObtnGgLGfweORmdZbyEz+3dgVePQBb3zipiaDsMHV1NpWm0rDFYIVXFV/AK+x4VIIfWHhrdMIeoTLyRr2g==}
    engines: {node: '>=6'}
    dependencies:
      core-js: 3.25.5
      object-assign: 4.1.1
      promise: 8.2.0
      raf: 3.4.1
      regenerator-runtime: 0.13.10
      whatwg-fetch: 3.6.2
    dev: false

  /react-app-polyfill/3.0.0:
    resolution: {integrity: sha512-sZ41cxiU5llIB003yxxQBYrARBqe0repqPTTYBTmMqTz9szeBbE37BehCE891NZsmdZqqP+xWKdT3eo3vOzN8w==}
    engines: {node: '>=14'}
    dependencies:
      core-js: 3.25.5
      object-assign: 4.1.1
      promise: 8.2.0
      raf: 3.4.1
      regenerator-runtime: 0.13.10
      whatwg-fetch: 3.6.2
    dev: false

  /react-copy-to-clipboard/5.1.0_react@16.14.0:
    resolution: {integrity: sha512-k61RsNgAayIJNoy9yDsYzDe/yAZAzEbEgcz3DZMhF686LEyukcE1hzurxe85JandPUG+yTfGVFzuEw3xt8WP/A==}
    peerDependencies:
      react: ^15.3.0 || 16 || 17 || 18
    dependencies:
      copy-to-clipboard: 3.3.2
      prop-types: 15.8.1
      react: 16.14.0
    dev: false

  /react-dev-utils/10.2.1_rhie7mtatz3augq54a72vkrn4a:
    resolution: {integrity: sha512-XxTbgJnYZmxuPtY3y/UV0D8/65NKkmaia4rXzViknVnZeVlklSh8u6TnaEYPfAi/Gh1TP4mEOXHI6jQOPbeakQ==}
    engines: {node: '>=8.10'}
    dependencies:
      '@babel/code-frame': 7.8.3
      address: 1.1.2
      browserslist: 4.10.0
      chalk: 2.4.2
      cross-spawn: 7.0.1
      detect-port-alt: 1.1.6
      escape-string-regexp: 2.0.0
      filesize: 6.0.1
      find-up: 4.1.0
      fork-ts-checker-webpack-plugin: 3.1.1_rhie7mtatz3augq54a72vkrn4a
      global-modules: 2.0.0
      globby: 8.0.2
      gzip-size: 5.1.1
      immer: 1.10.0
      inquirer: 7.0.4
      is-root: 2.1.0
      loader-utils: 1.2.3
      open: 7.4.2
      pkg-up: 3.1.0
      react-error-overlay: 6.0.11
      recursive-readdir: 2.2.2
      shell-quote: 1.7.2
      strip-ansi: 6.0.0
      text-table: 0.2.0
    transitivePeerDependencies:
      - eslint
      - supports-color
      - typescript
      - vue-template-compiler
      - webpack
    dev: false

  /react-dom/16.14.0_react@16.14.0:
    resolution: {integrity: sha512-1gCeQXDLoIqMgqD3IO2Ah9bnf0w9kzhwN5q4FGnHZ67hBm9yePzB5JJAIQCc8x3pFnNlwFq4RidZggNAAkzWWw==}
    peerDependencies:
      react: ^16.14.0
    dependencies:
      loose-envify: 1.4.0
      object-assign: 4.1.1
      prop-types: 15.8.1
      react: 16.14.0
      scheduler: 0.19.1

  /react-error-overlay/6.0.11:
    resolution: {integrity: sha512-/6UZ2qgEyH2aqzYZgQPxEnz33NJ2gNsnHA2o5+o4wW9bLM/JYQitNP9xPhsXwC08hMMovfGe/8retsdDsczPRg==}
    dev: false

  /react-ga/3.3.1_react@16.14.0:
    resolution: {integrity: sha512-4Vc0W5EvXAXUN/wWyxvsAKDLLgtJ3oLmhYYssx+YzphJpejtOst6cbIHCIyF50Fdxuf5DDKqRYny24yJ2y7GFQ==}
    peerDependencies:
      prop-types: ^15.6.0
      react: ^15.6.2 || ^16.0 || ^17 || ^18
    dependencies:
      react: 16.14.0
    dev: false

  /react-i18next/11.18.6_qxgl7uxr7nkpkat3aynhndjxqu:
    resolution: {integrity: sha512-yHb2F9BiT0lqoQDt8loZ5gWP331GwctHz9tYQ8A2EIEUu+CcEdjBLQWli1USG3RdWQt3W+jqQLg/d4rrQR96LA==}
    peerDependencies:
      i18next: '>= 19.0.0'
      react: '>= 16.8.0'
      react-dom: '*'
      react-native: '*'
    peerDependenciesMeta:
      react-dom:
        optional: true
      react-native:
        optional: true
    dependencies:
      '@babel/runtime': 7.19.4
      html-parse-stringify: 3.0.1
      i18next: 19.9.2
      react: 16.14.0
      react-dom: 16.14.0_react@16.14.0
    dev: false

  /react-is/16.13.1:
    resolution: {integrity: sha512-24e6ynE2H+OKt4kqsOvNd8kBpV65zoxbA4BVsEOB3ARVWQki/DHzaUoC5KuON/BiccDaCCTZBuOcfZs70kR8bQ==}

  /react-lifecycles-compat/3.0.4:
    resolution: {integrity: sha512-fBASbA6LnOU9dOU2eW7aQ8xmYBSXUIWr+UmF9b1efZBazGNO+rcXT/icdKnYm2pTwcRylVUYwW7H1PHfLekVzA==}
    dev: false

  /react-native-swipeout/2.3.6:
    resolution: {integrity: sha512-t9suUCspzck4vp2pWggWe0frS/QOtX6yYCawHnEes75A7dZCEE74bxX2A1bQzGH9cUMjq6xsdfC94RbiDKIkJg==}
    dependencies:
      create-react-class: 15.7.0
      prop-types: 15.8.1
      react-tween-state: 0.1.5
    dev: false

  /react-public-ip/1.0.0:
    resolution: {integrity: sha512-ne3UUnkYa8xLppXeNSFDFLkw0BklHh0di1ednz6RgHbDd/iwwzi1p/hFFOxOT+b67R4sffzyTQhOeH3WgkwQsg==}
    dev: false

  /react-router-dom/5.3.4_react@16.14.0:
    resolution: {integrity: sha512-m4EqFMHv/Ih4kpcBCONHbkT68KoAeHN4p3lAGoNryfHi0dMy0kCzEZakiKRsvg5wHZ/JLrLW8o8KomWiz/qbYQ==}
    peerDependencies:
      react: '>=15'
    dependencies:
      '@babel/runtime': 7.19.4
      history: 4.10.1
      loose-envify: 1.4.0
      prop-types: 15.8.1
      react: 16.14.0
      react-router: 5.3.4_react@16.14.0
      tiny-invariant: 1.3.1
      tiny-warning: 1.0.3
    dev: false

  /react-router/5.3.4_react@16.14.0:
    resolution: {integrity: sha512-Ys9K+ppnJah3QuaRiLxk+jDWOR1MekYQrlytiXxC1RyfbdsZkS5pvKAzCCr031xHixZwpnsYNT5xysdFHQaYsA==}
    peerDependencies:
      react: '>=15'
    dependencies:
      '@babel/runtime': 7.19.4
      history: 4.10.1
      hoist-non-react-statics: 3.3.2
      loose-envify: 1.4.0
      path-to-regexp: 1.8.0
      prop-types: 15.8.1
      react: 16.14.0
      react-is: 16.13.1
      tiny-invariant: 1.3.1
      tiny-warning: 1.0.3
    dev: false

  /react-scripts/3.4.0_iikfrvavpsqmuo57vprnqzfr6u:
    resolution: {integrity: sha512-pBqaAroFoHnFAkuX+uSK9Th1uEh2GYdGY2IG1I9/7HmuEf+ls3lLCk1p2GFYRSrLMz6ieQR/SyN6TLIGK3hKRg==}
    engines: {node: '>=8.10'}
    hasBin: true
    peerDependencies:
      react: '*'
      typescript: ^3.2.1
    peerDependenciesMeta:
      typescript:
        optional: true
    dependencies:
      '@babel/core': 7.8.4
      '@svgr/webpack': 4.3.3
      '@typescript-eslint/eslint-plugin': 2.34.0_fmavwhclprfd5wnbs7ocgoy2gu
      '@typescript-eslint/parser': 2.34.0_z6m2zvrkqxyghb4a2ijhravsdi
      babel-eslint: 10.0.3_eslint@6.8.0
      babel-jest: 24.9.0_@babel+core@7.8.4
      babel-loader: 8.0.6_7z6h6nfmdlc43mjyviod6jqfeu
      babel-plugin-named-asset-import: 0.3.8_@babel+core@7.8.4
      babel-preset-react-app: 9.1.2
      camelcase: 5.3.1
      case-sensitive-paths-webpack-plugin: 2.3.0
      css-loader: 3.4.2_webpack@4.41.5
      dotenv: 8.2.0
      dotenv-expand: 5.1.0
      eslint: 6.8.0
      eslint-config-react-app: 5.2.1_dk4otncx5baoszk7lp2bv4qwoi
      eslint-loader: 3.0.3_khdbo4kspv3m2napu5lhju627y
      eslint-plugin-flowtype: 4.6.0_eslint@6.8.0
      eslint-plugin-import: 2.20.0_tbglwmj7t2rhd43mvxmptakoay
      eslint-plugin-jsx-a11y: 6.2.3_eslint@6.8.0
      eslint-plugin-react: 7.18.0_eslint@6.8.0
      eslint-plugin-react-hooks: 1.7.0_eslint@6.8.0
      file-loader: 4.3.0_webpack@4.41.5
      fs-extra: 8.1.0
      html-webpack-plugin: 4.0.0-beta.11_webpack@4.41.5
      identity-obj-proxy: 3.0.0
      jest: 24.9.0
      jest-environment-jsdom-fourteen: 1.0.1
      jest-resolve: 24.9.0
      jest-watch-typeahead: 0.4.2
      mini-css-extract-plugin: 0.9.0_webpack@4.41.5
      optimize-css-assets-webpack-plugin: 5.0.3_webpack@4.41.5
      pnp-webpack-plugin: 1.6.0_typescript@3.9.10
      postcss-flexbugs-fixes: 4.1.0
      postcss-loader: 3.0.0
      postcss-normalize: 8.0.1
      postcss-preset-env: 6.7.0
      postcss-safe-parser: 4.0.1
      react: 16.14.0
      react-app-polyfill: 1.0.6
      react-dev-utils: 10.2.1_rhie7mtatz3augq54a72vkrn4a
      resolve: 1.15.0
      resolve-url-loader: 3.1.1
      sass-loader: 8.0.2_log2h4edco4ibha2agbtxc4qxy
      semver: 6.3.0
      style-loader: 0.23.1
      terser-webpack-plugin: 2.3.4_webpack@4.41.5
      ts-pnp: 1.1.5_typescript@3.9.10
      typescript: 3.9.10
      url-loader: 2.3.0_murjgdbes3wqnvdok3pwcx5key
      webpack: 4.41.5
      webpack-dev-server: 3.10.2_webpack@4.41.5
      webpack-manifest-plugin: 2.2.0_webpack@4.41.5
      workbox-webpack-plugin: 4.3.1_webpack@4.41.5
    optionalDependencies:
      fsevents: 2.1.2
    transitivePeerDependencies:
      - bluebird
      - bufferutil
      - eslint-import-resolver-typescript
      - eslint-import-resolver-webpack
      - fibers
      - node-sass
      - sass
      - supports-color
      - utf-8-validate
      - vue-template-compiler
      - webpack-cli
      - webpack-command
    dev: false

  /react-slick/0.27.14_wcqkhtmu7mswc6yz4uyexck3ty:
    resolution: {integrity: sha512-EjNQUpuj09Ef+nKuJjnZZ76v6JngHYFrMoACAt8I3c3TD8EIW3dHfjDKtMQCFvEbXTz8AdwQ2gCv990mazkvMA==}
    peerDependencies:
      react: ^0.14.0 || ^15.0.1 || ^16.0.0 || ^17.0.0
      react-dom: ^0.14.0 || ^15.0.1 || ^16.0.0 || ^17.0.0
    dependencies:
      classnames: 2.3.2
      enquire.js: 2.1.6
      json2mq: 0.2.0
      lodash.debounce: 4.0.8
      react: 16.14.0
      react-dom: 16.14.0_react@16.14.0
      resize-observer-polyfill: 1.5.1
    dev: false

  /react-transition-group/4.4.5_wcqkhtmu7mswc6yz4uyexck3ty:
    resolution: {integrity: sha512-pZcd1MCJoiKiBR2NRxeCRg13uCXbydPnmB4EOeRrY7480qNWO8IIgQG6zlDkm6uRMsURXPuKq0GWtiM59a5Q6g==}
    peerDependencies:
      react: '>=16.6.0'
      react-dom: '>=16.6.0'
    dependencies:
      '@babel/runtime': 7.19.4
      dom-helpers: 5.2.1
      loose-envify: 1.4.0
      prop-types: 15.8.1
      react: 16.14.0
      react-dom: 16.14.0_react@16.14.0
    dev: false

  /react-tween-state/0.1.5:
    resolution: {integrity: sha1-6YsGZVHvuTy5LdG+FJlcLj3q4zk=}
    dependencies:
      raf: 3.4.1
      tween-functions: 1.2.0
    dev: false

  /react/16.14.0:
    resolution: {integrity: sha512-0X2CImDkJGApiAlcf0ODKIneSwBPhqJawOa5wCtKbu7ZECrmS26NvtSILynQ66cgkT/RJ4LidJOc3bUESwmU8g==}
    engines: {node: '>=0.10.0'}
    dependencies:
      loose-envify: 1.4.0
      object-assign: 4.1.1
      prop-types: 15.8.1

  /read-pkg-up/1.0.1:
    resolution: {integrity: sha1-nWPBMnbAZZGNV/ACpX9AobZD+wI=}
    engines: {node: '>=0.10.0'}
    dependencies:
      find-up: 1.1.2
      read-pkg: 1.1.0

  /read-pkg-up/2.0.0:
    resolution: {integrity: sha1-a3KoBImE4MQeeVEP1en6mbO1Sb4=}
    engines: {node: '>=4'}
    dependencies:
      find-up: 2.1.0
      read-pkg: 2.0.0
    dev: false

  /read-pkg-up/4.0.0:
    resolution: {integrity: sha512-6etQSH7nJGsK0RbG/2TeDzZFa8shjQ1um+SwQQ5cwKy0dhSXdOncEhb1CPpvQG4h7FyOV6EB6YlV0yJvZQNAkA==}
    engines: {node: '>=6'}
    dependencies:
      find-up: 3.0.0
      read-pkg: 3.0.0
    dev: false

  /read-pkg-up/7.0.1:
    resolution: {integrity: sha512-zK0TB7Xd6JpCLmlLmufqykGE+/TlOePD6qKClNW7hHDKFh/J7/7gCWGR7joEQEW1bKq3a3yUZSObOoWLFQ4ohg==}
    engines: {node: '>=8'}
    dependencies:
      find-up: 4.1.0
      read-pkg: 5.2.0
      type-fest: 0.8.1
    dev: true

  /read-pkg/1.1.0:
    resolution: {integrity: sha1-9f+qXs0pyzHAR0vKfXVra7KePyg=}
    engines: {node: '>=0.10.0'}
    dependencies:
      load-json-file: 1.1.0
      normalize-package-data: 2.5.0
      path-type: 1.1.0

  /read-pkg/2.0.0:
    resolution: {integrity: sha1-jvHAYjxqbbDcZxPEv6xGMysjaPg=}
    engines: {node: '>=4'}
    dependencies:
      load-json-file: 2.0.0
      normalize-package-data: 2.5.0
      path-type: 2.0.0
    dev: false

  /read-pkg/3.0.0:
    resolution: {integrity: sha1-nLxoaXj+5l0WwA4rGcI3/Pbjg4k=}
    engines: {node: '>=4'}
    dependencies:
      load-json-file: 4.0.0
      normalize-package-data: 2.5.0
      path-type: 3.0.0
    dev: false

  /read-pkg/5.2.0:
    resolution: {integrity: sha512-Ug69mNOpfvKDAc2Q8DRpMjjzdtrnv9HcSMX+4VsZxD1aZ6ZzrIE7rlzXBtWTyhULSMKg076AW6WR5iZpD0JiOg==}
    engines: {node: '>=8'}
    dependencies:
      '@types/normalize-package-data': 2.4.1
      normalize-package-data: 2.5.0
      parse-json: 5.2.0
      type-fest: 0.6.0
    dev: true

  /readable-stream/2.3.7:
    resolution: {integrity: sha512-Ebho8K4jIbHAxnuxi7o42OrZgF/ZTNcsZj6nRKyUmkhLFq8CHItp/fy6hQZuZmP/n3yZ9VBUbp4zz/mX8hmYPw==}
    dependencies:
      core-util-is: 1.0.3
      inherits: 2.0.4
      isarray: 1.0.0
      process-nextick-args: 2.0.1
      safe-buffer: 5.1.2
      string_decoder: 1.1.1
      util-deprecate: 1.0.2

  /readable-stream/3.6.0:
    resolution: {integrity: sha512-BViHy7LKeTz4oNnkcLJ+lVSL6vpiFeX6/d3oSH8zCW7UxP2onchk+vTGB143xuFjHS3deTgkKoXXymXqymiIdA==}
    engines: {node: '>= 6'}
    dependencies:
      inherits: 2.0.4
      string_decoder: 1.3.0
      util-deprecate: 1.0.2

  /readdir-glob/1.1.2:
    resolution: {integrity: sha512-6RLVvwJtVwEDfPdn6X6Ille4/lxGl0ATOY4FN/B9nxQcgOazvvI0nodiD19ScKq0PvA/29VpaOQML36o5IzZWA==}
    dependencies:
      minimatch: 5.1.0
    dev: false

  /readdirp/2.2.1:
    resolution: {integrity: sha512-1JU/8q+VgFZyxwrJ+SVIOsh+KywWGpds3NTqikiKpDMZWScmAYyKIgqkO+ARvNWJfXeXR1zxz7aHF4u4CyH6vQ==}
    engines: {node: '>=0.10'}
    dependencies:
      graceful-fs: 4.2.10
      micromatch: 3.1.10
      readable-stream: 2.3.7
    transitivePeerDependencies:
      - supports-color
    dev: false
    optional: true

  /readdirp/2.2.1_supports-color@6.1.0:
    resolution: {integrity: sha512-1JU/8q+VgFZyxwrJ+SVIOsh+KywWGpds3NTqikiKpDMZWScmAYyKIgqkO+ARvNWJfXeXR1zxz7aHF4u4CyH6vQ==}
    engines: {node: '>=0.10'}
    dependencies:
      graceful-fs: 4.2.10
      micromatch: 3.1.10_supports-color@6.1.0
      readable-stream: 2.3.7
    transitivePeerDependencies:
      - supports-color
    dev: false

  /readdirp/3.6.0:
    resolution: {integrity: sha512-hOS089on8RduqdbhvQ5Z37A0ESjsqz6qnRcffsMU3495FuTdqSm+7bhJ29JvIOsBDEEnan5DPu9t3To9VRlMzA==}
    engines: {node: '>=8.10.0'}
    dependencies:
      picomatch: 2.3.1
    dev: false

  /realpath-native/1.1.0:
    resolution: {integrity: sha512-wlgPA6cCIIg9gKz0fgAPjnzh4yR/LnXovwuo9hvyGvx3h8nX4+/iLZplfUWasXpqD8BdnGnP5njOFjkUwPzvjA==}
    engines: {node: '>=4'}
    dependencies:
      util.promisify: 1.1.1
    dev: false

  /recursive-readdir/2.2.2:
    resolution: {integrity: sha512-nRCcW9Sj7NuZwa2XvH9co8NPeXUBhZP7CRKJtU+cS6PW9FpCIFoI5ib0NT1ZrbNuPoRy0ylyCaUL8Gih4LSyFg==}
    engines: {node: '>=0.10.0'}
    dependencies:
      minimatch: 3.0.4
    dev: false

  /redent/1.0.0:
    resolution: {integrity: sha1-z5Fqsf1fHxbfsggi3W7H9zDCr94=}
    engines: {node: '>=0.10.0'}
    dependencies:
      indent-string: 2.1.0
      strip-indent: 1.0.1

  /redent/3.0.0:
    resolution: {integrity: sha512-6tDA8g98We0zd0GvVeMT9arEOnTw9qM03L9cJXaCjrip1OO764RDBLBfrB4cwzNGDj5OA5ioymC9GkizgWJDUg==}
    engines: {node: '>=8'}
    dependencies:
      indent-string: 4.0.0
      strip-indent: 3.0.0
    dev: true

  /regenerate-unicode-properties/10.1.0:
    resolution: {integrity: sha512-d1VudCLoIGitcU/hEg2QqvyGZQmdC0Lf8BqdOMXGFSvJP4bNV1+XqbPQeHHLD51Jh4QJJ225dlIFvY4Ly6MXmQ==}
    engines: {node: '>=4'}
    dependencies:
      regenerate: 1.4.2
    dev: false

  /regenerate/1.4.2:
    resolution: {integrity: sha512-zrceR/XhGYU/d/opr2EKO7aRHUeiBI8qjtfHqADTwZd6Szfy16la6kqD0MIUs5z5hx6AaKa+PixpPrR289+I0A==}
    dev: false

  /regenerator-runtime/0.11.1:
    resolution: {integrity: sha512-MguG95oij0fC3QV3URf4V2SDYGJhJnJGqvIIgdECeODCT98wSWDAJ94SSuVpYQUoTcGUIL6L4yNB7j1DFFHSBg==}
    dev: false

  /regenerator-runtime/0.13.10:
    resolution: {integrity: sha512-KepLsg4dU12hryUO7bp/axHAKvwGOCV0sGloQtpagJ12ai+ojVDqkeGSiRX1zlq+kjIMZ1t7gpze+26QqtdGqw==}

  /regenerator-transform/0.15.0:
    resolution: {integrity: sha512-LsrGtPmbYg19bcPHwdtmXwbW+TqNvtY4riE3P83foeHRroMbH6/2ddFBfab3t7kbzc7v7p4wbkIecHImqt0QNg==}
    dependencies:
      '@babel/runtime': 7.19.4
    dev: false

  /regex-not/1.0.2:
    resolution: {integrity: sha512-J6SDjUgDxQj5NusnOtdFxDwN/+HWykR8GELwctJ7mdqhcyy1xEc4SRFHUXvxTp661YaVKAjfRLZ9cCqS6tn32A==}
    engines: {node: '>=0.10.0'}
    dependencies:
      extend-shallow: 3.0.2
      safe-regex: 1.1.0
    dev: false

  /regex-parser/2.2.10:
    resolution: {integrity: sha512-8t6074A68gHfU8Neftl0Le6KTDwfGAj7IyjPIMSfikI2wJUTHDMaIq42bUsfVnj8mhx0R+45rdUXHGpN164avA==}
    dev: false

  /regexp.prototype.flags/1.4.3:
    resolution: {integrity: sha512-fjggEOO3slI6Wvgjwflkc4NFRCTZAu5CnNfBd5qOMYhWdn67nJBBu34/TkD++eeFmd8C9r9jfXJ27+nSiRkSUA==}
    engines: {node: '>= 0.4'}
    dependencies:
      call-bind: 1.0.2
      define-properties: 1.1.4
      functions-have-names: 1.2.3
    dev: false

  /regexpp/2.0.1:
    resolution: {integrity: sha512-lv0M6+TkDVniA3aD1Eg0DVpfU/booSu7Eev3TDO/mZKHBfVjgCGTV4t4buppESEYDtkArYFOxTJWv6S5C+iaNw==}
    engines: {node: '>=6.5.0'}
    dev: false

  /regexpp/3.2.0:
    resolution: {integrity: sha512-pq2bWo9mVD43nbts2wGv17XLiNLya+GklZ8kaDLV2Z08gDCsGpnKn9BFMepvWuHCbyVvY7J5o5+BVvoQbmlJLg==}
    engines: {node: '>=8'}
    dev: false

  /regexpu-core/5.2.1:
    resolution: {integrity: sha512-HrnlNtpvqP1Xkb28tMhBUO2EbyUHdQlsnlAhzWcwHy8WJR53UWr7/MAvqrsQKMbV4qdpv03oTMG8iIhfsPFktQ==}
    engines: {node: '>=4'}
    dependencies:
      regenerate: 1.4.2
      regenerate-unicode-properties: 10.1.0
      regjsgen: 0.7.1
      regjsparser: 0.9.1
      unicode-match-property-ecmascript: 2.0.0
      unicode-match-property-value-ecmascript: 2.0.0
    dev: false

  /regjsgen/0.7.1:
    resolution: {integrity: sha512-RAt+8H2ZEzHeYWxZ3H2z6tF18zyyOnlcdaafLrm21Bguj7uZy6ULibiAFdXEtKQY4Sy7wDTwDiOazasMLc4KPA==}
    dev: false

  /regjsparser/0.9.1:
    resolution: {integrity: sha512-dQUtn90WanSNl+7mQKcXAgZxvUe7Z0SqXlgzv0za4LwiUhyzBC58yQO3liFoUgu8GiJVInAhJjkj1N0EtQ5nkQ==}
    hasBin: true
    dependencies:
      jsesc: 0.5.0
    dev: false

  /relateurl/0.2.7:
    resolution: {integrity: sha1-VNvzd+UUQKypCkzSdGANP/LYiKk=}
    engines: {node: '>= 0.10'}
    dev: false

  /remark-parse/9.0.0:
    resolution: {integrity: sha512-geKatMwSzEXKHuzBNU1z676sGcDcFoChMK38TgdHJNAYfFtsfHDQG7MoJAjs6sgYMqyLduCYWDIWZIxiPeafEw==}
    dependencies:
      mdast-util-from-markdown: 0.8.5
    transitivePeerDependencies:
      - supports-color
    dev: true

  /remark-stringify/9.0.1:
    resolution: {integrity: sha512-mWmNg3ZtESvZS8fv5PTvaPckdL4iNlCHTt8/e/8oN08nArHRHjNZMKzA/YW3+p7/lYqIw4nx1XsjCBo/AxNChg==}
    dependencies:
      mdast-util-to-markdown: 0.6.5
    dev: true

  /remark/13.0.0:
    resolution: {integrity: sha512-HDz1+IKGtOyWN+QgBiAT0kn+2s6ovOxHyPAFGKVE81VSzJ+mq7RwHFledEvB5F1p4iJvOah/LOKdFuzvRnNLCA==}
    dependencies:
      remark-parse: 9.0.0
      remark-stringify: 9.0.1
      unified: 9.2.2
    transitivePeerDependencies:
      - supports-color
    dev: true

  /remove-trailing-separator/1.1.0:
    resolution: {integrity: sha1-wkvOKig62tW8P1jg1IJJuSN52O8=}
    dev: false

  /renderkid/2.0.7:
    resolution: {integrity: sha512-oCcFyxaMrKsKcTY59qnCAtmDVSLfPbrv6A3tVbPdFMMrv5jaK10V6m40cKsoPNhAqN6rmHW9sswW4o3ruSrwUQ==}
    dependencies:
      css-select: 4.3.0
      dom-converter: 0.2.0
      htmlparser2: 6.1.0
      lodash: 4.17.21
      strip-ansi: 3.0.1
    dev: false

  /repeat-element/1.1.4:
    resolution: {integrity: sha512-LFiNfRcSu7KK3evMyYOuCzv3L10TW7yC1G2/+StMjK8Y6Vqd2MG7r/Qjw4ghtuCOjFvlnms/iMmLqpvW/ES/WQ==}
    engines: {node: '>=0.10.0'}
    dev: false

  /repeat-string/1.6.1:
    resolution: {integrity: sha1-jcrkcOHIirwtYA//Sndihtp15jc=}
    engines: {node: '>=0.10'}

  /repeating/2.0.1:
    resolution: {integrity: sha1-UhTFOpJtNVJwdSf7q0FdvAjQbdo=}
    engines: {node: '>=0.10.0'}
    dependencies:
      is-finite: 1.1.0

  /request-promise-core/1.1.4_request@2.88.2:
    resolution: {integrity: sha512-TTbAfBBRdWD7aNNOoVOBH4pN/KigV6LyapYNNlAPA8JwbovRti1E88m3sYAwsLi5ryhPKsE9APwnjFTgdUjTpw==}
    engines: {node: '>=0.10.0'}
    peerDependencies:
      request: ^2.34
    dependencies:
      lodash: 4.17.21
      request: 2.88.2
    dev: false

  /request-promise-native/1.0.9_request@2.88.2:
    resolution: {integrity: sha512-wcW+sIUiWnKgNY0dqCpOZkUbF/I+YPi+f09JZIDa39Ec+q82CpSYniDp+ISgTTbKmnpJWASeJBPZmoxH84wt3g==}
    engines: {node: '>=0.12.0'}
    deprecated: request-promise-native has been deprecated because it extends the now deprecated request package, see https://github.com/request/request/issues/3142
    peerDependencies:
      request: ^2.34
    dependencies:
      request: 2.88.2
      request-promise-core: 1.1.4_request@2.88.2
      stealthy-require: 1.1.1
      tough-cookie: 2.5.0
    dev: false

  /request/2.88.2:
    resolution: {integrity: sha512-MsvtOrfG9ZcrOwAW+Qi+F6HbD0CWXEh9ou77uOb7FM2WPhwT7smM833PzanhJLsgXjN89Ir6V2PczXNnMpwKhw==}
    engines: {node: '>= 6'}
    deprecated: request has been deprecated, see https://github.com/request/request/issues/3142
    dependencies:
      aws-sign2: 0.7.0
      aws4: 1.11.0
      caseless: 0.12.0
      combined-stream: 1.0.8
      extend: 3.0.2
      forever-agent: 0.6.1
      form-data: 2.3.3
      har-validator: 5.1.5
      http-signature: 1.2.0
      is-typedarray: 1.0.0
      isstream: 0.1.2
      json-stringify-safe: 5.0.1
      mime-types: 2.1.35
      oauth-sign: 0.9.0
      performance-now: 2.1.0
      qs: 6.5.3
      safe-buffer: 5.2.1
      tough-cookie: 2.5.0
      tunnel-agent: 0.6.0
      uuid: 3.4.0

  /require-directory/2.1.1:
    resolution: {integrity: sha1-jGStX9MNqxyXbiNE/+f3kqam30I=}
    engines: {node: '>=0.10.0'}

  /require-from-string/2.0.2:
    resolution: {integrity: sha512-Xf0nWe6RseziFMu+Ap9biiUbmplq6S9/p+7w7YXP/JBHhrUDDUhwa+vANyubuqfZWTveU//DYVGsDG7RKL/vEw==}
    engines: {node: '>=0.10.0'}
    dev: true

  /require-main-filename/1.0.1:
    resolution: {integrity: sha1-l/cXtp1IeE9fUmpsWqj/3aBVpNE=}
    dev: false

  /require-main-filename/2.0.0:
    resolution: {integrity: sha512-NKN5kMDylKuldxYLSUfrbo5Tuzh4hd+2E8NPPX02mZtn1VuREQToYe/ZdlJy+J3uCpfaiGF05e7B8W0iXbQHmg==}

  /requires-port/1.0.0:
    resolution: {integrity: sha1-kl0mAdOaxIXgkc8NpcbmlNw9yv8=}

  /resize-observer-polyfill/1.5.1:
    resolution: {integrity: sha512-LwZrotdHOo12nQuZlHEmtuXdqGoOD0OhaxopaNFxWzInpEgaLWoVuAMbTzixuosCx2nEG58ngzW3vxdWoxIgdg==}
    dev: false

  /resolve-cwd/2.0.0:
    resolution: {integrity: sha1-AKn3OHVW4nA46uIyyqNypqWbZlo=}
    engines: {node: '>=4'}
    dependencies:
      resolve-from: 3.0.0
    dev: false

  /resolve-from/3.0.0:
    resolution: {integrity: sha1-six699nWiBvItuZTM17rywoYh0g=}
    engines: {node: '>=4'}
    dev: false

  /resolve-from/4.0.0:
    resolution: {integrity: sha512-pb/MYmXstAkysRFx8piNI1tGFNQIFA3vkE3Gq4EuA1dF6gHp/+vgZqsCGJapvy8N3Q+4o7FwvquPJcnZ7RYy4g==}
    engines: {node: '>=4'}

  /resolve-from/5.0.0:
    resolution: {integrity: sha512-qYg9KP24dD5qka9J47d0aVky0N+b4fTU89LN9iDnjB5waksiC49rvMB0PrUJQGoTmH50XPiqOvAjDfaijGxYZw==}
    engines: {node: '>=8'}
    dev: true

  /resolve-pathname/3.0.0:
    resolution: {integrity: sha512-C7rARubxI8bXFNB/hqcp/4iUeIXJhJZvFPFPiSPRnhU5UPxzMFIl+2E6yY6c4k9giDJAhtV+enfA+G89N6Csng==}
    dev: false

  /resolve-url-loader/3.1.1:
    resolution: {integrity: sha512-K1N5xUjj7v0l2j/3Sgs5b8CjrrgtC70SmdCuZiJ8tSyb5J+uk3FoeZ4b7yTnH6j7ngI+Bc5bldHJIa8hYdu2gQ==}
    engines: {node: '>=6.0.0'}
    dependencies:
      adjust-sourcemap-loader: 2.0.0
      camelcase: 5.3.1
      compose-function: 3.0.3
      convert-source-map: 1.7.0
      es6-iterator: 2.0.3
      loader-utils: 1.2.3
      postcss: 7.0.21
      rework: 1.0.1
      rework-visit: 1.0.0
      source-map: 0.6.1
    dev: false

  /resolve-url/0.2.1:
    resolution: {integrity: sha1-LGN/53yJOv0qZj/iGqkIAGjiBSo=}
    deprecated: https://github.com/lydell/resolve-url#deprecated

  /resolve/1.1.7:
    resolution: {integrity: sha1-IDEU2CrSxe2ejgQRs5ModeiJ6Xs=}
    dev: false

  /resolve/1.15.0:
    resolution: {integrity: sha512-+hTmAldEGE80U2wJJDC1lebb5jWqvTYAfm3YZ1ckk1gBr0MnCqUKlwK1e+anaFljIl+F5tR5IoZcm4ZDA1zMQw==}
    dependencies:
      path-parse: 1.0.7
    dev: false

  /resolve/1.22.1:
    resolution: {integrity: sha512-nBpuuYuY5jFsli/JIs1oldw6fOQCBioohqWZg/2hiaOybXOft4lonv85uDOKXdf8rhyK159cxU5cDcK/NKk8zw==}
    hasBin: true
    dependencies:
      is-core-module: 2.11.0
      path-parse: 1.0.7
      supports-preserve-symlinks-flag: 1.0.0

  /restore-cursor/3.1.0:
    resolution: {integrity: sha512-l+sSefzHpj5qimhFSE5a8nufZYAM3sBSVMAPtYkmC+4EH2anSGaEMXSD0izRQbu9nfyQ9y5JrVmp7E8oZrUjvA==}
    engines: {node: '>=8'}
    dependencies:
      onetime: 5.1.2
      signal-exit: 3.0.7
    dev: false

  /ret/0.1.15:
    resolution: {integrity: sha512-TTlYpa+OL+vMMNG24xSlQGEJ3B/RzEfUlLct7b5G/ytav+wPrplCpVMFuwzXbkecJrb6IYo1iFb0S9v37754mg==}
    engines: {node: '>=0.12'}
    dev: false

  /retry/0.12.0:
    resolution: {integrity: sha1-G0KmJmoh8HQh0bC1S33BZ7AcATs=}
    engines: {node: '>= 4'}
    dev: false

  /reusify/1.0.4:
    resolution: {integrity: sha512-U9nH88a3fc/ekCF1l0/UP1IosiuIjyTh7hBvXVMHYgVcfGvt897Xguj2UOLDeI5BG2m7/uwyaLVT6fbtCwTyzw==}
    engines: {iojs: '>=1.0.0', node: '>=0.10.0'}
    dev: true

  /rework-visit/1.0.0:
    resolution: {integrity: sha1-mUWygD8hni96ygCtuLyfZA+ELJo=}
    dev: false

  /rework/1.0.1:
    resolution: {integrity: sha1-MIBqhBNCtUUQqkEQhQzUhTQUSqc=}
    dependencies:
      convert-source-map: 0.3.5
      css: 2.2.4
    dev: false

  /rgb-regex/1.0.1:
    resolution: {integrity: sha1-wODWiC3w4jviVKR16O3UGRX+rrE=}
    dev: false

  /rgba-regex/1.0.0:
    resolution: {integrity: sha1-QzdOLiyglosO8VI0YLfXMP8i7rM=}
    dev: false

  /rimraf/2.6.3:
    resolution: {integrity: sha512-mwqeW5XsA2qAejG46gYdENaxXjx9onRNCfn7L0duuP4hCuTIi/QO7PDK07KJfp1d+izWPrzEJDcSqBa0OZQriA==}
    hasBin: true
    dependencies:
      glob: 7.2.3
    dev: false

  /rimraf/2.7.1:
    resolution: {integrity: sha512-uWjbaKIK3T1OSVptzX7Nl6PvQ3qAGtKEtVRjRuazjfL3Bx5eI409VZSqgND+4UNnmzLVdPj9FqFJNPqBZFve4w==}
    hasBin: true
    dependencies:
      glob: 7.2.3

  /rimraf/3.0.2:
    resolution: {integrity: sha512-JZkJMZkAGFFPP2YqXZXPbMlMBgsxzE8ILs4lMIX/2o0L9UBw9O/Y3o6wFw/i9YLapcUJWwqbi3kdxIPdC62TIA==}
    hasBin: true
    dependencies:
      glob: 7.2.3
    dev: true

  /ripemd160/2.0.2:
    resolution: {integrity: sha512-ii4iagi25WusVoiC4B4lq7pbXfAp3D9v5CwfkY33vffw2+pkDjY1D8GaN7spsxvCSx8dkPqOZCEZyfxcmJG2IA==}
    dependencies:
      hash-base: 3.1.0
      inherits: 2.0.4
    dev: false

  /rmc-align/1.0.0:
    resolution: {integrity: sha512-3gEa5/+hqqoEVoeQ25KoRc8DOsXIdSaVpaBq1zQFaV941LR3xvZIRTlxTDT/IagYwoGM1KZea/jd7cNMYP34Rg==}
    dependencies:
      babel-runtime: 6.26.0
      dom-align: 1.12.3
      rc-util: 4.21.1
    dev: false

  /rmc-calendar/1.1.4_wcqkhtmu7mswc6yz4uyexck3ty:
    resolution: {integrity: sha512-xxQZaPFDnpHt4IFO8mukYrXSgC1W8LcNVp+EoX4iyeOJFimungOKB/iP5/cy+st8yXq8lUgk9TXsHNtM6Xo6ZA==}
    dependencies:
      babel-runtime: 6.26.0
      rc-animate: 2.11.1_wcqkhtmu7mswc6yz4uyexck3ty
      rmc-date-picker: 6.0.10_wcqkhtmu7mswc6yz4uyexck3ty
    transitivePeerDependencies:
      - react
      - react-dom
    dev: false

  /rmc-cascader/5.0.3_wcqkhtmu7mswc6yz4uyexck3ty:
    resolution: {integrity: sha512-PxDhMjWViDdG4SMZqoXtAthGwgDyYnyxxZEE17IDDYsiCHpWtOhoIL8nsI+/hZ212UT/XF2LpqCsOlMoJiYk+w==}
    dependencies:
      array-tree-filter: 2.1.0
      babel-runtime: 6.26.0
      rmc-picker: 5.0.10_wcqkhtmu7mswc6yz4uyexck3ty
    transitivePeerDependencies:
      - react
      - react-dom
    dev: false

  /rmc-date-picker/6.0.10_wcqkhtmu7mswc6yz4uyexck3ty:
    resolution: {integrity: sha512-/9+I6lm3EDEl6M7862V6++zFuxwsM0UEq8wSHbotYIPPmyB/65gx1cviblghOv2QfB0O9+U2w3qEJlRP/WsMrA==}
    dependencies:
      babel-runtime: 6.26.0
      rmc-picker: 5.0.10_wcqkhtmu7mswc6yz4uyexck3ty
    transitivePeerDependencies:
      - react
      - react-dom
    dev: false

  /rmc-dialog/1.1.1_wcqkhtmu7mswc6yz4uyexck3ty:
    resolution: {integrity: sha512-28aJqtPTX6v13Z/aU1WBy1AFIXkE74PxZXde7JvtEIy9hQDTjH8fqOi822BpzAbXCyNE7jF9iFomy3H2ClsDJA==}
    dependencies:
      babel-runtime: 6.26.0
      rc-animate: 2.11.1_wcqkhtmu7mswc6yz4uyexck3ty
    transitivePeerDependencies:
      - react
      - react-dom
    dev: false

  /rmc-drawer/0.4.11:
    resolution: {integrity: sha512-YfB9XEJ8iM0MMuLWAK4313uOxSM8NAljC8Cqun1KamXutglYTuRviUuTLNSOzV8HHPp5kNpsVduvPCGLWXvThw==}
    engines: {node: '>=4.0.0'}
    dependencies:
      babel-runtime: 6.26.0
      classnames: 2.3.2
      prop-types: 15.8.1
    dev: false

  /rmc-feedback/2.0.0:
    resolution: {integrity: sha512-5PWOGOW7VXks/l3JzlOU9NIxRpuaSS8d9zA3UULUCuTKnpwBHNvv1jSJzxgbbCQeYzROWUpgKI4za3X4C/mKmQ==}
    dependencies:
      babel-runtime: 6.26.0
      classnames: 2.3.2
    dev: false

  /rmc-input-number/1.0.5:
    resolution: {integrity: sha512-prPkEtoOVde77GnEnEaBeWjBobMOPgGqU5bd0gxfp1kt1pUN740mMpVAcH7uxpJjVfmw+kuGWtiz4S7CueagSg==}
    dependencies:
      babel-runtime: 6.26.0
      classnames: 2.3.2
      rmc-feedback: 2.0.0
    dev: false

  /rmc-list-view/0.11.5:
    resolution: {integrity: sha512-eMOC5394tLNawcdEEhF7boMpQgpjJGDdL5lS+LblAWdBec7Q4EYkUdnrKNbt+O9k5RGM6nSLAGZK5oB4FN85Lg==}
    dependencies:
      babel-runtime: 6.26.0
      classnames: 2.3.2
      fbjs: 0.8.18
      prop-types: 15.8.1
      warning: 3.0.0
      zscroller: 0.4.8
    dev: false

  /rmc-notification/1.0.0_wcqkhtmu7mswc6yz4uyexck3ty:
    resolution: {integrity: sha512-9sPxjltFvtRLt2v312Hu7OXwk53pHkBYgINRDmnJ3A5NF1qtJeCCcdN0Xr0fzJ6sbQvtGju822tWHdzYA9u7Vw==}
    dependencies:
      babel-runtime: 6.26.0
      classnames: 2.3.2
      prop-types: 15.8.1
      rc-animate: 2.11.1_wcqkhtmu7mswc6yz4uyexck3ty
      rc-util: 4.21.1
    transitivePeerDependencies:
      - react
      - react-dom
    dev: false

  /rmc-nuka-carousel/3.0.1:
    resolution: {integrity: sha512-w2EPTERMUUZqcUSKFuejjin7xsMlhrLrtS0A/igTXpFJGq3kemDKcRi7q3pSYDuZBHYBl5iV4UqsLLkjdFtrYA==}
    dependencies:
      exenv: 1.2.2
      raf: 3.4.1
    dev: false

  /rmc-picker/5.0.10_wcqkhtmu7mswc6yz4uyexck3ty:
    resolution: {integrity: sha512-KZ70+WjcaZHnG5GyCxWCPFWAZ12s6NqyrbW73LeqH0WEqaTMMs0sOrk2f4mQAZ/CGT0XcFN6VZLw7Ozoxfn7LA==}
    dependencies:
      babel-runtime: 6.26.0
      classnames: 2.3.2
      rmc-dialog: 1.1.1_wcqkhtmu7mswc6yz4uyexck3ty
      rmc-feedback: 2.0.0
    transitivePeerDependencies:
      - react
      - react-dom
    dev: false

  /rmc-pull-to-refresh/1.0.13:
    resolution: {integrity: sha512-iYLsURiR7G/sKmRA6p2kq6ZXicn7Hyeo6VQFljssV1eMW+fzDgihhaz0kv5mza0f88vphGJvjOihT9E6+xGb6Q==}
    dependencies:
      babel-runtime: 6.26.0
      classnames: 2.3.2
    dev: false

  /rmc-steps/1.0.1:
    resolution: {integrity: sha512-8ijtwp4D1CYTtI2yerXJYqCv+GQbiBc9T12nrFngd/vM0y+58CnznGphTAueF6IWf7qbxBwcjTrcFgg7bP2YGA==}
    dependencies:
      babel-runtime: 6.26.0
      classnames: 2.3.2
    dev: false

  /rmc-tabs/1.2.29:
    resolution: {integrity: sha512-wiJS9WSJi9JH9GQO+FqncX+zaHP31qHa/S8nDW9UXUx0qbCX294QcJEnvfB+WmsfUws7rXjs6sOQp5EDiObnHg==}
    dependencies:
      babel-runtime: 6.26.0
      rc-gesture: 0.0.22
    dev: false

  /rmc-tooltip/1.0.1_wcqkhtmu7mswc6yz4uyexck3ty:
    resolution: {integrity: sha512-fSDArf2BlMVrHExmBiqb2TkCRJHshvXFJQ/7tMraLellwaJLNiwrxtWpW329k3S+zTtoVG8UxFS1TjBGEsMzRg==}
    dependencies:
      babel-runtime: 6.26.0
      rmc-trigger: 1.0.12_wcqkhtmu7mswc6yz4uyexck3ty
    transitivePeerDependencies:
      - react
      - react-dom
    dev: false

  /rmc-trigger/1.0.12_wcqkhtmu7mswc6yz4uyexck3ty:
    resolution: {integrity: sha512-AccQniX7PX7Pm8hBhHEsnf3JU6CA61Xc7fAt2WbO+oXrGaI/jqN8C3COhhOXG54S5iTOjLS26j858zshwAxR9A==}
    dependencies:
      babel-runtime: 6.26.0
      rc-animate: 2.11.1_wcqkhtmu7mswc6yz4uyexck3ty
      rc-util: 4.21.1
      rmc-align: 1.0.0
    transitivePeerDependencies:
      - react
      - react-dom
    dev: false

  /rsvp/4.8.5:
    resolution: {integrity: sha512-nfMOlASu9OnRJo1mbEk2cz0D56a1MBNrJ7orjRZQG10XDyuvwksKbuXNp6qa+kbn839HwjwhBzhFmdsaEAfauA==}
    engines: {node: 6.* || >= 7.*}
    dev: false

  /run-async/2.4.1:
    resolution: {integrity: sha512-tvVnVv01b8c1RrA6Ep7JkStj85Guv/YrMcwqYQnwjsAS2cTmmPGBBjAjpCW7RrSodNSoE2/qg9O4bceNvUuDgQ==}
    engines: {node: '>=0.12.0'}
    dev: false

  /run-parallel/1.2.0:
    resolution: {integrity: sha512-5l4VyZR86LZ/lDxZTR6jqL8AFE2S0IFLMP26AbjsLVADxHdhB/c0GUsH+y39UfCi3dzz8OlQuPmnaJOMoDHQBA==}
    dependencies:
      queue-microtask: 1.2.3
    dev: true

  /run-queue/1.0.3:
    resolution: {integrity: sha1-6Eg5bwV9Ij8kOGkkYY4laUFh7Ec=}
    dependencies:
      aproba: 1.2.0
    dev: false

  /rxjs/6.6.7:
    resolution: {integrity: sha512-hTdwr+7yYNIT5n4AMYp85KA6yw2Va0FLa3Rguvbpa4W3I5xynaBZo41cM3XM+4Q6fRMj3sBYIR1VAmZMXYJvRQ==}
    engines: {npm: '>=2.0.0'}
    dependencies:
      tslib: 1.14.1
    dev: false

  /safe-buffer/5.1.2:
    resolution: {integrity: sha512-Gd2UZBJDkXlY7GbJxfsE8/nvKkUEU1G38c1siN6QP6a9PT9MmHB8GnpscSmMJSoF8LOIrt8ud/wPtojys4G6+g==}

  /safe-buffer/5.2.1:
    resolution: {integrity: sha512-rp3So07KcdmmKbGvgaNxQSJr7bGVSVk5S9Eq1F+ppbRo70+YeaDxkw5Dd8NPN+GD6bjnYm2VuPuCXmpuYvmCXQ==}

  /safe-regex-test/1.0.0:
    resolution: {integrity: sha512-JBUUzyOgEwXQY1NuPtvcj/qcBDbDmEvWufhlnXZIm75DEHp+afM1r1ujJpJsV/gSM4t59tpDyPi1sd6ZaPFfsA==}
    dependencies:
      call-bind: 1.0.2
      get-intrinsic: 1.1.3
      is-regex: 1.1.4
    dev: false

  /safe-regex/1.1.0:
    resolution: {integrity: sha1-QKNmnzsHfR6UPURinhV91IAjvy4=}
    dependencies:
      ret: 0.1.15
    dev: false

  /safer-buffer/2.1.2:
    resolution: {integrity: sha512-YZo3K82SD7Riyi0E1EQPojLz7kpepnSQI9IyPbHHg1XXXevb5dJI7tpyN2ADxGcQbHG7vcyRHk0cbwqcQriUtg==}

  /sane/4.1.0:
    resolution: {integrity: sha512-hhbzAgTIX8O7SHfp2c8/kREfEn4qO/9q8C9beyY6+tvZ87EpoZ3i1RIEvp27YBswnNbY9mWd6paKVmKbAgLfZA==}
    engines: {node: 6.* || 8.* || >= 10.*}
    hasBin: true
    dependencies:
      '@cnakazawa/watch': 1.0.4
      anymatch: 2.0.0
      capture-exit: 2.0.0
      exec-sh: 0.3.6
      execa: 1.0.0
      fb-watchman: 2.0.2
      micromatch: 3.1.10
      minimist: 1.2.7
      walker: 1.0.8
    transitivePeerDependencies:
      - supports-color
    dev: false

  /sanitize.css/10.0.0:
    resolution: {integrity: sha512-vTxrZz4dX5W86M6oVWVdOVe72ZiPs41Oi7Z6Km4W5Turyz28mrXSJhhEBZoRtzJWIv3833WKVwLSDWWkEfupMg==}
    dev: false

  /sass-graph/2.2.5:
    resolution: {integrity: sha512-VFWDAHOe6mRuT4mZRd4eKE+d8Uedrk6Xnh7Sh9b4NGufQLQjOrvf/MQoOdx+0s92L89FeyUUNfU597j/3uNpag==}
    hasBin: true
    dependencies:
      glob: 7.2.3
      lodash: 4.17.21
      scss-tokenizer: 0.2.3
      yargs: 13.3.2

  /sass-loader/12.6.0_node-sass@4.14.1:
    resolution: {integrity: sha512-oLTaH0YCtX4cfnJZxKSLAyglED0naiYfNG1iXfU5w1LNZ+ukoA5DtyDIN5zmKVZwYNJP4KRc5Y3hkWga+7tYfA==}
    engines: {node: '>= 12.13.0'}
    peerDependencies:
      fibers: '>= 3.1.0'
      node-sass: ^4.0.0 || ^5.0.0 || ^6.0.0 || ^7.0.0
      sass: ^1.3.0
      sass-embedded: '*'
      webpack: ^5.0.0
    peerDependenciesMeta:
      fibers:
        optional: true
      node-sass:
        optional: true
      sass:
        optional: true
      sass-embedded:
        optional: true
    dependencies:
      klona: 2.0.5
      neo-async: 2.6.2
      node-sass: 4.14.1
    dev: true

  /sass-loader/8.0.2_log2h4edco4ibha2agbtxc4qxy:
    resolution: {integrity: sha512-7o4dbSK8/Ol2KflEmSco4jTjQoV988bM82P9CZdmo9hR3RLnvNc0ufMNdMrB0caq38JQ/FgF4/7RcbcfKzxoFQ==}
    engines: {node: '>= 8.9.0'}
    peerDependencies:
      fibers: '>= 3.1.0'
      node-sass: ^4.0.0
      sass: ^1.3.0
      webpack: ^4.36.0 || ^5.0.0
    peerDependenciesMeta:
      fibers:
        optional: true
      node-sass:
        optional: true
      sass:
        optional: true
    dependencies:
      clone-deep: 4.0.1
      loader-utils: 1.4.0
      neo-async: 2.6.2
      node-sass: 4.14.1
      schema-utils: 2.7.1
      semver: 6.3.0
      webpack: 4.41.5
    dev: false

  /sass-rem/2.0.1:
    resolution: {integrity: sha512-Fuf7i1Wr7n1lIdCiImhCJ1/S0jEYq1OmrlqyKuJKSU2B2kHbU09HJIo5AuADvbf3DJLlRxU7b8Jm8D6m5lfqmA==}
    dev: true

  /sax/1.2.4:
    resolution: {integrity: sha512-NqVDv9TpANUjFm0N8uM5GxL36UgKi9/atZw+x7YFnQ8ckwFGKrl4xX4yWtrey3UJm5nP1kUbnYgLopqWNSRhWw==}
    dev: false

  /saxes/3.1.11:
    resolution: {integrity: sha512-Ydydq3zC+WYDJK1+gRxRapLIED9PWeSuuS41wqyoRmzvhhh9nc+QQrVMKJYzJFULazeGhzSV0QleN2wD3boh2g==}
    engines: {node: '>=8'}
    dependencies:
      xmlchars: 2.2.0
    dev: false

  /scheduler/0.19.1:
    resolution: {integrity: sha512-n/zwRWRYSUj0/3g/otKDRPMh6qv2SYMWNq85IEa8iZyAv8od9zDYpGSnpBEjNgcMNq6Scbu5KfIPxNF72R/2EA==}
    dependencies:
      loose-envify: 1.4.0
      object-assign: 4.1.1

  /schema-utils/1.0.0:
    resolution: {integrity: sha512-i27Mic4KovM/lnGsy8whRCHhc7VicJajAjTrYg11K9zfZXnYIt4k5F+kZkwjnrhKzLic/HLU4j11mjsz2G/75g==}
    engines: {node: '>= 4'}
    dependencies:
      ajv: 6.12.6
      ajv-errors: 1.0.1_ajv@6.12.6
      ajv-keywords: 3.5.2_ajv@6.12.6
    dev: false

  /schema-utils/2.7.1:
    resolution: {integrity: sha512-SHiNtMOUGWBQJwzISiVYKu82GiV4QYGePp3odlY1tuKO7gPtphAT5R/py0fA6xtbgLL/RvtJZnU9b8s0F1q0Xg==}
    engines: {node: '>= 8.9.0'}
    dependencies:
      '@types/json-schema': 7.0.11
      ajv: 6.12.6
      ajv-keywords: 3.5.2_ajv@6.12.6
    dev: false

  /scss-tokenizer/0.2.3:
    resolution: {integrity: sha1-jrBtualyMzOCTT9VMGQRSYR85dE=}
    dependencies:
      js-base64: 2.6.4
      source-map: 0.4.4

  /select-hose/2.0.0:
    resolution: {integrity: sha1-Yl2GWPhlr0Psliv8N2o3NZpJlMo=}
    dev: false

  /select/1.1.2:
    resolution: {integrity: sha1-DnNQrN7ICxEIUoeG7B1EGNEbOW0=}
    dev: false

  /selfsigned/1.10.14:
    resolution: {integrity: sha512-lkjaiAye+wBZDCBsu5BGi0XiLRxeUlsGod5ZP924CRSEoGuZAw/f7y9RKu28rwTfiHVhdavhB0qH0INV6P1lEA==}
    dependencies:
      node-forge: 0.10.0
    dev: false

  /semver/5.3.0:
    resolution: {integrity: sha1-myzl094C0XxgEq0yaqa00M9U+U8=}
    hasBin: true

  /semver/5.7.1:
    resolution: {integrity: sha512-sauaDf/PZdVgrLTNYHRtpXa1iRiKcaebiKQ1BJdpQlWH2lCvexQdX55snPFyK7QzpudqbCI0qXFfOasHdyNDGQ==}
    hasBin: true

  /semver/6.3.0:
    resolution: {integrity: sha512-b39TBaTSfV6yBrapU89p5fKekE2m/NwnDocOVruQFS1/veMgdzuPcnOM34M6CwxW8jH/lxEa5rBoDeUwu5HHTw==}
    hasBin: true

  /semver/7.3.8:
    resolution: {integrity: sha512-NB1ctGL5rlHrPJtFDVIVzTyQylMLu9N9VICA6HSFJo8MCGVTMW6gfpicwKmmK/dAjTOrqu5l63JJOpDSrAis3A==}
    engines: {node: '>=10'}
    hasBin: true
    dependencies:
      lru-cache: 6.0.0

  /send/0.18.0_supports-color@6.1.0:
    resolution: {integrity: sha512-qqWzuOjSFOuqPjFe4NOsMLafToQQwBSOEpS+FwEt3A2V3vKubTquT3vmLTQpFgMXp8AlFWFuP1qKaJZOtPpVXg==}
    engines: {node: '>= 0.8.0'}
    dependencies:
      debug: 2.6.9_supports-color@6.1.0
      depd: 2.0.0
      destroy: 1.2.0
      encodeurl: 1.0.2
      escape-html: 1.0.3
      etag: 1.8.1
      fresh: 0.5.2
      http-errors: 2.0.0
      mime: 1.6.0
      ms: 2.1.3
      on-finished: 2.4.1
      range-parser: 1.2.1
      statuses: 2.0.1
    transitivePeerDependencies:
      - supports-color
    dev: false

  /serialize-javascript/2.1.2:
    resolution: {integrity: sha512-rs9OggEUF0V4jUSecXazOYsLfu7OGK2qIn3c7IPBiffz32XniEp/TX9Xmc9LQfK2nQ2QKHvZ2oygKUGU0lG4jQ==}
    dev: false

  /serialize-javascript/4.0.0:
    resolution: {integrity: sha512-GaNA54380uFefWghODBWEGisLZFj00nS5ACs6yHa9nLqlLpVLO8ChDGeKRjZnV4Nh4n0Qi7nhYZD/9fCPzEqkw==}
    dependencies:
      randombytes: 2.1.0
    dev: false

  /serve-index/1.9.1_supports-color@6.1.0:
    resolution: {integrity: sha1-03aNabHn2C5c4FD/9bRTvqEqkjk=}
    engines: {node: '>= 0.8.0'}
    dependencies:
      accepts: 1.3.8
      batch: 0.6.1
      debug: 2.6.9_supports-color@6.1.0
      escape-html: 1.0.3
      http-errors: 1.6.3
      mime-types: 2.1.35
      parseurl: 1.3.3
    transitivePeerDependencies:
      - supports-color
    dev: false

  /serve-static/1.15.0_supports-color@6.1.0:
    resolution: {integrity: sha512-XGuRDNjXUijsUL0vl6nSD7cwURuzEgglbOaFuZM9g3kwDXOWVTck0jLzjPzGD+TazWbboZYu52/9/XPdUgne9g==}
    engines: {node: '>= 0.8.0'}
    dependencies:
      encodeurl: 1.0.2
      escape-html: 1.0.3
      parseurl: 1.3.3
      send: 0.18.0_supports-color@6.1.0
    transitivePeerDependencies:
      - supports-color
    dev: false

  /set-blocking/2.0.0:
    resolution: {integrity: sha1-BF+XgtARrppoA93TgrJDkrPYkPc=}

  /set-value/2.0.1:
    resolution: {integrity: sha512-JxHc1weCN68wRY0fhCoXpyK55m/XPHafOmK4UWD7m2CI14GMcFypt4w/0+NV5f/ZMby2F6S2wwA7fgynh9gWSw==}
    engines: {node: '>=0.10.0'}
    dependencies:
      extend-shallow: 2.0.1
      is-extendable: 0.1.1
      is-plain-object: 2.0.4
      split-string: 3.1.0
    dev: false

  /setimmediate/1.0.5:
    resolution: {integrity: sha1-KQy7Iy4waULX1+qbg3Mqt4VvgoU=}
    dev: false

  /setprototypeof/1.1.0:
    resolution: {integrity: sha512-BvE/TwpZX4FXExxOxZyRGQQv651MSwmWKZGqvmPcRIjDqWub67kTKuIMx43cZZrS/cBBzwBcNDWoFxt2XEFIpQ==}
    dev: false

  /setprototypeof/1.2.0:
    resolution: {integrity: sha512-E5LDX7Wrp85Kil5bhZv46j8jOeboKq5JMmYM3gVGdGH8xFpPWXUMsNrlODCrkoxMEeNi/XZIwuRvY4XNwYMJpw==}
    dev: false

  /sha.js/2.4.11:
    resolution: {integrity: sha512-QMEp5B7cftE7APOjk5Y6xgrbWu+WkLVQwk8JNjZ8nKRciZaByEW6MubieAiToS7+dwvrjGhH8jRXz3MVd0AYqQ==}
    hasBin: true
    dependencies:
      inherits: 2.0.4
      safe-buffer: 5.2.1
    dev: false

  /shallow-clone/0.1.2:
    resolution: {integrity: sha1-WQnodLp3EG1zrEFM/sH/yofZcGA=}
    engines: {node: '>=0.10.0'}
    dependencies:
      is-extendable: 0.1.1
      kind-of: 2.0.1
      lazy-cache: 0.2.7
      mixin-object: 2.0.1
    dev: false

  /shallow-clone/3.0.1:
    resolution: {integrity: sha512-/6KqX+GVUdqPuPPd2LxDDxzX6CAbjJehAAOKlNpqqUpAqPM6HeL8f+o3a+JsyGjn2lv0WY8UsTgUJjU9Ok55NA==}
    engines: {node: '>=8'}
    dependencies:
      kind-of: 6.0.3
    dev: false

  /shallowequal/1.1.0:
    resolution: {integrity: sha512-y0m1JoUZSlPAjXVtPPW70aZWfIL/dSP7AFkRnniLCrK/8MDKog3TySTBmckD+RObVxH0v4Tox67+F14PdED2oQ==}
    dev: false

  /shebang-command/1.2.0:
    resolution: {integrity: sha1-RKrGW2lbAzmJaMOfNj/uXer98eo=}
    engines: {node: '>=0.10.0'}
    dependencies:
      shebang-regex: 1.0.0
    dev: false

  /shebang-command/2.0.0:
    resolution: {integrity: sha512-kHxr2zZpYtdmrN1qDjrrX/Z1rR1kG8Dx+gkpK1G4eXmvXswmcE1hTWBWYUzlraYw1/yZp6YuDY77YtvbN0dmDA==}
    engines: {node: '>=8'}
    dependencies:
      shebang-regex: 3.0.0
    dev: false

  /shebang-regex/1.0.0:
    resolution: {integrity: sha1-2kL0l0DAtC2yypcoVxyxkMmO/qM=}
    engines: {node: '>=0.10.0'}
    dev: false

  /shebang-regex/3.0.0:
    resolution: {integrity: sha512-7++dFhtcx3353uBaq8DDR4NuxBetBzC7ZQOhmTQInHEd6bSrXdiEyzCvG07Z44UYdLShWUyXt5M/yhz8ekcb1A==}
    engines: {node: '>=8'}
    dev: false

  /shell-quote/1.7.2:
    resolution: {integrity: sha512-mRz/m/JVscCrkMyPqHc/bczi3OQHkLTqXHEFu0zDhK/qfv3UcOA4SVmRCLmos4bhjr9ekVQubj/R7waKapmiQg==}
    dev: false

  /shellwords/0.1.1:
    resolution: {integrity: sha512-vFwSUfQvqybiICwZY5+DAWIPLKsWO31Q91JSKl3UYv+K5c2QRPzn0qzec6QPu1Qc9eHYItiP3NdJqNVqetYAww==}
    dev: false

  /side-channel/1.0.4:
    resolution: {integrity: sha512-q5XPytqFEIKHkGdiMIrY10mvLRvnQh42/+GoBlFW3b2LXLE2xxJpZFdm94we0BaoV3RwJyGqg5wS7epxTv0Zvw==}
    dependencies:
      call-bind: 1.0.2
      get-intrinsic: 1.1.3
      object-inspect: 1.12.2
    dev: false

  /signal-exit/3.0.7:
    resolution: {integrity: sha512-wnD2ZE+l+SPC/uoS0vXeE9L1+0wuaMqKlfz9AMUo38JsyLSBWSFcHR1Rri62LZc12vLr1gb3jl7iwQhgwpAbGQ==}

  /simple-swizzle/0.2.2:
    resolution: {integrity: sha1-pNprY1/8zMoz9w0Xy5JZLeleVXo=}
    dependencies:
      is-arrayish: 0.3.2
    dev: false

  /sisteransi/1.0.5:
    resolution: {integrity: sha512-bLGGlR1QxBcynn2d5YmDX4MGjlZvy2MRBDRNHLJ8VI6l6+9FUiyTFNJ0IveOSP0bcXgVDPRcfGqA0pjaqUpfVg==}
    dev: false

  /slash/1.0.0:
    resolution: {integrity: sha1-xB8vbDn8FtHNF61LXYlhFK5HDVU=}
    engines: {node: '>=0.10.0'}
    dev: false

  /slash/2.0.0:
    resolution: {integrity: sha512-ZYKh3Wh2z1PpEXWr0MpSBZ0V6mZHAQfYevttO11c51CaWjGTaadiKZ+wVt1PbMlDV5qhMFslpZCemhwOK7C89A==}
    engines: {node: '>=6'}
    dev: false

  /slash/3.0.0:
    resolution: {integrity: sha512-g9Q1haeby36OSStwb4ntCGGGaKsaVSjQ68fBxoQcutl5fS1vuY18H3wSt3jFyFtrkx+Kz0V1G85A4MyAdDMi2Q==}
    engines: {node: '>=8'}

  /slice-ansi/2.1.0:
    resolution: {integrity: sha512-Qu+VC3EwYLldKa1fCxuuvULvSJOKEgk9pi8dZeCVK7TqBfUNTH4sFkk4joj8afVSfAYgJoSOetjx9QWOJ5mYoQ==}
    engines: {node: '>=6'}
    dependencies:
      ansi-styles: 3.2.1
      astral-regex: 1.0.0
      is-fullwidth-code-point: 2.0.0
    dev: false

  /slice-ansi/4.0.0:
    resolution: {integrity: sha512-qMCMfhY040cVHT43K9BFygqYbUPFZKHOg7K73mtTWJRb8pyP3fzf4Ixd5SzdEJQ6MRUg/WBnOLxghZtKKurENQ==}
    engines: {node: '>=10'}
    dependencies:
      ansi-styles: 4.3.0
      astral-regex: 2.0.0
      is-fullwidth-code-point: 3.0.0
    dev: true

  /slick-carousel/1.8.1:
    resolution: {integrity: sha512-XB9Ftrf2EEKfzoQXt3Nitrt/IPbT+f1fgqBdoxO3W/+JYvtEOW6EgxnWfr9GH6nmULv7Y2tPmEX3koxThVmebA==}
    peerDependencies:
      jquery: '>=1.8.0'
    dev: false

  /snapdragon-node/2.1.1:
    resolution: {integrity: sha512-O27l4xaMYt/RSQ5TR3vpWCAB5Kb/czIcqUFOM/C4fYcLnbZUc1PkjTAMjof2pBWaSTwOUd6qUHcFGVGj7aIwnw==}
    engines: {node: '>=0.10.0'}
    dependencies:
      define-property: 1.0.0
      isobject: 3.0.1
      snapdragon-util: 3.0.1
    dev: false

  /snapdragon-util/3.0.1:
    resolution: {integrity: sha512-mbKkMdQKsjX4BAL4bRYTj21edOf8cN7XHdYUJEe+Zn99hVEYcMvKPct1IqNe7+AZPirn8BCDOQBHQZknqmKlZQ==}
    engines: {node: '>=0.10.0'}
    dependencies:
      kind-of: 3.2.2
    dev: false

  /snapdragon/0.8.2:
    resolution: {integrity: sha512-FtyOnWN/wCHTVXOMwvSv26d+ko5vWlIDD6zoUJ7LW8vh+ZBC8QdljveRP+crNrtBwioEUWy/4dMtbBjA4ioNlg==}
    engines: {node: '>=0.10.0'}
    dependencies:
      base: 0.11.2
      debug: 2.6.9
      define-property: 0.2.5
      extend-shallow: 2.0.1
      map-cache: 0.2.2
      source-map: 0.5.7
      source-map-resolve: 0.5.3
      use: 3.1.1
    transitivePeerDependencies:
      - supports-color
    dev: false

  /snapdragon/0.8.2_supports-color@6.1.0:
    resolution: {integrity: sha512-FtyOnWN/wCHTVXOMwvSv26d+ko5vWlIDD6zoUJ7LW8vh+ZBC8QdljveRP+crNrtBwioEUWy/4dMtbBjA4ioNlg==}
    engines: {node: '>=0.10.0'}
    dependencies:
      base: 0.11.2
      debug: 2.6.9_supports-color@6.1.0
      define-property: 0.2.5
      extend-shallow: 2.0.1
      map-cache: 0.2.2
      source-map: 0.5.7
      source-map-resolve: 0.5.3
      use: 3.1.1
    transitivePeerDependencies:
      - supports-color
    dev: false

  /sockjs-client/1.4.0_supports-color@6.1.0:
    resolution: {integrity: sha512-5zaLyO8/nri5cua0VtOrFXBPK1jbL4+1cebT/mmKA1E1ZXOvJrII75bPu0l0k843G/+iAbhEqzyKr0w/eCCj7g==}
    dependencies:
      debug: 3.2.7_supports-color@6.1.0
      eventsource: 1.1.2
      faye-websocket: 0.11.4
      inherits: 2.0.4
      json3: 3.3.3
      url-parse: 1.5.10
    transitivePeerDependencies:
      - supports-color
    dev: false

  /sockjs/0.3.19:
    resolution: {integrity: sha512-V48klKZl8T6MzatbLlzzRNhMepEys9Y4oGFpypBFFn1gLI/QQ9HtLLyWJNbPlwGLelOVOEijUbTTJeLLI59jLw==}
    dependencies:
      faye-websocket: 0.10.0
      uuid: 3.4.0
    dev: false

  /sort-keys/1.1.2:
    resolution: {integrity: sha1-RBttTTRnmPG05J6JIK37oOVD+a0=}
    engines: {node: '>=0.10.0'}
    dependencies:
      is-plain-obj: 1.1.0
    dev: false

  /source-list-map/2.0.1:
    resolution: {integrity: sha512-qnQ7gVMxGNxsiL4lEuJwe/To8UnK7fAnmbGEEH8RpLouuKbeEm0lhbQVFIrNSuB+G7tVrAlVsZgETT5nljf+Iw==}
    dev: false

  /source-map-resolve/0.5.3:
    resolution: {integrity: sha512-Htz+RnsXWk5+P2slx5Jh3Q66vhQj1Cllm0zvnaY98+NFx+Dv2CF/f5O/t8x+KaNdrdIAsruNzoh/KpialbqAnw==}
    dependencies:
      atob: 2.1.2
      decode-uri-component: 0.2.0
      resolve-url: 0.2.1
      source-map-url: 0.4.1
      urix: 0.1.0

  /source-map-support/0.5.21:
    resolution: {integrity: sha1-BP58f54e0tZiIzwoyys1ufY/bk8=}
    dependencies:
      buffer-from: 1.1.2
      source-map: 0.6.1
    dev: false

  /source-map-url/0.4.1:
    resolution: {integrity: sha512-cPiFOTLUKvJFIg4SKVScy4ilPPW6rFgMgfuZJPNoDuMs3nC1HbMUycBoJw77xFIp6z1UJQJOfx6C9GMH80DiTw==}

  /source-map/0.4.4:
    resolution: {integrity: sha1-66T12pwNyZneaAMti092FzZSA2s=}
    engines: {node: '>=0.8.0'}
    dependencies:
      amdefine: 1.0.1

  /source-map/0.5.7:
    resolution: {integrity: sha1-igOdLRAh0i0eoUyA2OpGi6LvP8w=}
    engines: {node: '>=0.10.0'}
    dev: false

  /source-map/0.6.1:
    resolution: {integrity: sha512-UjgapumWlbMhkBgzT7Ykc5YXUT46F0iKu8SGXq0bcwP5dz/h0Plj6enJqjz1Zbq2l5WaqYnrVbwWOWMyF3F47g==}
    engines: {node: '>=0.10.0'}

  /spdx-correct/3.1.1:
    resolution: {integrity: sha512-cOYcUWwhCuHCXi49RhFRCyJEK3iPj1Ziz9DpViV3tbZOwXD49QzIN3MpOLJNxh2qwq2lJJZaKMVw9qNi4jTC0w==}
    dependencies:
      spdx-expression-parse: 3.0.1
      spdx-license-ids: 3.0.12

  /spdx-exceptions/2.3.0:
    resolution: {integrity: sha512-/tTrYOC7PPI1nUAgx34hUpqXuyJG+DTHJTnIULG4rDygi4xu/tfgmq1e1cIRwRzwZgo4NLySi+ricLkZkw4i5A==}

  /spdx-expression-parse/3.0.1:
    resolution: {integrity: sha512-cbqHunsQWnJNE6KhVSMsMeH5H/L9EpymbzqTQ3uLwNCLZ1Q481oWaofqH7nO6V07xlXwY6PhQdQ2IedWx/ZK4Q==}
    dependencies:
      spdx-exceptions: 2.3.0
      spdx-license-ids: 3.0.12

  /spdx-license-ids/3.0.12:
    resolution: {integrity: sha512-rr+VVSXtRhO4OHbXUiAF7xW3Bo9DuuF6C5jH+q/x15j2jniycgKbxU09Hr0WqlSLUs4i4ltHGXqTe7VHclYWyA==}

  /spdy-transport/3.0.0_supports-color@6.1.0:
    resolution: {integrity: sha512-hsLVFE5SjA6TCisWeJXFKniGGOpBgMLmerfO2aCyCU5s7nJ/rpAepqmFifv/GCbSbueEeAJJnmSQ2rKC/g8Fcw==}
    dependencies:
      debug: 4.3.4_supports-color@6.1.0
      detect-node: 2.1.0
      hpack.js: 2.1.6
      obuf: 1.1.2
      readable-stream: 3.6.0
      wbuf: 1.7.3
    transitivePeerDependencies:
      - supports-color
    dev: false

  /spdy/4.0.2_supports-color@6.1.0:
    resolution: {integrity: sha512-r46gZQZQV+Kl9oItvl1JZZqJKGr+oEkB08A6BzkiR7593/7IbtuncXHd2YoYeTsG4157ZssMu9KYvUHLcjcDoA==}
    engines: {node: '>=6.0.0'}
    dependencies:
      debug: 4.3.4_supports-color@6.1.0
      handle-thing: 2.0.1
      http-deceiver: 1.2.7
      select-hose: 2.0.0
      spdy-transport: 3.0.0_supports-color@6.1.0
    transitivePeerDependencies:
      - supports-color
    dev: false

  /specificity/0.4.1:
    resolution: {integrity: sha512-1klA3Gi5PD1Wv9Q0wUoOQN1IWAuPu0D1U03ThXTr0cJ20+/iq2tHSDnK7Kk/0LXJ1ztUB2/1Os0wKmfyNgUQfg==}
    hasBin: true
    dev: true

  /split-string/3.1.0:
    resolution: {integrity: sha512-NzNVhJDYpwceVVii8/Hu6DKfD2G+NrQHlS/V/qgv763EYudVwEcMQNxd2lh+0VrUByXN/oJkl5grOhYWvQUYiw==}
    engines: {node: '>=0.10.0'}
    dependencies:
      extend-shallow: 3.0.2
    dev: false

  /sprintf-js/1.0.3:
    resolution: {integrity: sha1-BOaSb2YolTVPPdAVIDYzuFcpfiw=}
    dev: false

  /sshpk/1.17.0:
    resolution: {integrity: sha512-/9HIEs1ZXGhSPE8X6Ccm7Nam1z8KcoCqPdI7ecm1N33EzAetWahvQWVqLZtaZQ+IDKX4IyA2o0gBzqIMkAagHQ==}
    engines: {node: '>=0.10.0'}
    hasBin: true
    dependencies:
      asn1: 0.2.6
      assert-plus: 1.0.0
      bcrypt-pbkdf: 1.0.2
      dashdash: 1.14.1
      ecc-jsbn: 0.1.2
      getpass: 0.1.7
      jsbn: 0.1.1
      safer-buffer: 2.1.2
      tweetnacl: 0.14.5

  /ssri/6.0.2:
    resolution: {integrity: sha512-cepbSq/neFK7xB6A50KHN0xHDotYzq58wWCa5LeWqnPrHG8GzfEjO/4O8kpmcGW+oaxkvhEJCWgbgNk4/ZV93Q==}
    dependencies:
      figgy-pudding: 3.5.2
    dev: false

  /ssri/7.1.1:
    resolution: {integrity: sha512-w+daCzXN89PseTL99MkA+fxJEcU3wfaE/ah0i0lnOlpG1CYLJ2ZjzEry68YBKfLs4JfoTShrTEsJkAZuNZ/stw==}
    engines: {node: '>= 8'}
    dependencies:
      figgy-pudding: 3.5.2
      minipass: 3.3.4
    dev: false

  /stable/0.1.8:
    resolution: {integrity: sha512-ji9qxRnOVfcuLDySj9qzhGSEFVobyt1kIOSkj1qZzYLzq7Tos/oUUWvotUPQLlrsidqsK6tBH89Bc9kL5zHA6w==}
    dev: false

  /stack-utils/1.0.5:
    resolution: {integrity: sha512-KZiTzuV3CnSnSvgMRrARVCj+Ht7rMbauGDK0LdVFRGyenwdylpajAp4Q0i6SX8rEmbTpMMf6ryq2gb8pPq2WgQ==}
    engines: {node: '>=8'}
    dependencies:
      escape-string-regexp: 2.0.0
    dev: false

  /static-extend/0.1.2:
    resolution: {integrity: sha1-YICcOcv/VTNyJv1eC1IPNB8ftcY=}
    engines: {node: '>=0.10.0'}
    dependencies:
      define-property: 0.2.5
      object-copy: 0.1.0
    dev: false

  /statuses/1.5.0:
    resolution: {integrity: sha1-Fhx9rBd2Wf2YEfQ3cfqZOBR4Yow=}
    engines: {node: '>= 0.6'}
    dev: false

  /statuses/2.0.1:
    resolution: {integrity: sha512-RwNA9Z/7PrK06rYLIzFMlaF+l73iwpzsqRIFgbMLbTcLD6cOao82TaWefPXQvB2fOC4AjuYSEndS7N/mTCbkdQ==}
    engines: {node: '>= 0.8'}
    dev: false

  /stdout-stream/1.4.1:
    resolution: {integrity: sha512-j4emi03KXqJWcIeF8eIXkjMFN1Cmb8gUlDYGeBALLPo5qdyTfA9bOtl8m33lRoC+vFMkP3gl0WsDr6+gzxbbTA==}
    dependencies:
      readable-stream: 2.3.7

  /stealthy-require/1.1.1:
    resolution: {integrity: sha1-NbCYdbT/SfJqd35QmzCQoyJr8ks=}
    engines: {node: '>=0.10.0'}
    dev: false

  /stream-browserify/2.0.2:
    resolution: {integrity: sha512-nX6hmklHs/gr2FuxYDltq8fJA1GDlxKQCz8O/IM4atRqBH8OORmBNgfvW5gG10GT/qQ9u0CzIvr2X5Pkt6ntqg==}
    dependencies:
      inherits: 2.0.4
      readable-stream: 2.3.7
    dev: false

  /stream-each/1.2.3:
    resolution: {integrity: sha512-vlMC2f8I2u/bZGqkdfLQW/13Zihpej/7PmSiMQsbYddxuTsJp8vRe2x2FvVExZg7FaOds43ROAuFJwPR4MTZLw==}
    dependencies:
      end-of-stream: 1.4.4
      stream-shift: 1.0.1
    dev: false

  /stream-http/2.8.3:
    resolution: {integrity: sha512-+TSkfINHDo4J+ZobQLWiMouQYB+UVYFttRA94FpEzzJ7ZdqcL4uUUQ7WkdkI4DSozGmgBUE/a47L+38PenXhUw==}
    dependencies:
      builtin-status-codes: 3.0.0
      inherits: 2.0.4
      readable-stream: 2.3.7
      to-arraybuffer: 1.0.1
      xtend: 4.0.2
    dev: false

  /stream-shift/1.0.1:
    resolution: {integrity: sha512-AiisoFqQ0vbGcZgQPY1cdP2I76glaVA/RauYR4G4thNFgkTqr90yXTo4LYX60Jl+sIlPNHHdGSwo01AvbKUSVQ==}
    dev: false

  /strict-uri-encode/1.1.0:
    resolution: {integrity: sha1-J5siXfHVgrH1TmWt3UNS4Y+qBxM=}
    engines: {node: '>=0.10.0'}
    dev: false

  /string-convert/0.2.1:
    resolution: {integrity: sha1-aYLMMEn7tM2F+LJFaLnZvznu/5c=}
    dev: false

  /string-length/2.0.0:
    resolution: {integrity: sha1-1A27aGo6zpYMHP/KVivyxF+DY+0=}
    engines: {node: '>=4'}
    dependencies:
      astral-regex: 1.0.0
      strip-ansi: 4.0.0
    dev: false

  /string-length/3.1.0:
    resolution: {integrity: sha512-Ttp5YvkGm5v9Ijagtaz1BnN+k9ObpvS0eIBblPMp2YWL8FBmi9qblQ9fexc2k/CXFgrTIteU3jAw3payCnwSTA==}
    engines: {node: '>=8'}
    dependencies:
      astral-regex: 1.0.0
      strip-ansi: 5.2.0
    dev: false

  /string-width/1.0.2:
    resolution: {integrity: sha1-EYvfW4zcUaKn5w0hHgfisLmxB9M=}
    engines: {node: '>=0.10.0'}
    dependencies:
      code-point-at: 1.1.0
      is-fullwidth-code-point: 1.0.0
      strip-ansi: 3.0.1

  /string-width/2.1.1:
    resolution: {integrity: sha512-nOqH59deCq9SRHlxq1Aw85Jnt4w6KvLKqWVik6oA9ZklXLNIOlqg4F2yrT1MVaTjAqvVwdfeZ7w7aCvJD7ugkw==}
    engines: {node: '>=4'}
    dependencies:
      is-fullwidth-code-point: 2.0.0
      strip-ansi: 4.0.0
    dev: false

  /string-width/3.1.0:
    resolution: {integrity: sha512-vafcv6KjVZKSgz06oM/H6GDBrAtz8vdhQakGjFIvNrHA6y3HCF1CInLy+QLq8dTJPQ1b+KDUqDFctkdRW44e1w==}
    engines: {node: '>=6'}
    dependencies:
      emoji-regex: 7.0.3
      is-fullwidth-code-point: 2.0.0
      strip-ansi: 5.2.0

  /string-width/4.2.3:
    resolution: {integrity: sha1-JpxxF9J7Ba0uU2gwqOyJXvnG0BA=}
    engines: {node: '>=8'}
    dependencies:
      emoji-regex: 8.0.0
      is-fullwidth-code-point: 3.0.0
      strip-ansi: 6.0.1

  /string.prototype.trimend/1.0.5:
    resolution: {integrity: sha512-I7RGvmjV4pJ7O3kdf+LXFpVfdNOxtCW/2C8f6jNiW4+PQchwxkCDzlk1/7p+Wl4bqFIZeF47qAHXLuHHWKAxog==}
    dependencies:
      call-bind: 1.0.2
      define-properties: 1.1.4
      es-abstract: 1.20.4
    dev: false

  /string.prototype.trimstart/1.0.5:
    resolution: {integrity: sha512-THx16TJCGlsN0o6dl2o6ncWUsdgnLRSA23rRE5pyGBw/mLr3Ej/R2LaqCtgP8VNMGZsvMWnf9ooZPyY2bHvUFg==}
    dependencies:
      call-bind: 1.0.2
      define-properties: 1.1.4
      es-abstract: 1.20.4
    dev: false

  /string_decoder/1.1.1:
    resolution: {integrity: sha512-n/ShnvDi6FHbbVfviro+WojiFzv+s8MPMHBczVePfUpDJLwoLT0ht1l4YwBCbi8pJAveEEdnkHyPyTP/mzRfwg==}
    dependencies:
      safe-buffer: 5.1.2

  /string_decoder/1.3.0:
    resolution: {integrity: sha512-hkRX8U1WjJFd8LsDJ2yQ/wWWxaopEsABU1XfkM8A+j0+85JAGppt16cr1Whg6KIbb4okU6Mql6BOj+uup/wKeA==}
    dependencies:
      safe-buffer: 5.2.1

  /stringify-object/3.3.0:
    resolution: {integrity: sha512-rHqiFh1elqCQ9WPLIC8I0Q/g/wj5J1eMkyoiD6eoQApWHP0FtlK7rqnhmabL5VUY9JQCcqwwvlOaSuutekgyrw==}
    engines: {node: '>=4'}
    dependencies:
      get-own-enumerable-property-symbols: 3.0.2
      is-obj: 1.0.1
      is-regexp: 1.0.0
    dev: false

  /strip-ansi/3.0.1:
    resolution: {integrity: sha1-ajhfuIU9lS1f8F0Oiq+UJ43GPc8=}
    engines: {node: '>=0.10.0'}
    dependencies:
      ansi-regex: 2.1.1

  /strip-ansi/4.0.0:
    resolution: {integrity: sha1-qEeQIusaw2iocTibY1JixQXuNo8=}
    engines: {node: '>=4'}
    dependencies:
      ansi-regex: 3.0.1
    dev: false

  /strip-ansi/5.2.0:
    resolution: {integrity: sha512-DuRs1gKbBqsMKIZlrffwlug8MHkcnpjs5VPmL1PAh+mA30U0DTotfDZ0d2UUsXpPmPmMMJ6W773MaA3J+lbiWA==}
    engines: {node: '>=6'}
    dependencies:
      ansi-regex: 4.1.1

  /strip-ansi/6.0.0:
    resolution: {integrity: sha512-AuvKTrTfQNYNIctbR1K/YGTR1756GycPsg7b9bdV9Duqur4gv6aKqHXah67Z8ImS7WEz5QVcOtlfW2rZEugt6w==}
    engines: {node: '>=8'}
    dependencies:
      ansi-regex: 5.0.1
    dev: false

  /strip-ansi/6.0.1:
    resolution: {integrity: sha1-nibGPTD1NEPpSJSVshBdN7Z6hdk=}
    engines: {node: '>=8'}
    dependencies:
      ansi-regex: 5.0.1

  /strip-bom/2.0.0:
    resolution: {integrity: sha1-YhmoVhZSBJHzV4i9vxRHqZx+aw4=}
    engines: {node: '>=0.10.0'}
    dependencies:
      is-utf8: 0.2.1

  /strip-bom/3.0.0:
    resolution: {integrity: sha1-IzTBjpx1n3vdVv3vfprj1YjmjtM=}
    engines: {node: '>=4'}
    dev: false

  /strip-comments/1.0.2:
    resolution: {integrity: sha512-kL97alc47hoyIQSV165tTt9rG5dn4w1dNnBhOQ3bOU1Nc1hel09jnXANaHJ7vzHLd4Ju8kseDGzlev96pghLFw==}
    engines: {node: '>=4'}
    dependencies:
      babel-extract-comments: 1.0.0
      babel-plugin-transform-object-rest-spread: 6.26.0
    dev: false

  /strip-eof/1.0.0:
    resolution: {integrity: sha1-u0P/VZim6wXYm1n80SnJgzE2Br8=}
    engines: {node: '>=0.10.0'}
    dev: false

  /strip-indent/1.0.1:
    resolution: {integrity: sha1-DHlipq3vp7vUrDZkYKY4VSrhoKI=}
    engines: {node: '>=0.10.0'}
    hasBin: true
    dependencies:
      get-stdin: 4.0.1

  /strip-indent/3.0.0:
    resolution: {integrity: sha512-laJTa3Jb+VQpaC6DseHhF7dXVqHTfJPCRDaEbid/drOhgitgYku/letMUqOXFoWV0zIIUbjpdH2t+tYj4bQMRQ==}
    engines: {node: '>=8'}
    dependencies:
      min-indent: 1.0.1
    dev: true

  /strip-json-comments/3.1.1:
    resolution: {integrity: sha512-6fPc+R4ihwqP6N/aIv2f1gMH8lOVtWQHoqC4yK6oSDVVocumAsfCqjkXnqiYMhmMwS/mEHLp7Vehlt3ql6lEig==}
    engines: {node: '>=8'}
    dev: false

  /style-loader/0.23.1:
    resolution: {integrity: sha512-XK+uv9kWwhZMZ1y7mysB+zoihsEj4wneFWAS5qoiLwzW0WzSqMrrsIy+a3zkQJq0ipFtBpX5W3MqyRIBF/WFGg==}
    engines: {node: '>= 0.12.0'}
    dependencies:
      loader-utils: 1.4.0
      schema-utils: 1.0.0
    dev: false

  /style-search/0.1.0:
    resolution: {integrity: sha1-eVjHk+R+MuB9K1yv5cC/jhLneQI=}
    dev: true

  /stylehacks/4.0.3:
    resolution: {integrity: sha512-7GlLk9JwlElY4Y6a/rmbH2MhVlTyVmiJd1PfTCqFaIBEGMYNsrO/v3SeGTdhBThLg4Z+NbOk/qFMwCa+J+3p/g==}
    engines: {node: '>=6.9.0'}
    dependencies:
      browserslist: 4.21.4
      postcss: 7.0.39
      postcss-selector-parser: 3.1.2
    dev: false

  /stylelint-config-recommended/3.0.0_stylelint@13.13.1:
    resolution: {integrity: sha512-F6yTRuc06xr1h5Qw/ykb2LuFynJ2IxkKfCMf+1xqPffkxh0S09Zc902XCffcsw/XMFq/OzQ1w54fLIDtmRNHnQ==}
    peerDependencies:
      stylelint: '>=10.1.0'
    dependencies:
      stylelint: 13.13.1
    dev: true

  /stylelint-config-standard/20.0.0_stylelint@13.13.1:
    resolution: {integrity: sha512-IB2iFdzOTA/zS4jSVav6z+wGtin08qfj+YyExHB3LF9lnouQht//YyB0KZq9gGz5HNPkddHOzcY8HsUey6ZUlA==}
    peerDependencies:
      stylelint: '>=10.1.0'
    dependencies:
      stylelint: 13.13.1
      stylelint-config-recommended: 3.0.0_stylelint@13.13.1
    dev: true

  /stylelint/13.13.1:
    resolution: {integrity: sha512-Mv+BQr5XTUrKqAXmpqm6Ddli6Ief+AiPZkRsIrAoUKFuq/ElkUh9ZMYxXD0iQNZ5ADghZKLOWz1h7hTClB7zgQ==}
    engines: {node: '>=10.13.0'}
    hasBin: true
    dependencies:
      '@stylelint/postcss-css-in-js': 0.37.3_j55xdkkcxc32kvnyvx3y7casfm
      '@stylelint/postcss-markdown': 0.36.2_j55xdkkcxc32kvnyvx3y7casfm
      autoprefixer: 9.8.8
      balanced-match: 2.0.0
      chalk: 4.1.2
      cosmiconfig: 7.0.1
      debug: 4.3.4
      execall: 2.0.0
      fast-glob: 3.2.12
      fastest-levenshtein: 1.0.16
      file-entry-cache: 6.0.1
      get-stdin: 8.0.0
      global-modules: 2.0.0
      globby: 11.1.0
      globjoin: 0.1.4
      html-tags: 3.2.0
      ignore: 5.2.0
      import-lazy: 4.0.0
      imurmurhash: 0.1.4
      known-css-properties: 0.21.0
      lodash: 4.17.21
      log-symbols: 4.1.0
      mathml-tag-names: 2.1.3
      meow: 9.0.0
      micromatch: 4.0.5
      normalize-selector: 0.2.0
      postcss: 7.0.39
      postcss-html: 0.36.0_j55xdkkcxc32kvnyvx3y7casfm
      postcss-less: 3.1.4
      postcss-media-query-parser: 0.2.3
      postcss-resolve-nested-selector: 0.1.1
      postcss-safe-parser: 4.0.2
      postcss-sass: 0.4.4
      postcss-scss: 2.1.1
      postcss-selector-parser: 6.0.10
      postcss-syntax: 0.36.2_kei4jy7wdgbhc236h4oijypxom
      postcss-value-parser: 4.2.0
      resolve-from: 5.0.0
      slash: 3.0.0
      specificity: 0.4.1
      string-width: 4.2.3
      strip-ansi: 6.0.1
      style-search: 0.1.0
      sugarss: 2.0.0
      svg-tags: 1.0.0
      table: 6.8.0
      v8-compile-cache: 2.3.0
      write-file-atomic: 3.0.3
    transitivePeerDependencies:
      - postcss-jsx
      - postcss-markdown
      - supports-color
    dev: true

  /sugarss/2.0.0:
    resolution: {integrity: sha512-WfxjozUk0UVA4jm+U1d736AUpzSrNsQcIbyOkoE364GrtWmIrFdk5lksEupgWMD4VaT/0kVx1dobpiDumSgmJQ==}
    dependencies:
      postcss: 7.0.39
    dev: true

  /supports-color/2.0.0:
    resolution: {integrity: sha1-U10EXOa2Nj+kARcIRimZXp3zJMc=}
    engines: {node: '>=0.8.0'}

  /supports-color/5.5.0:
    resolution: {integrity: sha512-QjVjwdXIt408MIiAqCX4oUKsgU2EqAGzs2Ppkm4aQYbjm+ZEWEcW4SfFNTr4uMNZma0ey4f5lgLrkB0aX0QMow==}
    engines: {node: '>=4'}
    dependencies:
      has-flag: 3.0.0

  /supports-color/6.1.0:
    resolution: {integrity: sha512-qe1jfm1Mg7Nq/NSh6XE24gPXROEVsWHxC1LIx//XNlD9iw7YZQGjZNjYN7xGaEG6iKdA8EtNFW6R0gjnVXp+wQ==}
    engines: {node: '>=6'}
    dependencies:
      has-flag: 3.0.0
    dev: false

  /supports-color/7.2.0:
    resolution: {integrity: sha512-qpCAvRl9stuOHveKsn7HncJRvv501qIacKzQlO/+Lwxc9+0q2wLyv4Dfvt80/DPn2pqOBsJdDiogXGR9+OvwRw==}
    engines: {node: '>=8'}
    dependencies:
      has-flag: 4.0.0

  /supports-preserve-symlinks-flag/1.0.0:
    resolution: {integrity: sha512-ot0WnXS9fgdkgIcePe6RHNk1WA8+muPa6cSjeR3V8K27q9BB1rTE3R1p7Hv0z1ZyAc8s6Vvv8DIyWf681MAt0w==}
    engines: {node: '>= 0.4'}

  /svg-parser/2.0.4:
    resolution: {integrity: sha512-e4hG1hRwoOdRb37cIMSgzNsxyzKfayW6VOflrwvR+/bzrkyxY/31WkbgnQpgtrNp1SdpJvpUAGTa/ZoiPNDuRQ==}
    dev: false

  /svg-tags/1.0.0:
    resolution: {integrity: sha1-WPcc7jvVGbWdSyqEO2x95krAR2Q=}
    dev: true

  /svgo/1.3.2:
    resolution: {integrity: sha512-yhy/sQYxR5BkC98CY7o31VGsg014AKLEPxdfhora76l36hD9Rdy5NZA/Ocn6yayNPgSamYdtX2rFJdcv07AYVw==}
    engines: {node: '>=4.0.0'}
    hasBin: true
    dependencies:
      chalk: 2.4.2
      coa: 2.0.2
      css-select: 2.1.0
      css-select-base-adapter: 0.1.1
      css-tree: 1.0.0-alpha.37
      csso: 4.2.0
      js-yaml: 3.14.1
      mkdirp: 0.5.6
      object.values: 1.1.5
      sax: 1.2.4
      stable: 0.1.8
      unquote: 1.1.1
      util.promisify: 1.0.1
    dev: false

  /symbol-tree/3.2.4:
    resolution: {integrity: sha512-9QNk5KwDF+Bvz+PyObkmSYjI5ksVUYtjW7AU22r2NKcfLJcXp96hkDWU3+XndOsUb+AQ9QhfzfCT2O+CNWT5Tw==}
    dev: false

  /table/5.4.6:
    resolution: {integrity: sha512-wmEc8m4fjnob4gt5riFRtTu/6+4rSe12TpAELNSqHMfF3IqnA+CH37USM6/YR3qRZv7e56kAEAtd6nKZaxe0Ug==}
    engines: {node: '>=6.0.0'}
    dependencies:
      ajv: 6.12.6
      lodash: 4.17.21
      slice-ansi: 2.1.0
      string-width: 3.1.0
    dev: false

  /table/6.8.0:
    resolution: {integrity: sha512-s/fitrbVeEyHKFa7mFdkuQMWlH1Wgw/yEXMt5xACT4ZpzWFluehAxRtUUQKPuWhaLAWhFcVx6w3oC8VKaUfPGA==}
    engines: {node: '>=10.0.0'}
    dependencies:
      ajv: 8.11.0
      lodash.truncate: 4.4.2
      slice-ansi: 4.0.0
      string-width: 4.2.3
      strip-ansi: 6.0.1
    dev: true

  /tapable/1.1.3:
    resolution: {integrity: sha512-4WK/bYZmj8xLr+HUCODHGF1ZFzsYffasLUgEiMBY4fgtltdO6B4WJtlSbPaDTLpYTcGVwM2qLnFTICEcNxs3kA==}
    engines: {node: '>=6'}
    dev: false

  /tar-stream/2.2.0:
    resolution: {integrity: sha512-ujeqbceABgwMZxEJnk2HDY2DlnUZ+9oEcb1KzTVfYHio0UE6dG71n60d8D2I4qNvleWrrXpmjpt7vZeF1LnMZQ==}
    engines: {node: '>=6'}
    dependencies:
      bl: 4.1.0
      end-of-stream: 1.4.4
      fs-constants: 1.0.0
      inherits: 2.0.4
      readable-stream: 3.6.0
    dev: false

  /tar/2.2.2:
    resolution: {integrity: sha512-FCEhQ/4rE1zYv9rYXJw/msRqsnmlje5jHP6huWeBZ704jUTy02c5AZyWujpMR1ax6mVw9NyJMfuK2CMDWVIfgA==}
    dependencies:
      block-stream: 0.0.9
      fstream: 1.0.12
      inherits: 2.0.4

  /terser-webpack-plugin/1.4.5_webpack@4.41.5:
    resolution: {integrity: sha512-04Rfe496lN8EYruwi6oPQkG0vo8C+HT49X687FZnpPF0qMAIHONI6HEXYPKDOE8e5HjXTyKfqRd/agHtH0kOtw==}
    engines: {node: '>= 6.9.0'}
    peerDependencies:
      webpack: ^4.0.0
    dependencies:
      cacache: 12.0.4
      find-cache-dir: 2.1.0
      is-wsl: 1.1.0
      schema-utils: 1.0.0
      serialize-javascript: 4.0.0
      source-map: 0.6.1
      terser: 4.8.1
      webpack: 4.41.5
      webpack-sources: 1.4.3
      worker-farm: 1.7.0
    dev: false

  /terser-webpack-plugin/2.3.4_webpack@4.41.5:
    resolution: {integrity: sha512-Nv96Nws2R2nrFOpbzF6IxRDpIkkIfmhvOws+IqMvYdFLO7o6wAILWFKONFgaYy8+T4LVz77DQW0f7wOeDEAjrg==}
    engines: {node: '>= 8.9.0'}
    peerDependencies:
      webpack: ^4.0.0 || ^5.0.0
    dependencies:
      cacache: 13.0.1
      find-cache-dir: 3.3.2
      jest-worker: 25.5.0
      p-limit: 2.3.0
      schema-utils: 2.7.1
      serialize-javascript: 2.1.2
      source-map: 0.6.1
      terser: 4.8.1
      webpack: 4.41.5
      webpack-sources: 1.4.3
    transitivePeerDependencies:
      - bluebird
    dev: false

  /terser/4.8.1:
    resolution: {integrity: sha512-4GnLC0x667eJG0ewJTa6z/yXrbLGv80D9Ru6HIpCQmO+Q4PfEtBFi0ObSckqwL6VyQv/7ENJieXHo2ANmdQwgw==}
    engines: {node: '>=6.0.0'}
    hasBin: true
    dependencies:
      acorn: 8.8.0
      commander: 2.20.3
      source-map: 0.6.1
      source-map-support: 0.5.21
    dev: false

  /test-exclude/5.2.3:
    resolution: {integrity: sha512-M+oxtseCFO3EDtAaGH7iiej3CBkzXqFMbzqYAACdzKui4eZA+pq3tZEwChvOdNfa7xxy8BfbmgJSIr43cC/+2g==}
    engines: {node: '>=6'}
    dependencies:
      glob: 7.2.3
      minimatch: 3.1.2
      read-pkg-up: 4.0.0
      require-main-filename: 2.0.0
    dev: false

  /text-segmentation/1.0.3:
    resolution: {integrity: sha512-iOiPUo/BGnZ6+54OsWxZidGCsdU8YbE4PSpdPinp7DeMtUJNJBoJ/ouUSTJjHkh1KntHaltHl/gDs2FC4i5+Nw==}
    dependencies:
      utrie: 1.0.2
    dev: false

  /text-table/0.2.0:
    resolution: {integrity: sha1-f17oI66AUgfACvLfSoTsP8+lcLQ=}
    dev: false

  /throat/4.1.0:
    resolution: {integrity: sha1-iQN8vJLFarGJJua6TLsgDhVnKmo=}
    dev: false

  /through/2.3.8:
    resolution: {integrity: sha1-DdTJ/6q8NXlgsbckEV1+Doai4fU=}
    dev: false

  /through2/2.0.5:
    resolution: {integrity: sha512-/mrRod8xqpA+IHSLyGCQ2s8SPHiCDEeQJSep1jqLYeEUClOFG2Qsh+4FU6G9VeqpZnGW/Su8LQGc4YKni5rYSQ==}
    dependencies:
      readable-stream: 2.3.7
      xtend: 4.0.2
    dev: false

  /thunky/1.1.0:
    resolution: {integrity: sha512-eHY7nBftgThBqOyHGVN+l8gF0BucP09fMo0oO/Lb0w1OF80dJv+lDVpXG60WMQvkcxAkNybKsrEIE3ZtKGmPrA==}
    dev: false

  /timers-browserify/2.0.12:
    resolution: {integrity: sha512-9phl76Cqm6FhSX9Xe1ZUAMLtm1BLkKj2Qd5ApyWkXzsMRaA7dgr81kf4wJmQf/hAvg8EEyJxDo3du/0KlhPiKQ==}
    engines: {node: '>=0.6.0'}
    dependencies:
      setimmediate: 1.0.5
    dev: false

  /timsort/0.3.0:
    resolution: {integrity: sha1-QFQRqOfmM5/mTbmiNN4R3DHgK9Q=}
    dev: false

  /tiny-emitter/2.1.0:
    resolution: {integrity: sha512-NB6Dk1A9xgQPMoGqC5CVXn123gWyte215ONT5Pp5a0yt4nlEoO1ZWeCwpncaekPHXO60i47ihFnZPiRPjRMq4Q==}
    dev: false

  /tiny-invariant/1.3.1:
    resolution: {integrity: sha512-AD5ih2NlSssTCwsMznbvwMZpJ1cbhkGd2uueNxzv2jDlEeZdU04JQfRnggJQ8DrcVBGjAsCKwFBbDlVNtEMlzw==}
    dev: false

  /tiny-warning/1.0.3:
    resolution: {integrity: sha512-lBN9zLN/oAf68o3zNXYrdCt1kP8WsiGW8Oo2ka41b2IM5JL/S1CTyX1rW0mb/zSuJun0ZUrDxx4sqvYS2FWzPA==}
    dev: false

  /tmp/0.0.33:
    resolution: {integrity: sha512-jRCJlojKnZ3addtTOjdIqoRuPEKBvNXcGYqzO6zWZX8KfKEpnGY5jfggJQ3EjKuu8D4bJRr0y+cYJFmYbImXGw==}
    engines: {node: '>=0.6.0'}
    dependencies:
      os-tmpdir: 1.0.2
    dev: false

  /tmpl/1.0.5:
    resolution: {integrity: sha1-hoPguQK7nCDE9ybjwLafNlGMB8w=}
    dev: false

  /to-arraybuffer/1.0.1:
    resolution: {integrity: sha1-fSKbH8xjfkZsoIEYCDanqr/4P0M=}
    dev: false

  /to-fast-properties/2.0.0:
    resolution: {integrity: sha1-3F5pjL0HkmW8c+A3doGk5Og/YW4=}
    engines: {node: '>=4'}

  /to-object-path/0.3.0:
    resolution: {integrity: sha1-KXWIt7Dn4KwI4E5nL4XB9JmeF68=}
    engines: {node: '>=0.10.0'}
    dependencies:
      kind-of: 3.2.2
    dev: false

  /to-regex-range/2.1.1:
    resolution: {integrity: sha1-fIDBe53+vlmeJzZ+DU3VWQFB2zg=}
    engines: {node: '>=0.10.0'}
    dependencies:
      is-number: 3.0.0
      repeat-string: 1.6.1
    dev: false

  /to-regex-range/5.0.1:
    resolution: {integrity: sha512-65P7iz6X5yEr1cwcgvQxbbIw7Uk3gOy5dIdtZ4rDveLqhrdJP+Li/Hx6tyK0NEb+2GCyneCMJiGqrADCSNk8sQ==}
    engines: {node: '>=8.0'}
    dependencies:
      is-number: 7.0.0

  /to-regex/3.0.2:
    resolution: {integrity: sha512-FWtleNAtZ/Ki2qtqej2CXTOayOH9bHDQF+Q48VpWyDXjbYxA4Yz8iDB31zXOBUlOHHKidDbqGVrTUvQMPmBGBw==}
    engines: {node: '>=0.10.0'}
    dependencies:
      define-property: 2.0.2
      extend-shallow: 3.0.2
      regex-not: 1.0.2
      safe-regex: 1.1.0
    dev: false

  /toggle-selection/1.0.6:
    resolution: {integrity: sha1-bkWxJj8gF/oKzH2J14sVuL932jI=}
    dev: false

  /toidentifier/1.0.1:
    resolution: {integrity: sha1-O+NDIaiKgg7RvYDfqjPkefu43TU=}
    engines: {node: '>=0.6'}
    dev: false

  /tough-cookie/2.5.0:
    resolution: {integrity: sha512-nlLsUzgm1kfLXSXfRZMc1KLAugd4hqJHDTvc2hDIwS3mZAfMEuMbc03SujMF+GEcpaX/qboeycw6iO8JwVv2+g==}
    engines: {node: '>=0.8'}
    dependencies:
      psl: 1.9.0
      punycode: 2.1.1

  /tr46/1.0.1:
    resolution: {integrity: sha1-qLE/1r/SSJUZZ0zN5VujaTtwbQk=}
    dependencies:
      punycode: 2.1.1
    dev: false

  /trim-newlines/1.0.0:
    resolution: {integrity: sha1-WIeWa7WCpFA6QetST301ARgVphM=}
    engines: {node: '>=0.10.0'}

  /trim-newlines/3.0.1:
    resolution: {integrity: sha512-c1PTsA3tYrIsLGkJkzHF+w9F2EyxfXGo4UyJc4pFL++FMjnq0HJS69T3M7d//gKrFKwy429bouPescbjecU+Zw==}
    engines: {node: '>=8'}
    dev: true

  /trough/1.0.5:
    resolution: {integrity: sha512-rvuRbTarPXmMb79SmzEp8aqXNKcK+y0XaB298IXueQ8I2PsrATcPBCSPyK/dDNa2iWOhKlfNnOjdAOTBU/nkFA==}
    dev: true

  /true-case-path/1.0.3:
    resolution: {integrity: sha512-m6s2OdQe5wgpFMC+pAJ+q9djG82O2jcHPOI6RNg1yy9rCYR+WD6Nbpl32fDpfC56nirdRy+opFa/Vk7HYhqaew==}
    dependencies:
      glob: 7.2.3

  /ts-pnp/1.1.5_typescript@3.9.10:
    resolution: {integrity: sha512-ti7OGMOUOzo66wLF3liskw6YQIaSsBgc4GOAlWRnIEj8htCxJUxskanMUoJOD6MDCRAXo36goXJZch+nOS0VMA==}
    engines: {node: '>=6'}
    peerDependencies:
      typescript: '*'
    peerDependenciesMeta:
      typescript:
        optional: true
    dependencies:
      typescript: 3.9.10
    dev: false

  /tslib/1.14.1:
    resolution: {integrity: sha512-Xni35NKzjgMrwevysHTCArtLDpPvye8zV/0E4EyYn43P7/7qvQwPh9BGkHewbMulVntbigmcT7rdX3BNo9wRJg==}
    dev: false

  /tslib/2.4.0:
    resolution: {integrity: sha512-d6xOpEDfsi2CZVlPQzGeux8XMwLT9hssAsaPYExaQMuYskwb+x1x7J371tWlbBdWHroy99KnVB6qIkUbs5X3UQ==}
    dev: false

  /tsutils/3.21.0_typescript@3.9.10:
    resolution: {integrity: sha512-mHKK3iUXL+3UF6xL5k0PEhKRUBKPBCv/+RkEOpjRWxxx27KKRBmmA60A9pgOUvMi8GKhRMPEmjBRPzs2W7O1OA==}
    engines: {node: '>= 6'}
    peerDependencies:
      typescript: '>=2.8.0 || >= 3.2.0-dev || >= 3.3.0-dev || >= 3.4.0-dev || >= 3.5.0-dev || >= 3.6.0-dev || >= 3.6.0-beta || >= 3.7.0-dev || >= 3.7.0-beta'
    dependencies:
      tslib: 1.14.1
      typescript: 3.9.10
    dev: false

  /tty-browserify/0.0.0:
    resolution: {integrity: sha1-oVe6QC2iTpv5V/mqadUk7tQpAaY=}
    dev: false

  /tunnel-agent/0.6.0:
    resolution: {integrity: sha1-J6XeoGs2sEoKmWZ3SykIaPD8QP0=}
    dependencies:
      safe-buffer: 5.2.1

  /tween-functions/1.2.0:
    resolution: {integrity: sha1-GuOlDnxguz3vd06scHrLynO7w/8=}
    dev: false

  /tweetnacl/0.14.5:
    resolution: {integrity: sha1-WuaBd/GS1EViadEIr6k/+HQ/T2Q=}

  /type-check/0.3.2:
    resolution: {integrity: sha1-WITKtRLPHTVeP7eE8wgEsrUg23I=}
    engines: {node: '>= 0.8.0'}
    dependencies:
      prelude-ls: 1.1.2
    dev: false

  /type-fest/0.18.1:
    resolution: {integrity: sha512-OIAYXk8+ISY+qTOwkHtKqzAuxchoMiD9Udx+FSGQDuiRR+PJKJHc2NJAXlbhkGwTt/4/nKZxELY1w3ReWOL8mw==}
    engines: {node: '>=10'}
    dev: true

  /type-fest/0.21.3:
    resolution: {integrity: sha512-t0rzBq87m3fVcduHDUFhKmyyX+9eo6WQjZvf51Ea/M0Q7+T374Jp1aUiyUl0GKxp8M/OETVHSDvmkyPgvX+X2w==}
    engines: {node: '>=10'}
    dev: false

  /type-fest/0.6.0:
    resolution: {integrity: sha512-q+MB8nYR1KDLrgr4G5yemftpMC7/QLqVndBmEEdqzmNj5dcFOO4Oo8qlwZE3ULT3+Zim1F8Kq4cBnikNhlCMlg==}
    engines: {node: '>=8'}
    dev: true

  /type-fest/0.8.1:
    resolution: {integrity: sha512-4dbzIzqvjtgiM5rw1k5rEHtBANKmdudhGyBEajN01fEyhaAIhsoKNy6y7+IN93IfpFtwY9iqi7kD+xwKhQsNJA==}
    engines: {node: '>=8'}

  /type-is/1.6.18:
    resolution: {integrity: sha512-TkRKr9sUTxEH8MdfuCSP7VizJyzRNMjj2J2do2Jr3Kym598JVdEksuzPQCnlFPW4ky9Q+iA+ma9BGm06XQBy8g==}
    engines: {node: '>= 0.6'}
    dependencies:
      media-typer: 0.3.0
      mime-types: 2.1.35
    dev: false

  /type/1.2.0:
    resolution: {integrity: sha512-+5nt5AAniqsCnu2cEQQdpzCAh33kVx8n0VoFidKpB1dVVLAN/F+bgVOqOJqOnEnrhp222clB5p3vUlD+1QAnfg==}
    dev: false

  /type/2.7.2:
    resolution: {integrity: sha512-dzlvlNlt6AXU7EBSfpAscydQ7gXB+pPGsPnfJnZpiNJBDj7IaJzQlBZYGdEi4R9HmPdBv2XmWJ6YUtoTa7lmCw==}
    dev: false

  /typedarray-to-buffer/3.1.5:
    resolution: {integrity: sha512-zdu8XMNEDepKKR+XYOXAVPtWui0ly0NtohUscw+UmaHiAWT8hrV1rr//H6V+0DvJ3OQ19S979M0laLfX8rm82Q==}
    dependencies:
      is-typedarray: 1.0.0
    dev: true

  /typedarray/0.0.6:
    resolution: {integrity: sha1-hnrHTjhkGHsdPUfZlqeOxciDB3c=}
    dev: false

  /typescript/3.9.10:
    resolution: {integrity: sha512-w6fIxVE/H1PkLKcCPsFqKE7Kv7QUwhU8qQY2MueZXWx5cPZdwFupLgKK3vntcK98BtNHZtAF4LA/yl2a7k8R6Q==}
    engines: {node: '>=4.2.0'}
    hasBin: true

  /ua-parser-js/0.7.32:
    resolution: {integrity: sha512-f9BESNVhzlhEFf2CHMSj40NWOjYPl1YKYbrvIr/hFTDEmLq7SRbWvm7FcdcpCYT95zrOhC7gZSxjdnnTpBcwVw==}
    dev: false

  /unbox-primitive/1.0.2:
    resolution: {integrity: sha512-61pPlCD9h51VoreyJ0BReideM3MDKMKnh6+V9L08331ipq6Q8OFXZYiqP6n/tbHx4s5I9uRhcye6BrbkizkBDw==}
    dependencies:
      call-bind: 1.0.2
      has-bigints: 1.0.2
      has-symbols: 1.0.3
      which-boxed-primitive: 1.0.2
    dev: false

  /unicode-canonical-property-names-ecmascript/2.0.0:
    resolution: {integrity: sha1-MBrNxSVjFnDTn2FG4Od/9rvevdw=}
    engines: {node: '>=4'}
    dev: false

  /unicode-match-property-ecmascript/2.0.0:
    resolution: {integrity: sha1-VP0W4OyxZ88Ezx91a9zJLrp5dsM=}
    engines: {node: '>=4'}
    dependencies:
      unicode-canonical-property-names-ecmascript: 2.0.0
      unicode-property-aliases-ecmascript: 2.1.0
    dev: false

  /unicode-match-property-value-ecmascript/2.0.0:
    resolution: {integrity: sha1-GgGqVyR8FMVouJd1pUk4eIGJpxQ=}
    engines: {node: '>=4'}
    dev: false

  /unicode-property-aliases-ecmascript/2.1.0:
    resolution: {integrity: sha512-6t3foTQI9qne+OZoVQB/8x8rk2k1eVy1gRXhV3oFQ5T6R1dqQ1xtin3XqSlx3+ATBkliTaR/hHyJBm+LVPNM8w==}
    engines: {node: '>=4'}
    dev: false

  /unified/9.2.2:
    resolution: {integrity: sha512-Sg7j110mtefBD+qunSLO1lqOEKdrwBFBrR6Qd8f4uwkhWNlbkaqwHse6e7QvD3AP/MNoJdEDLaf8OxYyoWgorQ==}
    dependencies:
      bail: 1.0.5
      extend: 3.0.2
      is-buffer: 2.0.5
      is-plain-obj: 2.1.0
      trough: 1.0.5
      vfile: 4.2.1
    dev: true

  /union-value/1.0.1:
    resolution: {integrity: sha512-tJfXmxMeWYnczCVs7XAEvIV7ieppALdyepWMkHkwciRpZraG/xwT+s2JN8+pr1+8jCRf80FFzvr+MpQeeoF4Xg==}
    engines: {node: '>=0.10.0'}
    dependencies:
      arr-union: 3.1.0
      get-value: 2.0.6
      is-extendable: 0.1.1
      set-value: 2.0.1
    dev: false

  /uniq/1.0.1:
    resolution: {integrity: sha1-sxxa6CVIRKOoKBVBzisEuGWnNP8=}
    dev: false

  /uniqs/2.0.0:
    resolution: {integrity: sha1-/+3ks2slKQaW5uFl1KWe25mOawI=}
    dev: false

  /unique-filename/1.1.1:
    resolution: {integrity: sha512-Vmp0jIp2ln35UTXuryvjzkjGdRyf9b2lTXuSYUiPmzRcl3FDtYqAwOnTJkAngD9SWhnoJzDbTKwaOrZ+STtxNQ==}
    dependencies:
      unique-slug: 2.0.2
    dev: false

  /unique-slug/2.0.2:
    resolution: {integrity: sha512-zoWr9ObaxALD3DOPfjPSqxt4fnZiWblxHIgeWqW8x7UqDzEtHEQLzji2cuJYQFCU6KmoJikOYAZlrTHHebjx2w==}
    dependencies:
      imurmurhash: 0.1.4
    dev: false

  /unist-util-find-all-after/3.0.2:
    resolution: {integrity: sha512-xaTC/AGZ0rIM2gM28YVRAFPIZpzbpDtU3dRmp7EXlNVA8ziQc4hY3H7BHXM1J49nEmiqc3svnqMReW+PGqbZKQ==}
    dependencies:
      unist-util-is: 4.1.0
    dev: true

  /unist-util-is/4.1.0:
    resolution: {integrity: sha512-ZOQSsnce92GrxSqlnEEseX0gi7GH9zTJZ0p9dtu87WRb/37mMPO2Ilx1s/t9vBHrFhbgweUwb+t7cIn5dxPhZg==}
    dev: true

  /unist-util-stringify-position/2.0.3:
    resolution: {integrity: sha512-3faScn5I+hy9VleOq/qNbAd6pAx7iH5jYBMS9I1HgQVijz/4mv5Bvw5iw1sC/90CODiKo81G/ps8AJrISn687g==}
    dependencies:
      '@types/unist': 2.0.6
    dev: true

  /universalify/0.1.2:
    resolution: {integrity: sha512-rBJeI5CXAlmy1pV+617WB9J63U6XcazHHF2f2dbJix4XzpUF0RS3Zbj0FGIOCAva5P/d/GBOYaACQ1w+0azUkg==}
    engines: {node: '>= 4.0.0'}
    dev: false

  /unpipe/1.0.0:
    resolution: {integrity: sha1-sr9O6FFKrmFltIF4KdIbLvSZBOw=}
    engines: {node: '>= 0.8'}
    dev: false

  /unquote/1.1.1:
    resolution: {integrity: sha1-j97XMk7G6IoP+LkF58CYzcCG1UQ=}
    dev: false

  /unset-value/1.0.0:
    resolution: {integrity: sha1-g3aHP30jNRef+x5vw6jtDfyKtVk=}
    engines: {node: '>=0.10.0'}
    dependencies:
      has-value: 0.3.1
      isobject: 3.0.1
    dev: false

  /upath/1.2.0:
    resolution: {integrity: sha512-aZwGpamFO61g3OlfT7OQCHqhGnW43ieH9WZeP7QxN/G/jS4jfqUkZxoryvJgVPEcrl5NL/ggHsSmLMHuH64Lhg==}
    engines: {node: '>=4'}
    dev: false

  /update-browserslist-db/1.0.10_browserslist@4.21.4:
    resolution: {integrity: sha512-OztqDenkfFkbSG+tRxBeAnCVPckDBcvibKd35yDONx6OU8N7sqgwc7rCbkJ/WcYtVRZ4ba68d6byhC21GFh7sQ==}
    hasBin: true
    peerDependencies:
      browserslist: '>= 4.21.0'
    dependencies:
      browserslist: 4.21.4
      escalade: 3.1.1
      picocolors: 1.0.0

  /uri-js/4.4.1:
    resolution: {integrity: sha512-7rKUyy33Q1yc98pQ1DAmLtwX109F7TIfWlW1Ydo8Wl1ii1SeHieeh0HHfPeL2fMXK6z0s8ecKs9frCuLJvndBg==}
    dependencies:
      punycode: 2.1.1

  /urix/0.1.0:
    resolution: {integrity: sha1-2pN/emLiH+wf0Y1Js1wpNQZ6bHI=}
    deprecated: Please see https://github.com/lydell/urix#deprecated

  /url-loader/2.3.0_murjgdbes3wqnvdok3pwcx5key:
    resolution: {integrity: sha512-goSdg8VY+7nPZKUEChZSEtW5gjbS66USIGCeSJ1OVOJ7Yfuh/36YxCwMi5HVEJh6mqUYOoy3NJ0vlOMrWsSHog==}
    engines: {node: '>= 8.9.0'}
    peerDependencies:
      file-loader: '*'
      webpack: ^4.0.0
    peerDependenciesMeta:
      file-loader:
        optional: true
    dependencies:
      file-loader: 4.3.0_webpack@4.41.5
      loader-utils: 1.4.0
      mime: 2.6.0
      schema-utils: 2.7.1
      webpack: 4.41.5
    dev: false

  /url-parse/1.5.10:
    resolution: {integrity: sha512-WypcfiRhfeUP9vvF0j6rw0J3hrWrw6iZv3+22h6iRMJ/8z1Tj6XfLP4DsUix5MhMPnXpiHDoKyoZ/bdCkwBCiQ==}
    dependencies:
      querystringify: 2.2.0
      requires-port: 1.0.0
    dev: false

  /url/0.11.0:
    resolution: {integrity: sha1-ODjpfPxgUh63PFJajlW/3Z4uKPE=}
    dependencies:
      punycode: 1.3.2
      querystring: 0.2.0
    dev: false

  /use/3.1.1:
    resolution: {integrity: sha512-cwESVXlO3url9YWlFW/TA9cshCEhtu7IKJ/p5soJ/gGpj7vbvFrAY/eIioQ6Dw23KjZhYgiIo8HOs1nQ2vr/oQ==}
    engines: {node: '>=0.10.0'}
    dev: false

  /util-deprecate/1.0.2:
    resolution: {integrity: sha1-RQ1Nyfpw3nMnYvvS1KKJgUGaDM8=}

  /util.promisify/1.0.0:
    resolution: {integrity: sha512-i+6qA2MPhvoKLuxnJNpXAGhg7HphQOSUq2LKMZD0m15EiskXUkMvKdF4Uui0WYeCUGea+o2cw/ZuwehtfsrNkA==}
    dependencies:
      define-properties: 1.1.4
      object.getownpropertydescriptors: 2.1.4
    dev: false

  /util.promisify/1.0.1:
    resolution: {integrity: sha512-g9JpC/3He3bm38zsLupWryXHoEcS22YHthuPQSJdMy6KNrzIRzWqcsHzD/WUnqe45whVou4VIsPew37DoXWNrA==}
    dependencies:
      define-properties: 1.1.4
      es-abstract: 1.20.4
      has-symbols: 1.0.3
      object.getownpropertydescriptors: 2.1.4
    dev: false

  /util.promisify/1.1.1:
    resolution: {integrity: sha512-/s3UsZUrIfa6xDhr7zZhnE9SLQ5RIXyYfiVnMMyMDzOc8WhWN4Nbh36H842OyurKbCDAesZOJaVyvmSl6fhGQw==}
    dependencies:
      call-bind: 1.0.2
      define-properties: 1.1.4
      for-each: 0.3.3
      has-symbols: 1.0.3
      object.getownpropertydescriptors: 2.1.4
    dev: false

  /util/0.10.3:
    resolution: {integrity: sha1-evsa/lCAUkZInj23/g7TeTNqwPk=}
    dependencies:
      inherits: 2.0.1
    dev: false

  /util/0.11.1:
    resolution: {integrity: sha512-HShAsny+zS2TZfaXxD9tYj4HQGlBezXZMZuM/S5PKLLoZkShZiGk9o5CzukI1LVHZvjdvZ2Sj1aW/Ndn2NB/HQ==}
    dependencies:
      inherits: 2.0.3
    dev: false

  /utila/0.4.0:
    resolution: {integrity: sha1-ihagXURWV6Oupe7MWxKk+lN5dyw=}
    dev: false

  /utils-merge/1.0.1:
    resolution: {integrity: sha1-n5VxD1CiZ5R7LMwSR0HBAoQn5xM=}
    engines: {node: '>= 0.4.0'}
    dev: false

  /utrie/1.0.2:
    resolution: {integrity: sha512-1MLa5ouZiOmQzUbjbu9VmjLzn1QLXBhwpUa7kdLUQK+KQ5KA9I1vk5U4YHe/X2Ch7PYnJfWuWT+VbuxbGwljhw==}
    dependencies:
      base64-arraybuffer: 1.0.2
    dev: false

  /uuid/3.4.0:
    resolution: {integrity: sha512-HjSDRw6gZE5JMggctHBcjVak08+KEVhSIiDzFnT9S9aegmp85S/bReBVTb4QTFaRNptJ9kuYaNhnbNEOkbKb/A==}
    hasBin: true

  /v8-compile-cache/2.3.0:
    resolution: {integrity: sha512-l8lCEmLcLYZh4nbunNZvQCJc5pv7+RCwa8q/LdUx8u7lsWvPDKmpodJAJNwkAhJC//dFY48KuIEmjtd4RViDrA==}

  /validate-npm-package-license/3.0.4:
    resolution: {integrity: sha512-DpKm2Ui/xN7/HQKCtpZxoRWBhZ9Z0kqtygG8XCgNQ8ZlDnxuQmWhj566j8fN4Cu3/JmbhsDo7fcAJq4s9h27Ew==}
    dependencies:
      spdx-correct: 3.1.1
      spdx-expression-parse: 3.0.1

  /value-equal/1.0.1:
    resolution: {integrity: sha512-NOJ6JZCAWr0zlxZt+xqCHNTEKOsrks2HQd4MqhP1qy4z1SkbEP467eNx6TgDKXMvUOb+OENfJCZwM+16n7fRfw==}
    dev: false

  /vary/1.1.2:
    resolution: {integrity: sha1-IpnwLG3tMNSllhsLn3RSShj2NPw=}
    engines: {node: '>= 0.8'}
    dev: false

  /vendors/1.0.4:
    resolution: {integrity: sha512-/juG65kTL4Cy2su4P8HjtkTxk6VmJDiOPBufWniqQ6wknac6jNiXS9vU+hO3wgusiyqWlzTbVHi0dyJqRONg3w==}
    dev: false

  /verror/1.10.0:
    resolution: {integrity: sha1-OhBcoXBTr1XW4nDB+CiGguGNpAA=}
    engines: {'0': node >=0.6.0}
    dependencies:
      assert-plus: 1.0.0
      core-util-is: 1.0.2
      extsprintf: 1.3.0

  /vfile-message/2.0.4:
    resolution: {integrity: sha512-DjssxRGkMvifUOJre00juHoP9DPWuzjxKuMDrhNbk2TdaYYBNMStsNhEOt3idrtI12VQYM/1+iM0KOzXi4pxwQ==}
    dependencies:
      '@types/unist': 2.0.6
      unist-util-stringify-position: 2.0.3
    dev: true

  /vfile/4.2.1:
    resolution: {integrity: sha512-O6AE4OskCG5S1emQ/4gl8zK586RqA3srz3nfK/Viy0UPToBc5Trp9BVFb1u0CjsKrAWwnpr4ifM/KBXPWwJbCA==}
    dependencies:
      '@types/unist': 2.0.6
      is-buffer: 2.0.5
      unist-util-stringify-position: 2.0.3
      vfile-message: 2.0.4
    dev: true

  /vm-browserify/1.1.2:
    resolution: {integrity: sha512-2ham8XPWTONajOR0ohOKOHXkm3+gaBmGut3SRuu75xLd/RRaY6vqgh8NBYYk7+RW3u5AtzPQZG8F10LHkl0lAQ==}
    dev: false

  /void-elements/3.1.0:
    resolution: {integrity: sha1-YU9/v42AHwu18GYfWy9XhXUOTwk=}
    engines: {node: '>=0.10.0'}
    dev: false

  /w3c-hr-time/1.0.2:
    resolution: {integrity: sha512-z8P5DvDNjKDoFIHK7q8r8lackT6l+jo/Ye3HOle7l9nICP9lf1Ci25fy9vHd0JOWewkIFzXIEig3TdKT7JQ5fQ==}
    dependencies:
      browser-process-hrtime: 1.0.0
    dev: false

  /w3c-xmlserializer/1.1.2:
    resolution: {integrity: sha512-p10l/ayESzrBMYWRID6xbuCKh2Fp77+sA0doRuGn4tTIMrrZVeqfpKjXHY+oDh3K4nLdPgNwMTVP6Vp4pvqbNg==}
    dependencies:
      domexception: 1.0.1
      webidl-conversions: 4.0.2
      xml-name-validator: 3.0.0
    dev: false

  /wait-for-expect/3.0.2:
    resolution: {integrity: sha512-cfS1+DZxuav1aBYbaO/kE06EOS8yRw7qOFoD3XtjTkYvCvh3zUvNST8DXK/nPaeqIzIv3P3kL3lRJn8iwOiSag==}
    dev: true

  /walker/1.0.8:
    resolution: {integrity: sha1-vUmNtHev5XPcBBhfAR06uKjXZT8=}
    dependencies:
      makeerror: 1.0.12
    dev: false

  /warning/3.0.0:
    resolution: {integrity: sha1-MuU3fLVy3kqwR1O9+IIcAe1gW3w=}
    dependencies:
      loose-envify: 1.4.0
    dev: false

  /watchpack-chokidar2/2.0.1:
    resolution: {integrity: sha512-nCFfBIPKr5Sh61s4LPpy1Wtfi0HE8isJ3d2Yb5/Ppw2P2B/3eVSEBjKfN0fmHJSK14+31KwMKmcrzs2GM4P0Ww==}
    requiresBuild: true
    dependencies:
      chokidar: 2.1.8
    transitivePeerDependencies:
      - supports-color
    dev: false
    optional: true

  /watchpack/1.7.5:
    resolution: {integrity: sha512-9P3MWk6SrKjHsGkLT2KHXdQ/9SNkyoJbabxnKOoJepsvJjJG8uYTR3yTPxPQvNDI3w4Nz1xnE0TLHK4RIVe/MQ==}
    dependencies:
      graceful-fs: 4.2.10
      neo-async: 2.6.2
    optionalDependencies:
      chokidar: 3.5.3
      watchpack-chokidar2: 2.0.1
    transitivePeerDependencies:
      - supports-color
    dev: false

  /wbuf/1.7.3:
    resolution: {integrity: sha512-O84QOnr0icsbFGLS0O3bI5FswxzRr8/gHwWkDlQFskhSPryQXvrTMxjxGP4+iWYoauLoBvfDpkrOauZ+0iZpDA==}
    dependencies:
      minimalistic-assert: 1.0.1
    dev: false

  /webidl-conversions/4.0.2:
    resolution: {integrity: sha512-YQ+BmxuTgd6UXZW3+ICGfyqRyHXVlD5GtQr5+qjiNW7bF0cqrzX500HVXPBOvgXb5YnzDd+h0zqyv61KUD7+Sg==}
    dev: false

  /webpack-dev-middleware/3.7.3_webpack@4.41.5:
    resolution: {integrity: sha512-djelc/zGiz9nZj/U7PTBi2ViorGJXEWo/3ltkPbDyxCXhhEXkW0ce99falaok4TPj+AsxLiXJR0EBOb0zh9fKQ==}
    engines: {node: '>= 6'}
    peerDependencies:
      webpack: ^4.0.0 || ^5.0.0
    dependencies:
      memory-fs: 0.4.1
      mime: 2.6.0
      mkdirp: 0.5.6
      range-parser: 1.2.1
      webpack: 4.41.5
      webpack-log: 2.0.0
    dev: false

  /webpack-dev-server/3.10.2_webpack@4.41.5:
    resolution: {integrity: sha512-pxZKPYb+n77UN8u9YxXT4IaIrGcNtijh/mi8TXbErHmczw0DtPnMTTjHj+eNjkqLOaAZM/qD7V59j/qJsEiaZA==}
    engines: {node: '>= 6.11.5'}
    hasBin: true
    peerDependencies:
      webpack: ^4.0.0 || ^5.0.0
      webpack-cli: '*'
    peerDependenciesMeta:
      webpack-cli:
        optional: true
    dependencies:
      ansi-html: 0.0.7
      bonjour: 3.5.0
      chokidar: 2.1.8_supports-color@6.1.0
      compression: 1.7.4_supports-color@6.1.0
      connect-history-api-fallback: 1.6.0
      debug: 4.3.4_supports-color@6.1.0
      del: 4.1.1
      express: 4.18.2_supports-color@6.1.0
      html-entities: 1.4.0
      http-proxy-middleware: 0.19.1_tmpgdztspuwvsxzgjkhoqk7duq
      import-local: 2.0.0
      internal-ip: 4.3.0
      ip: 1.1.8
      is-absolute-url: 3.0.3
      killable: 1.0.1
      loglevel: 1.8.0
      opn: 5.5.0
      p-retry: 3.0.1
      portfinder: 1.0.32_supports-color@6.1.0
      schema-utils: 1.0.0
      selfsigned: 1.10.14
      semver: 6.3.0
      serve-index: 1.9.1_supports-color@6.1.0
      sockjs: 0.3.19
      sockjs-client: 1.4.0_supports-color@6.1.0
      spdy: 4.0.2_supports-color@6.1.0
      strip-ansi: 3.0.1
      supports-color: 6.1.0
      url: 0.11.0
      webpack: 4.41.5
      webpack-dev-middleware: 3.7.3_webpack@4.41.5
      webpack-log: 2.0.0
      ws: 6.2.2
      yargs: 12.0.5
    transitivePeerDependencies:
      - bufferutil
      - utf-8-validate
    dev: false

  /webpack-log/2.0.0:
    resolution: {integrity: sha512-cX8G2vR/85UYG59FgkoMamwHUIkSSlV3bBMRsbxVXVUk2j6NleCKjQ/WE9eYg9WY4w25O9w8wKP4rzNZFmUcUg==}
    engines: {node: '>= 6'}
    dependencies:
      ansi-colors: 3.2.4
      uuid: 3.4.0
    dev: false

  /webpack-manifest-plugin/2.2.0_webpack@4.41.5:
    resolution: {integrity: sha512-9S6YyKKKh/Oz/eryM1RyLVDVmy3NSPV0JXMRhZ18fJsq+AwGxUY34X54VNwkzYcEmEkDwNxuEOboCZEebJXBAQ==}
    engines: {node: '>=6.11.5'}
    peerDependencies:
      webpack: 2 || 3 || 4
    dependencies:
      fs-extra: 7.0.1
      lodash: 4.17.21
      object.entries: 1.1.5
      tapable: 1.1.3
      webpack: 4.41.5
    dev: false

  /webpack-sources/1.4.3:
    resolution: {integrity: sha512-lgTS3Xhv1lCOKo7SA5TjKXMjpSM4sBjNV5+q2bqesbSPs5FjGmU6jjtBSkX9b4qW87vDIsCIlUPOEhbZrMdjeQ==}
    dependencies:
      source-list-map: 2.0.1
      source-map: 0.6.1
    dev: false

  /webpack/4.41.5:
    resolution: {integrity: sha512-wp0Co4vpyumnp3KlkmpM5LWuzvZYayDwM2n17EHFr4qxBBbRokC7DJawPJC7TfSFZ9HZ6GsdH40EBj4UV0nmpw==}
    engines: {node: '>=6.11.5'}
    hasBin: true
    peerDependencies:
      webpack-cli: '*'
      webpack-command: '*'
    peerDependenciesMeta:
      webpack-cli:
        optional: true
      webpack-command:
        optional: true
    dependencies:
      '@webassemblyjs/ast': 1.8.5
      '@webassemblyjs/helper-module-context': 1.8.5
      '@webassemblyjs/wasm-edit': 1.8.5
      '@webassemblyjs/wasm-parser': 1.8.5
      acorn: 6.4.2
      ajv: 6.12.6
      ajv-keywords: 3.5.2_ajv@6.12.6
      chrome-trace-event: 1.0.3
      enhanced-resolve: 4.5.0
      eslint-scope: 4.0.3
      json-parse-better-errors: 1.0.2
      loader-runner: 2.4.0
      loader-utils: 1.4.0
      memory-fs: 0.4.1
      micromatch: 3.1.10
      mkdirp: 0.5.6
      neo-async: 2.6.2
      node-libs-browser: 2.2.1
      schema-utils: 1.0.0
      tapable: 1.1.3
      terser-webpack-plugin: 1.4.5_webpack@4.41.5
      watchpack: 1.7.5
      webpack-sources: 1.4.3
    transitivePeerDependencies:
      - supports-color
    dev: false

  /websocket-driver/0.7.4:
    resolution: {integrity: sha512-b17KeDIQVjvb0ssuSDF2cYXSg2iztliJ4B9WdsuB6J952qCPKmnVq4DyW5motImXHDC1cBT/1UezrJVsKw5zjg==}
    engines: {node: '>=0.8.0'}
    dependencies:
      http-parser-js: 0.5.8
      safe-buffer: 5.2.1
      websocket-extensions: 0.1.4
    dev: false

  /websocket-extensions/0.1.4:
    resolution: {integrity: sha512-OqedPIGOfsDlo31UNwYbCFMSaO9m9G/0faIHj5/dZFDMFqPTcx6UwqyOy3COEaEOg/9VsGIpdqn62W5KhoKSpg==}
    engines: {node: '>=0.8.0'}
    dev: false

  /weixin-js-sdk/1.6.0:
    resolution: {integrity: sha512-3IYQH7aalJGFJrwdT3epvTdR1MboMiH7vIZ5BRL2eYOJ12BNah7csoMkmSZzkq1+l92sSq29XdTCVjCJoK2sBQ==}
    dev: false

  /whatwg-encoding/1.0.5:
    resolution: {integrity: sha512-b5lim54JOPN9HtzvK9HFXvBma/rnfFeqsic0hSpjtDbVxR3dJKLc+KB4V6GgiGOvl7CY/KNh8rxSo9DKQrnUEw==}
    dependencies:
      iconv-lite: 0.4.24
    dev: false

  /whatwg-fetch/3.6.2:
    resolution: {integrity: sha512-bJlen0FcuU/0EMLrdbJ7zOnW6ITZLrZMIarMUVmdKtsGvZna8vxKYaexICWPfZ8qwf9fzNq+UEIZrnSaApt6RA==}
    dev: false

  /whatwg-mimetype/2.3.0:
    resolution: {integrity: sha512-M4yMwr6mAnQz76TbJm914+gPpB/nCwvZbJU28cUD6dR004SAxDLOOSUaB1JDRqLtaOV/vi0IC5lEAGFgrjGv/g==}
    dev: false

  /whatwg-url/6.5.0:
    resolution: {integrity: sha512-rhRZRqx/TLJQWUpQ6bmrt2UV4f0HCQ463yQuONJqC6fO2VoEb1pTYddbe59SkYq87aoM5A3bdhMZiUiVws+fzQ==}
    dependencies:
      lodash.sortby: 4.7.0
      tr46: 1.0.1
      webidl-conversions: 4.0.2
    dev: false

  /whatwg-url/7.1.0:
    resolution: {integrity: sha512-WUu7Rg1DroM7oQvGWfOiAK21n74Gg+T4elXEQYkOhtyLeWiJFoOGLXPKI/9gzIie9CtwVLm8wtw6YJdKyxSjeg==}
    dependencies:
      lodash.sortby: 4.7.0
      tr46: 1.0.1
      webidl-conversions: 4.0.2
    dev: false

  /which-boxed-primitive/1.0.2:
    resolution: {integrity: sha512-bwZdv0AKLpplFY2KZRX6TvyuN7ojjr7lwkg6ml0roIy9YeuSr7JS372qlNW18UQYzgYK9ziGcerWqZOmEn9VNg==}
    dependencies:
      is-bigint: 1.0.4
      is-boolean-object: 1.1.2
      is-number-object: 1.0.7
      is-string: 1.0.7
      is-symbol: 1.0.4
    dev: false

  /which-module/2.0.0:
    resolution: {integrity: sha1-2e8H3Od7mQK4o6j6SzHD4/fm6Ho=}

  /which/1.3.1:
    resolution: {integrity: sha512-HxJdYWq1MTIQbJ3nw0cqssHoTNU267KlrDuGZ1WYlxDStUtKUhOaJmh112/TZmHxxUfuJqPXSOm7tDyas0OSIQ==}
    hasBin: true
    dependencies:
      isexe: 2.0.0

  /which/2.0.2:
    resolution: {integrity: sha512-BLI3Tl1TW3Pvl70l3yq3Y64i+awpwXqsGBYWkkqMtnbXgrMD+yj7rhW0kuEDxzJaYXGjEW5ogapKNMEKNMjibA==}
    engines: {node: '>= 8'}
    hasBin: true
    dependencies:
      isexe: 2.0.0
    dev: false

  /wide-align/1.1.5:
    resolution: {integrity: sha1-3x1MIGhUNp7PPJpImPGyP72dFdM=}
    dependencies:
      string-width: 1.0.2

  /word-wrap/1.2.3:
    resolution: {integrity: sha512-Hz/mrNwitNRh/HUAtM/VT/5VH+ygD6DV7mYKZAtHOrbs8U7lvPS6xf7EJKMF0uW1KJCl0H701g3ZGus+muE5vQ==}
    engines: {node: '>=0.10.0'}
    dev: false

  /workbox-background-sync/4.3.1:
    resolution: {integrity: sha512-1uFkvU8JXi7L7fCHVBEEnc3asPpiAL33kO495UMcD5+arew9IbKW2rV5lpzhoWcm/qhGB89YfO4PmB/0hQwPRg==}
    dependencies:
      workbox-core: 4.3.1
    dev: false

  /workbox-broadcast-update/4.3.1:
    resolution: {integrity: sha512-MTSfgzIljpKLTBPROo4IpKjESD86pPFlZwlvVG32Kb70hW+aob4Jxpblud8EhNb1/L5m43DUM4q7C+W6eQMMbA==}
    dependencies:
      workbox-core: 4.3.1
    dev: false

  /workbox-build/4.3.1:
    resolution: {integrity: sha512-UHdwrN3FrDvicM3AqJS/J07X0KXj67R8Cg0waq1MKEOqzo89ap6zh6LmaLnRAjpB+bDIz+7OlPye9iii9KBnxw==}
    engines: {node: '>=4.0.0'}
    dependencies:
      '@babel/runtime': 7.19.4
      '@hapi/joi': 15.1.1
      common-tags: 1.8.2
      fs-extra: 4.0.3
      glob: 7.2.3
      lodash.template: 4.5.0
      pretty-bytes: 5.6.0
      stringify-object: 3.3.0
      strip-comments: 1.0.2
      workbox-background-sync: 4.3.1
      workbox-broadcast-update: 4.3.1
      workbox-cacheable-response: 4.3.1
      workbox-core: 4.3.1
      workbox-expiration: 4.3.1
      workbox-google-analytics: 4.3.1
      workbox-navigation-preload: 4.3.1
      workbox-precaching: 4.3.1
      workbox-range-requests: 4.3.1
      workbox-routing: 4.3.1
      workbox-strategies: 4.3.1
      workbox-streams: 4.3.1
      workbox-sw: 4.3.1
      workbox-window: 4.3.1
    dev: false

  /workbox-cacheable-response/4.3.1:
    resolution: {integrity: sha512-Rp5qlzm6z8IOvnQNkCdO9qrDgDpoPNguovs0H8C+wswLuPgSzSp9p2afb5maUt9R1uTIwOXrVQMmPfPypv+npw==}
    dependencies:
      workbox-core: 4.3.1
    dev: false

  /workbox-core/4.3.1:
    resolution: {integrity: sha512-I3C9jlLmMKPxAC1t0ExCq+QoAMd0vAAHULEgRZ7kieCdUd919n53WC0AfvokHNwqRhGn+tIIj7vcb5duCjs2Kg==}
    dev: false

  /workbox-expiration/4.3.1:
    resolution: {integrity: sha512-vsJLhgQsQouv9m0rpbXubT5jw0jMQdjpkum0uT+d9tTwhXcEZks7qLfQ9dGSaufTD2eimxbUOJfWLbNQpIDMPw==}
    dependencies:
      workbox-core: 4.3.1
    dev: false

  /workbox-google-analytics/4.3.1:
    resolution: {integrity: sha512-xzCjAoKuOb55CBSwQrbyWBKqp35yg1vw9ohIlU2wTy06ZrYfJ8rKochb1MSGlnoBfXGWss3UPzxR5QL5guIFdg==}
    dependencies:
      workbox-background-sync: 4.3.1
      workbox-core: 4.3.1
      workbox-routing: 4.3.1
      workbox-strategies: 4.3.1
    dev: false

  /workbox-navigation-preload/4.3.1:
    resolution: {integrity: sha512-K076n3oFHYp16/C+F8CwrRqD25GitA6Rkd6+qAmLmMv1QHPI2jfDwYqrytOfKfYq42bYtW8Pr21ejZX7GvALOw==}
    dependencies:
      workbox-core: 4.3.1
    dev: false

  /workbox-precaching/4.3.1:
    resolution: {integrity: sha512-piSg/2csPoIi/vPpp48t1q5JLYjMkmg5gsXBQkh/QYapCdVwwmKlU9mHdmy52KsDGIjVaqEUMFvEzn2LRaigqQ==}
    dependencies:
      workbox-core: 4.3.1
    dev: false

  /workbox-range-requests/4.3.1:
    resolution: {integrity: sha512-S+HhL9+iTFypJZ/yQSl/x2Bf5pWnbXdd3j57xnb0V60FW1LVn9LRZkPtneODklzYuFZv7qK6riZ5BNyc0R0jZA==}
    dependencies:
      workbox-core: 4.3.1
    dev: false

  /workbox-routing/4.3.1:
    resolution: {integrity: sha512-FkbtrODA4Imsi0p7TW9u9MXuQ5P4pVs1sWHK4dJMMChVROsbEltuE79fBoIk/BCztvOJ7yUpErMKa4z3uQLX+g==}
    dependencies:
      workbox-core: 4.3.1
    dev: false

  /workbox-strategies/4.3.1:
    resolution: {integrity: sha512-F/+E57BmVG8dX6dCCopBlkDvvhg/zj6VDs0PigYwSN23L8hseSRwljrceU2WzTvk/+BSYICsWmRq5qHS2UYzhw==}
    dependencies:
      workbox-core: 4.3.1
    dev: false

  /workbox-streams/4.3.1:
    resolution: {integrity: sha512-4Kisis1f/y0ihf4l3u/+ndMkJkIT4/6UOacU3A4BwZSAC9pQ9vSvJpIi/WFGQRH/uPXvuVjF5c2RfIPQFSS2uA==}
    dependencies:
      workbox-core: 4.3.1
    dev: false

  /workbox-sw/4.3.1:
    resolution: {integrity: sha512-0jXdusCL2uC5gM3yYFT6QMBzKfBr2XTk0g5TPAV4y8IZDyVNDyj1a8uSXy3/XrvkVTmQvLN4O5k3JawGReXr9w==}
    dev: false

  /workbox-webpack-plugin/4.3.1_webpack@4.41.5:
    resolution: {integrity: sha512-gJ9jd8Mb8wHLbRz9ZvGN57IAmknOipD3W4XNE/Lk/4lqs5Htw4WOQgakQy/o/4CoXQlMCYldaqUg+EJ35l9MEQ==}
    engines: {node: '>=4.0.0'}
    peerDependencies:
      webpack: ^2.0.0 || ^3.0.0 || ^4.0.0
    dependencies:
      '@babel/runtime': 7.19.4
      json-stable-stringify: 1.0.1
      webpack: 4.41.5
      workbox-build: 4.3.1
    dev: false

  /workbox-window/4.3.1:
    resolution: {integrity: sha512-C5gWKh6I58w3GeSc0wp2Ne+rqVw8qwcmZnQGpjiek8A2wpbxSJb1FdCoQVO+jDJs35bFgo/WETgl1fqgsxN0Hg==}
    dependencies:
      workbox-core: 4.3.1
    dev: false

  /worker-farm/1.7.0:
    resolution: {integrity: sha512-rvw3QTZc8lAxyVrqcSGVm5yP/IJ2UcB3U0graE3LCFoZ0Yn2x4EoVSqJKdB/T5M+FLcRPjz4TDacRf3OCfNUzw==}
    dependencies:
      errno: 0.1.8
    dev: false

  /worker-rpc/0.1.1:
    resolution: {integrity: sha512-P1WjMrUB3qgJNI9jfmpZ/htmBEjFh//6l/5y8SD9hg1Ef5zTTVVoRjTrTEzPrNBQvmhMxkoTsjOXN10GWU7aCg==}
    dependencies:
      microevent.ts: 0.1.1
    dev: false

  /wrap-ansi/2.1.0:
    resolution: {integrity: sha1-2Pw9KE3QV5T+hJc8rs3Rz4JP3YU=}
    engines: {node: '>=0.10.0'}
    dependencies:
      string-width: 1.0.2
      strip-ansi: 3.0.1
    dev: false

  /wrap-ansi/5.1.0:
    resolution: {integrity: sha512-QC1/iN/2/RPVJ5jYK8BGttj5z83LmSKmvbvrXPNCLZSEb32KKVDJDl/MOt2N01qU2H/FkzEa9PKto1BqDjtd7Q==}
    engines: {node: '>=6'}
    dependencies:
      ansi-styles: 3.2.1
      string-width: 3.1.0
      strip-ansi: 5.2.0

  /wrap-ansi/7.0.0:
    resolution: {integrity: sha512-YVGIj2kamLSTxw6NsZjoBxfSwsn0ycdesmc4p+Q21c5zPuZ1pl+NfxVdxPtdHvmNVOQ6XSYG4AUtyt/Fi7D16Q==}
    engines: {node: '>=10'}
    dependencies:
      ansi-styles: 4.3.0
      string-width: 4.2.3
      strip-ansi: 6.0.1
    dev: false

  /wrappy/1.0.2:
    resolution: {integrity: sha1-tSQ9jz7BqjXxNkYFvA0QNuMKtp8=}

  /write-file-atomic/2.4.1:
    resolution: {integrity: sha512-TGHFeZEZMnv+gBFRfjAcxL5bPHrsGKtnb4qsFAws7/vlh+QfwAaySIw4AXP9ZskTTh5GWu3FLuJhsWVdiJPGvg==}
    dependencies:
      graceful-fs: 4.2.10
      imurmurhash: 0.1.4
      signal-exit: 3.0.7
    dev: false

  /write-file-atomic/3.0.3:
    resolution: {integrity: sha512-AvHcyZ5JnSfq3ioSyjrBkH9yW4m7Ayk8/9My/DD9onKeu/94fwrMocemO2QAJFAlnnDN+ZDS+ZjAR5ua1/PV/Q==}
    dependencies:
      imurmurhash: 0.1.4
      is-typedarray: 1.0.0
      signal-exit: 3.0.7
      typedarray-to-buffer: 3.1.5
    dev: true

  /write/1.0.3:
    resolution: {integrity: sha512-/lg70HAjtkUgWPVZhZcm+T4hkL8Zbtp1nFNOn3lRrxnlv50SRBv7cR7RqR+GMsd3hUXy9hWBo4CHTbFTcOYwig==}
    engines: {node: '>=4'}
    dependencies:
      mkdirp: 0.5.6
    dev: false

  /ws/5.2.3:
    resolution: {integrity: sha512-jZArVERrMsKUatIdnLzqvcfydI85dvd/Fp1u/VOpfdDWQ4c9qWXe+VIeAbQ5FrDwciAkr+lzofXLz3Kuf26AOA==}
    peerDependencies:
      bufferutil: ^4.0.1
      utf-8-validate: ^5.0.2
    peerDependenciesMeta:
      bufferutil:
        optional: true
      utf-8-validate:
        optional: true
    dependencies:
      async-limiter: 1.0.1
    dev: false

  /ws/6.2.2:
    resolution: {integrity: sha512-zmhltoSR8u1cnDsD43TX59mzoMZsLKqUweyYBAIvTngR3shc0W6aOZylZmq/7hqyVxPdi+5Ud2QInblgyE72fw==}
    peerDependencies:
      bufferutil: ^4.0.1
      utf-8-validate: ^5.0.2
    peerDependenciesMeta:
      bufferutil:
        optional: true
      utf-8-validate:
        optional: true
    dependencies:
      async-limiter: 1.0.1
    dev: false

  /xml-name-validator/3.0.0:
    resolution: {integrity: sha512-A5CUptxDsvxKJEU3yO6DuWBSJz/qizqzJKOMIfUJHETbBw/sFaDxgd6fxm1ewUaM0jZ444Fc5vC5ROYurg/4Pw==}
    dev: false

  /xmlchars/2.2.0:
    resolution: {integrity: sha512-JZnDKK8B0RCDw84FNdDAIpZK+JuJw+s7Lz8nksI7SIuU3UXJJslUthsi+uWBUYOwPFwW7W7PRLRfUKpxjtjFCw==}
    dev: false

  /xtend/4.0.2:
    resolution: {integrity: sha512-LKYU1iAXJXUgAXn9URjiu+MWhyUXHsvfp7mcuYm9dSUKK0/CjtrUwFAxD82/mCWbtLsGjFIad0wIsod4zrTAEQ==}
    engines: {node: '>=0.4'}
    dev: false

  /y18n/4.0.3:
    resolution: {integrity: sha512-JKhqTOwSrqNA1NY5lSztJ1GrBiUodLMmIZuLiDaMRJ+itFd+ABVE8XBjOvIWL+rSqNDC74LCSFmlb/U4UZ4hJQ==}

  /y18n/5.0.8:
    resolution: {integrity: sha512-0pfFzegeDWJHJIAmTLRP2DwHjdF5s7jo9tuztdQxAhINCdvS+3nGINqPd00AphqJR/0LhANUS6/+7SCb98YOfA==}
    engines: {node: '>=10'}
    dev: false

  /yallist/2.1.2:
    resolution: {integrity: sha1-HBH5IY8HYImkfdUS+TxmmaaoHVI=}

  /yallist/3.1.1:
    resolution: {integrity: sha512-a4UGQaWPH59mOXUYnAG2ewncQS4i4F43Tv3JoAM+s2VDAmS9NsK8GpDMLrCHPksFT7h3K6TOoUNn2pb7RoXx4g==}
    dev: false

  /yallist/4.0.0:
    resolution: {integrity: sha512-3wdGidZyq5PB084XLES5TpOSRA3wjXAlIWMhum2kRcv/41Sn2emQ0dycQW4uZXLejwKvg6EsvbdlVL+FYEct7A==}

  /yaml/1.10.2:
    resolution: {integrity: sha512-r3vXyErRCYJ7wg28yvBY5VSoAF8ZvlcW9/BwUzEtUsjvX/DKs24dIkuwjtuprwJJHsbyUbLApepYTR1BN4uHrg==}
    engines: {node: '>= 6'}

  /yargs-parser/11.1.1:
    resolution: {integrity: sha512-C6kB/WJDiaxONLJQnF8ccx9SEeoTTLek8RVbaOIsrAUS8VrBEXfmeSnCZxygc+XC2sNMBIwOOnfcxiynjHsVSQ==}
    dependencies:
      camelcase: 5.3.1
      decamelize: 1.2.0
    dev: false

  /yargs-parser/13.1.2:
    resolution: {integrity: sha512-3lbsNRf/j+A4QuSZfDRA7HRSfWrzO0YjqTJd5kjAq37Zep1CEgaYmrH9Q3GwPiB9cHyd1Y1UwggGhJGoxipbzg==}
    dependencies:
      camelcase: 5.3.1
      decamelize: 1.2.0

  /yargs-parser/20.2.9:
    resolution: {integrity: sha512-y11nGElTIV+CT3Zv9t7VKl+Q3hTQoT9a1Qzezhhl6Rp21gJ/IVTW7Z3y9EWXhuUBC2Shnf+DX0antecpAwSP8w==}
    engines: {node: '>=10'}

  /yargs/12.0.5:
    resolution: {integrity: sha512-Lhz8TLaYnxq/2ObqHDql8dX8CJi97oHxrjUcYtzKbbykPtVW9WB+poxI+NM2UIzsMgNCZTIf0AQwsjK5yMAqZw==}
    dependencies:
      cliui: 4.1.0
      decamelize: 1.2.0
      find-up: 3.0.0
      get-caller-file: 1.0.3
      os-locale: 3.1.0
      require-directory: 2.1.1
      require-main-filename: 1.0.1
      set-blocking: 2.0.0
      string-width: 2.1.1
      which-module: 2.0.0
      y18n: 4.0.3
      yargs-parser: 11.1.1
    dev: false

  /yargs/13.3.2:
    resolution: {integrity: sha512-AX3Zw5iPruN5ie6xGRIDgqkT+ZhnRlZMLMHAs8tg7nRruy2Nb+i5o9bwghAogtM08q1dpr2LVoS8KSTMYpWXUw==}
    dependencies:
      cliui: 5.0.0
      find-up: 3.0.0
      get-caller-file: 2.0.5
      require-directory: 2.1.1
      require-main-filename: 2.0.0
      set-blocking: 2.0.0
      string-width: 3.1.0
      which-module: 2.0.0
      y18n: 4.0.3
      yargs-parser: 13.1.2

  /yargs/16.2.0:
    resolution: {integrity: sha512-D1mvvtDG0L5ft/jGWkLpG1+m0eQxOfaBvTNELraWj22wSVUMWxZUvYgJYcKh6jGGIkJFhH4IZPQhR4TKpc8mBw==}
    engines: {node: '>=10'}
    dependencies:
      cliui: 7.0.4
      escalade: 3.1.1
      get-caller-file: 2.0.5
      require-directory: 2.1.1
      string-width: 4.2.3
      y18n: 5.0.8
      yargs-parser: 20.2.9
    dev: false

  /zip-stream/4.1.0:
    resolution: {integrity: sha512-zshzwQW7gG7hjpBlgeQP9RuyPGNxvJdzR8SUM3QhxCnLjWN2E7j3dOvpeDcQoETfHx0urRS7EtmVToql7YpU4A==}
    engines: {node: '>= 10'}
    dependencies:
      archiver-utils: 2.1.0
      compress-commons: 4.1.1
      readable-stream: 3.6.0
    dev: false

  /zscroller/0.4.8:
    resolution: {integrity: sha512-G5NiNLKx2+QhhvZi2yV1jjVXY50otktxkseX2hG2N/eixohOUk0AY8ZpbAxNqS9oJS/NxItCsowupy2tsXxAMw==}
    dependencies:
      babel-runtime: 6.26.0
    dev: false

  /zwitch/1.0.5:
    resolution: {integrity: sha512-V50KMwwzqJV0NpZIZFwfOD5/lyny3WlSzRiXgA0G7VUnRlqttta1L6UQIHzd6EuBY/cHGfwTIck7w1yH6Q5zUw==}
    dev: true
