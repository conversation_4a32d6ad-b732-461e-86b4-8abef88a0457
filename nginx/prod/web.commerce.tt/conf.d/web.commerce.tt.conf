server {
  listen *:20336;
  access_log /var/log/nginx/nginx-commerce.tantanapp.com-access.log combined buffer=4k;
  error_log /var/log/nginx/nginx-commerce.tantanapp.com-error.log;

  location /commerce {
    rewrite ^/commerce(/.*)$ $1 break;
    root /app/web.commerce.tt/commerce;
    try_files $uri $uri/ /index.html;
  }

  location / {
    root /app/web.commerce.tt/commerce;
    try_files $uri $uri/ /index.html;
  }
}
